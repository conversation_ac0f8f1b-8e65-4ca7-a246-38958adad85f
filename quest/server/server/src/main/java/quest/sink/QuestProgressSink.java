package quest.sink;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toSet;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static quest.util.GameRoundUtils.isDebitOrCreditTx;
import static quest.util.GameRoundUtils.isGameSession;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.slf4j.MDC;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.support.Acknowledgment;

import com.google.common.io.ByteSource;
import com.google.protobuf.Any;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.executor.WorkUnit;
import com.turbospaces.kafka.KafkaWorkUnit;
import com.turbospaces.kafka.sink.AbstractSink;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.metrics.MetricTags;
import com.turbospaces.metrics.Metrics;
import com.turbospaces.rpc.QueuePostTemplate;

import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.repo.QuestJpaManager;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;

import jakarta.inject.Inject;

import lombok.Data;
import loyalty.api.v1.AccountBalanceEventType;
import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.api.v1.QuestlineOverallStatusEnum;
import quest.mapper.QuestlineMapper;
import quest.service.AccountQuestProgressListener;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SignalType;
import uam.api.v1.internal.GameRoundEvent;

public class QuestProgressSink extends AbstractSink {
    private static final Tag TAG_SINK_NAME = Tag.of(MetricTags.NAME, QuestProgressSink.class.getSimpleName());
    private static final String QUEST_PROGRESS_SPIN_TO_ACCOUNT = "quest.progress.spin_to_account.lag";

    private static final Map<QuestlineStatusSpec, QuestlineOverallStatusEnum> UI_STATUS = Map.of(
            QuestlineStatusSpec.COMPLETED, QuestlineOverallStatusEnum.QUESTLINE_COMPLETED,
            QuestlineStatusSpec.UNCLAIMED, QuestlineOverallStatusEnum.QUESTLINE_UNCLAIMED,
            QuestlineStatusSpec.CLAIMED, QuestlineOverallStatusEnum.QUESTLINE_CLAIMED);

    private final MeterRegistry meterRegistry;
    private final QuestServerProperties props;
    private final ThreadPoolContextWorker contextWorker;
    private final QuestJpaManager ebean;
    private final AccountQuestProgressListener accountQuestProgressListener;
    private final QuestlineExternalIdentityManager identityManager;
    private final QueuePostTemplate<?> queuePostTemplate;

    @Inject
    public QuestProgressSink(
            KafkaListenerEndpointRegistry registry,
            MeterRegistry meterRegistry,
            QuestServerProperties props,
            ThreadPoolContextWorker contextWorker,
            QuestJpaManager ebean,
            AccountQuestProgressListener accountQuestProgressListener,
            QuestlineExternalIdentityManager identityManager,
            QueuePostTemplate<?> queuePostTemplate) {
        super(registry, props.QUEST_PROGRESS_SINK_ENABLED);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.props = Objects.requireNonNull(props);
        this.contextWorker = Objects.requireNonNull(contextWorker);
        this.ebean = Objects.requireNonNull(ebean);
        this.accountQuestProgressListener = Objects.requireNonNull(accountQuestProgressListener);
        this.identityManager = Objects.requireNonNull(identityManager);
        this.queuePostTemplate = Objects.requireNonNull(queuePostTemplate);
    }

    @Override
    public void accept(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment, Consumer<?, ?> consumer) {
        var batchTimer = Timer.start(meterRegistry);
        try {
            process(flux, acknowledgment);
        } catch (Throwable e) {
            logger.error("Failed to process batch: ", e);
            throw ExceptionUtils.asRuntimeException(e);
        } finally {
            batchTimer.stop(meterRegistry.timer(Metrics.SINK, List.of(TAG_SINK_NAME)));
        }
    }

    private void process(Flux<KafkaWorkUnit> flux, Acknowledgment acknowledgment) throws Throwable {
        var records = flux.collectList().block();
        List<EventWrapper> events = Collections.emptyList();
        if (records != null) {
            logger.trace("accepted {} events ...", records.size());
            events = records.stream()
                    .map(this::parseWorkUnit)
                    .filter(Objects::nonNull)
                    .filter(this::isValidEvent)
                    .toList();
            logger.debug("After parsing and validation: {} valid events out of {} total records", events.size(), records.size());
        }
        if (records == null || events.isEmpty()) {
            logger.debug("No events to process, acknowledging batch");
            acknowledgment.acknowledge();
            return;
        }
        var eventsPerAccount = getAccountsWithActiveQuestLines(events);
        if (eventsPerAccount.isEmpty()) {
            logger.debug("No accounts with active questlines found, acknowledging batch");
            acknowledgment.acknowledge();
            return;
        }
        if (logger.isTraceEnabled()) {
            logBatch(eventsPerAccount);
        }
        var latch = new CountDownLatch(eventsPerAccount.size());
        var bufferSize = props.QUEST_PROGRESS_SINK_BUFFER_SIZE.get();
        var batchHasError = new AtomicBoolean(false);
        for (var accountEvents : eventsPerAccount.entrySet()) {
            handleAccountEvents(accountEvents.getKey(), accountEvents.getValue(), bufferSize, batchHasError, latch);
        }
        boolean isCompleted = latch.await(props.QUEST_PROGRESS_SINK_BATCH_MAX_WAIT.get().toSeconds(), TimeUnit.SECONDS);
        if (isCompleted && !batchHasError.get()) {
            acknowledgment.acknowledge();
        }
    }

    private EventWrapper parseWorkUnit(KafkaWorkUnit unit) {
        try (InputStream io = unit.value().openStream()) {
            Any any = Any.newBuilder().mergeFrom(io).build();

            if (any.is(GameRoundEvent.class)) {
                var event = any.unpack(GameRoundEvent.class);
                if (logger.isTraceEnabled()) {
                    logger.trace("IN GameRoundEvent {}", event);
                }
                var eventId = getGameRoundEventId(unit, event);
                var accountData = new AccountData(
                        event.getAccount().getId(),
                        event.getAccount().getRoutingKey(),
                        event.getAccount().getBrandName()
                );
                return new EventWrapper(eventId, accountData, event);

            } else if (any.is(LoyaltyAccountBalanceUpdateEvent.class)) {
                var event = any.unpack(LoyaltyAccountBalanceUpdateEvent.class);
                if (logger.isTraceEnabled()) {
                    logger.trace("IN LoyaltyAccountBalanceUpdateEvent {}", event);
                }
                var eventId = getLoyaltyEventId(unit, event);
                var accountData = new AccountData(
                        event.getRouting().getId(),
                        event.getRouting().getHash(),
                        event.getRouting().getBrand()
                );
                return new EventWrapper(eventId, accountData, event);
            }

            return null;
        } catch (Exception e) {
            throw ExceptionUtils.asRuntimeException(e);
        }
    }

    private boolean isValidEvent(EventWrapper wrapper) {
        if (wrapper.payload() instanceof GameRoundEvent event) {
            boolean isValid = Objects.nonNull(event)
                    && isGameSession(event)
                    && isDebitOrCreditTx(event)
                    && (isNotBlank(event.getTxWagerAmount()) || isNotBlank(event.getTxWinAmount()));
            if (!isValid) {
                logger.debug("Invalid GameRoundEvent: sessionType={}, txWagerAmount={}, txWinAmount={}",
                        event.getSessionType(), event.getTxWagerAmount(), event.getTxWinAmount());
            }
            return isValid;
        } else if (wrapper.payload() instanceof LoyaltyAccountBalanceUpdateEvent event) {
            boolean isValid = Objects.nonNull(event)
                    && isNotBlank(event.getVariantCode())
                    && isNotBlank(event.getXpAmount())
                    && event.getEventType() == AccountBalanceEventType.ACCOUNT_BALANCE_EVENT_TYPE_CREDIT;
            if (!isValid) {
                logger.debug("Invalid LoyaltyAccountBalanceUpdateEvent: variantCode={}, xpAmount={}, eventType={}",
                        event.getVariantCode(), event.getXpAmount(), event.getEventType());
            }
            return isValid;
        }
        return false;
    }

    private Map<AccountData, List<EventWrapper>> getAccountsWithActiveQuestLines(Collection<EventWrapper> records) throws Throwable {
        var eventsPerAccount = records.stream().collect(groupingBy(EventWrapper::accountData));
        var remoteAccountIds = eventsPerAccount.keySet().stream().map(AccountData::remoteId).collect(toSet());

        logger.debug("Found {} unique accounts with events: {}", eventsPerAccount.size(), remoteAccountIds);

        Set<Long> remoteAccountIdsWithQuestLines;
        try (var tx = ebean.newReadOnlyTransaction()) {
            remoteAccountIdsWithQuestLines = ebean.questRepo().accountsWithAssignedQuestLines(remoteAccountIds, tx);
        }

        logger.debug("Found {} accounts with active questlines: {}", remoteAccountIdsWithQuestLines.size(), remoteAccountIdsWithQuestLines);

        eventsPerAccount.keySet().removeIf(account -> !remoteAccountIdsWithQuestLines.contains(account.remoteId()));

        logger.debug("After filtering: {} accounts remain with events", eventsPerAccount.size());
        return eventsPerAccount;
    }

    private void handleAccountEvents(AccountData account, List<EventWrapper> events,
                                     Integer bufferSize, AtomicBoolean batchHasError, CountDownLatch latch) {
        var routingKey = new ContextWorkUnit(account.routingKey());
        logger.debug("Handling {} events for account {}", events.size(), account.remoteId());
        contextWorker.forKey(routingKey).submit(() -> {
            Flux.fromIterable(events)
                    .buffer(bufferSize)
                    .doFinally(signalType -> {
                        if (signalType == SignalType.ON_ERROR || signalType == SignalType.CANCEL) {
                            batchHasError.set(true);
                        }
                        latch.countDown();
                    })
                    .subscribe(eventBatch -> {
                        try {
                            MDC.put(MdcTags.MDC_ROUTING_KEY, routingKey.keyStr());
                            List<QuestlineInstance> questLines = getQuestLineInstances(account);

                            logger.debug("Processing {} events for account {} with {} questlines",
                                    eventBatch.size(), account.remoteId(), questLines.size());

                            for (var eventWrapper : eventBatch) {
                                var payload = eventWrapper.payload();
                                long before = System.currentTimeMillis();

                                if (payload instanceof GameRoundEvent gameRoundEvent) {
                                    MDC.put(MdcTags.MDC_TRANSACTION_ID, gameRoundEvent.getSessionId());
                                    logger.debug("Calling onGameRound for account {} with eventId {}", account.remoteId(), eventWrapper.id());
                                    accountQuestProgressListener.onGameRound(account.routingKey(), questLines, eventWrapper.id(), gameRoundEvent);
                                    before = gameRoundEvent.getAt();
                                } else if (payload instanceof LoyaltyAccountBalanceUpdateEvent loyaltyEvent) {
                                    MDC.put(MdcTags.MDC_TRANSACTION_ID, loyaltyEvent.getTransactionCode());
                                    logger.debug("Calling onLoyaltyXpUpdate for account {} with eventId {}", account.remoteId(), eventWrapper.id());
                                    accountQuestProgressListener.onLoyaltyXpUpdate(account.routingKey(), questLines, eventWrapper.id(), loyaltyEvent);
                                    before = loyaltyEvent.getAt();
                                }

                                meterRegistry.timer(QUEST_PROGRESS_SPIN_TO_ACCOUNT)
                                        .record(System.currentTimeMillis() - before, TimeUnit.MILLISECONDS);
                            }

                            for (QuestlineInstance q : questLines) {
                                QuestlineOverallStatusEnum uiStatus = UI_STATUS.get(q.getStatus());
                                if (uiStatus == null) {
                                    continue;
                                }

                                QuestlineMapper.toQuestlineUpdatedEvent(q, uiStatus)
                                        .ifPresent(evt -> queuePostTemplate.sendEvent(evt.build()));
                            }

                        } finally {
                            MDC.remove(MdcTags.MDC_ROUTING_KEY);
                            MDC.remove(MdcTags.MDC_TRANSACTION_ID);
                        }
                    });
        });
    }

    private List<QuestlineInstance> getQuestLineInstances(AccountData accountData) {
        List<QuestlineInstance> questLines;
        try (var tx = ebean.newTransaction()) {
            var brand = identityManager.brandOrCreate(accountData.brandName(), tx);
            var account = identityManager.accountOrCreate(accountData.remoteId(), accountData.routingKey(), brand, tx);

            questLines = ebean.questRepo().activeQuestlineInstances(account, tx);
            logger.debug("Found {} active questlines for account {} before filtering", questLines.size(), accountData.remoteId());

            questLines = questLines.stream()
                    .filter(this::isQuestlineStillValid)
                    .collect(Collectors.toList());

            logger.debug("Found {} active questlines for account {} after filtering", questLines.size(), accountData.remoteId());

            tx.commit();
        } catch (Throwable e) {
            throw ExceptionUtils.asRuntimeException(e);
        }
        return questLines;
    }

    private static String getLoyaltyEventId(KafkaWorkUnit unit, LoyaltyAccountBalanceUpdateEvent event) {
        var eventId = unit.headersAsMap().get("ce_id");
        if (isBlank(eventId)) {
            eventId = event.getTransactionCode() + "_" + unit.offset();
        }
        return eventId;
    }

    private static String getGameRoundEventId(KafkaWorkUnit unit, GameRoundEvent event) {
        var eventId = unit.headersAsMap().get("ce_id");
        if (isBlank(eventId)) {
            eventId = event.getSessionId() + unit.offset();
        }
        return eventId;
    }

    private void logBatch(Map<AccountData, List<EventWrapper>> eventsPerAccount) {
        for (var entry : eventsPerAccount.entrySet()) {
            var accountData = entry.getKey();
            var events = entry.getValue();
            logger.trace("accepted {} events for accountId={}: {}", events.size(), accountData.remoteId(), events);
        }
    }

    private record AccountData(Long remoteId, String routingKey, String brandName) {}

    private record EventWrapper(String id, AccountData accountData, Object payload) {}

    private boolean isQuestlineStillValid(QuestlineInstance questline) {
        boolean isValid = true;
        if (questline.getExpiresAt() != null &&
                questline.getExpiresAt().toInstant().isBefore(Instant.now())) {
            logger.debug("Questline {} expired at {}", questline.getId(), questline.getExpiresAt());
            isValid = false;
        }

        if (!questline.isActive()) {
            logger.debug("Questline {} is not active", questline.getId());
            isValid = false;
        }

        if (questline.isExpired()) {
            logger.debug("Questline {} is expired", questline.getId());
            isValid = false;
        }

        return isValid;
    }

    @Data
    private static class ContextWorkUnit implements WorkUnit {
        private final String key;

        @Override
        public long timestamp() {
            throw new NotImplementedException();
        }

        @Override
        public String topic() {
            throw new NotImplementedException();
        }

        @Override
        public byte[] key() {
            return key.getBytes(StandardCharsets.UTF_8);
        }

        public String keyStr() {
            return key;
        }

        @Override
        public ByteSource value() {
            throw new NotImplementedException();
        }
    }
}