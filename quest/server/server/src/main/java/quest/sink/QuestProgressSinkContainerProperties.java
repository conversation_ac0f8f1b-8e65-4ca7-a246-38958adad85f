package quest.sink;

import org.springframework.kafka.listener.ContainerProperties;

import com.turbospaces.api.CommonTopics;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;

public class QuestProgressSinkContainerProperties extends ContainerProperties {
    public QuestProgressSinkContainerProperties(ApplicationProperties props, QuestProgressSink consumer) {
        super(CommonTopics.asEvents(props).name().toString());
        var manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(props, consumer);
        setAckMode(AckMode.MANUAL);
        setMessageListener(manualAcknowledgingKafkaConsumer);
        setGroupId("quest-progress-sink");
    }
}
