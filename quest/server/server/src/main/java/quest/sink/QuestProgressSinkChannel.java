package quest.sink;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;

public class QuestProgressSinkChannel extends KafkaAcceptorChannel {

    public QuestProgressSinkChannel(ApplicationProperties props, ConsumerFactory<byte[], byte[]> consumerFactory, ContainerProperties cfg) {
        super(props, consumerFactory, cfg);
    }
}
