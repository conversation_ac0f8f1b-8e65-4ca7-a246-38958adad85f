package quest.mapper;

import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.placement.PlacementTemplateTypeSpec;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import quest.api.v1.Placement;
import quest.api.v1.PlacementTypeEnum;

import java.util.Collection;
import java.util.Optional;

@Slf4j
@UtilityClass
public class PlacementMapper {

    public static Collection<Placement> toPlacement(QuestlineTemplate template) {
        return template.getPlacements().stream()
                .map(PlacementMapper::toPlacement)
                .filter(Optional::isPresent)
                .map(Optional::get).toList();
    }

    public static Optional<Placement> toPlacement(engagement.model.quest.line.placement.Placement placement) {
        try {
            Placement.Builder proto = Placement.newBuilder().setPlacementId(placement.getId())
                    .setPlacementTemplateId(placement.placementTemplate().getId()).setQuestlineTemplateId(placement.questlineTemplate().getId())
                    .setTheme(placement.theme()).setUrl1(placement.url1()).setType(toProto(placement.placementTemplate().type()));

            if (placement.url2() != null) {
                proto.setUrl2(placement.url2());
            }

            return Optional.of(proto.build());
        } catch (StatusNotSupportedException e) {
            log.error("Unknown type : {}", e.getMessage());
            return Optional.empty();
        }

    }

    public static  PlacementTypeEnum toProto(PlacementTemplateTypeSpec t) throws StatusNotSupportedException {
        return switch (t) {
            case PlacementTemplateTypeSpec.INBOX_NOTIFICATION -> PlacementTypeEnum.INBOX_NOTIFICATION;
            case PlacementTemplateTypeSpec.HOME_CENTER_STAGE -> PlacementTypeEnum.HOME_CENTER_STAGE;
            case PlacementTemplateTypeSpec.HOME_ICON -> PlacementTypeEnum.HOME_ICON;
            case PlacementTemplateTypeSpec.IN_GAME_ICON -> PlacementTypeEnum.IN_GAME_ICON;
            default -> throw new StatusNotSupportedException("Unknown type: " + t);
        };
    }

}
