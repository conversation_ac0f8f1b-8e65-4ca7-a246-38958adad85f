package quest.mapper;

import com.turbospaces.common.PlatformUtil;
import engagement.model.quest.contribution.QuestContributionEventLog;
import engagement.model.quest.contribution.QuestContributionInstance;
import lombok.experimental.UtilityClass;
import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;
import uam.api.v1.internal.GameRoundEvent;

import java.math.BigDecimal;
import java.util.Date;

@UtilityClass
public class QuestMapper {

    public static QuestContributionEventLog toEventLog(QuestContributionInstance instance, BigDecimal amount,
                                                       GameRoundEvent event, String eventId) {
        var log = new QuestContributionEventLog();
        log.setEventId(eventId);
        log.setContribution(amount);
        log.setQuestContributionInstance(instance);

        var eventTime = new Date(event.getAt());
        log.setEventTime(eventTime);
        log.setAt(PlatformUtil.toLocalUTCDate(eventTime));
        return log;
    }

    public static QuestContributionEventLog toLoyaltyEventLog(QuestContributionInstance instance, BigDecimal amount,
                                                              LoyaltyAccountBalanceUpdateEvent event, String eventId) {
        var log = new QuestContributionEventLog();
        log.setEventId(eventId);
        log.setContribution(amount);
        log.setQuestContributionInstance(instance);

        var eventTime = new Date(event.getAt());
        log.setEventTime(eventTime);
        log.setAt(PlatformUtil.toLocalUTCDate(eventTime));
        return log;
    }

}
