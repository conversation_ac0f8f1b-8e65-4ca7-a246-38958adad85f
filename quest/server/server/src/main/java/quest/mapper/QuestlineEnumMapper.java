package quest.mapper;

import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import engagement.model.quest.line.QuestlineDetailsViewSpec;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineTypeSpec;
import lombok.experimental.UtilityClass;
import quest.api.v1.DetailViewType;
import quest.api.v1.QuestlineStartTriggerTypeSpecPB;
import quest.api.v1.QuestlineTypeSpecPB;

@UtilityClass
public class QuestlineEnumMapper {

    // ----------------------------------------------------------------
    // DetailViewType ↔ QuestlineDetailsViewSpec
    // ----------------------------------------------------------------
    public static DetailViewType toProto(QuestlineDetailsViewSpec domain) {
        if (domain == null) {
            return DetailViewType.UNKNOWN_DETAIL_VIEW;
        }
        return switch (domain) {
            case SIMPLE_QUEST_POPUP -> DetailViewType.SIMPLE_DETAILS_VIEW;
            case MULTIREWARD_QUEST_POPUP -> DetailViewType.MULTIREWARD_DETAIL_VIEW;
            case FULL_LIST -> DetailViewType.LIST_DETAIL_VIEW;
            case SIMPLE_LIST_WITH_BONUS -> DetailViewType.LIST_WITH_BONUS_DETAIL_VIEW;
        };
    }

    public static QuestlineDetailsViewSpec fromProto(DetailViewType proto) throws EnhancedApplicationException {
        return switch (proto) {
            case SIMPLE_DETAILS_VIEW -> QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP;
            case MULTIREWARD_DETAIL_VIEW -> QuestlineDetailsViewSpec.MULTIREWARD_QUEST_POPUP;
            case LIST_DETAIL_VIEW -> QuestlineDetailsViewSpec.FULL_LIST;
            case LIST_WITH_BONUS_DETAIL_VIEW -> QuestlineDetailsViewSpec.SIMPLE_LIST_WITH_BONUS;
            default -> throw EnhancedApplicationException.of(
                    "Unknown detail view type: " + proto,
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        };
    }

    // ----------------------------------------------------------------
    // QuestlineTypeSpecPB ↔ QuestlineTypeSpec
    // ----------------------------------------------------------------
    public static QuestlineTypeSpecPB toProto(QuestlineTypeSpec domain) throws EnhancedApplicationException {
        return switch (domain) {
            case SIMPLE_QUEST -> QuestlineTypeSpecPB.SIMPLE_QUEST;
            case MULTIREWARD_QUEST -> QuestlineTypeSpecPB.MULTIREWARD_QUEST;
            case MULTIPLE_QUESTS -> QuestlineTypeSpecPB.MULTIPLE_QUESTS;
            case SIMPLE_LIST_WITH_BONUS -> QuestlineTypeSpecPB.SIMPLE_LIST_WITH_BONUS;
            default -> throw EnhancedApplicationException.of(
                    "Unknown questline type: " + domain,
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        };
    }

    public static QuestlineTypeSpec fromProto(QuestlineTypeSpecPB proto) throws EnhancedApplicationException {
        return switch (proto) {
            case SIMPLE_QUEST -> QuestlineTypeSpec.SIMPLE_QUEST;
            case MULTIREWARD_QUEST -> QuestlineTypeSpec.MULTIREWARD_QUEST;
            case MULTIPLE_QUESTS -> QuestlineTypeSpec.MULTIPLE_QUESTS;
            case SIMPLE_LIST_WITH_BONUS -> QuestlineTypeSpec.SIMPLE_LIST_WITH_BONUS;
            default -> throw EnhancedApplicationException.of(
                    "Unknown questline type: " + proto,
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        };
    }

    // ----------------------------------------------------------------
    // QuestlineStartTriggerTypeSpecPB ↔ QuestlineStartTriggerTypeSpec
    // ----------------------------------------------------------------
    public static QuestlineStartTriggerTypeSpecPB toProto(QuestlineStartTriggerTypeSpec domain) throws EnhancedApplicationException {
        return switch (domain) {
            case ASSIGNED -> QuestlineStartTriggerTypeSpecPB.QASSIGNED;
            case USER_MAKES_ANY_PROGRESS -> QuestlineStartTriggerTypeSpecPB.USER_MAKES_ANY_PROGRESS;
            default -> throw EnhancedApplicationException.of(
                    "Unknown questline start trigger type: " + domain,
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        };
    }

    public static QuestlineStartTriggerTypeSpec fromProto(QuestlineStartTriggerTypeSpecPB proto) throws EnhancedApplicationException {
        return switch (proto) {
            case QASSIGNED -> QuestlineStartTriggerTypeSpec.ASSIGNED;
            case USER_MAKES_ANY_PROGRESS -> QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS;
            default -> throw EnhancedApplicationException.of(
                    "Unknown questline start trigger type: " + proto,
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        };
    }
}
