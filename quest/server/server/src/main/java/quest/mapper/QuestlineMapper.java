package quest.mapper;

import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.placement.Placement;
import engagement.model.quest.line.placement.PlacementTemplateTypeSpec;
import lombok.experimental.UtilityClass;
import quest.api.v1.QuestlineInfo;
import quest.api.v1.QuestlineOverallStatusEnum;
import quest.api.v1.QuestlineUpdatedEvent;

import java.util.List;
import java.util.Optional;

@UtilityClass
public class QuestlineMapper {

    public static Optional<QuestlineUpdatedEvent.Builder> toQuestlineUpdatedEvent(QuestlineInstance q, QuestlineOverallStatusEnum status) {
        // Check if questline has INBOX_NOTIFICATION placement
        boolean hasInboxNotificationPlacement = false;
        String icon = null;

        if (q.getQuestlineTemplate().getPlacements() != null) {
            List<Placement> inboxPlacements = q.getQuestlineTemplate().getPlacements().stream()
                    .filter(t -> PlacementTemplateTypeSpec.INBOX_NOTIFICATION == t.placementTemplate().type()).toList();
            if (!inboxPlacements.isEmpty()) {
                hasInboxNotificationPlacement = true;
                icon = inboxPlacements.getFirst().url1();
            }
        }

        // Only create event if questline should appear in inbox
        if (!hasInboxNotificationPlacement) {
            return Optional.empty();
        }

        var builder = QuestlineInfo.newBuilder().setStatus(status)
                .setCode(q.getCode().toString())
                .setTitle(q.getQuestlineTemplate().getDisplayName())
                .setDescription(q.getQuestlineTemplate().getDisplayDescription())
                .setExpirationTimestamp(q.getExpiresAt().getTime());

        if (icon != null) {
            builder.setIcon(icon);
        }

        return Optional.of(QuestlineUpdatedEvent.newBuilder()
                .setRouting(CommonMapper.toAccountRouting(q.getAccount()))
                .setInfo(builder.build()));
    }
}
