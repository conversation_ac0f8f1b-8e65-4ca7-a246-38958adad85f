package quest.mapper;

import api.v1.AccountRoutingInfo;
import engagement.model.quest.QuestlineAccount;

public class CommonMapper {

    public static AccountRoutingInfo toAccountRouting(QuestlineAccount account) {
        return AccountRoutingInfo.newBuilder()
                .setHash(account.getHash())
                .setId(account.getRemoteId())
                .setBrand(account.getBrand().getName())
                .build();
    }

}
