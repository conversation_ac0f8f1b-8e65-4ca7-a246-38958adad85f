package quest.mapper;

import java.util.Comparator;
import java.util.UUID;

import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.contribution.LoyaltyXpQuestContributionTemplate;
import engagement.model.quest.contribution.PlayQuestContributionTemplate;
import engagement.model.quest.contribution.QuestContributionTemplate;
import engagement.model.quest.contribution.WagerQuestContributionTemplate;
import engagement.model.quest.contribution.WinQuestContributionTemplate;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.milestone.ProgressMilestone;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import quest.api.v1.CurrencyFilter;
import quest.api.v1.Placement;
import quest.api.v1.ProgressEvent;
import quest.api.v1.QuestInfo;
import quest.api.v1.QuestlineMilestoneInfo;
import quest.api.v1.QuestlineTemplateInfo;

@Slf4j
@UtilityClass
public class QuestlineTemplateMapper {

    public static QuestlineTemplateInfo mapDbToProto(QuestlineTemplate template, boolean locked) throws EnhancedApplicationException {
        QuestlineTemplateInfo.Builder b = QuestlineTemplateInfo.newBuilder();
        b.setLocked(locked);
        b.setTemplateId(template.getId() == null ? 0 : template.getId());
        b.setTemplateCode(template.getCode() == null ? "" : template.getCode().toString());

        b.setName(nullSafe(template.getName()));
        b.setDisplayName(nullSafe(template.getDisplayName()));
        b.setDisplayDescription(nullSafe(template.getDisplayDescription()));
        b.setDisplayTagline(nullSafe(template.getDisplayTagline()));
        b.setDisplayNameShort(nullSafe(template.getDisplayNameShort()));

        b.setActive(template.isActive());
        b.setTest(template.isTest());
        b.setTimeLimited(template.isTimeLimited());
        if (template.getStartAt() != null) {
            b.setAvailableFrom(template.getStartAt().getTime());
        }
        if (template.getEndAt() != null) {
            b.setAvailableTo(template.getEndAt().getTime());
        }
        if (template.getMinutesToStartExpire() != null) {
            b.setHoursToStart(template.getMinutesToStartExpire() / 60);
        }
        if (template.getMinutesToFinishExpire() != null) {
            b.setHoursToComplete(template.getMinutesToFinishExpire() / 60);
        }
        if (template.getMinutesToClaim() != null) {
            b.setHoursToClaim(template.getMinutesToClaim() / 60);
        }
        b.setTermUrl(nullSafe(template.getTermUrl()));
        b.setOptInRequired(template.isOptInRequired());

        // Using the new enum mapper:
        b.setQuestlineType(QuestlineEnumMapper.toProto(template.getType()));
        b.setStartTrigger(QuestlineEnumMapper.toProto(template.getStartTimerTrigger()));

        // Map the detailView type via the enum mapper
        b.setDetailViewType(QuestlineEnumMapper.toProto(template.getDetailView().getType()));
        if (template.getDetailView().getTheme() != null) {
            b.setDetailViewTypeTheme(template.getDetailView().getTheme());
        }
        if (template.getDetailView().getListHeaderImageUrl() != null) {
            b.setListHeaderImageUrl(template.getDetailView().getListHeaderImageUrl());
        }
        if (template.getDetailView().getListHeaderImageUrlUserLost() != null) {
            b.setListHeaderImageUrlUserLost(template.getDetailView().getListHeaderImageUrlUserLost());
        }

        // Map quests
        if (template.getQuests() != null) {
            for (QuestTemplate qt : template.getQuests()) {
                QuestInfo.Builder qi = QuestInfo.newBuilder();
                if (qt.getId() != null) {
                    qi.setId(qt.getId());
                }
                qi.setName(nullSafe(qt.getName()));
                qi.setDisplayName(nullSafe(qt.getDisplayName()));
                qi.setDisplayDescription(nullSafe(qt.getDisplayDescription()));

                qi.setRanking(qt.getOrderingIndex());
                qi.setActionUrl(nullSafe(qt.getActionUrl()));
                qi.setIconUrl(nullSafe(qt.getIconUrl()));

                double threshold = 0.0;
                if (qt.getContributions() != null && !qt.getContributions().isEmpty()) {
                    var c = qt.getContributions().stream()
                            .filter(contrib -> !contrib.isDeleted()).max(Comparator
                                    .comparing(QuestContributionTemplate::getModifiedAt))
                            .orElseThrow(() -> EnhancedApplicationException.of("No valid contributions found for quest: " + qt.getName(), Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST));

                    if (c != null) {
                        // now fill in the proto fields from c
                        switch (c) {
                            case PlayQuestContributionTemplate _ -> qi.setProgressEvent(ProgressEvent.PLAY);
                            case WagerQuestContributionTemplate _ -> qi.setProgressEvent(ProgressEvent.WAGER);
                            case WinQuestContributionTemplate _ -> qi.setProgressEvent(ProgressEvent.WIN);
                            case LoyaltyXpQuestContributionTemplate loyaltyXp -> {
                                qi.setProgressEvent(ProgressEvent.EARN_LOYALTY_XP);
                                if (loyaltyXp.getXpVariantCode() != null) {
                                    qi.setXpVariantCode(loyaltyXp.getXpVariantCode().toString());
                                }
                            }
                            default -> {
                            }
                        }

                        // For non-loyalty XP events, set currency and game filters
                        if (!(c instanceof LoyaltyXpQuestContributionTemplate)) {
                            if (c.getCurrency() != null) {
                                try {
                                    qi.setCurrencyFilter(CurrencyFilter.valueOf(c.getCurrency().name()));
                                } catch (Exception e) {
                                    log.warn("Unrecognized currency: {}", c.getCurrency());
                                }
                            }
                            if (c.getMinWager() != null) {
                                qi.setMinWager(c.getMinWager().toPlainString());
                            }
                            qi.addAllSuppliers(c.getGameSuppliers());
                            qi.addAllGameCodes(c.getGameCodes());
                        }
                        if (c.getMinWager() != null) {
                            qi.setMinWager(c.getMinWager().toPlainString());
                        }
                        qi.addAllSuppliers(c.getGameSuppliers());
                        qi.addAllGameCodes(c.getGameCodes());

                        // for threshold, if needed:
                        if (c.getCompletionThreshold() != null) {
                            threshold = c.getCompletionThreshold().doubleValue();
                        }
                    }
                }

                qi.setUnitsToCompletion(threshold);

                // Quest-level 100% milestone reward
                if (qt.getMilestones() != null) {
                    qt.getMilestones().stream()
                            .filter(pm -> pm.getProgress() == 100)
                            .findFirst()
                            .ifPresent(pm -> {
                                pm.getRewardCodes().forEach(rc -> qi.addRewardCodes(rc.toString()));
                                pm.getRandomRewardCodes().forEach(rc -> qi.addRandomRewardCodes(rc.toString()));
                            });
                }

                b.addQuests(qi.build());
            }
        }

        // Questline-level Milestones
        if (template.getMilestones() != null) {
            for (ProgressMilestone pm : template.getMilestones()) {
                QuestlineMilestoneInfo.Builder msb = QuestlineMilestoneInfo.newBuilder();
                if (pm.getId() != null) {
                    msb.setMilestoneId(pm.getId());
                }
                msb.setProgressRequiredPercentage(pm.getProgress());
                msb.setRewardIconUrl(pm.getRewardIconUrl());
                msb.addAllRewards(pm.getRewardCodes().stream().map(UUID::toString).toList());
                msb.addAllRandomRewards(pm.getRandomRewardCodes().stream().map(UUID::toString).toList());
                b.addMilestones(msb.build());
            }
        }

        // Placements
        if (template.getPlacements() != null) {
            template.getPlacements().forEach(p -> {
                Placement.Builder pb = Placement.newBuilder();
                if (p.getId() != null) {
                    pb.setPlacementId(p.getId());
                }
                if (p.placementTemplate() != null && p.placementTemplate().getId() != null) {
                    pb.setPlacementTemplateId(p.placementTemplate().getId());
                }
                if (template.getId() != null) {
                    pb.setQuestlineTemplateId(template.getId());
                }
                pb.setTheme(nullSafe(p.theme()));
                pb.setUrl1(nullSafe(p.url1()));
                if (p.url2() != null) {
                    pb.setUrl2(p.url2());
                }
                if (p.placementTemplate() != null) {
                    try {
                        pb.setType(PlacementMapper.toProto(p.placementTemplate().type()));
                    } catch (StatusNotSupportedException e) {
                        log.error("Unknown type : {}", e.getMessage());
                    }
                }
                b.addPlacements(pb.build());
            });
        }

        return b.build();
    }

    private static String nullSafe(String s) {
        return s == null ? "" : s;
    }
}
