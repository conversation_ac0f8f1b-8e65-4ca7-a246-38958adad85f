package quest.di;

import api.v1.ApiFactory;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.QueuePostTemplate;
import engagement.repo.QuestJpaManager;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.service.AccountQuestProgressListener;
import quest.service.DatabaseBrandAutoconfigurator;
import quest.service.DefaultAccountQuestProgressListener;
import quest.service.contribution.GameRoundQuestContributionSource;
import quest.service.contribution.LoyaltyXpQuestContributionSource;
import quest.service.contribution.QuestContributionSource;
import quest.worker.DefaultQuestWorkerServiceApi;
import quest.worker.QuestWorkerServiceApi;
import uam.api.v1.internal.GameRoundEvent;

@Configuration
public class QuestServiceApiDiModule {

    @Bean
    public QuestlineExternalIdentityManager questlineExternalIdentityManager(
            ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            QuestJpaManager ebean) {
        return new QuestlineExternalIdentityManager(props, apiFactory, cloud, meterRegistry, ebean);
    }

    @Bean
    public QuestContributionSource<GameRoundEvent> gameRoundQuestContributionSource() {
        return new GameRoundQuestContributionSource();
    }

    @Bean
    public QuestContributionSource<LoyaltyAccountBalanceUpdateEvent> loyaltyXpContributionSource() {
        return new LoyaltyXpQuestContributionSource();
    }

    @Bean
    public AccountQuestProgressListener questProgressService(QuestJpaManager ebean,
                                                             QueuePostTemplate<?> postTemplate,
                                                             ApiFactory apiFactory,
                                                             QuestServerProperties props,
                                                             QuestContributionSource<GameRoundEvent> gameRoundQuestContributionSource,
                                                             QuestContributionSource<LoyaltyAccountBalanceUpdateEvent> loyaltyXpContributionSource
    ) {
        return new DefaultAccountQuestProgressListener(ebean, gameRoundQuestContributionSource, loyaltyXpContributionSource, postTemplate, apiFactory, props);
    }

    @Bean
    public DatabaseBrandAutoconfigurator questDatabaseBrandAutoconfigurator(
            QuestJpaManager ebean,
            QuestServerProperties props) {
        return new DatabaseBrandAutoconfigurator(ebean, props);
    }

    @Bean
    public QuestWorkerServiceApi workerServiceApi(QueuePostTemplate<?> postTemplate) {
        return new DefaultQuestWorkerServiceApi(postTemplate);
    }

}
