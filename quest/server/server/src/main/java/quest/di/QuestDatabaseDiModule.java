package quest.di;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.RedisCacheManagerFactoryBean;
import com.turbospaces.ups.UPSs;
import engagement.QuestDataSourceFactoryBean;
import engagement.QuestDatasourceProvider;
import engagement.QuestServiceEbeanConfiguration;
import io.ebean.config.EncryptKeyManager;
import io.ebean.platform.postgres.Postgres9Platform;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.RelationalServiceInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import quest.QuestEbeanFactoryBean;
import quest.QuestProto;

import javax.sql.DataSource;
import java.util.List;

@Configuration
public class QuestDatabaseDiModule {

    @Bean
    public QuestDataSourceFactoryBean questDs(ApplicationProperties props, MeterRegistry meterRegistry, DynamicCloud cloud) {
        RelationalServiceInfo appUps = UPSs.findRequiredServiceInfoByName(cloud, QuestProto.QUEST_POSTGRES_APP);
        return new QuestDataSourceFactoryBean(props, meterRegistry, appUps);
    }

    @Bean
    public QuestServiceEbeanConfiguration questEbeanConfiguration(
            ApplicationProperties props,
            QuestDatasourceProvider<DataSource> ds,
            RedisCacheManagerFactoryBean cache,
            EncryptKeyManager encryptKeyManager) throws Exception {
        return new QuestServiceEbeanConfiguration(ds.getObject(), props, cache.getObject(), encryptKeyManager, List.of(), new Postgres9Platform());
    }

    @Bean
    public QuestEbeanFactoryBean questEbean(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            Tracer tracer,
            QuestServiceEbeanConfiguration configuration) {
        return new QuestEbeanFactoryBean(props, meterRegistry, tracer, configuration);
    }

}
