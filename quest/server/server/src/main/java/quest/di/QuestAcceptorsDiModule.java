package quest.di;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaRequestConsumer;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;
import engagement.repo.QuestJpaManager;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.core.MicrometerConsumerListener;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.kafka.sink.QuestBatchEventStreamKafkaWithMetricsConsumerFactory;
import quest.kafka.sink.QuestBatchKafkaConsumerProperties;
import quest.kafka.QuestKafkaAcceptorChannel;
import quest.kafka.QuestReadKafkaAcceptorChannel;
import quest.kafka.QuestReadRequestContainerProperties;
import quest.kafka.QuestRequestContainerProperties;
import quest.kafka.QuestRequestKafkaWithMetricsConsumerFactory;
import quest.service.AccountQuestProgressListener;
import quest.sink.QuestProgressSink;
import quest.sink.QuestProgressSinkChannel;
import quest.sink.QuestProgressSinkContainerProperties;

import java.security.KeyStore;

@Configuration
public class QuestAcceptorsDiModule {

    //
    // ~ request
    //

    @Bean
    public QuestRequestContainerProperties questRequestContainerProps(
            ApplicationProperties props,
            KafkaRequestConsumer consumer) {
        return new QuestRequestContainerProperties(props, consumer);
    }

    @Bean
    public QuestReadRequestContainerProperties questReadRequestContainerProps(
            ApplicationProperties props,
            KafkaRequestConsumer consumer) {
        return new QuestReadRequestContainerProperties(props, consumer);
    }

    @Bean
    public QuestRequestKafkaWithMetricsConsumerFactory questRequestConsumerFactory(
            KafkaConsumerProperties containerProps,
            MicrometerConsumerListener<byte[], byte[]> listener) {
        return new QuestRequestKafkaWithMetricsConsumerFactory(containerProps, listener);
    }

    @Bean
    public QuestKafkaAcceptorChannel questRequestChannel(
            ApplicationProperties props,
            QuestRequestKafkaWithMetricsConsumerFactory consumerFactory,
            QuestRequestContainerProperties cfg) {
        return new QuestKafkaAcceptorChannel(props, consumerFactory, cfg);
    }

    @Bean
    public QuestReadKafkaAcceptorChannel questReadRequestChannel(
            ApplicationProperties props,
            QuestRequestKafkaWithMetricsConsumerFactory consumerFactory,
            QuestReadRequestContainerProperties cfg) {
        return new QuestReadKafkaAcceptorChannel(props, consumerFactory, cfg);
    }

    // ~ progress sink

    @Bean
    public QuestProgressSink questProgressSink(
            KafkaListenerEndpointRegistry registry,
            MeterRegistry meterRegistry,
            QuestServerProperties props,
            ThreadPoolContextWorker defaultThreadPoolContextWorker,
            QuestJpaManager ebean,
            AccountQuestProgressListener listener,
            QuestlineExternalIdentityManager identityManager,
            QueuePostTemplate<?> queuePostTemplate) {
        return new QuestProgressSink(registry, meterRegistry, props, defaultThreadPoolContextWorker, ebean, listener, identityManager, queuePostTemplate);
    }

    @Bean
    public QuestProgressSinkContainerProperties questProgressSinkContainerProperties(
            ApplicationProperties props,
            QuestProgressSink consumer) {
        return new QuestProgressSinkContainerProperties(props, consumer);
    }

    @Bean
    public QuestProgressSinkChannel questProgressSinkChannel(
            ApplicationProperties props,
            QuestBatchEventStreamKafkaWithMetricsConsumerFactory consumerFactory,
            QuestProgressSinkContainerProperties cfg) {
        return new QuestProgressSinkChannel(props, consumerFactory, cfg);
    }

    @Bean
    public QuestBatchKafkaConsumerProperties batchKafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
        KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        return new QuestBatchKafkaConsumerProperties(props, keyStore, si);
    }

    @Bean
    public QuestBatchEventStreamKafkaWithMetricsConsumerFactory batchEventsConsumerFactory(
            QuestBatchKafkaConsumerProperties cfg,
            MicrometerConsumerListener<byte[], byte[]> listener) {
        return new QuestBatchEventStreamKafkaWithMetricsConsumerFactory(cfg, listener);
    }

}
