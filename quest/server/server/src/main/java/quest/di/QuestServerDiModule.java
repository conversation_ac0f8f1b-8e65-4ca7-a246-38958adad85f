package quest.di;

import com.turbospaces.cfg.DynamicPropertyFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import quest.QuestServerProperties;

@Configuration
@Import({QuestEmbeddedServerDiModule.class,})
public class QuestServerDiModule {
    @Bean
    public QuestServerProperties questProps(DynamicPropertyFactory cloud) {
        return new QuestServerProperties(cloud);
    }
}
