package quest.di;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;
import quest.handlers.AbstractRequestHandler;

@Configuration
@Import({
        QuestDatabaseDiModule.class,
        QuestServiceApiDiModule.class,
        QuestAcceptorsDiModule.class,
})
@ComponentScan(basePackageClasses = {
        AbstractRequestHandler.class
})
@EnableKafka
public class QuestEmbeddedServerDiModule {
}
