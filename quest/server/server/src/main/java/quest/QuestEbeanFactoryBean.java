package quest;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.AbstractEbeanFactoryBean;
import engagement.QuestServiceEbeanConfiguration;
import engagement.repo.QuestEbeanJpaManager;
import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class QuestEbeanFactoryBean extends AbstractEbeanFactoryBean<QuestEbeanJpaManager> {

    public QuestEbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, QuestServiceEbeanConfiguration config) {
        super(props, meterRegistry, tracer, config);
    }

    @Override
    public Class<?> getObjectType() {
        return QuestEbeanJpaManager.class;
    }

    @Override
    protected QuestEbeanJpaManager createEbean(SpiEbeanServer db) {
        return new QuestEbeanJpaManager(props, meterRegistry, tracer, db, timer);
    }
}
