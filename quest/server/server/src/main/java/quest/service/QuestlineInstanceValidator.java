package quest.service;

import engagement.model.quest.QuestTemplate;
import engagement.model.quest.contribution.LoyaltyXpQuestContributionTemplate;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.repo.QuestJpaManager;
import io.ebean.Transaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

/**
 * Validates questline instance creation based on user's loyalty system assignments.
 */
@Slf4j
@RequiredArgsConstructor
public class QuestlineInstanceValidator {

    /**
     * Validates that a user can have a questline instance created based on their loyalty system assignments.
     *
     * @param template The questline template to validate
     * @param accountRemoteId The user's remote account ID
     * @param brandName The brand name
     * @param tx Database transaction
     * @return true if the instance can be created, false otherwise
     */
    public boolean canCreateInstance(QuestlineTemplate template, Long accountRemoteId, String brandName, Transaction tx) {
        // Check if any quest in the template uses EARN_LOYALTY_XP progress event
        Set<UUID> requiredXpVariants = getRequiredXpVariants(template);

        if (requiredXpVariants.isEmpty()) {
            // No loyalty XP quests, creation is allowed
            return true;
        }

        // Check if user has loyalty systems assigned that use the required XP variants
        return hasRequiredLoyaltySystems(accountRemoteId, brandName, requiredXpVariants, tx);
    }

    /**
     * Gets all XP variant codes required by EARN_LOYALTY_XP quests in the template.
     */
    private Set<UUID> getRequiredXpVariants(QuestlineTemplate template) {
        return template.getQuests().stream()
                .flatMap(quest -> quest.getContributions().stream())
                .filter(LoyaltyXpQuestContributionTemplate.class::isInstance)
                .map(LoyaltyXpQuestContributionTemplate.class::cast)
                .map(LoyaltyXpQuestContributionTemplate::getXpVariantCode)
                .collect(Collectors.toSet());
    }

    /**
     * Checks if the user has any loyalty system assigned that uses one of the required XP variants.
     * This would require access to loyalty service data or a cross-service call.
     */
    private boolean hasRequiredLoyaltySystems(Long accountRemoteId, String brandName, Set<UUID> requiredXpVariants, Transaction tx) {
        // Note: This is a simplified implementation. In a real system, you would either:
        // 1. Make a cross-service call to the loyalty service
        // 2. Have a local cache/replica of loyalty system assignments
        // 3. Use an event-driven approach to maintain user loyalty system state

        log.info("Validating loyalty system assignments for user {} with required XP variants: {}",
                accountRemoteId, requiredXpVariants);

        // For now, we'll implement this as a service call or assume validation happens elsewhere
        // In a real implementation, you'd call something like:
        // return loyaltyServiceApi.hasLoyaltySystemWithVariants(accountRemoteId, brandName, requiredXpVariants);

        // Temporary implementation - you should replace this with actual loyalty service integration
        log.warn("Loyalty system validation not fully implemented - allowing instance creation");
        return true;
    }
}