package quest.service.contribution;

import engagement.model.quest.contribution.LoyaltyXpQuestContributionTemplate;
import engagement.model.quest.contribution.QuestContributionTemplate;
import lombok.extern.slf4j.Slf4j;
import loyalty.api.v1.AccountBalanceEventType;
import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;

import java.math.BigDecimal;

@Slf4j
public class LoyaltyXpQuestContributionSource implements QuestContributionSource<LoyaltyAccountBalanceUpdateEvent> {

    @Override
    public BigDecimal resolveAmount(LoyaltyAccountBalanceUpdateEvent event, QuestContributionTemplate template) {
        if (!(template instanceof LoyaltyXpQuestContributionTemplate loyaltyXpTemplate)) {
            throw new IllegalArgumentException("Template must be LoyaltyXpQuestContributionTemplate for loyalty XP events");
        }

        // Check if event matches the XP variant filter
        if (!loyaltyXpTemplate.isMatchedByXpVariant(event.getVariantCode())) {
            return BigDecimal.ZERO;
        }

        // Only count XP additions (credits), not debits or expiry
        if (event.getEventType() != AccountBalanceEventType.ACCOUNT_BALANCE_EVENT_TYPE_CREDIT) {
            return BigDecimal.ZERO;
        }

        // Return the XP amount earned (base unit is 1 XP as per requirements)
        try {
            return new BigDecimal(event.getXpAmount());
        } catch (NumberFormatException e) {
            // If the XP amount is not a valid number, return zero
            log.warn("Invalid XP amount in event: {}", event, e);
            return BigDecimal.ZERO;
        }
    }
}