package quest.service;

import engagement.model.quest.line.QuestlineInstance;
import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;
import uam.api.v1.internal.GameRoundEvent;

import java.util.List;

public interface AccountQuestProgressListener {

    void onGameRound(String routingKey, List<QuestlineInstance> questLines, String eventId, GameRoundEvent event);

    void onLoyaltyXpUpdate(String routingKey, List<QuestlineInstance> questLines, String eventId, LoyaltyAccountBalanceUpdateEvent event);


}
