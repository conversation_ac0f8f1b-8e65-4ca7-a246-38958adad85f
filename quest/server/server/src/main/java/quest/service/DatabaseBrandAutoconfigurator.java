package quest.service;

import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.line.placement.PlacementTemplateTypeSpec;
import engagement.repo.QuestJpaManager;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.InitializingBean;
import quest.QuestServerProperties;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

@Slf4j
public class DatabaseBrandAutoconfigurator implements InitializingBean {

    private final QuestJpaManager ebean;
    private final QuestServerProperties props;

    public DatabaseBrandAutoconfigurator(QuestJpaManager ebean, QuestServerProperties props) {
        this.ebean = ebean;
        this.props = props;
    }

    @Override
    public void afterPropertiesSet() {
        if (isTrue(props.BRANDS_AUTOCONFIGURATION_ENABLED.get())) {
            configureBrands();
        } else {
            log.info("Brands autoconfiguration disabled");
        }
        if (isTrue(props.PLACEMENT_TEMPLATES_AUTOCONFIGURATION_ENABLED.get())) {
            configurePlacementTemplates();
        } else {
            log.info("PlacementTemplates autoconfiguration disabled");
        }
    }

    private void configureBrands() {
        List<String> brands = props.BRANDS_AUTOCONFIGURATION_LIST.get();
        for (String brand : brands) {
            log.info("Autoconfiguring brand={}", brand);
            try (Transaction tx = ebean.newTransaction()) {
                ebean.questlineBrandRepo().configureBrand(brand, tx);
                tx.commit();
            } catch (Throwable e) {
                ExceptionUtils.wrapAndThrow(e);
            }
            log.info("Autoconfiguring brand={} - done", brand);
        }
    }

    private void configurePlacementTemplates() {
        log.info("Autoconfiguring PlacementTemplates");
        try (Transaction tx = ebean.newTransaction()) {
            for (QuestlineBrand b : ebean.find(QuestlineBrand.class).findList()) {
                Map<String, String> names = props.PLACEMENT_TEMPLATES_AUTOCONFIGURATION_NAME.get(b.getName());
                Map<String, String> themes = props.PLACEMENT_TEMPLATES_AUTOCONFIGURATION_THEMES.get(b.getName());
                for (PlacementTemplateTypeSpec t : PlacementTemplateTypeSpec.values()) {
                    ebean.placementRepo().configurePlacementTemplate(b, t, names.get(t.code()), t.code(),
                            Arrays.asList(themes.get(t.code()).split(",")), tx);
                }
            }
            tx.commit();
        } catch (Throwable e) {
            ExceptionUtils.wrapAndThrow(e);
        }
        log.info("Autoconfiguring PlacementTemplates - done");
    }
}
