package quest.service.contribution;

import engagement.model.quest.contribution.PlayQuestContributionTemplate;
import engagement.model.quest.contribution.QuestContributionTemplate;
import engagement.model.quest.contribution.WagerQuestContributionTemplate;
import engagement.model.quest.contribution.WinQuestContributionTemplate;
import uam.api.v1.internal.GameRoundEvent;

import java.math.BigDecimal;

import static quest.util.GameRoundUtils.isCredit;
import static quest.util.GameRoundUtils.isDebit;
import static quest.util.GameRoundUtils.isNotZero;

public class GameRoundQuestContributionSource implements QuestContributionSource<GameRoundEvent> {

    @Override
    public BigDecimal resolveAmount(GameRoundEvent event, QuestContributionTemplate template) {
        if (template instanceof PlayQuestContributionTemplate) {
            return anyDebitWager(event) ? BigDecimal.ONE : BigDecimal.ZERO;
        } else if (template instanceof WagerQuestContributionTemplate) {
            return anyDebitWager(event) ? new BigDecimal(event.getTxWagerAmount()) : BigDecimal.ZERO;
        } else if (template instanceof WinQuestContributionTemplate) {
            return anyCreditWin(event) ? new BigDecimal(event.getTxWinAmount()) : BigDecimal.ZERO;
        }
        throw new IllegalArgumentException("unknown template type: " + template.getClass());
    }

    // any debit/wager on a game
    public boolean anyDebitWager(GameRoundEvent event) {
        return event.getWalletTransactionsList().stream().anyMatch(tx -> isDebit(tx) || isNotZero(event.getTxWagerAmount()));
    }

    // any win/credit in a game
    public boolean anyCreditWin(GameRoundEvent event) {
        return event.getWalletTransactionsList().stream().anyMatch(tx -> isCredit(tx) || isNotZero(event.getTxWinAmount()));
    }

}
