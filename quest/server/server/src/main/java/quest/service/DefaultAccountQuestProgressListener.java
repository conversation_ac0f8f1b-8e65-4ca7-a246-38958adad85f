package quest.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.ApiFactory;
import engagement.model.quest.Progress;
import engagement.model.quest.QuestInstance;
import engagement.model.quest.contribution.LoyaltyXpQuestContributionTemplate;
import engagement.model.quest.contribution.QuestContributionEventLog;
import engagement.model.quest.contribution.QuestContributionTemplate;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.milestone.ProgressMilestoneInstance;
import engagement.repo.QuestJpaManager;
import io.netty.util.AsciiString;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;
import quest.QuestServerProperties;
import quest.api.v1.MilestoneNotificationStatus;
import quest.api.v1.MilestoneStatusEnum;
import quest.api.v1.MilestoneUpdate;
import quest.api.v1.MilestoneUpdatedEvent;
import quest.api.v1.QuestProgressDetail;
import quest.api.v1.QuestlineProgressUpdateNotification;
import quest.mapper.CommonMapper;
import quest.mapper.QuestMapper;
import quest.service.contribution.QuestContributionSource;
import uam.api.v1.internal.GameRoundEvent;

@Slf4j
@RequiredArgsConstructor
public class DefaultAccountQuestProgressListener implements AccountQuestProgressListener {

    private final QuestJpaManager ebean;
    private final QuestContributionSource<GameRoundEvent> gameRoundContributionSource;
    private final QuestContributionSource<LoyaltyAccountBalanceUpdateEvent> loyaltyXpContributionSource;
    private final QueuePostTemplate<?> postTemplate;
    private final ApiFactory apiFactory;
    private final QuestServerProperties props;

    @Override
    public void onGameRound(String routingKey, List<QuestlineInstance> questLines, String eventId, GameRoundEvent event) {
        log.debug("Processing GameRound event for routingKey={}, eventId={}, questLines={}", routingKey, eventId, questLines.size());

        if (eventAlreadyHandled(eventId)) {
            log.warn("duplicate event for rk={}, eventId={}: {}", routingKey, eventId, event);
            return;
        }

        processEventContribution(questLines, eventId, event, gameRoundContributionSource, this::isMatchedByGameRoundFilters);
    }

    @Override
    public void onLoyaltyXpUpdate(String routingKey, List<QuestlineInstance> questLines, String eventId, LoyaltyAccountBalanceUpdateEvent event) {
        log.debug("Processing LoyaltyXpUpdate event for routingKey={}, eventId={}, questLines={}", routingKey, eventId, questLines.size());

        if (eventAlreadyHandled(eventId)) {
            log.warn("duplicate loyalty XP event for rk={}, eventId={}: {}", routingKey, eventId, event);
            return;
        }

        processEventContribution(questLines, eventId, event, loyaltyXpContributionSource, this::isMatchedByLoyaltyXpFilters);
    }

    private <T> void processEventContribution(
            List<QuestlineInstance> questLines,
            String eventId,
            T event,
            QuestContributionSource<T> contributionSource,
            BiPredicate<QuestContributionTemplate, T> filterMatcher) {

        log.debug("Processing event contribution for {} questlines, eventId={}", questLines.size(), eventId);

        List<QuestContributionEventLog> contributionLog = new ArrayList<>();
        List<QuestlineProgressUpdateNotification.Builder> notifications = new ArrayList<>();
        List<MilestoneUpdatedEvent> newlyAchivedMilestonesEvents = new ArrayList<>();

        for (var questLine : questLines) {
            log.debug("Processing questline {}: canEarnNewRewards={}, isActive={}, inClaimPhase={}",
                    questLine.getId(), questLine.canEarnNewRewards(), questLine.isActive(), questLine.isInClaimPhase());

            if (!questLine.canEarnNewRewards()) {
                log.trace("skip questline that cannot earn new rewards: id={}, status={}, inClaimPhase={}",
                        questLine.getId(), questLine.getStatus(), questLine.isInClaimPhase());
                continue;
            }
            if (!questLine.isActive() || questLine.checkAndExpireIfNeeded()) {
                log.trace("skip inactive or expired questline: id={}, status={}, expired={}",
                        questLine.getId(), questLine.getStatus(), questLine.isExpiredByTime());
                continue;
            }
            contributeQuests(eventId, event, questLine, contributionLog, notifications, contributionSource, filterMatcher);

            for (ProgressMilestoneInstance m : questLine.getAchievedMilestones()) {
                if (m.getId() == null) {
                    var milestoneEvent = MilestoneUpdatedEvent.newBuilder()
                            .setRouting(CommonMapper.toAccountRouting(questLine.getAccount()))
                            .setMilestoneCode(m.getCode().toString())
                            .setQuestlineCode(questLine.getCode().toString())
                            .setStatus(MilestoneStatusEnum.MILESTONE_UNCLAIMED);
                    if (m.getQuestInstance() != null) {
                        milestoneEvent.setQuestCode(m.getQuestInstance().getCode().toString());
                    }
                    newlyAchivedMilestonesEvents.add(milestoneEvent.build());
                }
            }
        }

        try (var tx = ebean.newTransaction()) {
            ebean.saveAll(questLines, tx);
            ebean.saveAll(contributionLog, tx);
            tx.commit();
        } catch (Throwable e) {
            throw ExceptionUtils.asRuntimeException(e);
        }

        sendMilestoneAchieved(newlyAchivedMilestonesEvents);
        notifications.forEach(ntf -> {
            log.trace("Sending detailed progress notification: questline={}, questlineProgress={}, questCount={}",
                    ntf.getQuestlineCode(), ntf.getProgress(), ntf.getQuestProgressCount());
            postTemplate.sendNotify(apiFactory.notificationMapper().pack(ntf), AsciiString.of(ntf.getRouting().getHash()));
        });
    }

    private void sendMilestoneAchieved(List<MilestoneUpdatedEvent> events) {
        for (MilestoneUpdatedEvent e : events) {
            postTemplate.sendEvent(e);
        }
    }

    private <T> void contributeQuests(
            String eventId,
            T event,
            QuestlineInstance questLine,
            List<QuestContributionEventLog> contributionLog,
            List<QuestlineProgressUpdateNotification.Builder> detailedNotifications,
            QuestContributionSource<T> contributionSource,
            BiPredicate<QuestContributionTemplate, T> filterMatcher) {

        log.debug("Contributing to quests for questline {}, eventId={}", questLine.getId(), eventId);

        Set<UUID> updatedQuestCodes = new HashSet<>();
        List<MilestoneUpdate> allMilestoneUpdates = new ArrayList<>();

        for (QuestInstance quest : questLine.getQuests()) {
            log.debug("Processing quest {}: isActive={}, status={}, isContributable={}, questlineExpired={}",
                    quest.getId(), quest.isActive(), quest.getStatus(), quest.getStatus().isContributable(), quest.getQuestLine().isExpiredByTime());

            if (!quest.isActive() || !quest.getStatus().isContributable() || quest.getQuestLine().isExpiredByTime()) {
                log.trace("skip non-contributable or expired quest: id={}, status={}, questlineExpired={}",
                        quest.getId(), quest.getStatus(), quest.getQuestLine().isExpiredByTime());
                continue;
            }

            // Track quest milestones before contribution
            Set<Long> questMilestoneIdsBefore = quest.getAchievedMilestones().stream()
                    .map(ProgressMilestoneInstance::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            for (var template : quest.getQuestTemplate().getContributions()) {
                log.debug("Checking contribution template for quest {}: filterMatches={}", quest.getId(), filterMatcher.test(template, event));

                if (!filterMatcher.test(template, event)) {
                    log.trace("skip quest contribution due to event filters: id={}", quest.getId());
                    continue;
                }

                var amount = contributionSource.resolveAmount(event, template);
                log.debug("Resolved contribution amount for quest {}: {}", quest.getId(), amount);

                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    log.trace("skip quest contribution due to insufficient amount: id={}", quest.getId());
                    continue;
                }

                log.info("before quest contribution: questId={}, progress={}, contributionAmount={}",
                        quest.getId(), quest.getCompletionProgress(), amount);
                var contributionInstance = quest.contribute(template, amount);
                log.info("after quest contribution: questId={}, progress={}, contributionAmount={}",
                        quest.getId(), quest.getCompletionProgress(), amount);

                updatedQuestCodes.add(quest.getCode());

                if (event instanceof GameRoundEvent gameRoundEvent) {
                    contributionLog.add(QuestMapper.toEventLog(contributionInstance, amount, gameRoundEvent, eventId));
                } else if (event instanceof LoyaltyAccountBalanceUpdateEvent loyaltyEvent) {
                    contributionLog.add(QuestMapper.toLoyaltyEventLog(contributionInstance, amount, loyaltyEvent, eventId));
                }
            }

            // Check for new quest-level milestones
            for (ProgressMilestoneInstance milestone : quest.getAchievedMilestones()) {
                if (milestone.getId() == null || !questMilestoneIdsBefore.contains(milestone.getId())) {
                    // This is a newly achieved milestone
                    allMilestoneUpdates.add(createMilestoneUpdate(milestone, quest.getCode()));
                }
            }
        }

        // Track questline milestones before questline progress update
        Set<Long> questlineMilestoneIdsBefore = questLine.getAchievedMilestones().stream()
                .map(ProgressMilestoneInstance::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Update questline progress with proper precision
        Progress beforeQuestlineProgress = questLine.getContribution();
        questLine.updateProgress();
        Progress afterQuestlineProgress = questLine.getContribution();

        // Check for new questline-level milestones
        for (ProgressMilestoneInstance milestone : questLine.getAchievedMilestones()) {
            if (milestone.getId() == null || !questlineMilestoneIdsBefore.contains(milestone.getId())) {
                // This is a newly achieved questline milestone (no quest code)
                allMilestoneUpdates.add(createMilestoneUpdate(milestone, null));
            }
        }

        if (!updatedQuestCodes.isEmpty() ||
                !beforeQuestlineProgress.isEqualTo(afterQuestlineProgress) ||
                !allMilestoneUpdates.isEmpty()) {
            log.trace("Questline {} progress changed from {} to {}, updated quests: {}, milestone updates: {}",
                    questLine.getCode(), beforeQuestlineProgress, afterQuestlineProgress, updatedQuestCodes, allMilestoneUpdates.size());


            var detailedNotification = QuestlineProgressUpdateNotification.newBuilder()
                    .setRouting(CommonMapper.toAccountRouting(questLine.getAccount()))
                    .setQuestlineCode(questLine.getCode().toString())
                    .setProgress(afterQuestlineProgress.toFullPrecisionString());

            for (QuestInstance quest : questLine.getQuests()) {
                detailedNotification.addQuestProgress(
                        QuestProgressDetail.newBuilder()
                                .setQuestCode(quest.getCode().toString())
                                .setProgress(quest.getCompletionProgress().toFullPrecisionString())
                                .setStatus(quest.getStatus().name())
                                .build());
            }

            detailedNotification.addAllMilestoneUpdates(allMilestoneUpdates);
            detailedNotifications.add(detailedNotification);
        }
    }

    private MilestoneUpdate createMilestoneUpdate(ProgressMilestoneInstance milestone, UUID questCode) {
        var builder = MilestoneUpdate.newBuilder()
                .setProgressRequired(milestone.getMilestoneTemplate().getProgress())
                .setStatus(milestone.isClaimed() ? MilestoneNotificationStatus.MILESTONE_NOTIFICATION_COMPLETED
                        : MilestoneNotificationStatus.MILESTONE_NOTIFICATION_UNCLAIMED)
                .setClaimed(milestone.isClaimed())
                .setClaimCode(milestone.isReadyForClaim() ? milestone.getCode().toString() : "")
                .setImage(milestone.getMilestoneTemplate().getRewardIconUrl() != null ? milestone.getMilestoneTemplate().getRewardIconUrl() : "")
                .addAllRewardCodes(milestone.getMilestoneTemplate().getRewardCodes().stream()
                        .map(UUID::toString).collect(Collectors.toList()))
                .addAllRandomRewardCodes(milestone.getMilestoneTemplate().getRandomRewardCodes().stream()
                        .map(UUID::toString).collect(Collectors.toList()));

        if (questCode != null) {
            builder.setQuestCode(questCode.toString());
        }

        return builder.build();
    }

    private boolean isMatchedByLoyaltyXpFilters(QuestContributionTemplate template, LoyaltyAccountBalanceUpdateEvent event) {
        if (!(template instanceof LoyaltyXpQuestContributionTemplate loyaltyTemplate)) {
            return false;
        }

        return loyaltyTemplate.isMatchedByXpVariant(event.getVariantCode());
    }

    private boolean isMatchedByGameRoundFilters(QuestContributionTemplate template, GameRoundEvent event) {
        // Skip loyalty XP templates for game round events
        if (template instanceof LoyaltyXpQuestContributionTemplate) {
            return false;
        }

        boolean currencyMatch = template.isMatchedByCurrency(event.getCurrency());
        boolean wagerMatch = template.isMatchedByWager(new BigDecimal(event.getTxWagerAmount()));
        boolean supplierMatch = template.isMatchedByGameSuppliers(event.getProductInfo().getSupplier());
        boolean gameCodeMatch = template.isMatchedByGameCodes(event.getProductInfo().getCode());

        boolean overallMatch = currencyMatch && wagerMatch && supplierMatch && gameCodeMatch;

        log.debug("GameRound filter check: currency={}, wager={}, supplier={}, gameCode={}, overall={}",
                currencyMatch, wagerMatch, supplierMatch, gameCodeMatch, overallMatch);

        return overallMatch;
    }

    private boolean eventAlreadyHandled(String eventId) {
        try (var tx = ebean.newReadOnlyTransaction()) {
            boolean exists = ebean.questRepo().contributionEventExists(eventId, tx);
            log.debug("Event {} already handled: {}", eventId, exists);
            return exists;
        } catch (Throwable e) {
            throw ExceptionUtils.asRuntimeException(e);
        }
    }
}