package quest.handlers;

import static java.util.stream.Collectors.toMap;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_CLAIMED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_COMPLETED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_EXPIRED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_IN_PROGRESS;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_NOT_STARTED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_REPLACED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_RETRACTED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_UNCLAIMED;
import static uam.api.v1.Identity.TypeCase.BYACCOUNTID;
import static uam.api.v1.Identity.TypeCase.BYTOKEN;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ReadOnlyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import engagement.model.quest.Progress;
import engagement.model.quest.QuestInstance;
import engagement.model.quest.line.QuestlineDetailsViewSpec;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.QuestlineTypeSpec;
import engagement.model.quest.milestone.ProgressMilestone;
import engagement.model.quest.milestone.ProgressMilestoneInstance;
import engagement.repo.QuestJpaManager;
import lombok.extern.slf4j.Slf4j;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.api.v1.DetailViewType;
import quest.api.v1.ExpirationKind;
import quest.api.v1.GetQuestlinesRequest;
import quest.api.v1.GetQuestlinesResponse;
import quest.api.v1.Milestone;
import quest.api.v1.Quest;
import quest.api.v1.QuestStatusEnum;
import quest.api.v1.Questline;
import quest.api.v1.QuestlineOverallStatusEnum;
import quest.api.v1.QuestlineStartTriggerTypeSpecPB;
import quest.api.v1.QuestlineTypeEnum;
import quest.mapper.PlacementMapper;
import uam.api.v1.Identity;

@Service
@Slf4j
public class GetQuestlinesRequestHandler
        extends AbstractAuthorizedRequestHandler<GetQuestlinesRequest, GetQuestlinesResponse.Builder>
        implements ReadOnlyHandler<GetQuestlinesRequest, GetQuestlinesResponse.Builder> {

    public GetQuestlinesRequestHandler(
            QuestServerProperties props,
            QuestJpaManager ebean,
            QuestlineExternalIdentityManager sessionManager) {
        super(props, ebean, sessionManager);
    }

    @Override
    public void apply(TransactionalRequest<GetQuestlinesRequest, GetQuestlinesResponse.Builder> cmd)
            throws Throwable {

        var request = cmd.request();
        var reply = cmd.reply();
        var account = requiredAccount(request.getIdentity(), cmd);
        try (var tx = ebean.newReadOnlyTransaction()) {
            List<QuestlineInstance> instances = questRepo().activeQuestlineInstances(account, tx);

            List<QuestlineInstance> sortedInstances = getSortedInstances(instances);

            for (QuestlineInstance instance : sortedInstances) {
                QuestlineTemplate tmpl = instance.getQuestlineTemplate();

                Progress lineProgress = instance.getContribution();
                String lineProgressStr = lineProgress.toFullPrecisionString();

                Questline.Builder active = Questline.newBuilder()
                        .setQuestlineCode(instance.getCode().toString())
                        .setTitle(nullSafe(tmpl.getDisplayName()))
                        .setDescription(nullSafe(tmpl.getDisplayDescription()))
                        .setProgressPercent(lineProgressStr)
                        .setOverallStatus(mapQuestlineStatus(instance))
                        .setDetailViewType(mapDetailsView(tmpl.getDetailView().getType()))
                        .setStartTrigger(mapStartTrigger(tmpl.getStartTimerTrigger()));

                active.setListHeaderImageUrl(nullSafe(tmpl.getDetailView().getListHeaderImageUrl()));
                active.setListHeaderImageUrlUserLost(nullSafe(tmpl.getDetailView().getListHeaderImageUrlUserLost()));
                active.setDetailViewTypeTheme(nullSafe(tmpl.getDetailView().getTheme()));
                active.setType(mapQuestlineType(tmpl.getType()));

                var result = instance.computeExpiration();
                if (result.getRight() > 0) {
                    active.setExpirationTimestamp(result.getRight());
                }
                active.setExpirationKind(
                        switch (result.getLeft()) {
                            case START -> ExpirationKind.START;
                            case COMPLETE -> ExpirationKind.COMPLETE;
                            case CLAIM -> ExpirationKind.CLAIM;
                            case NONE -> ExpirationKind.NONE;
                        });

                long totalMs = computeTimecapMillis(tmpl);
                active.setTimecap(totalMs);

                if (tmpl.getTermUrl() != null) {
                    active.setTermsAndConditionsUrl(tmpl.getTermUrl());
                }

                var questlineMilestoneTemplates = tmpl.getMilestones().stream()
                        .filter(ProgressMilestone::hasAnyReward)
                        .toList();
                active.addAllMilestones(getMilestones(instance.getAchievedMilestones(), questlineMilestoneTemplates));

                for (QuestInstance qi : instance.getQuests()) {
                    Progress questProg = qi.getCompletionProgress();
                    String questProgressStr = questProg.toFullPrecisionString(); // or .toFrontendString()

                    Quest.Builder qd = Quest.newBuilder()
                            .setQuestCode(qi.getCode().toString())
                            .setRanking(qi.getQuestTemplate().getOrderingIndex())
                            .setDisplayName(qi.getQuestTemplate().getDisplayName())
                            .setProgressPercent(questProgressStr)
                            .setRanking(qi.getQuestTemplate().getOrderingIndex())
                            .setStatus(mapQuestStatus(qi));

                    if (qi.getQuestTemplate().getDisplayDescription() != null) {
                        qd.setDescription(qi.getQuestTemplate().getDisplayDescription());
                    }

                    if (qi.getQuestTemplate().getActionUrl() != null) {
                        qd.setActionUrl(qi.getQuestTemplate().getActionUrl());
                    }

                    var questMilestoneTemplates = qi.getQuestTemplate().getMilestones().stream()
                            .filter(ProgressMilestone::hasAnyReward)
                            .toList();
                    qd.addAllMilestones(getMilestones(qi.getAchievedMilestones(), questMilestoneTemplates));
                    active.addQuests(qd);
                }
                active.addAllPlacements(PlacementMapper.toPlacement(instance.getQuestlineTemplate()));
                reply.addQuestlines(active);
            }

            tx.commit();
        }
    }

    private List<QuestlineInstance> getSortedInstances(List<QuestlineInstance> instances) {
        return instances.stream()
                .sorted(Comparator
                        .comparingInt(this::sortPriority)
                        .thenComparingLong(instance -> instance.computeExpiration().getRight()))
                .toList();
    }

    private int sortPriority(QuestlineInstance instance) {
        return switch (instance.getStatus()) {
            case UNCLAIMED -> 0;
            case CREATED, CLAIMED -> {
                if (instance.getFirstProgressAt() != null)
                    yield 1;
                yield 2;
            }
            default -> 3;
        };
    }

    private List<Milestone> getMilestones(List<ProgressMilestoneInstance> achievedMilestones,
            List<ProgressMilestone> milestoneTemplates) {
        var achievedMilestonesByTemplateId = achievedMilestones.stream()
                .collect(toMap(pmi -> pmi.getMilestoneTemplate().getId(), Function.identity()));
        List<Milestone> milestones = new ArrayList<>();
        for (ProgressMilestone ms : milestoneTemplates) {
            var milestone = Milestone.newBuilder()
                    .setProgressRequired(ms.getProgress())
                    .addAllRewardCodes(ms.getRewardCodes().stream()
                            .map(UUID::toString).toList())
                    .addAllRandomRewardCodes(ms.getRandomRewardCodes().stream()
                            .map(UUID::toString).toList())
                    .setImage("https://abcd.com/test.png");
            if (achievedMilestonesByTemplateId.containsKey(ms.getId())) {
                var achievedMilestone = achievedMilestonesByTemplateId.get(ms.getId());
                if (achievedMilestone.isReadyForClaim()) {
                    milestone.setClaimCode(achievedMilestone.getCode().toString());
                }
                milestone.setClaimed(achievedMilestone.isClaimed());
            }
            milestones.add(milestone.build());
        }
        return milestones;
    }

    private long computeTimecapMillis(QuestlineTemplate tmpl) {
        if (tmpl.getMinutesToFinishExpire() != null) {
            return tmpl.getMinutesToFinishExpire() * 60_000L;
        }
        return 0L;
    }

    private QuestlineOverallStatusEnum mapQuestlineStatus(QuestlineInstance instance) {
        return switch (instance.getStatus()) {
            case CREATED -> instance.isActive() ? QUESTLINE_IN_PROGRESS : QUESTLINE_NOT_STARTED;
            case UNCLAIMED -> QUESTLINE_UNCLAIMED;
            case CLAIMED -> QUESTLINE_CLAIMED;
            case COMPLETED -> QUESTLINE_COMPLETED;
            case EXPIRED -> QUESTLINE_EXPIRED;
            case REPLACED -> QUESTLINE_REPLACED;
            case RETRACTED -> QUESTLINE_RETRACTED;
        };
    }

    private QuestlineStartTriggerTypeSpecPB mapStartTrigger(QuestlineStartTriggerTypeSpec startTrigger) {
        return switch (startTrigger) {
            case ASSIGNED -> QuestlineStartTriggerTypeSpecPB.QASSIGNED;
            case USER_MAKES_ANY_PROGRESS -> QuestlineStartTriggerTypeSpecPB.USER_MAKES_ANY_PROGRESS;
        };
    }

    private static QuestStatusEnum mapQuestStatus(QuestInstance quest) {
        return switch (quest.getStatus()) {
            case OPTED_OUT -> QuestStatusEnum.QUEST_OPTED_OUT;
            case CREATED -> quest.isActive()
                    ? QuestStatusEnum.QUEST_IN_PROGRESS
                    : QuestStatusEnum.QUEST_OPTED_OUT;
            case UNCLAIMED -> QuestStatusEnum.QUEST_UNCLAIMED;
            case COMPLETED -> QuestStatusEnum.QUEST_COMPLETED;
            case EXPIRED -> QuestStatusEnum.QUEST_EXPIRED;
        };
    }

    private QuestlineTypeEnum mapQuestlineType(QuestlineTypeSpec t) {
        return switch (t) {
            case SIMPLE_QUEST -> QuestlineTypeEnum.SIMPLE;
            case MULTIREWARD_QUEST -> QuestlineTypeEnum.MULTIREWARD;
            case MULTIPLE_QUESTS -> QuestlineTypeEnum.LIST;
            case SIMPLE_LIST_WITH_BONUS -> QuestlineTypeEnum.LIST_WITH_BONUS;
        };
    }

    private DetailViewType mapDetailsView(QuestlineDetailsViewSpec t) {
        return switch (t) {
            case SIMPLE_QUEST_POPUP -> DetailViewType.SIMPLE_DETAILS_VIEW;
            case MULTIREWARD_QUEST_POPUP -> DetailViewType.MULTIREWARD_DETAIL_VIEW;
            case FULL_LIST -> DetailViewType.LIST_DETAIL_VIEW;
            case SIMPLE_LIST_WITH_BONUS -> DetailViewType.LIST_WITH_BONUS_DETAIL_VIEW;
        };
    }

    @Override
    protected Set<Identity.TypeCase> authTypes() {
        return Set.of(BYACCOUNTID, BYTOKEN);
    }

    private static String nullSafe(String input) {
        return input == null ? "" : input;
    }
}
