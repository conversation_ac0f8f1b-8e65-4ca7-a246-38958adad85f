package quest.handlers;

import api.v1.ApplicationException;
import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.repo.QuestJpaManager;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import quest.QuestServerProperties;
import quest.api.v1.RetractQuestlineInstanceRequest;
import quest.api.v1.RetractQuestlineInstanceResponse;
import quest.mapper.QuestlineMapper;

import java.util.UUID;

import static api.v1.Code.ERR_BAD_REQUEST;
import static api.v1.Reason.BAD_REQUEST;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_EXPIRED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_RETRACTED;

@Slf4j
@Service
public class RetractQuestlineInstanceRequestHandler
        extends AbstractRequestHandler<RetractQuestlineInstanceRequest, RetractQuestlineInstanceResponse.Builder>
        implements ModifyHandler<RetractQuestlineInstanceRequest, RetractQuestlineInstanceResponse.Builder> {

    public RetractQuestlineInstanceRequestHandler(
            QuestServerProperties props,
            QuestJpaManager ebean) {
        super(props, ebean);
    }

    @Override
    public void apply(TransactionalRequest<RetractQuestlineInstanceRequest, RetractQuestlineInstanceResponse.Builder> cmd) throws Throwable {
        RetractQuestlineInstanceRequest request = cmd.request();
        RetractQuestlineInstanceResponse.Builder reply = cmd.reply();

        try (Transaction tx = ebean.newTransaction()) {
            UUID code = UUID.fromString(request.getInstanceCode());
            QuestlineInstance instance = ebean.questRepo().requiredQuestlineByCode(code, tx);

            if (instance.getStatus().canTransitionTo(QuestlineStatusSpec.RETRACTED)) {
                instance.setStatus(QuestlineStatusSpec.RETRACTED);
                ebean.questRepo().save(instance, tx);
            } else {
                throw ApplicationException.of("Instance %s is already in terminal state %s".formatted(code, instance.getStatus()), ERR_BAD_REQUEST,
                        BAD_REQUEST);
            }

            tx.commit();

            QuestlineMapper.toQuestlineUpdatedEvent(instance, QUESTLINE_RETRACTED)
                    .ifPresent(cmd::eventStream);
        }

    }
}
