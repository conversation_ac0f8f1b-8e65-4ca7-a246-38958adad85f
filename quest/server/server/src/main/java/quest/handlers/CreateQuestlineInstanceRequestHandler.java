package quest.handlers;

import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_IN_PROGRESS;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_REPLACED;

import java.time.Instant;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.repo.QuestJpaManager;
import lombok.extern.slf4j.Slf4j;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.api.v1.CreateQuestlineInstanceRequest;
import quest.api.v1.CreateQuestlineInstanceResponse;
import quest.mapper.QuestlineMapper;

@Service
@Slf4j
public class CreateQuestlineInstanceRequestHandler
        extends AbstractRequestHandler<CreateQuestlineInstanceRequest, CreateQuestlineInstanceResponse.Builder>
        implements ModifyHandler<CreateQuestlineInstanceRequest, CreateQuestlineInstanceResponse.Builder> {

    private final QuestlineExternalIdentityManager identityManager;

    public CreateQuestlineInstanceRequestHandler(
            QuestServerProperties props,
            QuestJpaManager ebean,
            QuestlineExternalIdentityManager identityManager) {
        super(props, ebean);
        this.identityManager = Objects.requireNonNull(identityManager);
    }

    @Override
    public void apply(TransactionalRequest<CreateQuestlineInstanceRequest, CreateQuestlineInstanceResponse.Builder> cmd)
            throws Throwable {

        CreateQuestlineInstanceRequest request = cmd.request();
        CreateQuestlineInstanceResponse.Builder reply = cmd.reply();

        var externalAccount = request.getAccount();

        try (var tx = ebean.newTransaction()) {
            // 1) brand + account
            var brand = identityManager.brandOrCreate(request.getBrandName(), tx);
            var account = identityManager.accountOrCreate(
                    externalAccount.getAccountId(),
                    externalAccount.getHash(),
                    brand,
                    tx);

            // 2) Validate request has either templateId or code
            if (!request.hasQuestlineTemplateId() && !request.hasQuestlineTemplateCode()) {
                reply.setSuccess(false)
                        .setErrorMessage("QuestlineTemplate ID or CODE missing");
                return;
            }

            // 3) Fetch template
            Optional<QuestlineTemplate> templateOpt = request.hasQuestlineTemplateId()
                    ? questRepo().findQuestlineTemplateById(request.getQuestlineTemplateId(), tx)
                    : questRepo().findQuestlineTemplateByCode(request.getQuestlineTemplateCode(), tx);

            if (templateOpt.isEmpty()) {
                if (request.hasQuestlineTemplateId()) {
                    reply.setSuccess(false)
                            .setErrorMessage("QuestlineTemplate ID [" + request.getQuestlineTemplateId() + "] not found");
                } else {
                    reply.setSuccess(false)
                            .setErrorMessage("QuestlineTemplate CODE [" + request.getQuestlineTemplateCode() + "] not found");
                }
                return;
            }

            QuestlineTemplate template = templateOpt.get();
            // 4) If overrideExisting == true, override existing instances
            if (request.hasOverrideExisting() && request.getOverrideExisting()) {
                var updatedRows = questRepo().overrideExistingQuestlinesAndGet(account, tx);
                log.info("Overrode {} existing questline instances for account={} and templateCode={}",
                        updatedRows.size(), account.getId(), template.getCode().toString());

                for (QuestlineInstance replaced : updatedRows) {
                    QuestlineMapper.toQuestlineUpdatedEvent(replaced, QUESTLINE_REPLACED)
                            .ifPresent(cmd::eventStream);
                }
            } else {
                // 5) If overrideExisting is false/missing, check if an active instance already exists
                var activeInstances = questRepo().findQuestlineActiveInstanceByAccountAndTemplate(
                        account,
                        template.getCode().toString(),
                        tx);
                if (!activeInstances.isEmpty()) {
                    log.warn("Active questline instance(s) already exist for account={} and templateCode={}. "
                                    + "Skipping creation unless overrideExisting=true.",
                            account.getId(), request.getQuestlineTemplateCode());

                    reply.setSuccess(false)
                            .setErrorMessage("Active questline instance already exists. Skipping creation. "
                                    + "Use overrideExisting to replace it.");
                    return;
                }
            }

            // 6) Create the new instance
            var instance = new QuestlineInstance(template, account, parseOrNull(request.getScheduledTime()));
            questRepo().save(instance, tx);

            // 7) Mark success + commit
            reply.setSuccess(true)
                    .setErrorMessage("")
                    .setInstanceId(instance.getId() == null ? 0L : instance.getId())
                    .setInstanceCode(instance.getCode().toString());
            tx.commit();

            // 8) Publish an event if the instance is active
            if (instance.isActive()) {
                QuestlineMapper.toQuestlineUpdatedEvent(instance, QUESTLINE_IN_PROGRESS)
                        .ifPresent(cmd::eventStream);
            }
        }
    }
    private Date parseOrNull(String time) {
        if (time.isBlank()) {
            return null;
        }
        try {
            return Date.from(Instant.parse(time));
        } catch (Exception e) {
            return null;
        }
    }
}
