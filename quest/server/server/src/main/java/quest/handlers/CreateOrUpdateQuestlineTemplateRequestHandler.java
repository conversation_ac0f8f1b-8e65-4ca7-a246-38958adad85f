package quest.handlers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import engagement.model.quest.CurrentFilter;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.contribution.LoyaltyXpQuestContributionTemplate;
import engagement.model.quest.contribution.PlayQuestContributionTemplate;
import engagement.model.quest.contribution.QuestContributionTemplate;
import engagement.model.quest.contribution.WagerQuestContributionTemplate;
import engagement.model.quest.contribution.WinQuestContributionTemplate;
import engagement.model.quest.line.DetailViewEmbeddable;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.placement.PlacementTemplate;
import engagement.model.quest.line.placement.PlacementTemplateTypeSpec;
import engagement.model.quest.line.placement.query.QPlacement;
import engagement.model.quest.line.query.QQuestlineTemplate;
import engagement.model.quest.milestone.ProgressMilestone;
import engagement.model.quest.query.QQuestTemplate;
import engagement.repo.QuestJpaManager;
import io.ebean.BeanState;
import io.ebean.DuplicateKeyException;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import quest.QuestServerProperties;
import quest.api.v1.CreateOrUpdateQuestlineTemplateRequest;
import quest.api.v1.CreateOrUpdateQuestlineTemplateResponse;
import quest.api.v1.Placement;
import quest.api.v1.PlacementTypeEnum;
import quest.api.v1.ProgressEvent;
import quest.api.v1.QuestInfo;
import quest.api.v1.QuestlineMilestoneInfo;
import quest.api.v1.QuestlineTemplateInfo;
import quest.api.v1.RetractAllQuestlineInstancesByTemplateWorkerRequest;
import quest.mapper.QuestlineEnumMapper;
import quest.mapper.QuestlineTemplateMapper;
import quest.worker.QuestWorkerServiceApi;
import quest.worker.api.v1.QuestBackgroundRequest;

@Service
@Slf4j
public class CreateOrUpdateQuestlineTemplateRequestHandler
        extends AbstractRequestHandler<CreateOrUpdateQuestlineTemplateRequest, CreateOrUpdateQuestlineTemplateResponse.Builder>
        implements ModifyHandler<CreateOrUpdateQuestlineTemplateRequest, CreateOrUpdateQuestlineTemplateResponse.Builder> {

    private static final Set<String> ALLOWED_TEMPLATE_FIELDS_FOR_UPDATE = Set.of(
            QQuestlineTemplate.Alias.name.toString(),
            QQuestlineTemplate.Alias.displayName.toString(),
            QQuestlineTemplate.Alias.displayNameShort.toString(),
            QQuestlineTemplate.Alias.displayDescription.toString(),
            QQuestlineTemplate.Alias.displayTagline.toString(),
            QQuestlineTemplate.Alias.termUrl.toString(),
            QQuestlineTemplate.Alias.timeLimited.toString(),
            QQuestlineTemplate.Alias.startAt.toString(),
            QQuestlineTemplate.Alias.endAt.toString(),
            QQuestlineTemplate.Alias.quests.toString(),
            QQuestlineTemplate.Alias.placements.toString());

    private static final Set<String> ALLOWED_QUEST_FIELDS_FOR_UPDATE = Set.of(
            QQuestTemplate.Alias.name.toString(),
            QQuestTemplate.Alias.displayName.toString(),
            QQuestTemplate.Alias.displayDescription.toString(),
            QQuestTemplate.Alias.actionUrl.toString(),
            QQuestTemplate.Alias.iconUrl.toString());

    private static final Set<String> ALLOWED_PLACEMENT_FIELDS_FOR_UPDATE = Set.of(
            QPlacement.Alias.theme.toString(),
            QPlacement.Alias.url1.toString(),
            QPlacement.Alias.url2.toString());

    private final QuestWorkerServiceApi questWorkerServiceApi;

    public CreateOrUpdateQuestlineTemplateRequestHandler(
            QuestServerProperties props,
            QuestWorkerServiceApi questWorkerServiceApi,
            QuestJpaManager ebean) {
        super(props, ebean);
        this.questWorkerServiceApi = questWorkerServiceApi;
    }

    @Override
    public void apply(
            TransactionalRequest<CreateOrUpdateQuestlineTemplateRequest, CreateOrUpdateQuestlineTemplateResponse.Builder> cmd) throws Throwable {

        CreateOrUpdateQuestlineTemplateRequest req = cmd.request();
        CreateOrUpdateQuestlineTemplateResponse.Builder reply = cmd.reply();

        try (Transaction tx = ebean.newTransaction()) {

            QuestlineTemplate template = findOrCreateTemplate(req, tx);

            // If it's a "test" template, we auto-retract all existing questline instances
            if (req.hasQuestlineTemplate() && req.getQuestlineTemplate().getTest()) {
                var job = RetractAllQuestlineInstancesByTemplateWorkerRequest.newBuilder()
                        .setQuestlineTemplateCode(template.getCode().toString())
                        .build();

                var backgroundRequest = QuestBackgroundRequest.newBuilder()
                        .setRequest(Any.pack(job))
                        .setRoutingKey(template.getBrand().getName())
                        .build();

                questWorkerServiceApi.processInBackground(backgroundRequest);

                log.info("Auto-retract triggered for test questline template code={}", template.getCode());
            }

            // If it's not a test template, check if it's locked (has active instances).
            boolean locked = !req.getQuestlineTemplate().getTest() && isLocked(template, tx);
            if (locked) {
                validateLockedTemplateConstraints(template, req.getQuestlineTemplate());
            }

            applyTemplateUpdates(template, req.getQuestlineTemplate(), tx, locked);

            ebean.questRepo().save(template, tx);

            QuestlineTemplateInfo finalProto = QuestlineTemplateMapper.mapDbToProto(template, locked);
            reply.setQuestlineTemplate(finalProto);
            tx.commit();
        } catch (DuplicateKeyException e) {
            throw EnhancedApplicationException.of(e.getMessage(), Code.ERR_BAD_REQUEST, Reason.BAD_REQUEST);
        }
    }

    private QuestlineTemplate findOrCreateTemplate(CreateOrUpdateQuestlineTemplateRequest req, Transaction tx)
            throws ApplicationException {
        // 1) If templateId is provided, load by id
        if (req.hasTemplateId() && req.getTemplateId() > 0) {
            Optional<QuestlineTemplate> maybe = ebean.questRepo().findQuestlineTemplateById(req.getTemplateId(), tx);
            if (maybe.isEmpty()) {
                throw EnhancedApplicationException.of(
                        "No questline template found by ID " + req.getTemplateId(),
                        Code.ERR_NOT_FOUND,
                        Reason.BAD_REQUEST);
            }
            return maybe.get();
        }
        // 2) else if templateCode is provided, load by code
        if (req.hasTemplateCode() && !req.getTemplateCode().isBlank()) {
            Optional<QuestlineTemplate> maybe = ebean.questRepo().findQuestlineTemplateByCode(req.getTemplateCode(), tx);
            if (maybe.isEmpty()) {
                throw EnhancedApplicationException.of(
                        "No questline template found by CODE " + req.getTemplateCode(),
                        Code.ERR_NOT_FOUND,
                        Reason.BAD_REQUEST);
            }
            return maybe.get();
        }
        // 3) otherwise, create a new template
        QuestlineBrand brand = ebean.questlineBrandRepo().requiredBrand(req.getBrandName(), tx);
        QuestlineTemplate tmpl = new QuestlineTemplate();
        tmpl.setBrand(brand);
        tmpl.setCode(PlatformUtil.randomUUID());
        return tmpl;
    }

    /**
     * Check if template is locked (non-test and has any active questline instance references)
     */
    private boolean isLocked(QuestlineTemplate template, Transaction tx) {
        return ebean.questRepo().hasAnyActiveInstance(template.getId(), tx);
    }

    private void validateLockedTemplateConstraints(QuestlineTemplate template, QuestlineTemplateInfo reqInfo)
            throws EnhancedApplicationException {
        disallowAddOrRemoveQuests(template, reqInfo);
        disallowAddOrRemovePlacements(template, reqInfo);
        verifyAllowedTemplateFields(template);
    }

    /**
     * Ensure no new placements or removed placements when locked.
     */
    private void disallowAddOrRemovePlacements(QuestlineTemplate existing, QuestlineTemplateInfo reqInfo)
            throws EnhancedApplicationException {
        // old IDs
        Set<Long> oldPlacementIds = new HashSet<>();
        existing.getPlacements().forEach(p -> {
            if (p.getId() != null) {
                oldPlacementIds.add(p.getId());
            }
        });
        // new IDs
        Set<Long> newIds = new HashSet<>();
        boolean sawNewPlacementWithoutId = false;
        for (Placement pProto : reqInfo.getPlacementsList()) {
            if (pProto.hasPlacementId() && pProto.getPlacementId() > 0) {
                newIds.add(pProto.getPlacementId());
            } else {
                sawNewPlacementWithoutId = true;
            }
        }
        if (sawNewPlacementWithoutId) {
            throw EnhancedApplicationException.of(
                    "Cannot add new placement to locked template",
                    Code.ERR_SYSTEM,
                    Reason.BAD_REQUEST);
        }
        for (Long oldId : oldPlacementIds) {
            if (!newIds.contains(oldId)) {
                throw EnhancedApplicationException.of(
                        "Cannot remove an existing placement from locked template",
                        Code.ERR_SYSTEM,
                        Reason.BAD_REQUEST);
            }
        }
    }

    /**
     * Ensure no new quests or removed quests when locked.
     */
    private void disallowAddOrRemoveQuests(QuestlineTemplate existing, QuestlineTemplateInfo reqInfo)
            throws EnhancedApplicationException {
        Set<Long> oldQuestIds = new HashSet<>();
        existing.getQuests().forEach(q -> {
            if (q.getId() != null) oldQuestIds.add(q.getId());
        });

        boolean sawNewQuestWithoutId = false;
        Set<Long> newIds = new HashSet<>();
        for (QuestInfo qi : reqInfo.getQuestsList()) {
            if (qi.hasId()) {
                newIds.add(qi.getId());
            } else {
                sawNewQuestWithoutId = true;
            }
        }
        if (sawNewQuestWithoutId) {
            throw EnhancedApplicationException.of(
                    "Cannot add new quest to locked template",
                    Code.ERR_SYSTEM,
                    Reason.BAD_REQUEST);
        }
        for (Long oldId : oldQuestIds) {
            if (!newIds.contains(oldId)) {
                throw EnhancedApplicationException.of(
                        "Cannot remove an existing quest from locked template",
                        Code.ERR_SYSTEM,
                        Reason.BAD_REQUEST);
            }
        }
    }

    private void verifyAllowedTemplateFields(QuestlineTemplate template)
            throws EnhancedApplicationException {
        BeanState tmplState = ebean.beanState(template);
        Set<String> changedProps = tmplState.changedProps();
        if (!ALLOWED_TEMPLATE_FIELDS_FOR_UPDATE.containsAll(changedProps)) {
            Set<String> disallowed = new HashSet<>(changedProps);
            disallowed.removeAll(ALLOWED_TEMPLATE_FIELDS_FOR_UPDATE);
            if (!disallowed.isEmpty()) {
                throw EnhancedApplicationException.of(
                        "Cannot modify these fields on locked template: " + disallowed,
                        Code.ERR_SYSTEM,
                        Reason.BAD_REQUEST);
            }
        }
        if (template.getQuests() != null) {
            for (QuestTemplate qt : template.getQuests()) {
                Set<String> questChanged = ebean.beanState(qt).changedProps();
                if (!ALLOWED_QUEST_FIELDS_FOR_UPDATE.containsAll(questChanged)) {
                    Set<String> bad = new HashSet<>(questChanged);
                    bad.removeAll(ALLOWED_QUEST_FIELDS_FOR_UPDATE);
                    if (!bad.isEmpty()) {
                        throw EnhancedApplicationException.of(
                                "Cannot modify these quest fields on locked template: " + bad,
                                Code.ERR_SYSTEM,
                                Reason.BAD_REQUEST);
                    }
                }
            }
        }
        if (template.getPlacements() != null) {
            for (var placement : template.getPlacements()) {
                BeanState pState = ebean.beanState(placement);
                Set<String> pChanged = pState.changedProps();
                if (!ALLOWED_PLACEMENT_FIELDS_FOR_UPDATE.containsAll(pChanged)) {
                    Set<String> bad = new HashSet<>(pChanged);
                    bad.removeAll(ALLOWED_PLACEMENT_FIELDS_FOR_UPDATE);
                    if (!bad.isEmpty()) {
                        throw EnhancedApplicationException.of(
                                "Cannot modify these Placement fields on locked template: " + bad,
                                Code.ERR_SYSTEM,
                                Reason.BAD_REQUEST);
                    }
                }
            }
        }
    }

    private void applyTemplateUpdates(QuestlineTemplate template,
            QuestlineTemplateInfo info,
            Transaction tx,
            boolean locked) throws ApplicationException {

        // Basic fields
        template.setName(info.getName());
        template.setDisplayName(info.getDisplayName());
        template.setDisplayDescription(info.getDisplayDescription());
        template.setDisplayNameShort(info.getDisplayNameShort());
        template.setDisplayTagline(info.getDisplayTagline());
        template.setTimeLimited(info.getTimeLimited());
        template.setTermUrl(info.getTermUrl());
        template.setOptInRequired(info.getOptInRequired());
        template.setActive(info.getActive());
        template.setTest(info.getTest());

        template.setType(QuestlineEnumMapper.fromProto(info.getQuestlineType()));
        template.setStartTimerTrigger(QuestlineEnumMapper.fromProto(info.getStartTrigger()));

        // detail view fields
        DetailViewEmbeddable updatedView = DetailViewEmbeddable.builder()
                .type(QuestlineEnumMapper.fromProto(info.getDetailViewType()))
                .theme(info.hasDetailViewTypeTheme() ? info.getDetailViewTypeTheme() : null)
                .listHeaderImageUrl(info.getListHeaderImageUrl())
                .listHeaderImageUrlUserLost(info.hasListHeaderImageUrlUserLost() ? info.getListHeaderImageUrlUserLost() : null)
                .build();
        template.setDetailView(updatedView);

        if (info.hasAvailableFrom()) {
            template.setStartAt(new Date(info.getAvailableFrom()));
        }
        if (info.hasAvailableTo()) {
            template.setEndAt(new Date(info.getAvailableTo()));
        }
        if (info.hasHoursToStart()) {
            template.setMinutesToStartExpire(info.getHoursToStart() * 60);
        }
        if (info.hasHoursToComplete()) {
            template.setMinutesToFinishExpire(info.getHoursToComplete() * 60);
        }
        if (info.hasHoursToClaim()) {
            template.setMinutesToClaim(info.getHoursToClaim() * 60);
        }

        // Questline-level milestones
        if (info.getMilestonesCount() > 0) {
            if (template.getMilestones() == null) {
                template.setMilestones(new ArrayList<>());
            } else {
                // soft-delete old milestones
                for (ProgressMilestone pm : template.getMilestones()) {
                    ebean.delete(pm); // triggers soft-delete
                }
                template.getMilestones().clear();
            }
            for (QuestlineMilestoneInfo ms : info.getMilestonesList()) {
                ProgressMilestone pm = new ProgressMilestone();
                pm.setCode(PlatformUtil.randomUUID());
                pm.setQuestlineTemplate(template);
                pm.setProgress((int) ms.getProgressRequiredPercentage());
                pm.setRewardIconUrl(ms.getRewardIconUrl());
                pm.setRewardCodes(ms.getRewardsList().stream().map(UUID::fromString).toList());
                pm.setRandomRewardCodes(ms.getRandomRewardsList().stream().map(UUID::fromString).toList());
                template.getMilestones().add(pm);
            }
        }

        // Quests
        upsertQuests(template, info, locked);

        // Placements
        upsertPlacements(template, info, tx, locked);
    }

    /**
     * For "unlocked" templates, we remove (soft-delete) only
     * the quests that no longer appear in the request, and
     * keep/update the ones that do match. For "locked", we
     * throw if we see any brand-new or removed quests.
     */
    private void upsertQuests(QuestlineTemplate template,
            QuestlineTemplateInfo info,
            boolean locked)
            throws EnhancedApplicationException {

        if (template.getQuests() == null) {
            template.setQuests(new ArrayList<>());
        }

        // 1) Build a map of existing (old) quests by ID
        Map<Long, QuestTemplate> oldById = new HashMap<>();
        for (QuestTemplate oldQuest : template.getQuests()) {
            if (oldQuest.getId() != null) {
                oldById.put(oldQuest.getId(), oldQuest);
            }
        }

        // 2) We'll accumulate the final list here
        List<QuestTemplate> newList = new ArrayList<>();

        // 3) For each QuestInfo in the request
        for (QuestInfo qi : info.getQuestsList()) {
            QuestTemplate quest;
            if (qi.hasId() && qi.getId() > 0) {
                // an existing quest ID
                quest = oldById.remove(qi.getId());
                if (quest == null) {
                    // The request gave an ID that's not in oldById => brand-new?
                    if (locked) {
                        throw EnhancedApplicationException.of(
                                "Cannot add new quest to locked template (id not found: " + qi.getId() + ")",
                                Code.ERR_SYSTEM,
                                Reason.BAD_REQUEST);
                    } else {
                        // for unlocked => create brand-new
                        quest = buildOrUpdateQuest(null, template, qi, false);
                    }
                } else {
                    // update the existing quest
                    quest = buildOrUpdateQuest(quest, template, qi, locked);
                }
            } else {
                // brand-new quest => no ID was passed
                if (locked) {
                    throw EnhancedApplicationException.of(
                            "Cannot add new quest to locked template",
                            Code.ERR_SYSTEM,
                            Reason.BAD_REQUEST);
                } else {
                    quest = buildOrUpdateQuest(null, template, qi, false);
                }
            }
            newList.add(quest);
        }

        // 4) Anything leftover in oldById was removed from the new request
        if (!oldById.isEmpty()) {
            if (locked) {
                throw EnhancedApplicationException.of(
                        "Cannot remove an existing quest from locked template",
                        Code.ERR_SYSTEM,
                        Reason.BAD_REQUEST);
            } else {
                // for unlocked => soft-delete them
                for (QuestTemplate removed : oldById.values()) {
                    ebean.delete(removed); // sets removed.deleted = true
                }
            }
        }

        // 5) Replace the entire quest list
        template.setQuests(newList);
    }

    /**
     * Create or update a single QuestTemplate from the request data.
     */
    private QuestTemplate buildOrUpdateQuest(QuestTemplate quest,
            QuestlineTemplate template,
            QuestInfo qi,
            boolean locked)
            throws EnhancedApplicationException {
        if (quest == null) {
            quest = new QuestTemplate();
            quest.setCode(PlatformUtil.randomUUID());
            quest.setQuestlineTemplate(template);
        }
        quest.setName(qi.getName());
        quest.setDisplayName(qi.getDisplayName());
        quest.setDisplayDescription(qi.getDisplayDescription());
        quest.setOrderingIndex(qi.getRanking());

        if (qi.hasActionUrl()) {
            quest.setActionUrl(qi.getActionUrl());
        }
        if (qi.hasIconUrl()) {
            quest.setIconUrl(qi.getIconUrl());
        }

        // quest-level contributions
        updateQuestContributions(qi, quest, qi.getProgressEvent(), qi.getUnitsToCompletion(), locked);

        // ensure or update the 100% milestone
        buildOrUpdateQuestMilestone100(
                quest,
                qi.getRewardCodesList(),
                qi.getRandomRewardCodesList(),
                qi.getIconUrl());
        return quest;
    }

    /**
     * If locked => can only adjust the threshold, not replace the entire contributions list.
     * Otherwise => re-create the list.
     */
    private void updateQuestContributions(QuestInfo qi,
            QuestTemplate quest,
            ProgressEvent pbEvent,
            double units,
            boolean locked)
            throws EnhancedApplicationException {
        var current = quest.getContributions();
        if (current == null) {
            current = new ArrayList<>();
            quest.setContributions(current);
        }

        if (current.isEmpty()) {
            // no existing => just add
            QuestContributionTemplate fresh = createContributionByEvent(qi, pbEvent, units);
            fresh.setQuestTemplate(quest);
            current.add(fresh);
            return;
        }

        if (locked) {
            QuestContributionTemplate existing = current.getFirst();
            if (units > 0) {
                existing.setCompletionThreshold(BigDecimal.valueOf(units));
            }

            if (qi.hasMinWager()) {
                existing.setMinWager(new BigDecimal(qi.getMinWager()));
            }
            if (qi.hasCurrencyFilter()) {
                existing.setCurrency(CurrentFilter.valueOf(qi.getCurrencyFilter().name()));
            }

            existing.setGameSuppliers(new LinkedList<>(qi.getSuppliersList()));
            existing.setGameCodes(new LinkedList<>(qi.getGameCodesList()));

        } else {
            // If unlocked => remove old contributions, add a fresh single item
            for (QuestContributionTemplate oldC : current) {
                ebean.delete(oldC); // sets oldC.deleted = true
            }
            current.clear();
            QuestContributionTemplate fresh = createContributionByEvent(qi, pbEvent, units);
            fresh.setQuestTemplate(quest);
            current.add(fresh);
        }
    }

    private QuestContributionTemplate createContributionByEvent(QuestInfo questInfo,
            ProgressEvent pbEvent,
            double units)
            throws EnhancedApplicationException {
        QuestContributionTemplate tmpl;
        switch (pbEvent) {
            case PLAY -> tmpl = new PlayQuestContributionTemplate();
            case WAGER -> tmpl = new WagerQuestContributionTemplate();
            case WIN -> tmpl = new WinQuestContributionTemplate();
            case EARN_LOYALTY_XP -> {
                if (!questInfo.hasXpVariantCode() || questInfo.getXpVariantCode().isBlank()) {
                    throw EnhancedApplicationException.of(
                            "XP variant code is mandatory for EARN_LOYALTY_XP progress event",
                            Code.ERR_BAD_REQUEST,
                            Reason.BAD_REQUEST);
                }
                var loyaltyXpTemplate = new LoyaltyXpQuestContributionTemplate();
                try {
                    loyaltyXpTemplate.setXpVariantCode(UUID.fromString(questInfo.getXpVariantCode()));
                } catch (IllegalArgumentException e) {
                    throw EnhancedApplicationException.of(
                            "Invalid XP variant code format: " + questInfo.getXpVariantCode(),
                            Code.ERR_BAD_REQUEST,
                            Reason.BAD_REQUEST);
                }
                tmpl = loyaltyXpTemplate;
            }
            default -> throw EnhancedApplicationException.of(
                    "Unknown progress event: " + pbEvent,
                    Code.ERR_BAD_REQUEST,
                    Reason.BAD_REQUEST);
        }

        tmpl.setCode(PlatformUtil.randomUUID());
        tmpl.setName(pbEvent.name());
        tmpl.setDisplayName(pbEvent.name());

        if (questInfo.hasMinWager()) {
            tmpl.setMinWager(new BigDecimal(questInfo.getMinWager()));
        }
        if (questInfo.hasCurrencyFilter()) {
            tmpl.setCurrency(CurrentFilter.valueOf(questInfo.getCurrencyFilter().name()));
        }

        tmpl.getGameCodes().clear();
        tmpl.getGameSuppliers().clear();
        tmpl.getGameCodes().addAll(questInfo.getGameCodesList());
        tmpl.getGameSuppliers().addAll(questInfo.getSuppliersList());

        if (units > 0) {
            tmpl.setCompletionThreshold(BigDecimal.valueOf(units));
        }
        return tmpl;
    }
    /**
     * Ensure there's exactly one 100% milestone. If we have more than one, remove extras.
     */
    private void buildOrUpdateQuestMilestone100(QuestTemplate quest,
            List<String> rewardCodes,
            List<String> randomRewardCodes,
            String iconUrl)
            throws EnhancedApplicationException {
        if (quest.getMilestones() == null) {
            quest.setMilestones(new ArrayList<>());
        }
        List<ProgressMilestone> hundredMilestones = new ArrayList<>();
        for (ProgressMilestone pm : quest.getMilestones()) {
            if (pm.getProgress() == 100) {
                hundredMilestones.add(pm);
            }
        }

        // If none, create it
        if (hundredMilestones.isEmpty()) {
            ProgressMilestone ms = new ProgressMilestone();
            ms.setCode(PlatformUtil.randomUUID());
            ms.setProgress(100);
            ms.setRewardIconUrl(iconUrl);
            ms.setQuestTemplate(quest);
            quest.getMilestones().add(ms);
            hundredMilestones.add(ms);
        } else if (hundredMilestones.size() > 1) {
            // remove extras
            for (int i = 1; i < hundredMilestones.size(); i++) {
                ProgressMilestone old = hundredMilestones.get(i);
                ebean.delete(old);
                quest.getMilestones().remove(old);
            }
        }

        ProgressMilestone ms100 = hundredMilestones.get(0);
        ms100.getRewardCodes().clear();
        ms100.getRandomRewardCodes().clear();
        ms100.setRewardIconUrl(iconUrl);

        ms100.setRewardCodes(transformCodesToUUIDs(rewardCodes));
        ms100.setRandomRewardCodes(transformCodesToUUIDs(randomRewardCodes));
    }

    /**
     * Upsert the placements. If locked => we disallow additions or removals. If unlocked => we can remove old & add new.
     */
    private void upsertPlacements(QuestlineTemplate template,
            QuestlineTemplateInfo info,
            Transaction tx,
            boolean locked)
            throws ApplicationException {
        if (template.getPlacements() == null) {
            template.setPlacements(new ArrayList<>());
        }
        Map<Long, engagement.model.quest.line.placement.Placement> oldMap = new HashMap<>();
        for (engagement.model.quest.line.placement.Placement p : template.getPlacements()) {
            if (p.getId() != null) {
                oldMap.put(p.getId(), p);
            }
        }
        List<engagement.model.quest.line.placement.Placement> newList = new ArrayList<>();

        for (Placement pProto : info.getPlacementsList()) {
            engagement.model.quest.line.placement.Placement dbPlacement;
            if (pProto.hasPlacementId() && pProto.getPlacementId() > 0) {
                dbPlacement = oldMap.remove(pProto.getPlacementId());
                if (dbPlacement == null) {
                    throw EnhancedApplicationException.of(
                            "Placement with ID " + pProto.getPlacementId() + " not found on this questline template",
                            Code.ERR_NOT_FOUND,
                            Reason.BAD_REQUEST);
                }
                updatePlacement(dbPlacement, pProto);
            } else {
                // brand new placement
                if (locked) {
                    throw EnhancedApplicationException.of(
                            "Cannot add new placement to locked template",
                            Code.ERR_SYSTEM,
                            Reason.BAD_REQUEST);
                }
                dbPlacement = buildNewPlacement(template, pProto, tx);
            }
            newList.add(dbPlacement);
        }

        // leftover => if locked => disallow removal. if unlocked => soft-delete
        if (!locked) {
            for (engagement.model.quest.line.placement.Placement removed : oldMap.values()) {
                ebean.delete(removed); // sets removed.deleted=true
            }
        } else if (!oldMap.isEmpty()) {
            throw EnhancedApplicationException.of(
                    "Cannot remove an existing placement from locked template",
                    Code.ERR_SYSTEM,
                    Reason.BAD_REQUEST);
        }
        template.setPlacements(newList);
    }

    private engagement.model.quest.line.placement.Placement buildNewPlacement(
            QuestlineTemplate template,
            Placement proto,
            Transaction tx) throws ApplicationException {
        PlacementTemplate placementTemplate = null;
        if (proto.hasPlacementTemplateId()) {
            placementTemplate = ebean.placementRepo().findById(proto.getPlacementTemplateId(), tx);
        } else if (proto.hasType()) {
            placementTemplate = ebean.placementRepo()
                    .findByType(template.getBrand(), fromProto(proto.getType()), tx);
        }
        log.debug("Fetch placement template {}", placementTemplate);

        var db = new engagement.model.quest.line.placement.Placement(placementTemplate, template);
        db.theme(proto.getTheme());
        db.url1(proto.getUrl1());
        if (proto.hasUrl2()) {
            db.url2(proto.getUrl2());
        }
        return db;
    }

    private void updatePlacement(engagement.model.quest.line.placement.Placement dbPlacement, Placement proto) {
        dbPlacement.theme(proto.getTheme());
        dbPlacement.url1(proto.getUrl1());
        if (proto.hasUrl2()) {
            dbPlacement.url2(proto.getUrl2());
        }
    }

    private PlacementTemplateTypeSpec fromProto(PlacementTypeEnum t) {
        return switch (t) {
            case INBOX_NOTIFICATION -> PlacementTemplateTypeSpec.INBOX_NOTIFICATION;
            case HOME_CENTER_STAGE -> PlacementTemplateTypeSpec.HOME_CENTER_STAGE;
            case HOME_ICON -> PlacementTemplateTypeSpec.HOME_ICON;
            case IN_GAME_ICON -> PlacementTemplateTypeSpec.IN_GAME_ICON;
            case UNRECOGNIZED -> null;
        };
    }

    private List<UUID> transformCodesToUUIDs(List<String> rawCodes) {
        List<UUID> result = new ArrayList<>();
        for (String s : rawCodes) {
            try {
                result.add(UUID.fromString(s));
            } catch (Exception e) {
                log.warn("Invalid reward code: {}", s);
            }
        }
        return result;
    }
}
