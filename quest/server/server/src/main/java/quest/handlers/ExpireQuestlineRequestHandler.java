package quest.handlers;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;
import engagement.repo.QuestJpaManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.api.v1.ExpireQuestlineRequest;
import quest.api.v1.ExpireQuestlineResponse;
import quest.mapper.QuestlineMapper;

import java.util.Set;
import java.util.UUID;

import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_EXPIRED;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_IN_PROGRESS;
import static uam.api.v1.Identity.TypeCase.BYACCOUNTID;

@Slf4j
@Service
public class ExpireQuestlineRequestHandler
        extends AbstractAuthorizedRequestHandler<ExpireQuestlineRequest, ExpireQuestlineResponse.Builder>
        implements ModifyHandler<ExpireQuestlineRequest, ExpireQuestlineResponse.Builder> {

    public ExpireQuestlineRequestHandler(QuestServerProperties props,
            QuestJpaManager ebean,
            QuestlineExternalIdentityManager sessionManager) {
        super(props, ebean, sessionManager);
    }

    @Override
    protected Set<uam.api.v1.Identity.TypeCase> authTypes() {
        return Set.of(BYACCOUNTID);
    }

    @Override
    public void apply(TransactionalRequest<ExpireQuestlineRequest, ExpireQuestlineResponse.Builder> cmd) throws Throwable {
        var req = cmd.request();
        var account = requiredAccount(req.getIdentity(), cmd);
        try (var tx = ebean.newTransaction()) {
            var questLine = ebean.questRepo().requiredQuestline(account, UUID.fromString(req.getInstanceCode()), tx);
            if (questLine.expire()) {
                ebean.save(questLine, tx);
            }
            tx.commit();

            QuestlineMapper.toQuestlineUpdatedEvent(questLine, QUESTLINE_EXPIRED)
                    .ifPresent(cmd::eventStream);

        }
    }

}
