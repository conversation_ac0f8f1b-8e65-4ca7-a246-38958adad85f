package quest.handlers;

import java.util.Optional;

import engagement.repo.QuestJpaManager;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ReadOnlyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.ApplicationException;
import api.v1.Code;
import quest.QuestServerProperties;
import quest.api.v1.GetQuestlineTemplateRequest;
import quest.api.v1.GetQuestlineTemplateResponse;
import engagement.model.quest.line.QuestlineTemplate;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import quest.mapper.QuestlineTemplateMapper;

@Service
@Slf4j
public class GetQuestlineTemplateRequestHandler
        extends AbstractRequestHandler<GetQuestlineTemplateRequest, GetQuestlineTemplateResponse.Builder>
        implements ReadOnlyHandler<GetQuestlineTemplateRequest, GetQuestlineTemplateResponse.Builder> {

    public GetQuestlineTemplateRequestHandler(QuestServerProperties props, QuestJpaManager ebean) {
        super(props, ebean);
    }

    @Override
    public void apply(TransactionalRequest<GetQuestlineTemplateRequest, GetQuestlineTemplateResponse.Builder> cmd) throws Throwable {
        GetQuestlineTemplateRequest req = cmd.request();
        GetQuestlineTemplateResponse.Builder reply = cmd.reply();
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            if (req.getQuestlineTemplateCode().isBlank()) {
                throw ApplicationException.of("Missing questlineTemplateCode", Code.ERR_BAD_REQUEST);
            }
            Optional<QuestlineTemplate> maybe = ebean.questRepo().findQuestlineTemplateByCode(req.getQuestlineTemplateCode(), tx);
            if (maybe.isEmpty()) {
                throw ApplicationException.of("QuestlineTemplate not found for code=" + req.getQuestlineTemplateCode(), Code.ERR_NOT_FOUND);
            }
            QuestlineTemplate template = maybe.get();
            if (!template.getBrand().getName().equalsIgnoreCase(req.getBrandName())) {
                throw ApplicationException.of("Brand mismatch", Code.ERR_BAD_REQUEST);
            }
            boolean locked = isLocked(template, tx);
            reply.setQuestlineTemplate(QuestlineTemplateMapper.mapDbToProto(template, locked));
        }
    }

    private boolean isLocked(QuestlineTemplate template, Transaction tx) {
        if (template.isTest()) {
            return false;
        }
        return ebean.questRepo().hasAnyActiveInstance(template.getId(), tx);
    }
}
