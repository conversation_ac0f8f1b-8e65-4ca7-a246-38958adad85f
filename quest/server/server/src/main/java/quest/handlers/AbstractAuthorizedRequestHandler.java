package quest.handlers;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import com.google.protobuf.Message;
import com.turbospaces.dispatch.TransactionalRequest;

import engagement.model.quest.QuestlineAccount;
import engagement.repo.QuestJpaManager;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import uam.api.v1.Identity;

public abstract class AbstractAuthorizedRequestHandler<REQ extends Message, RESP extends Message.Builder>
        extends AbstractRequestHandler<REQ, RESP> {

    protected final QuestlineExternalIdentityManager identityManager;
    private final Set<Identity.TypeCase> authTypes;

    protected AbstractAuthorizedRequestHandler(
            QuestServerProperties props,
            QuestJpaManager ebean,
            QuestlineExternalIdentityManager identityManager) {
        super(props, ebean);
        this.identityManager = Objects.requireNonNull(identityManager);
        this.authTypes = authTypes();
    }

    protected abstract Set<Identity.TypeCase> authTypes();

    protected QuestlineAccount requiredAccount(Identity identity, TransactionalRequest<?, ?> cmd) throws Throwable {
        var account = identityManager.requiredAccount(identity, authTypes, cmd.timestamp());
        account.assertHash(cmd.routingKey().toString());
        return account;
    }

    protected Optional<QuestlineAccount> account(Identity identity, TransactionalRequest<?, ?> cmd) throws Throwable {
        var accountOpt = identityManager.account(identity, authTypes, cmd.timestamp());
        if (accountOpt.isPresent()) {
            accountOpt.get().assertHash(cmd.routingKey().toString());
        }
        return accountOpt;
    }

}
