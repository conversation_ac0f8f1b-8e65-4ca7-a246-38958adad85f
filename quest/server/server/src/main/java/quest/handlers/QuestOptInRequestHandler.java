package quest.handlers;

import java.util.Set;
import java.util.UUID;

import engagement.model.quest.QuestlineAccount;
import engagement.repo.QuestJpaManager;
import org.springframework.stereotype.Service;

import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;

import api.v1.EnhancedApplicationException;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.api.v1.QuestOptInRequest;
import quest.api.v1.QuestOptInResponse;
import engagement.model.quest.QuestInstance;
import engagement.model.quest.QuestStatusSpec;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;

import static api.util.ErrorDetailUtils.detail;
import static api.v1.Code.ERR_NOT_FOUND;
import static api.v1.Reason.SERVER_ERROR;
import static uam.api.v1.Identity.TypeCase.BYTOKEN;

/**
 * Handles the server-side logic for quest "opt-in":
 *   1) Finds the relevant QuestInstance by code and account
 *   2) If quest is in "OPTED_OUT" state, set it to "CREATED" (or "IN_PROGRESS")
 *   3) Save changes
 *   4) Return success
 */
@Slf4j
@Service
public class QuestOptInRequestHandler
        extends AbstractAuthorizedRequestHandler<QuestOptInRequest, QuestOptInResponse.Builder>
        implements ModifyHandler<QuestOptInRequest, QuestOptInResponse.Builder>
{

    public QuestOptInRequestHandler(QuestServerProperties props,
                                    QuestJpaManager ebean,
                                    QuestlineExternalIdentityManager sessionManager) {
        super(props, ebean, sessionManager);
    }

    @Override
    protected Set<uam.api.v1.Identity.TypeCase> authTypes() {
        return Set.of(BYTOKEN);
    }

    @Override
    public void apply(TransactionalRequest<QuestOptInRequest, QuestOptInResponse.Builder> cmd) throws Throwable {
        QuestOptInRequest request = cmd.request();
        QuestOptInResponse.Builder reply = cmd.reply();
        QuestlineAccount account = requiredAccount(request.getIdentity(), cmd);
        try (Transaction tx = ebean.newTransaction()) {
            UUID questCode = UUID.fromString(request.getQuestCode());
            QuestInstance questInstance =
                    ebean.questRepo().requiredQuestInstance(account, questCode, tx);

            if (questInstance.getStatus() != QuestStatusSpec.OPTED_OUT) {
                throw EnhancedApplicationException.of("Quest is not in OPTED_OUT", ERR_NOT_FOUND, SERVER_ERROR)
                        .errorDetail(detail("status", questInstance.getStatus().toString()));
            }

            questInstance.setStatus(QuestStatusSpec.CREATED);

            ebean.questRepo().save(questInstance, tx);

            reply.setSuccess(true);
            tx.commit();
        }
    }
}
