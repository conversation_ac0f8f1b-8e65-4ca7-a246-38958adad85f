package quest.handlers;

import com.google.protobuf.Message;
import com.turbospaces.dispatch.TransactionalRequestHandler;
import com.turbospaces.ebean.JpaManager;
import engagement.repo.QuestJpaManager;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import quest.QuestServerProperties;

@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class AbstractRequestHandler<REQ extends Message, RESP extends Message.Builder> extends TransactionalRequestHandler<REQ, RESP> {

    protected final QuestServerProperties props;

    @Delegate(excludes = JpaManager.class)
    protected final QuestJpaManager ebean;

}
