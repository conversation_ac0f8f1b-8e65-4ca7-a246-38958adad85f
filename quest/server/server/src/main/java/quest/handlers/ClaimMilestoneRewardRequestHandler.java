package quest.handlers;

import static uam.api.v1.Identity.TypeCase.BYTOKEN;

import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.springframework.stereotype.Service;

import com.fasterxml.uuid.Generators;
import com.turbospaces.dispatch.ModifyHandler;
import com.turbospaces.dispatch.TransactionalRequest;
import com.turbospaces.rpc.QueuePostTemplate;

import api.v1.ApplicationException;
import api.v1.Code;
import engagement.model.quest.QuestStatusSpec;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.model.quest.milestone.ProgressMilestoneInstance;
import engagement.repo.QuestJpaManager;
import gamehub.GameHubServiceApi;
import gamehub.api.v1.CreateRewardInstanceRequest;
import gamehub.api.v1.RandomRewardSource;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import quest.QuestServerProperties;
import quest.QuestlineExternalIdentityManager;
import quest.api.v1.ClaimMilestoneRewardRequest;
import quest.api.v1.ClaimMilestoneRewardResponse;
import quest.api.v1.QuestlineOverallStatusEnum;
import quest.mapper.QuestlineMapper;
import reward.api.v1.AccountRewardClaimedEvent;
import reward.api.v1.ExternalAccount;
import reward.api.v1.RewardSource;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;

@Slf4j
@Service
public class ClaimMilestoneRewardRequestHandler
        extends AbstractAuthorizedRequestHandler<ClaimMilestoneRewardRequest, ClaimMilestoneRewardResponse.Builder>
        implements ModifyHandler<ClaimMilestoneRewardRequest, ClaimMilestoneRewardResponse.Builder> {

    private final GameHubServiceApi gameHubServiceApi;
    private final QueuePostTemplate<?> queuePostTemplate;


    public ClaimMilestoneRewardRequestHandler(
            QuestServerProperties props,
            QuestJpaManager ebean,
            QuestlineExternalIdentityManager sessionManager,
            GameHubServiceApi gameHubServiceApi,
            QueuePostTemplate<?> queuePostTemplate) {
        super(props, ebean, sessionManager);
        this.gameHubServiceApi = Objects.requireNonNull(gameHubServiceApi);
        this.queuePostTemplate = Objects.requireNonNull(queuePostTemplate);
    }

    @Override
    protected Set<uam.api.v1.Identity.TypeCase> authTypes() {
        return Set.of(BYTOKEN);
    }

    @Override
    public void apply(TransactionalRequest<ClaimMilestoneRewardRequest, ClaimMilestoneRewardResponse.Builder> cmd) throws Throwable {
        var request = cmd.request();
        var reply = cmd.reply();
        var account = requiredAccount(request.getIdentity(), cmd);
        ProgressMilestoneInstance milestoneInstance;
        try (var tx = ebean.newReadOnlyTransaction()) {
            milestoneInstance = ebean.milestoneRepo().requiredInstance(account, UUID.fromString(request.getInstanceCode()), tx);
        }

        if (!milestoneInstance.isReadyForClaim()) {
            throw ApplicationException.of("milestone cannot be claimed: code=%s", Code.ERR_DENIED, milestoneInstance.getCode());
        }

        validateClaimingPhase(milestoneInstance);

        claimAll(cmd, account, milestoneInstance);
        try (var tx = ebean.newTransaction()) {
            milestoneInstance.claim();
            ebean.save(milestoneInstance, tx);
            completeQuestIfNeeded(milestoneInstance, tx);
            completeQuestlineIfNeeded(milestoneInstance, tx);
            tx.commit();
        }
    }

    /**
     * Validates whether rewards can be claimed based on the questline's current phase:
     * <p>
     * Phase 1 (Timer to Complete): Can earn AND claim rewards
     * Phase 2 (Timer to Claim): Can claim rewards but NOT earn new ones
     * Phase 3 (Expired): Cannot claim rewards, questline disappears
     */
    private void validateClaimingPhase(ProgressMilestoneInstance milestoneInstance) throws ApplicationException {

        // Check quest-level claiming
        if (milestoneInstance.getQuestInstance() != null) {
            var quest = milestoneInstance.getQuestInstance();

            // If quest is formally expired, no claiming allowed
            if (quest.getStatus() == QuestStatusSpec.EXPIRED) {
                throw ApplicationException.of("Cannot claim rewards from expired quest", Code.ERR_DENIED);
            }
        }

        // Check questline-level claiming (primary validation)
        if (milestoneInstance.getQuestlineInstance() != null) {
            var questline = milestoneInstance.getQuestlineInstance();

            // If questline is formally expired, no claiming allowed (Phase 3)
            if (questline.getStatus() == QuestlineStatusSpec.EXPIRED) {
                throw ApplicationException.of("Cannot claim rewards from expired questline", Code.ERR_DENIED);
            }

            // Check if we're in the "claim phase" vs "complete phase"
            if (isInClaimPhase(questline)) {
                // Phase 2: Can claim but not earn - this is allowed
                log.debug("Questline {} is in claim phase - allowing reward claim", questline.getCode());
            } else if (questline.isExpiredByTime()) {
                // Phase 3: Claim timer has expired - no claiming allowed
                throw ApplicationException.of("Claim period has expired for questline", Code.ERR_DENIED);
            }
            // Phase 1: Normal operation - claiming is allowed
        }
    }

    /**
     * Determines if questline is in the "claim phase" (timer to claim is running)
     * vs "complete phase" (timer to complete is running)
     */
    private boolean isInClaimPhase(QuestlineInstance questline) {
        // A questline is in claim phase if:
        // 1. It's in UNCLAIMED status (has unclaimed rewards)
        // 2. OR it has reached completion but still has time to claim

        if (questline.getStatus() == QuestlineStatusSpec.UNCLAIMED) {
            return true;
        }

        // Additional check: if questline is completed but claim timer hasn't expired
        if (questline.getReadyForClaimAt() != null) {
            var claimExpiration = calculateClaimExpiration(questline);
            if (claimExpiration != null && new Date().before(claimExpiration)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculates when the claim period expires based on questline timing
     */
    private Date calculateClaimExpiration(QuestlineInstance questline) {
        if (questline.getReadyForClaimAt() == null ||
                questline.getQuestlineTemplate().getMinutesToClaim() == null) {
            return null;
        }

        long claimPeriodMs = questline.getQuestlineTemplate().getMinutesToClaim() * 60_000L;
        return new Date(questline.getReadyForClaimAt().getTime() + claimPeriodMs);
    }

    private void claimAll(TransactionalRequest<?, ?> cmd, QuestlineAccount account, ProgressMilestoneInstance milestoneInstance) throws Throwable {
        claimRewardsAsync(cmd, account, milestoneInstance);
        cmd.preserveEventStream();
        // todo what todo if random reward not created, maybe better async, how it is possible it is not created?
        createRandomRewardInstance(account, milestoneInstance);
    }

    private void claimRewardsAsync(TransactionalRequest<?, ?> cmd, QuestlineAccount account, ProgressMilestoneInstance milestoneInstance) throws Throwable {
        var externalAccount = ExternalAccount.newBuilder()
                .setAccountId(account.getRemoteId())
                .setBrandName(account.getBrand().getName())
                .setHash(account.getHash())
                .build();
        for (var rewardCode : milestoneInstance.getMilestoneTemplate().getRewardCodes()) {
            var event = AccountRewardClaimedEvent.newBuilder()
                    .setExternalAccount(externalAccount)
                    .setSource(RewardSource.QUEST_SOURCE)
                    .setRewardCode(rewardCode.toString())
                    .setTransactionCode(Generators.nameBasedGenerator().generate(milestoneInstance.getCode().toString() + rewardCode).toString())
                    .setSourceReference(milestoneInstance.getId().toString());
            cmd.eventStream(event);
        }
    }

    private void createRandomRewardInstance(QuestlineAccount account, ProgressMilestoneInstance milestoneInstance) throws Exception {
        for (var randomRewardCode : milestoneInstance.getMilestoneTemplate().getRandomRewardCodes()) {
            var createRandomRewardReq = CreateRewardInstanceRequest.newBuilder()
                    .setIdentity(Identity.newBuilder().setByAccountId(IdentityByAccountId.newBuilder().setAccountId(account.getRemoteId())))
                    .setBrandName(account.getBrand().getName())
                    .setRequestId(milestoneInstance.getCode().toString() + randomRewardCode.toString())
                    .setRewardTemplateCode(randomRewardCode.toString())
                    .setSource(RandomRewardSource.QUESTS)
                    .setSourceReference(milestoneInstance.getCode().toString())
                    .build();
            boolean isNotCreated = gameHubServiceApi.createRewardInstance(createRandomRewardReq, account.routingKey()).thenVerifyOk()
                    .get(props.REQUEST_REPLY_TIMEOUT.get(), TimeUnit.SECONDS)
                    .unpack()
                    .getRewardInstanceCode().isBlank();
            if (isNotCreated) {
                throw ApplicationException.of("random reward instance was not created: requestId=%s, rewardTemplateCode=%s",
                        Code.ERR_DENIED, createRandomRewardReq.getRequestId(), createRandomRewardReq.getRewardTemplateCode());
            }
        }
    }

    private void completeQuestIfNeeded(ProgressMilestoneInstance milestone, Transaction tx) {
        var questInstance = milestone.getQuestInstance();
        if (questInstance == null) {
            return;
        }
        questInstance.complete();
        var questlineInstance = questInstance.getQuestLine();
        if (questlineInstance.isAllQuestsCompleted() && !questlineInstance.hasAnyUnclaimedRewardsForQuestline()) {
            questlineInstance.complete();
            QuestlineMapper.toQuestlineUpdatedEvent(questlineInstance, QuestlineOverallStatusEnum.QUESTLINE_COMPLETED)
                    .ifPresent(evt -> queuePostTemplate.sendEvent(evt.build()));

        }
        ebean.save(questlineInstance, tx);
    }

    private void completeQuestlineIfNeeded(ProgressMilestoneInstance milestone, Transaction tx) {
        var questline = milestone.getQuestlineInstance();
        if (questline == null || questline.isCompleted()) {
            return;
        }

        boolean allMilestones = questline.isAllMilestonesClaimed();
        boolean allQuests = questline.isAllQuestsCompleted();

        if (allMilestones && allQuests) {
            // fully done => COMPLETED
            questline.complete();
            QuestlineMapper.toQuestlineUpdatedEvent(questline, QuestlineOverallStatusEnum.QUESTLINE_COMPLETED)
                    .ifPresent(evt -> queuePostTemplate.sendEvent(evt.build()));
        } else if (allMilestones) {
            // only questline milestones are fully claimed => CLAIMED
            questline.claim();
            QuestlineMapper.toQuestlineUpdatedEvent(questline, QuestlineOverallStatusEnum.QUESTLINE_CLAIMED)
                    .ifPresent(evt -> queuePostTemplate.sendEvent(evt.build()));

        }

        ebean.save(questline, tx);
    }


}
