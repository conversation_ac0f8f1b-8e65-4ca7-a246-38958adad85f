package quest.kafka;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;

public class QuestReadKafkaAcceptorChannel extends KafkaAcceptorChannel {

    public QuestReadKafkaAcceptorChannel(ApplicationProperties props,
                                         QuestRequestKafkaWithMetricsConsumerFactory factory,
                                         QuestReadRequestContainerProperties cfg) {
        super(props, factory, cfg);
        setConcurrency(props.KAFKA_MAX_POLL_CONCURRENCY.get());
    }

}
