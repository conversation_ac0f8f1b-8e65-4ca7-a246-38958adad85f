package quest.kafka;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.KafkaAcceptorChannel;

public class QuestKafkaAcceptorChannel extends KafkaAcceptorChannel {

    public QuestKafkaAcceptorChannel(ApplicationProperties props,
                                     QuestRequestKafkaWithMetricsConsumerFactory factory,
                                     QuestRequestContainerProperties cfg) {
        super(props, factory, cfg);
        setConcurrency(props.KAFKA_MAX_POLL_CONCURRENCY.get());
    }

}
