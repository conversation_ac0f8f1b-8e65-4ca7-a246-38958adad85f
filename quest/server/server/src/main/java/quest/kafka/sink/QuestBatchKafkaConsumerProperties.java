package quest.kafka.sink;

import java.security.KeyStore;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.AbstractBatchKafkaConsumerProperties;
import com.turbospaces.ups.KafkaServiceInfo;

public class QuestBatchKafkaConsumerProperties extends AbstractBatchKafkaConsumerProperties {
    public QuestBatchKafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, KafkaServiceInfo serviceInfo) {
        super(props, keyStore, serviceInfo);
    }
}
