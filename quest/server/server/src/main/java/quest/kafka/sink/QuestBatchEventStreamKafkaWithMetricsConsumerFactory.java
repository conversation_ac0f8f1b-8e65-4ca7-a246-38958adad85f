package quest.kafka.sink;

import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import org.springframework.kafka.core.MicrometerConsumerListener;

public class QuestBatchEventStreamKafkaWithMetricsConsumerFactory extends KafkaWithMetricsConsumerFactory {
    public QuestBatchEventStreamKafkaWithMetricsConsumerFactory(QuestBatchKafkaConsumerProperties configs, MicrometerConsumerListener<byte[], byte[]> listener) {
        super(configs, listener);
    }
}
