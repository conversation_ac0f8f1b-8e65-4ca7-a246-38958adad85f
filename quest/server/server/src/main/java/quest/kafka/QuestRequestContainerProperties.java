package quest.kafka;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.KafkaRequestConsumer;
import com.turbospaces.kafka.consumer.ManualAcknowledgingKafkaConsumer;
import engagement.QuestTopics;
import org.springframework.kafka.listener.ContainerProperties;

public class QuestRequestContainerProperties extends ContainerProperties {

    public QuestRequestContainerProperties(ApplicationProperties props, KafkaRequestConsumer consumer) {
        super(QuestTopics.REQ.name().toString());
        var manualAcknowledgingKafkaConsumer = new ManualAcknowledgingKafkaConsumer(props, consumer);
        setAckMode(AckMode.MANUAL);
        setMessageListener(manualAcknowledgingKafkaConsumer);
        setConsumerRebalanceListener(consumer);
    }
}
