package quest.kafka;

import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaWithMetricsConsumerFactory;
import org.springframework.kafka.core.MicrometerConsumerListener;

public class QuestRequestKafkaWithMetricsConsumerFactory extends KafkaWithMetricsConsumerFactory {

    public QuestRequestKafkaWithMetricsConsumerFactory(KafkaConsumerProperties configs,
                                                       MicrometerConsumerListener<byte[], byte[]> listener) {
        super(configs, listener);
    }

}
