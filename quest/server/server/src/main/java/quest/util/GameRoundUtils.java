package quest.util;

import lombok.experimental.UtilityClass;
import uam.api.v1.WalletTransactionInfo;
import uam.api.v1.internal.GameRoundEvent;
import uam.model.WalletSessionTypeSpec;

import java.math.BigDecimal;

import static uam.model.TransactionTypeSpec.CREDIT;
import static uam.model.TransactionTypeSpec.DEBIT;

@UtilityClass
public class GameRoundUtils {

    public static boolean isGameSession(GameRoundEvent event) {
        return WalletSessionTypeSpec.GAME_TYPES.contains(WalletSessionTypeSpec.fromString(event.getSessionType()));
    }

    public static boolean isDebitOrCreditTx(GameRoundEvent event) {
        return event.getWalletTransactionsList().stream().anyMatch(tx -> isDebit(tx) || isCredit(tx));
    }

    public static boolean isDebit(WalletTransactionInfo tx) {
        return isNotZero(tx.getAmount()) && tx.getType().equalsIgnoreCase(DEBIT.toString());
    }

    public static boolean isCredit(WalletTransactionInfo tx) {
        return isNotZero(tx.getAmount()) && tx.getType().equalsIgnoreCase(CREDIT.toString());
    }

    public static boolean isNotZero(String amount) {
        return new BigDecimal(amount).compareTo(BigDecimal.ZERO) != 0;
    }

}
