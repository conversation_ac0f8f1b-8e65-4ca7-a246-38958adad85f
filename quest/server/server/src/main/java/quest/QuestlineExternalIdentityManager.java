package quest;

import java.util.Objects;
import java.util.Optional;

import api.v1.ForcementModeSpec;
import engagement.repo.QuestJpaManager;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.JpaManager;

import api.v1.ApiFactory;
import api.v1.ApplicationException;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import identity.AbstractExternalIdentityManager;
import io.ebean.Transaction;
import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.CheckedConsumer;
import jakarta.inject.Inject;
import lombok.experimental.Delegate;

public class QuestlineExternalIdentityManager extends AbstractExternalIdentityManager<QuestlineAccount, QuestlineBrand> {

    @Delegate(excludes = JpaManager.class)
    private final QuestJpaManager ebean;

    @Inject
    public QuestlineExternalIdentityManager(
            ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            QuestJpaManager ebean) {
        super(props, apiFactory, ebean, cloud, meterRegistry);
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    protected Optional<QuestlineBrand> brandByName(String brandName, Transaction tx) {
        return ebean.questlineBrandRepo().brand(brandName, tx);
    }

    @Override
    protected CheckedConsumer<QuestlineAccount> accountInitializer(long externalId, String hash, QuestlineBrand brand) {
        return account -> {
            account.setBrand(brand);
            account.setHash(hash);
            account.setCode(PlatformUtil.randomUUID());
            account.setRemoteId(externalId);
        };
    }

    @Override
    protected CheckedConsumer<QuestlineBrand> brandInitializer(String brandName) {
        return brand -> {
            brand.setName(brandName);
            brand.setMode(ForcementModeSpec.DEFAULT);
        };
    }

    @Override
    protected Optional<QuestlineAccount> account(Long externalAccountId, Transaction tx) throws ApplicationException {
        return ebean.questlineAccountRepo().accountByRemoteId(externalAccountId, tx);
    }

}
