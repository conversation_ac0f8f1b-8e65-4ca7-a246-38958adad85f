package quest;

import api.v1.PlatformSpec;
import common.JwtTags;
import core.model.CoreBrand;
import io.jsonwebtoken.Jwts;
import org.apache.commons.lang3.time.DateUtils;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAnonymous;
import uam.api.v1.IdentityBySessionToken;

import java.util.Date;

public class IdentityUtil {

    public static final String REMOTE_IP = "127.0.0.1";
    public static final String IP_COUNTRY = "US";

    public static Identity getIdentityByToken(QuestlineExternalIdentityManager identityManager, long remoteAccountId, CoreBrand brand) throws Throwable {
        String token = identityManager.encrypt(brand, Jwts.builder().setExpiration(DateUtils.addMinutes(new Date(), 30)).claim(JwtTags.JWT_BRAND, brand.getName()).claim(JwtTags.JWT_ROUTING_KEY_CLAIM, String.valueOf(remoteAccountId)).setSubject(String.valueOf(remoteAccountId)));
        return Identity.newBuilder().setByToken(IdentityBySessionToken.newBuilder().setRemoteIp(REMOTE_IP).setIpCountry(IP_COUNTRY).setAccountId(remoteAccountId).setToken(token).setPlatform("WEB").build()).build();
    }

    public static IdentityByAnonymous getAnonymousIdentity() {
        return IdentityByAnonymous.newBuilder().setRemoteIp(REMOTE_IP).setPlatform(PlatformSpec.WEB.code()).build();
    }

}

