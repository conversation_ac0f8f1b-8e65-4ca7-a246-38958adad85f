package quest.service.contribution;

import static loyalty.api.v1.AccountBalanceEventType.ACCOUNT_BALANCE_EVENT_TYPE_CREDIT;
import static loyalty.api.v1.AccountBalanceEventType.ACCOUNT_BALANCE_EVENT_TYPE_DEBIT;
import static loyalty.api.v1.AccountBalanceEventType.ACCOUNT_BALANCE_EVENT_TYPE_EXPIRE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.math.BigDecimal;
import java.util.UUID;

import org.bouncycastle.pqc.crypto.ExchangePair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.turbospaces.common.PlatformUtil;

import api.v1.AccountRoutingInfo;
import engagement.model.quest.contribution.LoyaltyXpQuestContributionTemplate;
import engagement.model.quest.contribution.PlayQuestContributionTemplate;
import loyalty.api.v1.AccountBalanceEventType;
import loyalty.api.v1.AccountBalanceSource;
import loyalty.api.v1.LoyaltyAccountBalanceUpdateEvent;

class LoyaltyXpQuestContributionSourceTest {

    private LoyaltyXpQuestContributionSource source;
    private UUID xpVariantCode;
    private UUID loyaltySystemCode;
    private LoyaltyXpQuestContributionTemplate template;

    @BeforeEach
    void setUp() {
        source = new LoyaltyXpQuestContributionSource();
        xpVariantCode = PlatformUtil.randomUUID();
        loyaltySystemCode = PlatformUtil.randomUUID();

        template = LoyaltyXpQuestContributionTemplate.builder()
                .xpVariantCode(xpVariantCode)
                .completionThreshold(new BigDecimal("100"))
                .build();
    }

    @Test
    void resolveAmountValidCreditEventTest() {
        var event = createLoyaltyXpEvent("50", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, template);

        assertEquals(new BigDecimal("50"), result);
    }

    @Test
    void resolveAmountValidCreditEventWithoutLoyaltySystemTest() {
        // Template without loyalty system filter
        var templateNoSystem = LoyaltyXpQuestContributionTemplate.builder()
                .xpVariantCode(xpVariantCode)
                .completionThreshold(new BigDecimal("100"))
                .build();

        var event = createLoyaltyXpEvent("75", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, null);

        BigDecimal result = source.resolveAmount(event, templateNoSystem);

        assertEquals(new BigDecimal("75"), result);
    }

    @Test
    void resolveAmountWrongVariantReturnsZeroTest() {
        UUID wrongVariantCode = PlatformUtil.randomUUID();
        var event = createLoyaltyXpEvent("50", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, wrongVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, template);

        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void resolveAmountDebitEventReturnsZeroTest() {
        var event = createLoyaltyXpEvent("50", ACCOUNT_BALANCE_EVENT_TYPE_DEBIT, xpVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, template);

        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void resolveAmountExpireEventReturnsZeroTest() {
        var event = createLoyaltyXpEvent("50", ACCOUNT_BALANCE_EVENT_TYPE_EXPIRE, xpVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, template);

        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void resolveAmountInvalidXpAmountReturnsZeroTest() {
        var event = createLoyaltyXpEvent("invalid_number", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, template);

        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void resolveAmountDecimalXpAmountTest() {
        var event = createLoyaltyXpEvent("50.75", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, template);

        assertEquals(new BigDecimal("50.75"), result);
    }

    @Test
    void resolveAmountLargeXpAmountTest() {
        var event = createLoyaltyXpEvent("999999.99", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, template);

        assertEquals(new BigDecimal("999999.99"), result);
    }

    @Test
    void resolveAmountWithWrongTemplateTypeThrowsExceptionTest() {
        var wrongTemplate = PlayQuestContributionTemplate.builder()
                .completionThreshold(new BigDecimal("100"))
                .build();

        var event = createLoyaltyXpEvent("50", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, loyaltySystemCode);

        assertThrows(IllegalArgumentException.class, () -> {
            source.resolveAmount(event, wrongTemplate);
        });
    }

    @Test
    void resolveAmountEventWithoutLoyaltySystemMatchesTemplateWithoutSystemTest() {
        // Template without loyalty system requirement
        var templateNoSystem = LoyaltyXpQuestContributionTemplate.builder()
                .xpVariantCode(xpVariantCode)
                .completionThreshold(new BigDecimal("100"))
                .build();

        // Event without loyalty system
        var event = createLoyaltyXpEvent("50", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, null);

        BigDecimal result = source.resolveAmount(event, templateNoSystem);

        assertEquals(new BigDecimal("50"), result);
    }

    @Test
    void resolveAmountEventWithLoyaltySystemMatchesTemplateWithoutSystemTest() {
        // Template without loyalty system requirement (should accept any)
        var templateNoSystem = LoyaltyXpQuestContributionTemplate.builder()
                .xpVariantCode(xpVariantCode)
                .completionThreshold(new BigDecimal("100"))
                .build();

        // Event with loyalty system
        var event = createLoyaltyXpEvent("50", ACCOUNT_BALANCE_EVENT_TYPE_CREDIT, xpVariantCode, loyaltySystemCode);

        BigDecimal result = source.resolveAmount(event, templateNoSystem);

        assertEquals(new BigDecimal("50"), result);
    }

    private LoyaltyAccountBalanceUpdateEvent createLoyaltyXpEvent(String xpAmount, AccountBalanceEventType eventType,
                                                                  UUID variantCode, UUID loyaltySystemCode) {
        var builder = LoyaltyAccountBalanceUpdateEvent.newBuilder()
                .setRouting(AccountRoutingInfo.newBuilder()
                        .setId(123L)
                        .setHash("test_hash")
                        .setBrand("test_brand")
                        .build())
                .setVariantCode(variantCode.toString())
                .setVariantAbbreviation("XP")
                .setTransactionCode("test_tx")
                .setEventType(eventType)
                .setSource(AccountBalanceSource.ACCOUNT_BALANCE_SOURCE_PLAYER_CONTRIBUTION)
                .setXpCurrent("100")
                .setXpBefore("50")
                .setXpAmount(xpAmount)
                .setAt(System.currentTimeMillis());

        if (loyaltySystemCode != null) {
            builder.setLoyaltySystemCode(loyaltySystemCode.toString())
                    .setAccountLoyaltySystemCode(PlatformUtil.randomUUID().toString());
        }

        return builder.build();
    }
}