package quest.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import api.v1.EnhancedApplicationException;
import engagement.model.quest.line.QuestlineDetailsViewSpec;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineTypeSpec;
import quest.api.v1.DetailViewType;
import quest.api.v1.QuestlineStartTriggerTypeSpecPB;
import quest.api.v1.QuestlineTypeSpecPB;

class QuestlineEnumMapperTest {

    @ParameterizedTest
    @EnumSource(QuestlineDetailsViewSpec.class)
    void testDetailsViewSpec_toProto(QuestlineDetailsViewSpec domainVal) {
        DetailViewType protoVal = QuestlineEnumMapper.toProto(domainVal);
        assertNotEquals(DetailViewType.UNKNOWN_DETAIL_VIEW, protoVal);
    }

    @ParameterizedTest
    @EnumSource(DetailViewType.class)
    void testDetailsViewSpec_fromProto(DetailViewType protoVal) throws EnhancedApplicationException {
        if (protoVal == DetailViewType.UNRECOGNIZED || protoVal == DetailViewType.UNKNOWN_DETAIL_VIEW) {
            return;
        }
        QuestlineDetailsViewSpec domainVal = QuestlineEnumMapper.fromProto(protoVal);
        DetailViewType protoVal2 = QuestlineEnumMapper.toProto(domainVal);
        assertEquals(protoVal, protoVal2);
    }

    @ParameterizedTest
    @EnumSource(QuestlineTypeSpec.class)
    void testQuestlineType_toProto(QuestlineTypeSpec domainVal) throws EnhancedApplicationException {
        QuestlineTypeSpecPB protoVal = QuestlineEnumMapper.toProto(domainVal);
        assertNotEquals(QuestlineTypeSpecPB.QTYPE_UNSPECIFIED, protoVal);
    }

    @ParameterizedTest
    @EnumSource(QuestlineTypeSpecPB.class)
    void testQuestlineType_fromProto(QuestlineTypeSpecPB protoVal) throws EnhancedApplicationException {
        if (protoVal == QuestlineTypeSpecPB.UNRECOGNIZED || protoVal == QuestlineTypeSpecPB.QTYPE_UNSPECIFIED) {
            return;
        }
        QuestlineTypeSpec domainVal = QuestlineEnumMapper.fromProto(protoVal);
        QuestlineTypeSpecPB protoVal2 = QuestlineEnumMapper.toProto(domainVal);
        assertEquals(protoVal, protoVal2);
    }

    @ParameterizedTest
    @EnumSource(QuestlineStartTriggerTypeSpec.class)
    void testStartTrigger_toProto(QuestlineStartTriggerTypeSpec domainVal) throws EnhancedApplicationException {
        QuestlineStartTriggerTypeSpecPB protoVal = QuestlineEnumMapper.toProto(domainVal);
        assertNotEquals(QuestlineStartTriggerTypeSpecPB.QTRIGGER_UNSPECIFIED, protoVal);
    }

    @ParameterizedTest
    @EnumSource(QuestlineStartTriggerTypeSpecPB.class)
    void testStartTrigger_fromProto(QuestlineStartTriggerTypeSpecPB protoVal) throws EnhancedApplicationException {
        if (protoVal == QuestlineStartTriggerTypeSpecPB.UNRECOGNIZED
                || protoVal == QuestlineStartTriggerTypeSpecPB.QTRIGGER_UNSPECIFIED) {
            return;
        }
        QuestlineStartTriggerTypeSpec domainVal = QuestlineEnumMapper.fromProto(protoVal);
        QuestlineStartTriggerTypeSpecPB protoVal2 = QuestlineEnumMapper.toProto(domainVal);
        assertEquals(protoVal, protoVal2);
    }
}
