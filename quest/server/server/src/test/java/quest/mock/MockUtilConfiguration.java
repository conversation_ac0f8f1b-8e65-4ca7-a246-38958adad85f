package quest.mock;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

@TestConfiguration
public class MockUtilConfiguration {
    @Bean
    public CommonObjectMapper objectMapper() {
        return new CommonObjectMapper();
    }

    @Bean
    public ApiFactory apiFactory(ApplicationProperties props, CommonObjectMapper mapper) {
        return new DefaultApiFactory(props, mapper);
    }

    @Bean
    public MockUtil mockUtil(ApplicationProperties props, DefaultApiFactory defaultApiFactory) {
        return new MockUtil(props, defaultApiFactory);
    }

}
