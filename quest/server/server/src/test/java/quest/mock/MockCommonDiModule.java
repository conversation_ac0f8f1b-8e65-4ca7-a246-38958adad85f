package quest.mock;

import org.mockito.Mockito;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.MockQueuePostTemplate;
import com.turbospaces.rpc.QueuePostTemplate;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import gamehub.GameHubServiceApi;
import identity.EmptyGeoLocatorManager;
import identity.GeoLocationManager;
import quest.handlers.AbstractRequestHandler;

@Configuration
@ComponentScan(
        basePackageClasses = {
                AbstractRequestHandler.class
        })
public class MockCommonDiModule {
    @Bean
    public QueuePostTemplate<?> queuePostTemplate(ApplicationProperties props, ApiFactory apiFactory) {
        return new MockQueuePostTemplate(props, apiFactory);
    }
    @Bean
    public GeoLocationManager geoLocationManager() {
        return new EmptyGeoLocatorManager();
    }
    @Bean
    public GameHubServiceApi gameHubServiceApi(ApplicationProperties props, DefaultApiFactory defaultApiFactory) {
        return Mockito.mock(GameHubServiceApi.class);
    }
}
