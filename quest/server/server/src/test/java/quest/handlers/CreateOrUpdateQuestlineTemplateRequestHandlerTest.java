package quest.handlers;

import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.common.PlatformUtil;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.contribution.QuestContributionTemplate;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.repo.QuestEbeanJpaManager;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import quest.api.v1.CreateOrUpdateQuestlineTemplateRequest;
import quest.api.v1.CreateOrUpdateQuestlineTemplateResponse;
import quest.api.v1.DetailViewType;
import quest.api.v1.Placement;
import quest.api.v1.PlacementTypeEnum;
import quest.api.v1.ProgressEvent;
import quest.api.v1.QuestInfo;
import quest.api.v1.QuestlineMilestoneInfo;
import quest.api.v1.QuestlineStartTriggerTypeSpecPB;
import quest.api.v1.QuestlineTemplateInfo;
import quest.api.v1.QuestlineTypeSpecPB;
import quest.testutils.DataManager;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static quest.InitialTestDataGenerator.TEST_BRAND;

@Slf4j
class CreateOrUpdateQuestlineTemplateRequestHandlerTest extends AbstractRequestHandlerTest {

    @Autowired
    private MockUtil mockUtil;

    @Autowired
    private CreateOrUpdateQuestlineTemplateRequestHandler updateHandler;

    @Autowired
    private QuestEbeanJpaManager ebean;

    @Autowired
    private DataManager dataManager;

    private long existingTemplateId;

    @BeforeEach
    void setup() throws Throwable {
        existingTemplateId = createInitialTemplate();
        createPlacements(TEST_BRAND);
        assertTrue(existingTemplateId > 0);
    }

    @Test
    void testFullUpdateNameAndActive() throws Throwable {
        long questId = findFirstQuestIdOfTemplate(existingTemplateId);
        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("UpdatedName")
                                .setDisplayName("Updated Display")
                                .setActive(false)
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setId(questId)
                                                .setName("QuestRenamed")
                                                .setDisplayName("Renamed Display")
                                                .setDisplayDescription("Desc changed")
                                                .setProgressEvent(ProgressEvent.WIN)
                                                .setUnitsToCompletion(444)))
                .build();
        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));
        updateHandler.apply(cmd);
        QuestlineTemplate updated = fetchTemplateFromDb(existingTemplateId);
        assertEquals("UpdatedName", updated.getName());
        assertFalse(updated.isActive());
    }

    @Test
    void testUpdateAddQuest() throws Throwable {
        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("M")
                                .setDisplayName("M2")
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName("NewQuest")
                                                .setDisplayName("NQ dsp")
                                                .setDisplayDescription("QQdesc")
                                                .setProgressEvent(ProgressEvent.PLAY)
                                                .setUnitsToCompletion(150)))
                .build();
        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));
        updateHandler.apply(cmd);
        QuestlineTemplate t = fetchTemplateFromDb(existingTemplateId);
        assertEquals(1, t.getQuests().size());
    }

    @Test
    void testLockedTemplatePartialUpdate() throws Throwable {
        lockTemplate(existingTemplateId);
        long questId = findFirstQuestIdOfTemplate(existingTemplateId);
        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("LockedChangedName")
                                .setDisplayName("Locked Display")
                                .setActive(true)
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setId(questId)
                                                .setName("NewNameForLockedQuest")
                                                .setDisplayDescription("Locked Desc changed")
                                                .setProgressEvent(ProgressEvent.WIN)
                                                .setUnitsToCompletion(9999)))
                .build();
        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));
        updateHandler.apply(cmd);
        QuestlineTemplate updated = fetchTemplateFromDb(existingTemplateId);
        assertEquals("LockedChangedName", updated.getName());
        long lockedQid = findFirstQuestIdOfTemplate(existingTemplateId);
        var quest = updated.getQuests().stream().filter(q -> q.getId() == lockedQid).findFirst().get();
        assertEquals("NewNameForLockedQuest", quest.getName());
        assertEquals("Locked Desc changed", quest.getDisplayDescription());
        var c = quest.getContributions().getFirst();
        assertEquals(ProgressEvent.PLAY, figureOutEvent(c));
    }

    @Test
    void testLockedTemplateDisallowAddQuest() throws Throwable {
        lockTemplate(existingTemplateId);
        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("LockedPartialOk")
                                .setDisplayName("StillOk")
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName("NewQuestInLockedTemplate")
                                                .setDisplayName("NoNoNo")
                                                .setProgressEvent(ProgressEvent.WAGER)
                                                .setUnitsToCompletion(25)))
                .build();
        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));
        EnhancedApplicationException ex = assertThrows(
                EnhancedApplicationException.class,
                () -> updateHandler.apply(cmd));
        assertEquals(Code.ERR_SYSTEM, ex.getCode());
        assertEquals(Reason.BAD_REQUEST, ex.getReason());
        log.info("Got expected EnhancedApplicationException for adding quest: {}", ex.getMessage());
    }

    @Test
    void testRemoveQuestFromUnlockedOk() throws Throwable {
        addExtraQuestToExistingTemplate(existingTemplateId, "ExtraQuest", 200);

        QuestlineTemplate beforeRemoval = fetchTemplateFromDb(existingTemplateId);
        assertEquals(2, beforeRemoval.getQuests().size(), "Should have 2 quests now.");

        long firstQuestId = beforeRemoval.getQuests().get(0).getId();
        long secondQuestId = beforeRemoval.getQuests().get(1).getId();

        CreateOrUpdateQuestlineTemplateRequest removeReq = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName(beforeRemoval.getName())
                                .setDisplayName("RemoveOneQuest")
                                .setActive(false)
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setId(secondQuestId)
                                                .setName("StillHere")
                                                .setDisplayDescription("Second Quest Survives")
                                                .setProgressEvent(ProgressEvent.WAGER)
                                                .setUnitsToCompletion(777)))
                .build();

        CreateOrUpdateQuestlineTemplateResponse.Builder removeRespB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, removeReq, removeRespB));
        updateHandler.apply(cmd);

        QuestlineTemplate afterRemoval = fetchTemplateFromDb(existingTemplateId);
        assertEquals(1, afterRemoval.getQuests().size(), "Should have only 1 quest after removal");
        assertEquals("StillHere", afterRemoval.getQuests().get(0).getName());
    }

    @Test
    void testRemoveQuestFromLockedDisallowed() throws Throwable {
        lockTemplate(existingTemplateId);

        QuestlineTemplate beforeRemoval = fetchTemplateFromDb(existingTemplateId);
        assertEquals(1, beforeRemoval.getQuests().size());

        CreateOrUpdateQuestlineTemplateRequest removeReq = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("TryRemoveQuestLocked")
                                .setDisplayName("Will Fail")
                                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.SIMPLE_DETAILS_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                )

                .build();

        CreateOrUpdateQuestlineTemplateResponse.Builder removeRespB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, removeReq, removeRespB));

        EnhancedApplicationException ex = assertThrows(
                EnhancedApplicationException.class,
                () -> updateHandler.apply(cmd));
        assertEquals(Code.ERR_SYSTEM, ex.getCode());
        assertEquals(Reason.BAD_REQUEST, ex.getReason());
        log.info("Got expected exception for removing a quest from locked template: {}", ex.getMessage());
    }

    @Test
    void testUnknownQuestlineType() throws Throwable {
        QuestlineTemplateInfo badInfo = QuestlineTemplateInfo.newBuilder()
                .setName("BadTemplateType")
                .setDisplayName("Invalid Type")
                .setQuestlineTypeValue(999) // sets an invalid enum value
                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                .build();

        var req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(badInfo)
                .build();

        var respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

        EnhancedApplicationException ex = assertThrows(
                EnhancedApplicationException.class,
                () -> updateHandler.apply(cmd));
        assertEquals(Code.ERR_BAD_REQUEST, ex.getCode());
        assertEquals(Reason.BAD_REQUEST, ex.getReason());
        log.info("Got expected EnhancedApplicationException for unknown questline type: {}", ex.getMessage());
    }

    @Test
    void testUnknownStartTrigger() throws Throwable {
        QuestlineTemplateInfo badInfo = QuestlineTemplateInfo.newBuilder()
                .setName("BadStartTrigger")
                .setDisplayName("Invalid Trigger")
                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                .setStartTriggerValue(999) // invalid enum
                .build();

        var req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(badInfo)
                .build();

        var respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

        EnhancedApplicationException ex = assertThrows(
                EnhancedApplicationException.class,
                () -> updateHandler.apply(cmd));
        assertEquals(Code.ERR_BAD_REQUEST, ex.getCode());
        assertEquals(Reason.BAD_REQUEST, ex.getReason());
        log.info("Got expected EnhancedApplicationException for unknown start trigger: {}", ex.getMessage());
    }

    @Test
    void testBrandNotFound() throws Throwable {
        QuestlineTemplateInfo info = QuestlineTemplateInfo.newBuilder()
                .setName("BrandDoesNotExistTest")
                .setDisplayName("MissingBrand")
                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                .build();

        var req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(0) // create new
                .setBrandName("NonExistentBrand_12345")
                .setQuestlineTemplate(info)
                .build();

        var respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

        EnhancedApplicationException ex = assertThrows(
                EnhancedApplicationException.class,
                () -> updateHandler.apply(cmd));
        assertEquals(Code.ERR_NOT_FOUND, ex.getCode());
        log.info("Got expected EnhancedApplicationException for missing brand: {}", ex.getMessage());
    }

    @Test
    void createTemplateWithPlacementsTest() throws Throwable {

        var request = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("MyQuestline Template2")
                                .setDisplayName("MyQuestline Display")
                                .setDisplayDescription("Some Description")
                                .setDisplayTagline("Some Tagline")
                                .setActive(true)
                                .setTimeLimited(false)
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName("Quest #12")
                                                .setDisplayName("Spin 50 times")
                                                .setDisplayDescription("Do 50 spins to complete.")
                                                .setProgressEvent(ProgressEvent.PLAY)
                                                .setUnitsToCompletion(50))
                                .addMilestones(
                                        QuestlineMilestoneInfo.newBuilder()
                                                .setProgressRequiredPercentage(100.0)
                                                .addRewards(PlatformUtil.randomUUID().toString()))
                                .addAllPlacements(List.of(
                                        Placement.newBuilder()
                                                .setTheme("theme-1")
                                                .setUrl1("icon-url")
                                                .setType(PlacementTypeEnum.INBOX_NOTIFICATION)
                                                .build(),
                                        Placement.newBuilder()
                                                .setTheme("theme-2")
                                                .setUrl1("icon-url")
                                                .setType(PlacementTypeEnum.HOME_ICON)
                                                .build(),
                                        Placement.newBuilder()
                                                .setTheme("theme-3")
                                                .setUrl1("icon-url")
                                                .setUrl2("icon-url")
                                                .setType(PlacementTypeEnum.HOME_CENTER_STAGE)
                                                .build())));
        var resp = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var createCmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, request.build(), resp));
        updateHandler.apply(createCmd);

        var templateId = createCmd.reply().getQuestlineTemplate().getTemplateId();
        var template = ebean.find(QuestlineTemplate.class, templateId);
        assertNotNull(template);
        var placements = template.getPlacements();
        assertEquals(3, placements.size());
    }

    private ProgressEvent figureOutEvent(QuestContributionTemplate contribution) {
        String clazz = contribution.getClass().getSimpleName();
        if (clazz.contains("Play")) {
            return ProgressEvent.PLAY;
        }
        if (clazz.contains("Wager")) {
            return ProgressEvent.WAGER;
        }
        if (clazz.contains("Win")) {
            return ProgressEvent.WIN;
        }
        return ProgressEvent.UNRECOGNIZED;
    }

    private void lockTemplate(long templateId) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            Optional<QuestlineTemplate> maybe = ebean.questRepo().findQuestlineTemplateById(templateId, tx);
            if (maybe.isEmpty()) {
                return;
            }
            var tpl = maybe.get();
            var brandOpt = ebean.questlineBrandRepo().brand(TEST_BRAND, tx);
            if (brandOpt.isEmpty()) {
                return;
            }
            QuestlineAccount account = dataManager.createQuestlineAccount(999L, brandOpt.get());
            QuestlineInstance instance = new QuestlineInstance(tpl, account, null);
            ebean.questRepo().save(instance, tx);
            tx.commit();
        }
    }

    private void addExtraQuestToExistingTemplate(long templateId, String questName, double unitsToCompletion) throws Throwable {
        // We'll just re-call the endpoint with an additional quest
        CreateOrUpdateQuestlineTemplateRequest addReq = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(templateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("Unlocked can add quest")
                                .setDisplayName("UnlockedAddQuest")
                                .setActive(false)
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIPLE_QUESTS)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName("Quest #1")
                                                .setDisplayName("Spin 50 times")
                                                .setDisplayDescription("Do 50 spins to complete.")
                                                .setProgressEvent(ProgressEvent.PLAY)
                                                .setUnitsToCompletion(50))
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName(questName)
                                                .setDisplayName("Extra Quest")
                                                .setProgressEvent(ProgressEvent.WAGER)
                                                .setUnitsToCompletion(unitsToCompletion)))
                .build();
        var respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, addReq, respB));
        updateHandler.apply(cmd);
    }

    private long createInitialTemplate() throws Throwable {
        var request = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("MyQuestline Template")
                                .setDisplayName("MyQuestline Display")
                                .setDisplayDescription("Some Description")
                                .setDisplayTagline("Some Tagline")
                                .setActive(true)
                                .setTimeLimited(false)
                                .setQuestlineType(QuestlineTypeSpecPB.MULTIREWARD_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.MULTIREWARD_DETAIL_VIEW)
                                .setListHeaderImageUrl("https://www.patrianna.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName("Quest #1")
                                                .setDisplayName("Spin 50 times")
                                                .setDisplayDescription("Do 50 spins to complete.")
                                                .setProgressEvent(ProgressEvent.PLAY)
                                                .setUnitsToCompletion(50))
                                .addMilestones(
                                        QuestlineMilestoneInfo.newBuilder()
                                                .setProgressRequiredPercentage(100.0)
                                                .addRewards(PlatformUtil.randomUUID().toString())))
                .build();
        var resp = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var createCmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, request, resp));
        updateHandler.apply(createCmd);
        long tid = resp.getQuestlineTemplate().getTemplateId();
        log.info("Created initial template with ID={}", tid);
        return tid;
    }

    private long findFirstQuestIdOfTemplate(long templateId) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var opt = ebean.questRepo().findQuestlineTemplateById(templateId, tx);
            if (opt.isPresent()) {
                var t = opt.get();
                if (!t.getQuests().isEmpty()) {
                    return t.getQuests().getFirst().getId();
                }
            }
            tx.commit();
        }
        return 0L;
    }

    private QuestlineTemplate fetchTemplateFromDb(long templateId) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var opt = ebean.questRepo().findQuestlineTemplateById(templateId, tx);
            tx.commit();
            return opt.orElse(null);
        }
    }

    private void createPlacements(String brandName) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            QuestlineBrand brand = ebean.questlineBrandRepo().requiredBrand(brandName, tx);
            dataManager.createPlacementTemplatesForBrand(brand);
            tx.commit();
        }
    }
}
