package quest.handlers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static quest.InitialTestDataGenerator.TEST_BRAND;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.turbospaces.boot.test.MockUtil;

import engagement.model.quest.CurrentFilter;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.contribution.QuestContributionTemplate;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.repo.QuestEbeanJpaManager;
import io.ebean.Transaction;
import lombok.extern.slf4j.Slf4j;
import quest.api.v1.CreateOrUpdateQuestlineTemplateRequest;
import quest.api.v1.CreateOrUpdateQuestlineTemplateResponse;
import quest.api.v1.CurrencyFilter;
import quest.api.v1.DetailViewType;
import quest.api.v1.ProgressEvent;
import quest.api.v1.QuestInfo;
import quest.api.v1.QuestlineStartTriggerTypeSpecPB;
import quest.api.v1.QuestlineTemplateInfo;
import quest.api.v1.QuestlineTypeSpecPB;
import quest.testutils.DataManager;

@Slf4j
class DuplicateGameCodesFixTest extends AbstractRequestHandlerTest {

    @Autowired
    private MockUtil mockUtil;

    @Autowired
    private CreateOrUpdateQuestlineTemplateRequestHandler updateHandler;

    @Autowired
    private QuestEbeanJpaManager ebean;

    @Autowired
    private DataManager dataManager;

    private long existingTemplateId;
    private static final String GAME_CODE_1 = "rlx.gc.gc.freedom_eagle_94";
    private static final String GAME_CODE_2 = "rlx.gc.gc.candy_slot_95";
    private static final String SUPPLIER_1 = "relax_gaming";
    private static final String SUPPLIER_2 = "pragmatic_play";

    @BeforeEach
    void setup() throws Throwable {
        existingTemplateId = createTemplateWithGameCodes();
        createPlacements(TEST_BRAND);
        assertTrue(existingTemplateId > 0);
    }

    @Test
    void testUpdateTemplateDoesNotDuplicateGameCodes() throws Throwable {
        long questId = findFirstQuestIdOfTemplate(existingTemplateId);

        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("UpdatedTemplate")
                                .setDisplayName("Updated Display")
                                .setActive(true)
                                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.SIMPLE_DETAILS_VIEW)
                                .setListHeaderImageUrl("https://example.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setId(questId)
                                                .setName("UpdatedQuest")
                                                .setDisplayName("Updated Quest")
                                                .setProgressEvent(ProgressEvent.WAGER)
                                                .setUnitsToCompletion(100)
                                                .addGameCodes(GAME_CODE_1)
                                                .addGameCodes(GAME_CODE_2)
                                                .addSuppliers(SUPPLIER_1)))
                .build();

        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

        updateHandler.apply(cmd);

        QuestlineTemplate updated = fetchTemplateFromDb(existingTemplateId);
        assertNotNull(updated);
        assertEquals(1, updated.getQuests().size());

        QuestTemplate quest = updated.getQuests().get(0);
        assertEquals(1, quest.getContributions().size());

        QuestContributionTemplate contribution = quest.getContributions().get(0);
        assertEquals(2, contribution.getGameCodes().size());
        assertEquals(1, contribution.getGameSuppliers().size());

        long gameCode1Count = contribution.getGameCodes().stream()
                .filter(code -> GAME_CODE_1.equals(code))
                .count();
        long gameCode2Count = contribution.getGameCodes().stream()
                .filter(code -> GAME_CODE_2.equals(code))
                .count();
        long supplier1Count = contribution.getGameSuppliers().stream()
                .filter(supplier -> SUPPLIER_1.equals(supplier))
                .count();

        assertEquals(1, gameCode1Count, "Game code should not be duplicated");
        assertEquals(1, gameCode2Count, "Game code should not be duplicated");
        assertEquals(1, supplier1Count, "Supplier should not be duplicated");
    }

    @Test
    void testMultipleUpdatesDoNotAccumulateGameCodes() throws Throwable {
        long questId = findFirstQuestIdOfTemplate(existingTemplateId);

        for (int i = 0; i < 3; i++) {
            CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                    .setTemplateId(existingTemplateId)
                    .setBrandName(TEST_BRAND)
                    .setQuestlineTemplate(
                            QuestlineTemplateInfo.newBuilder()
                                    .setName("MultiUpdateTemplate" + i)
                                    .setDisplayName("Multi Update " + i)
                                    .setActive(true)
                                    .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                                    .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                    .setDetailViewType(DetailViewType.SIMPLE_DETAILS_VIEW)
                                    .setListHeaderImageUrl("https://example.com/icon.jpg")
                                    .addQuests(
                                            QuestInfo.newBuilder()
                                                    .setId(questId)
                                                    .setName("MultiUpdateQuest" + i)
                                                    .setDisplayName("Multi Update Quest " + i)
                                                    .setProgressEvent(ProgressEvent.WAGER)
                                                    .setUnitsToCompletion(100 + i * 10)
                                                    .addGameCodes(GAME_CODE_1)
                                                    .addGameCodes(GAME_CODE_2)
                                                    .addSuppliers(SUPPLIER_1)
                                                    .addSuppliers(SUPPLIER_2)))
                    .build();

            CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
            var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

            updateHandler.apply(cmd);
        }

        QuestlineTemplate updated = fetchTemplateFromDb(existingTemplateId);
        assertNotNull(updated);
        assertEquals(1, updated.getQuests().size());

        QuestTemplate quest = updated.getQuests().get(0);
        assertEquals(1, quest.getContributions().size());

        QuestContributionTemplate contribution = quest.getContributions().get(0);
        assertEquals(2, contribution.getGameCodes().size(), "Should have exactly 2 game codes after multiple updates");
        assertEquals(2, contribution.getGameSuppliers().size(), "Should have exactly 2 suppliers after multiple updates");

        assertTrue(contribution.getGameCodes().contains(GAME_CODE_1));
        assertTrue(contribution.getGameCodes().contains(GAME_CODE_2));
        assertTrue(contribution.getGameSuppliers().contains(SUPPLIER_1));
        assertTrue(contribution.getGameSuppliers().contains(SUPPLIER_2));
    }

    @Test
    void testLockedTemplateGameCodesUpdate() throws Throwable {
        lockTemplate(existingTemplateId);
        long questId = findFirstQuestIdOfTemplate(existingTemplateId);

        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("LockedTemplateUpdate")
                                .setDisplayName("Locked Template")
                                .setActive(true)
                                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.SIMPLE_DETAILS_VIEW)
                                .setListHeaderImageUrl("https://example.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setId(questId)
                                                .setName("LockedQuest")
                                                .setDisplayName("Locked Quest")
                                                .setProgressEvent(ProgressEvent.WAGER)
                                                .setUnitsToCompletion(200)
                                                .setCurrencyFilter(CurrencyFilter.SC)
                                                .setMinWager("5.0")
                                                .addGameCodes("new_game_code_1")
                                                .addGameCodes("new_game_code_2")
                                                .addSuppliers("new_supplier")))
                .build();

        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

        updateHandler.apply(cmd);

        QuestlineTemplate updated = fetchTemplateFromDb(existingTemplateId);
        assertNotNull(updated);

        QuestTemplate quest = updated.getQuests().get(0);
        QuestContributionTemplate contribution = quest.getContributions().get(0);

        assertEquals(2, contribution.getGameCodes().size());
        assertEquals(1, contribution.getGameSuppliers().size());
        assertTrue(contribution.getGameCodes().contains("new_game_code_1"));
        assertTrue(contribution.getGameCodes().contains("new_game_code_2"));
        assertTrue(contribution.getGameSuppliers().contains("new_supplier"));
        assertEquals(CurrentFilter.SC, contribution.getCurrency());
    }

    @Test
    void testEmptyGameCodesAndSuppliersHandling() throws Throwable {
        long questId = findFirstQuestIdOfTemplate(existingTemplateId);

        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setTemplateId(existingTemplateId)
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("EmptyCodesTemplate")
                                .setDisplayName("Empty Codes Template")
                                .setActive(true)
                                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.SIMPLE_DETAILS_VIEW)
                                .setListHeaderImageUrl("https://example.com/icon.jpg")
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setId(questId)
                                                .setName("EmptyCodesQuest")
                                                .setDisplayName("Empty Codes Quest")
                                                .setProgressEvent(ProgressEvent.PLAY)
                                                .setUnitsToCompletion(50)))
                .build();

        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

        updateHandler.apply(cmd);

        QuestlineTemplate updated = fetchTemplateFromDb(existingTemplateId);
        assertNotNull(updated);

        QuestTemplate quest = updated.getQuests().get(0);
        QuestContributionTemplate contribution = quest.getContributions().get(0);

        assertTrue(contribution.getGameCodes().isEmpty());
        assertTrue(contribution.getGameSuppliers().isEmpty());
    }

    @Test
    void testNewTemplateWithGameCodesCreation() throws Throwable {
        CreateOrUpdateQuestlineTemplateRequest req = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("NewTemplateWithCodes")
                                .setDisplayName("New Template")
                                .setActive(true)
                                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.SIMPLE_DETAILS_VIEW)
                                .setListHeaderImageUrl("https://example.com/icon.jpg")
                                .setHoursToStart(1)
                                .setHoursToComplete(72)
                                .setHoursToClaim(24)
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName("NewQuest")
                                                .setDisplayName("New Quest")
                                                .setProgressEvent(ProgressEvent.WAGER)
                                                .setUnitsToCompletion(100)
                                                .addGameCodes(GAME_CODE_1)
                                                .addGameCodes(GAME_CODE_2)
                                                .addSuppliers(SUPPLIER_1)
                                                .addSuppliers(SUPPLIER_2)))
                .build();

        CreateOrUpdateQuestlineTemplateResponse.Builder respB = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, req, respB));

        updateHandler.apply(cmd);

        long newTemplateId = respB.getQuestlineTemplate().getTemplateId();
        QuestlineTemplate newTemplate = fetchTemplateFromDb(newTemplateId);
        assertNotNull(newTemplate);

        QuestTemplate quest = newTemplate.getQuests().get(0);
        QuestContributionTemplate contribution = quest.getContributions().get(0);

        assertEquals(2, contribution.getGameCodes().size());
        assertEquals(2, contribution.getGameSuppliers().size());
        assertTrue(contribution.getGameCodes().contains(GAME_CODE_1));
        assertTrue(contribution.getGameCodes().contains(GAME_CODE_2));
        assertTrue(contribution.getGameSuppliers().contains(SUPPLIER_1));
        assertTrue(contribution.getGameSuppliers().contains(SUPPLIER_2));
    }

    private long createTemplateWithGameCodes() throws Throwable {
        var request = CreateOrUpdateQuestlineTemplateRequest.newBuilder()
                .setBrandName(TEST_BRAND)
                .setQuestlineTemplate(
                        QuestlineTemplateInfo.newBuilder()
                                .setName("GameCodesTestTemplate")
                                .setDisplayName("Game Codes Test")
                                .setDisplayDescription("Template for testing game codes")
                                .setDisplayNameShort("Game Test")
                                .setActive(true)
                                .setTimeLimited(false)
                                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                                .setDetailViewType(DetailViewType.SIMPLE_DETAILS_VIEW)
                                .setListHeaderImageUrl("https://example.com/icon.jpg")
                                .setHoursToStart(1)
                                .setHoursToComplete(72)
                                .setHoursToClaim(24)
                                .addQuests(
                                        QuestInfo.newBuilder()
                                                .setName("InitialQuest")
                                                .setDisplayName("Initial Quest")
                                                .setDisplayDescription("Initial quest with game codes")
                                                .setProgressEvent(ProgressEvent.PLAY)
                                                .setUnitsToCompletion(50)
                                                .addGameCodes(GAME_CODE_1)
                                                .addSuppliers(SUPPLIER_1)))
                .build();

        var resp = CreateOrUpdateQuestlineTemplateResponse.newBuilder();
        var createCmd = spy(mockUtil.toTransactionalRequest(CreateOrUpdateQuestlineTemplateRequest.class, request, resp));
        updateHandler.apply(createCmd);

        long tid = resp.getQuestlineTemplate().getTemplateId();
        log.info("Created template with game codes, ID={}", tid);
        return tid;
    }

    private void lockTemplate(long templateId) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            Optional<QuestlineTemplate> maybe = ebean.questRepo().findQuestlineTemplateById(templateId, tx);
            if (maybe.isEmpty()) {
                return;
            }
            var tpl = maybe.get();
            var brandOpt = ebean.questlineBrandRepo().brand(TEST_BRAND, tx);
            if (brandOpt.isEmpty()) {
                return;
            }
            QuestlineAccount account = dataManager.createQuestlineAccount(999L, brandOpt.get());
            QuestlineInstance instance = new QuestlineInstance(tpl, account, null);
            ebean.questRepo().save(instance, tx);
            tx.commit();
        }
    }

    private long findFirstQuestIdOfTemplate(long templateId) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var opt = ebean.questRepo().findQuestlineTemplateById(templateId, tx);
            if (opt.isPresent()) {
                var t = opt.get();
                if (!t.getQuests().isEmpty()) {
                    return t.getQuests().getFirst().getId();
                }
            }
            tx.commit();
        }
        return 0L;
    }

    private QuestlineTemplate fetchTemplateFromDb(long templateId) throws Throwable {
        try (Transaction tx = ebean.newReadOnlyTransaction()) {
            var opt = ebean.questRepo().findQuestlineTemplateById(templateId, tx);
            tx.commit();
            return opt.orElse(null);
        }
    }

    private void createPlacements(String brandName) throws Throwable {
        try (Transaction tx = ebean.newTransaction()) {
            QuestlineBrand brand = ebean.questlineBrandRepo().requiredBrand(brandName, tx);
            dataManager.createPlacementTemplatesForBrand(brand);
            tx.commit();
        }
    }
}
