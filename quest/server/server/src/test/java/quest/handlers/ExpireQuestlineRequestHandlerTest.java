package quest.handlers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.beans.factory.annotation.Autowired;

import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.common.PlatformUtil;

import engagement.model.quest.QuestStatusSpec;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.line.DetailViewEmbeddable;
import engagement.model.quest.line.QuestlineDetailsViewSpec;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.QuestlineTypeSpec;
import engagement.model.quest.milestone.ProgressMilestone;
import io.ebean.Transaction;
import quest.IdentityUtil;
import quest.TestDataCommonUtil;
import quest.api.v1.ExpireQuestlineRequest;
import quest.api.v1.ExpireQuestlineResponse;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;

class ExpireQuestlineRequestHandlerTest extends AbstractRequestHandlerTest {

    @Autowired
    private MockUtil mockUtil;
    @Autowired
    private ExpireQuestlineRequestHandler handler;

    @ParameterizedTest
    @MethodSource("expirableStatuses")
    void expireTest(QuestlineStatusSpec questLineSpec, QuestStatusSpec questSpec) throws Throwable {
        QuestlineAccount account;
        QuestlineInstance instance;
        try (var tx = ebean.newTransaction()) {
            var brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            instance = saveQuestlineInstance(brand, account, questLineSpec, questSpec, tx);
            tx.commit();
        }

        invokeHandler(account, instance);

        instance = ebean.find(QuestlineInstance.class, instance.getId());
        assertNotNull(instance);
        assertEquals(QuestlineStatusSpec.EXPIRED, instance.getStatus());
        assertEquals(QuestStatusSpec.EXPIRED, instance.getQuests().getFirst().getStatus());
    }

    @ParameterizedTest
    @MethodSource("notExpirableStatuses")
    void cannotExpireTest(QuestlineStatusSpec questLineSpec, QuestStatusSpec questSpec) throws Throwable {
        QuestlineAccount account;
        QuestlineInstance instance;
        try (var tx = ebean.newTransaction()) {
            var brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            instance = saveQuestlineInstance(brand, account, questLineSpec, questSpec, tx);
            tx.commit();
        }

        invokeHandler(account, instance);

        instance = ebean.find(QuestlineInstance.class, instance.getId());
        assertNotNull(instance);
        assertEquals(questLineSpec, instance.getStatus());
        assertEquals(questSpec, instance.getQuests().getFirst().getStatus());
    }

    @Test
    void expireQuestlineWithFutureExpirationDateShouldNotExpire() throws Throwable {
        QuestlineAccount account;
        QuestlineInstance instance;
        try (var tx = ebean.newTransaction()) {
            var brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            instance = saveQuestlineInstanceWithExpirationDate(brand, account,
                    QuestlineStatusSpec.CREATED, QuestStatusSpec.CREATED,
                    Date.from(Instant.now().plus(1, ChronoUnit.HOURS)), tx);
            tx.commit();
        }

        invokeHandler(account, instance);

        instance = ebean.find(QuestlineInstance.class, instance.getId());
        assertNotNull(instance);
        assertEquals(QuestlineStatusSpec.EXPIRED, instance.getStatus()); // Handler should still expire on demand
        assertEquals(QuestStatusSpec.EXPIRED, instance.getQuests().getFirst().getStatus());
    }

    @Test
    void expireQuestlineWithPastExpirationDateShouldExpire() throws Throwable {
        QuestlineAccount account;
        QuestlineInstance instance;
        try (var tx = ebean.newTransaction()) {
            var brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);
            instance = saveQuestlineInstanceWithExpirationDate(brand, account,
                    QuestlineStatusSpec.CREATED, QuestStatusSpec.CREATED,
                    Date.from(Instant.now().minus(1, ChronoUnit.HOURS)), tx);
            tx.commit();
        }

        // Verify it's expired by time before we even call the handler
        assertTrue(instance.isExpiredByTime());

        invokeHandler(account, instance);

        instance = ebean.find(QuestlineInstance.class, instance.getId());
        assertNotNull(instance);
        assertEquals(QuestlineStatusSpec.EXPIRED, instance.getStatus());
        assertEquals(QuestStatusSpec.EXPIRED, instance.getQuests().getFirst().getStatus());
    }

    @Test
    void testIsExpiredByTimeMethod() throws Throwable {
        QuestlineAccount account;
        QuestlineInstance instance;
        try (var tx = ebean.newTransaction()) {
            var brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);

            // Test with past expiration date
            instance = saveQuestlineInstanceWithExpirationDate(brand, account,
                    QuestlineStatusSpec.CREATED, QuestStatusSpec.CREATED,
                    Date.from(Instant.now().minus(1, ChronoUnit.HOURS)), tx);
            tx.commit();
        }

        assertTrue(instance.isExpiredByTime(), "Questline should be expired by time");
        assertTrue(instance.isExpired(), "Questline should be considered expired");
    }

    @Test
    void testCheckAndExpireIfNeededMethod() throws Throwable {
        QuestlineAccount account;
        QuestlineInstance instance;
        try (var tx = ebean.newTransaction()) {
            var brand = TestDataCommonUtil.findQuestlineTestBrand(ebean, tx);
            account = TestDataCommonUtil.generateQuestlineAccount(ebean, brand, tx);

            // Test with past expiration date
            instance = saveQuestlineInstanceWithExpirationDate(brand, account,
                    QuestlineStatusSpec.CREATED, QuestStatusSpec.CREATED,
                    Date.from(Instant.now().minus(1, ChronoUnit.HOURS)), tx);
            tx.commit();
        }

        // Should automatically expire when we check
        boolean wasExpired = instance.checkAndExpireIfNeeded();
        assertTrue(wasExpired, "checkAndExpireIfNeeded should return true");
        assertEquals(QuestlineStatusSpec.EXPIRED, instance.getStatus());
    }

    private QuestlineInstance saveQuestlineInstance(QuestlineBrand brand,
                                                    QuestlineAccount account,
                                                    QuestlineStatusSpec questLineSpec,
                                                    QuestStatusSpec questSpec,
                                                    Transaction tx) throws Throwable {
        return saveQuestlineInstanceWithExpirationDate(brand, account, questLineSpec, questSpec,
                new Date(), tx); // Default to current time for existing tests
    }

    private QuestlineInstance saveQuestlineInstanceWithExpirationDate(QuestlineBrand brand,
                                                                      QuestlineAccount account,
                                                                      QuestlineStatusSpec questLineSpec,
                                                                      QuestStatusSpec questSpec,
                                                                      Date expirationDate,
                                                                      Transaction tx) throws Throwable {
        var detailView = new DetailViewEmbeddable();
        detailView.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);
        detailView.setListHeaderImageUrl("https://goooogle.com/icon.png");

        var questlineTemplate = QuestlineTemplate.builder()
                .brand(brand)
                .type(QuestlineTypeSpec.SIMPLE_QUEST)
                .name("quest line")
                .code(PlatformUtil.randomUUID())
                .displayName("promo")
                .displayTagline("tag")
                .displayDescription("desc")
                .displayNameShort("desc")
                .minutesToFinishExpire(60) // 1 hour to complete
                .milestones(List
                        .of(ProgressMilestone.builder()
                                .rewardIconUrl("https://google.com/icon.png")
                                .rewardCodes(List.of(PlatformUtil.randomUUID()))
                                .code(PlatformUtil.randomUUID())
                                .progress(100)
                                .build()))
                .quests(List.of(QuestTemplate.builder()
                        .code(PlatformUtil.randomUUID())
                        .orderingIndex(1)
                        .name("quest1")
                        .displayName("quest1")
                        .displayDescription("desc")
                        .milestones(List.of(ProgressMilestone.builder()
                                .rewardIconUrl("https://google.com/icon.png")
                                .code(PlatformUtil.randomUUID())
                                .progress(100)
                                .build()))
                        .build()))
                .startTimerTrigger(QuestlineStartTriggerTypeSpec.USER_MAKES_ANY_PROGRESS)
                .detailView(detailView)
                .build();
        ebean.save(questlineTemplate, tx);

        var instance = new QuestlineInstance(questlineTemplate, account, null);
        instance.setStatus(questLineSpec);
        instance.setExpiresAt(expirationDate);
        ebean.save(instance, tx);

        var quest = instance.getQuests().getFirst();
        quest.setStatus(questSpec);
        ebean.save(quest, tx);

        return instance;
    }

    private void invokeHandler(QuestlineAccount account, QuestlineInstance instance) throws Throwable {
        var req = ExpireQuestlineRequest.newBuilder()
                .setIdentity(Identity.newBuilder()
                        .setByAccountId(IdentityByAccountId.newBuilder()
                                .setAccountId(account.getRemoteId())
                                .setRemoteIp(IdentityUtil.REMOTE_IP)
                                .build())
                        .build())
                .setInstanceCode(instance.getCode().toString())
                .build();
        var cmd = mockUtil.toTransactionalRequest(ExpireQuestlineRequest.class, req, ExpireQuestlineResponse.newBuilder(), account.routingKey());
        handler.apply(cmd);
    }

    private static Stream<Arguments> expirableStatuses() {
        return Stream.of(
                Arguments.of(QuestlineStatusSpec.CREATED, QuestStatusSpec.OPTED_OUT),
                Arguments.of(QuestlineStatusSpec.CREATED, QuestStatusSpec.CREATED),
                Arguments.of(QuestlineStatusSpec.UNCLAIMED, QuestStatusSpec.UNCLAIMED));
    }

    private static Stream<Arguments> notExpirableStatuses() {
        return Stream.of(
                Arguments.of(QuestlineStatusSpec.COMPLETED, QuestStatusSpec.COMPLETED),
                Arguments.of(QuestlineStatusSpec.EXPIRED, QuestStatusSpec.EXPIRED),
                Arguments.of(QuestlineStatusSpec.RETRACTED, QuestStatusSpec.EXPIRED));
    }
}