package quest.handlers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.List;

import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import engagement.repo.QuestEbeanJpaManager;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.common.PlatformUtil;

import engagement.model.quest.line.QuestlineDetailsViewSpec;
import quest.IdentityUtil;
import quest.InitialTestDataGenerator;
import quest.QuestlineExternalIdentityManager;
import quest.api.v1.GetQuestlinesRequest;
import quest.api.v1.GetQuestlinesResponse;
import quest.api.v1.Questline;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.QuestlineTypeSpec;
import engagement.model.quest.milestone.ProgressMilestone;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import quest.testutils.DataManager;

@Slf4j
class GetQuestlinesRequestHandlerTest extends AbstractRequestHandlerTest {
    @Autowired
    private MockUtil mockUtil;

    @Autowired
    private GetQuestlinesRequestHandler handler;

    @Autowired
    private QuestEbeanJpaManager ebean;

    @Autowired
    private QuestlineExternalIdentityManager im;

    @Autowired
    private DataManager dataManager;

    @Test
    void testFetchQuestlines() throws Throwable {
        QuestlineBrand brand = InitialTestDataGenerator.getQuestlineBrand(ebean);
        assertNotNull(brand);

        QuestlineAccount account = dataManager.createQuestlineAccount(5001L, brand);

        // Create milestone for a quest (100%).
        ProgressMilestone milestoneQuest = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        // Single quest
        QuestTemplate quest1 = QuestTemplate.builder()
                .code(PlatformUtil.randomUUID())
                .orderingIndex(1)
                .name("quest1")
                .displayName("quest1")
                .displayDescription("desc")
                .milestones(List.of(milestoneQuest))
                .build();

        // Questline-level final milestone at 100%
        ProgressMilestone milestoneLine = ProgressMilestone.builder()
                .code(PlatformUtil.randomUUID())
                .progress(100)
                .rewardIconUrl("https://google.com/icon.png")
                .build();

        // Create questline template
        QuestlineTemplate template = dataManager.createQuestlineTemplate(
                brand,
                "Sample Template #1",
                "shortName",
                "Display tagline",
                "Description",
                QuestlineTypeSpec.SIMPLE_QUEST,
                QuestlineStartTriggerTypeSpec.ASSIGNED,
                false,
                List.of(quest1),
                List.of(milestoneLine),
                QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP,
                "https://www.google.com/image.jpg"
        );
        QuestlineInstance instance = dataManager.createQuestlineInstance(template, account, QuestlineStatusSpec.CREATED);

        // Build proto request
        var req = GetQuestlinesRequest.newBuilder()
                .setIdentity(IdentityUtil.getIdentityByToken(im, account.getRemoteId(), brand))
                .build();

        // Prepare the response builder
        var respBuilder = GetQuestlinesResponse.newBuilder();

        var cmd = Mockito.spy(mockUtil.toTransactionalRequest(
                GetQuestlinesRequest.class,
                req,
                respBuilder
        ));
        cmd.setRoutingKey(AsciiString.of(account.getHash()));

        handler.apply(cmd);

        var resp = respBuilder.build();
        log.info("reply: {}", resp);

        assertEquals(1, resp.getQuestlinesCount());
        Questline activeQuestline = resp.getQuestlines(0);

        assertEquals(instance.getCode().toString(), activeQuestline.getQuestlineCode());
        assertEquals("Sample Template #1", activeQuestline.getTitle());

    }
}
