package quest.handlers;

import static engagement.model.quest.PlacementTypeSpec.INBOX_NOTIFICATION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static quest.api.v1.QuestlineOverallStatusEnum.QUESTLINE_REPLACED;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.turbospaces.boot.test.MockUtil;
import com.turbospaces.common.PlatformUtil;

import engagement.model.quest.QuestInstance;
import engagement.model.quest.QuestTemplate;
import engagement.model.quest.QuestlineAccount;
import engagement.model.quest.QuestlineBrand;
import engagement.model.quest.line.DetailViewEmbeddable;
import engagement.model.quest.line.QuestlineDetailsViewSpec;
import engagement.model.quest.line.QuestlineInstance;
import engagement.model.quest.line.QuestlineStartTriggerTypeSpec;
import engagement.model.quest.line.QuestlineStatusSpec;
import engagement.model.quest.line.QuestlineTemplate;
import engagement.model.quest.line.QuestlineTypeSpec;
import engagement.model.quest.line.placement.Placement;
import engagement.model.quest.line.placement.PlacementTemplate;
import engagement.model.quest.line.placement.PlacementTemplateTypeSpec;
import engagement.model.quest.milestone.ProgressMilestone;
import engagement.repo.QuestEbeanJpaManager;
import io.ebean.Transaction;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import quest.InitialTestDataGenerator;
import quest.api.v1.CreateQuestlineInstanceRequest;
import quest.api.v1.CreateQuestlineInstanceResponse;
import quest.api.v1.ExternalAccount;
import quest.api.v1.ProgressEvent;
import quest.api.v1.QuestInfo;
import quest.api.v1.QuestlineStartTriggerTypeSpecPB;
import quest.api.v1.QuestlineTemplateInfo;
import quest.api.v1.QuestlineTypeSpecPB;
import quest.api.v1.QuestlineUpdatedEvent;
import quest.testutils.DataManager;

@Slf4j
class CreateQuestlineInstanceRequestHandlerTest extends AbstractRequestHandlerTest {
    @Autowired
    private MockUtil mockUtil;
    @Autowired
    private CreateQuestlineInstanceRequestHandler handler;
    @Autowired
    private QuestEbeanJpaManager ebean;

    @Autowired
    private DataManager dataManager;

    @Test
    void testCreateQuestlineInstance_Success() throws Throwable {
        QuestlineBrand brand = InitialTestDataGenerator.getQuestlineBrand(ebean);
        assertNotNull(brand);

        QuestlineAccount account = dataManager.createQuestlineAccount(42L, brand);
        QuestlineTemplate template = createSampleTemplate("One", brand);

        CreateQuestlineInstanceRequest request = CreateQuestlineInstanceRequest.newBuilder()
                .setBrandName(brand.getName())
                .setQuestlineTemplateId(template.getId())
                .setAccount(ExternalAccount.newBuilder()
                        .setAccountId(42)
                        .setBrandName(InitialTestDataGenerator.TEST_BRAND)
                        .setHash("hash"))
                .build();

        CreateQuestlineInstanceResponse.Builder resp = CreateQuestlineInstanceResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(
                CreateQuestlineInstanceRequest.class,
                request,
                resp));
        cmd.setRoutingKey(AsciiString.of(account.getHash()));

        handler.apply(cmd);

        assertTrue(resp.getSuccess());
        assertEquals("", resp.getErrorMessage());
        long instanceId = resp.getInstanceId();
        assertTrue(instanceId > 0);
        String instanceCode = resp.getInstanceCode();
        assertNotNull(instanceCode);

        QuestlineInstance instance;
        try (Transaction tx = ebean.newTransaction()) {
            instance = ebean.find(QuestlineInstance.class).usingTransaction(tx)
                    .where().idEq(instanceId)
                    .findOne();
            tx.commit();
        }
        assertNotNull(instance);
        assertEquals(template.getId(), instance.getQuestlineTemplate().getId());
        assertEquals(account.getId(), instance.getAccount().getId());
        assertFalse(instance.getQuests().isEmpty());

        QuestInstance qi = instance.getQuests().get(0);
    }

    @Test
    void testCreateQuestlineInstance_TemplateNotFound() throws Throwable {
        QuestlineBrand brand = InitialTestDataGenerator.getQuestlineBrand(ebean);
        assertNotNull(brand);
        QuestlineAccount account = dataManager.createQuestlineAccount(9999L, brand);

        CreateQuestlineInstanceRequest request = CreateQuestlineInstanceRequest.newBuilder()
                .setBrandName(brand.getName())
                .setQuestlineTemplateId(123456789L) // something not real
                .setAccount(ExternalAccount.newBuilder()
                        .setAccountId(9999L)
                        .setBrandName(InitialTestDataGenerator.TEST_BRAND)
                        .setHash("hash"))
                .build();

        CreateQuestlineInstanceResponse.Builder resp = CreateQuestlineInstanceResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(
                CreateQuestlineInstanceRequest.class,
                request,
                resp));
        cmd.setRoutingKey(AsciiString.of(account.getHash()));

        handler.apply(cmd);

        assertFalse(resp.getSuccess());
        assertTrue(resp.getErrorMessage().contains("not found"));
        assertEquals(0, resp.getInstanceId());
        assertEquals("", resp.getInstanceCode());
    }

    @Test
    void testOverrideQuestlineInstance_Success() throws Throwable {
        QuestlineBrand brand = InitialTestDataGenerator.getQuestlineBrand(ebean);
        assertNotNull(brand);

        QuestlineAccount account = dataManager.createQuestlineAccount(42L, brand);
        QuestlineTemplate template = createSampleTemplate("One", brand);

        CreateQuestlineInstanceRequest request = CreateQuestlineInstanceRequest.newBuilder()
                .setBrandName(brand.getName())
                .setQuestlineTemplateId(template.getId())
                .setAccount(ExternalAccount.newBuilder()
                        .setAccountId(42)
                        .setBrandName(InitialTestDataGenerator.TEST_BRAND)
                        .setHash("hash"))
                .build();

        CreateQuestlineInstanceResponse.Builder resp = CreateQuestlineInstanceResponse.newBuilder();
        var cmd = spy(mockUtil.toTransactionalRequest(
                CreateQuestlineInstanceRequest.class,
                request,
                resp));
        cmd.setRoutingKey(AsciiString.of(account.getHash()));

        handler.apply(cmd);

        assertTrue(resp.getSuccess());
        assertEquals("", resp.getErrorMessage());
        long instanceId = resp.getInstanceId();
        assertTrue(instanceId > 0);
        String instanceCode = resp.getInstanceCode();
        assertNotNull(instanceCode);

        QuestlineInstance instance;
        try (Transaction tx = ebean.newTransaction()) {
            instance = ebean.find(QuestlineInstance.class).usingTransaction(tx)
                    .where().idEq(instanceId)
                    .findOne();
            tx.commit();
        }
        assertNotNull(instance);
        assertEquals(template.getId(), instance.getQuestlineTemplate().getId());
        assertEquals(account.getId(), instance.getAccount().getId());
        assertFalse(instance.getQuests().isEmpty());

        //Overrider questline instance
        CreateQuestlineInstanceRequest updateRequest = CreateQuestlineInstanceRequest.newBuilder()
                .setBrandName(brand.getName())
                .setQuestlineTemplateId(template.getId())
                .setAccount(ExternalAccount.newBuilder()
                        .setAccountId(42)
                        .setBrandName(InitialTestDataGenerator.TEST_BRAND)
                        .setHash("hash"))
                .setOverrideExisting(true)
                .build();

        CreateQuestlineInstanceResponse.Builder updateResp = CreateQuestlineInstanceResponse.newBuilder();
         var cmdB = spy(mockUtil.toTransactionalRequest(
                CreateQuestlineInstanceRequest.class,
                updateRequest,
                updateResp));
        cmdB.setRoutingKey(AsciiString.of(account.getHash()));

        handler.apply(cmdB);

        verify(cmdB).eventStream(argThat(event -> {
            if (!(event.build() instanceof QuestlineUpdatedEvent e)) return false;

            return e.getInfo().getStatus() == QUESTLINE_REPLACED &&
                    e.getInfo().getCode().equals(instanceCode);
        }));

        assertTrue(updateResp.getSuccess());
        assertEquals("", updateResp.getErrorMessage());
        long instanceIdB = updateResp.getInstanceId();
        assertTrue(instanceIdB > 0);
        String instanceCodeB = updateResp.getInstanceCode();
        assertNotNull(instanceCodeB);

        QuestlineInstance oldInstance;
        try (Transaction tx = ebean.newTransaction()) {
            oldInstance = ebean.find(QuestlineInstance.class).usingTransaction(tx)
                    .where().idEq(instanceId)
                    .findOne();
            tx.commit();
        }
        assertNotNull(oldInstance);
        assertEquals(QuestlineStatusSpec.REPLACED, oldInstance.getStatus());

        QuestlineInstance newInstance;
        try (Transaction tx = ebean.newTransaction()) {
            newInstance = ebean.find(QuestlineInstance.class).usingTransaction(tx)
                    .where().idEq(instanceIdB)
                    .findOne();
            tx.commit();
        }
        assertNotNull(newInstance);
        assertEquals(QuestlineStatusSpec.CREATED, newInstance.getStatus());
    }

    private QuestlineTemplate createSampleTemplate(String name, QuestlineBrand brand) throws Throwable {
        QuestInfo questInfo = QuestInfo.newBuilder()
                .setName("Sample Quest Name")
                .setProgressEvent(ProgressEvent.PLAY)
                .build();

        QuestlineTemplateInfo templateInfo = QuestlineTemplateInfo.newBuilder()
                .setName(name)
                .setDisplayName("Display " + name)
                .setDisplayTagline("Tag " + name)
                .setDisplayDescription("Display desc " + name)
                .setStartTrigger(QuestlineStartTriggerTypeSpecPB.QASSIGNED)
                .setQuestlineType(QuestlineTypeSpecPB.SIMPLE_QUEST)
                .addQuests(questInfo)
                .build();

        QuestlineTemplate template = new QuestlineTemplate();
        template.setBrand(brand);
        template.setName(templateInfo.getName());
        template.setCode(PlatformUtil.randomUUID());
        template.setDisplayName("Display " + name);
        template.setDisplayTagline("Tag " + name);
        template.setDisplayDescription("Display desc " + name);
        template.setDisplayNameShort("short " + name);
        template.setMinutesToStartExpire(10);
        template.setMinutesToFinishExpire(60);
        template.setType(QuestlineTypeSpec.SIMPLE_QUEST);
        template.setStartTimerTrigger(QuestlineStartTriggerTypeSpec.ASSIGNED);
        template.setMilestones(List.of(
                ProgressMilestone.builder()
                        .code(PlatformUtil.randomUUID())
                        .progress(100)
                        .rewardIconUrl("https://goooogle.com/icon.png")
                        .build()
        ));
        var detailView = new DetailViewEmbeddable();
        detailView.setType(QuestlineDetailsViewSpec.SIMPLE_QUEST_POPUP);
        detailView.setListHeaderImageUrl("https://goooogle.com/icon.png");

        template.setDetailView(detailView);
        QuestTemplate questT = new QuestTemplate();
        questT.setCode(PlatformUtil.randomUUID());
        questT.setName(questInfo.getName());
        questT.setDisplayName("Display of " + questInfo.getName());
        questT.setIconUrl("https//icon.url/test.png");
        questT.setOrderingIndex(0);
        questT.setQuestlineTemplate(template);
        questT.setMilestones(List.of(
                ProgressMilestone.builder()
                        .code(PlatformUtil.randomUUID())
                        .progress(100)
                        .rewardIconUrl("https://goooogle.com/icon.png")
                        .build()
        ));
        questT.setDisplayDescription("Display of " + questInfo.getName());

        List<QuestTemplate> questTemplates = new ArrayList<>();
        questTemplates.add(questT);
        template.setQuests(questTemplates);

        PlacementTemplate placementTemplate = new PlacementTemplate();
        placementTemplate.setId(setupInboxNotificationIcon(brand));
        placementTemplate.type(PlacementTemplateTypeSpec.INBOX_NOTIFICATION);

        Placement placement = new Placement();
        placement.theme("light");
        placement.placementTemplate(placementTemplate);
        placement.url1("https://example.com/icon.png");
        placement.questlineTemplate(template);

        template.setPlacements(List.of(placement));

        try (Transaction tx = ebean.newTransaction()) {
            ebean.questRepo().save(template, tx);
            tx.commit();
        }
        return template;
    }

    private long setupInboxNotificationIcon(QuestlineBrand brand) throws Throwable {
        try (var tx = ebean.newTransaction()) {
            PlacementTemplate placementTemplate = new PlacementTemplate();
            placementTemplate.brand(brand);
            placementTemplate.featureId(INBOX_NOTIFICATION.toLowerCase());
            placementTemplate.name(INBOX_NOTIFICATION);
            placementTemplate.themes(List.of("light", "dark"));
            placementTemplate.type(PlacementTemplateTypeSpec.INBOX_NOTIFICATION);
            ebean.save(placementTemplate, tx);
            tx.commit();
            return placementTemplate.getId();
        }
    }
}
