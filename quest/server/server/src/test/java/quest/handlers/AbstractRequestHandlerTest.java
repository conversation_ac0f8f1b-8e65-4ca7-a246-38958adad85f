package quest.handlers;

import com.turbospaces.cfg.DynamicPropertyFactory;
import engagement.repo.QuestEbeanJpaManager;
import lombok.extern.log4j.Log4j;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import quest.InitialTestDataGenerator;
import quest.QuestServerProperties;
import quest.config.H2DatabaseDiModule;
import quest.config.QuestSpringBootTestContextBootstrapper;
import quest.di.QuestServiceApiDiModule;
import quest.mock.MockCommonDiModule;
import quest.mock.MockUtilConfiguration;

@Log4j
@ExtendWith(SpringExtension.class)
@BootstrapWith(QuestSpringBootTestContextBootstrapper.class)
@TestExecutionListeners(mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS, listeners = {InitialTestDataGenerator.class})
@ContextConfiguration(classes = {
        QuestServiceApiDiModule.class,
        H2DatabaseDiModule.class,
        MockCommonDiModule.class,
        MockUtilConfiguration.class,
        QuestServerProperties.class,
        DynamicPropertyFactory.class
})
public abstract class AbstractRequestHandlerTest {

    @Autowired
    protected QuestEbeanJpaManager ebean;

}
