package quest.di;

import com.turbospaces.cfg.ApplicationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.ThreadPoolContextWorker;

import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import quest.QuestServerProperties;

@Slf4j
@Configuration
public class QuestTestServiceDiModule {

    @Bean
    public DefaultPlatformExecutorService defaultPlatformExecutorService(
            ApplicationProperties props,
            QuestServerProperties serverProps,
            MeterRegistry meterRegistry) {
        var toReturn = new DefaultPlatformExecutorService(props, meterRegistry);
        toReturn.setKeepAlive(serverProps.GAME_ROUND_EVENT_REPLICATION_THREADS_MAX_IDLE);
        toReturn.setMinPoolSize(serverProps.GAME_ROUND_EVENT_REPLICATION_THREADS_MIN_SIZE);
        toReturn.setMaxPoolSize(serverProps.GAME_ROUND_EVENT_REPLICATION_THREADS_MAX_SIZE);
        return toReturn;
    }

    @Bean
    public ThreadPoolContextWorker threadPoolContextWorker(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            DefaultPlatformExecutorService executor) {
        return new ThreadPoolContextWorker(props, meterRegistry, executor);
    }

}
