cloud {
  application {
    space_name = kiev1-dev
  }
}

service.jwt.uri="3AvKNCEPYiZocTVbvDOZiNt3zouw2flsmKJPUBxLXA09BXIF29nG69KaSyEneT1X"

service.kafka.uri = "kafka://127.0.0.1:9092"
service.postgres-owner.uri = "postgres://app_owner:app_owner@127.0.0.1:5432/defaultdb"
service.postgres-app.uri = "postgres://app_user:app_user@127.0.0.1:5432/defaultdb"
service.quartz-app.uri = "postgres://quartz_user:quartz_user@127.0.0.1:5432/defaultdb"
service.onl-sendgrid.uri = "https://api_key:<EMAIL>"
service.pulsz-sendgrid.uri = "https://api_key:<EMAIL>"
service.pulszbingo-sendgrid.uri = "https://api_key:<EMAIL>"
service.pragmatic.uri = "https://ysi_onlinelotto:<EMAIL>"
service.netent.uri = "https://yellowsocialinteractive:<EMAIL>"
service.onl.uri = "https://ozam7kt9oqbxwh50a8gyv5woe6a2r44qqyk:<EMAIL>"
service.onl-s3.uri = "file://********************:nhgnqGoQTkSlybdZ+B6ixsw0Hc0N6yhwmxfHhiD2@eu-west-1/alot-hedging-test/ONL"

#
# system
#
app.data.start-clean=false
app.shutdown-hook.enabled=false

#
# Jobs
#
quartz.enforce-disabled-jobs.enabled=false

#
# kafka
#
kafka.system-exit.on-queue-full=false
kafka.nack.on-queue-full=false

#
# spring
#
spring.output.ansi.enabled=detect

# app.logging.reset-to=error
