update lotto.lotto_sessions s set payout_id = i.payout_id from (select p.id as payout_id, p.product_id, p.currency from lotto.lotto_payouts p where p.code = 'latest') i  where i.currency = s.currency and i.product_id = s.product_id and s.payout_id is null;

/* ------------------------------ */
/* --- free to play lotteries --- */
/* ------------------------------ */

/* ------------------- */
/* --- onl morning --- */
/* ------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'USD', 0.00, 'hourly', 
'{500000, 1500000, 1800000, 2000000, 500000, 1250000, 1500000, 1750000, 2000000, 500000, 1000000, 1100000, 1250000, 1800000, 900000}', 
10000, 'settlement_currency', -- t2  (Match 5 + 1 Bonus Ball)
100,   'settlement_currency', -- t3  (Match 5 + 0 Bonus Ball)
10,    'settlement_currency', -- t4  (Match 4 + 2 Bonus Ball)
10,    'settlement_currency', -- t5  (Match 4 + 1 Bonus Ball)
5,     'settlement_currency', -- t6  (Match 4 + 0 Bonus Ball)
1,     'settlement_currency', -- t7  (Match 3 + 2 Bonus Ball)
1,     'settlement_currency', -- t8  (Match 3 + 1 Bonus Ball)
0.3,   'settlement_currency', -- t9  (Match 3 + 0 Bonus Ball)
0.15,  'settlement_currency', -- t10 (Match 2 + 2 Bonus Ball)
0.15,  'settlement_currency', -- t11 (Match 2 + 1 Bonus Ball)
0.04,  'settlement_currency', -- t12 (Match 2 + 0 Bonus Ball)
0.02,  'settlement_currency', -- t13 (Match 1 + 2 Bonus Ball)
0.02,  'settlement_currency', -- t14 (Match 1 + 1 Bonus Ball)
100,   'free_currency',       -- t15 (Match 1 + 0 Bonus Ball)
50,    'free_currency',       -- t16 (Match 0 + 2 Bonus Ball)
50,    'free_currency',       -- t17 (Match 0 + 1 Bonus Ball)
0,     'free_currency',       -- t18 (Match 0 + 0 Bonus Ball)
'settlement_currency', now(), now(), 1 from core.products p where p.code in ('onlMorning'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* --------------------- */
/* --- onl afternoon --- */
/* --------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'USD', 0, 'hourly', 
'{700000, 900000, 1350000, 800000, 1100000, 2000000, 600000, 1400000, 1750000, 2000000, 500000, 1200000, 1600000, 1900000, 2000000}', 
10000, 'settlement_currency', -- t2  (Match 5 + 1 Bonus Ball)
100,   'settlement_currency', -- t3  (Match 5 + 0 Bonus Ball)
10,    'settlement_currency', -- t4  (Match 4 + 2 Bonus Ball)
10,    'settlement_currency', -- t5  (Match 4 + 1 Bonus Ball)
5,     'settlement_currency', -- t6  (Match 4 + 0 Bonus Ball)
1,     'settlement_currency', -- t7  (Match 3 + 2 Bonus Ball)
1,     'settlement_currency', -- t8  (Match 3 + 1 Bonus Ball)
0.3,   'settlement_currency', -- t9  (Match 3 + 0 Bonus Ball)
0.15,  'settlement_currency', -- t10 (Match 2 + 2 Bonus Ball)
0.15,  'settlement_currency', -- t11 (Match 2 + 1 Bonus Ball)
0.04,  'settlement_currency', -- t12 (Match 2 + 0 Bonus Ball)
0.02,  'settlement_currency', -- t13 (Match 1 + 2 Bonus Ball)
0.02,  'settlement_currency', -- t14 (Match 1 + 1 Bonus Ball)
100,   'free_currency',       -- t15 (Match 1 + 0 Bonus Ball)
50,    'free_currency',       -- t16 (Match 0 + 2 Bonus Ball)
50,    'free_currency',       -- t17 (Match 0 + 1 Bonus Ball)
0,     'free_currency',       -- t18 (Match 0 + 0 Bonus Ball)
'settlement_currency', now(), now(), 1 from core.products p where p.code in ('onlAfternoon'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* ------------------- */
/* --- onl evening --- */
/* ------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'USD', 0, 'hourly', 
'{1900000, 750000, 1200000, 1800000, 2000000, 600000, 1400000, 1000000, 1900000, 2000000, 900000, 1100000, 1350000, 1500000, 1800000}', 
10000, 'settlement_currency', -- t2  (Match 5 + 1 Bonus Ball)
100,   'settlement_currency', -- t3  (Match 5 + 0 Bonus Ball)
10,    'settlement_currency', -- t4  (Match 4 + 2 Bonus Ball)
10,    'settlement_currency', -- t5  (Match 4 + 1 Bonus Ball)
5,     'settlement_currency', -- t6  (Match 4 + 0 Bonus Ball)
1,     'settlement_currency', -- t7  (Match 3 + 2 Bonus Ball)
1,     'settlement_currency', -- t8  (Match 3 + 1 Bonus Ball)
0.3,   'settlement_currency', -- t9  (Match 3 + 0 Bonus Ball)
0.15,  'settlement_currency', -- t10 (Match 2 + 2 Bonus Ball)
0.15,  'settlement_currency', -- t11 (Match 2 + 1 Bonus Ball)
0.04,  'settlement_currency', -- t12 (Match 2 + 0 Bonus Ball)
0.02,  'settlement_currency', -- t13 (Match 1 + 2 Bonus Ball)
0.02,  'settlement_currency', -- t14 (Match 1 + 1 Bonus Ball)
100,   'free_currency',       -- t15 (Match 1 + 0 Bonus Ball)
50,    'free_currency',       -- t16 (Match 0 + 2 Bonus Ball)
50,    'free_currency',       -- t17 (Match 0 + 1 Bonus Ball)
0,     'free_currency',       -- t18 (Match 0 + 0 Bonus Ball)
'settlement_currency', now(), now(), 1 from core.products p where p.code in ('onlEvening'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* ---------------- */
/* --- onl hour --- */
/* ---------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'USD', 0, 'hourly', 
'{95000, 37500, 60000, 90000, 100000, 30000, 70000, 50000, 85000, 100000, 45000, 55000, 67500, 75000, 90000}', 
1000,  'settlement_currency', -- t2  (Match 5 + 1 Bonus Ball)
100,   'settlement_currency', -- t3  (Match 5 + 0 Bonus Ball)
7.5,   'settlement_currency', -- t4  (Match 4 + 2 Bonus Ball)
7.5,   'settlement_currency', -- t5  (Match 4 + 1 Bonus Ball)
2.5,   'settlement_currency', -- t6  (Match 4 + 0 Bonus Ball)
1,     'settlement_currency', -- t7  (Match 3 + 2 Bonus Ball)
1,     'settlement_currency', -- t8  (Match 3 + 1 Bonus Ball)
0.3,   'settlement_currency', -- t9  (Match 3 + 0 Bonus Ball)
0.15,  'settlement_currency', -- t10 (Match 2 + 2 Bonus Ball)
0.15,  'settlement_currency', -- t11 (Match 2 + 1 Bonus Ball)
0.04,  'settlement_currency', -- t12 (Match 2 + 0 Bonus Ball)
0.02,  'settlement_currency', -- t13 (Match 1 + 2 Bonus Ball)
0.02,  'settlement_currency', -- t14 (Match 1 + 1 Bonus Ball)
100,   'free_currency',       -- t15 (Match 1 + 0 Bonus Ball)
50,    'free_currency',       -- t16 (Match 0 + 2 Bonus Ball)
50,    'free_currency',       -- t17 (Match 0 + 1 Bonus Ball)
0,     'free_currency',       -- t18 (Match 0 + 0 Bonus Ball)
'settlement_currency', now(), now(), 1 from core.products p where p.code in ('onlHour'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;


/* -------------------- */
/* --- onl millions --- */
/* -------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'USD', 0, 'hourly', 
'{15000000, 17000000, 19000000, 21000000, 23000000, 25000000, 16000000, 18000000, 20000000, 22000000, 24000000, 18000000, 20000000, 22500000, 24000000}', 
5000,  'settlement_currency', -- t2  (Match 5 + 1 Bonus Ball)
1000,  'settlement_currency', -- t3  (Match 5 + 0 Bonus Ball)
10,    'settlement_currency', -- t4  (Match 4 + 2 Bonus Ball)
10,    'settlement_currency', -- t5  (Match 4 + 1 Bonus Ball)
5,     'settlement_currency', -- t6  (Match 4 + 0 Bonus Ball)
0.5,   'settlement_currency', -- t7  (Match 3 + 2 Bonus Ball)
0.5,   'settlement_currency', -- t8  (Match 3 + 1 Bonus Ball)
0.1,   'settlement_currency', -- t9  (Match 3 + 0 Bonus Ball)
1000,  'free_currency',       -- t10 (Match 2 + 2 Bonus Ball)
1000,  'free_currency',       -- t11 (Match 2 + 1 Bonus Ball)
250,   'free_currency',       -- t12 (Match 2 + 0 Bonus Ball)
200,   'free_currency',       -- t13 (Match 1 + 2 Bonus Ball)
200,   'free_currency',       -- t14 (Match 1 + 1 Bonus Ball)
150,   'free_currency',       -- t15 (Match 1 + 0 Bonus Ball)
100,   'free_currency',       -- t16 (Match 0 + 2 Bonus Ball)
100,    'free_currency',      -- t17 (Match 0 + 1 Bonus Ball)
0,     'free_currency',       -- t18 (Match 0 + 0 Bonus Ball)
'settlement_currency', now(), now(), 1 from core.products p where p.code in ('onlMillions'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* ---------------------------- */
/* --- onl milliins virtual --- */
/* ---------------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'USD', 0, 'minutely', 
'{13000000, 14000000, 15000000, 16000000, 23000000, 25000000, 11000000, 18000000, 17000000, 22000000, 24000000, 18000000, 25000000, 22500000, 24500000}', 
10000, 'settlement_currency', -- t2  (Match 5 + 1 Bonus Ball)
1000,  'settlement_currency', -- t3  (Match 5 + 0 Bonus Ball)
100,   'settlement_currency', -- t4  (Match 4 + 2 Bonus Ball)
100,   'settlement_currency', -- t5  (Match 4 + 1 Bonus Ball)
25,    'settlement_currency', -- t6  (Match 4 + 0 Bonus Ball)
2.5,   'settlement_currency', -- t7  (Match 3 + 2 Bonus Ball)
2.5,   'settlement_currency', -- t8  (Match 3 + 1 Bonus Ball)
1.0,   'settlement_currency', -- t9  (Match 3 + 0 Bonus Ball)
1000,  'free_currency',       -- t10 (Match 2 + 2 Bonus Ball)
1000,  'free_currency',       -- t11 (Match 2 + 1 Bonus Ball)
250,   'free_currency',       -- t12 (Match 2 + 0 Bonus Ball)
200,   'free_currency',       -- t13 (Match 1 + 2 Bonus Ball)
200,   'free_currency',       -- t14 (Match 1 + 1 Bonus Ball)
150,   'free_currency',       -- t15 (Match 1 + 0 Bonus Ball)
100,   'free_currency',       -- t16 (Match 0 + 2 Bonus Ball)
50,    'free_currency',       -- t17 (Match 0 + 1 Bonus Ball)
0,     'free_currency',       -- t18 (Match 0 + 0 Bonus Ball)
'settlement_currency', now(), now(), 1 from core.products p where p.code in ('onlMillionsVirtual'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;



/* ---------------------------- */
/* --- real money lotteries --- */
/* ---------------------------- */

/* ------------------- */
/* --- onl jackpot --- */
/* ------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'SC', 1, 'hourly', 
'{10000000, 15000000, 18000000, 21000000, 25000000, 10000000, 13000000, 16000000, 21000000, 25000000, 11000000, 14000000, 17000000, 20000000, 25000000}', 
500000, 'real_currency', -- t2  (Match 5 + 0 Bonus Ball)
1000,   'real_currency', -- t3  (Match 4 + 1 Bonus Ball)
90,     'real_currency', -- t4  (Match 4 + 0 Bonus Ball)
90,     'real_currency', -- t5  (Match 3 + 1 Bonus Ball)
9,      'real_currency', -- t6  (Match 3 + 0 Bonus Ball)
9,      'real_currency', -- t7  (Match 2 + 1 Bonus Ball)
1.80,   'real_currency', -- t8  (Match 2 + 0 Bonus Ball)
1.80,   'real_currency', -- t9  (Match 1 + 1 Bonus Ball)
0.40,   'real_currency', -- t10 (Match 1 + 0 Bonus Ball)
0.40,   'real_currency', -- t11 (Match 0 + 1 Bonus Ball)
0,      'real_currency', -- t12 (Match 0 + 0 Bonus Ball)
-- undefined
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
'real_currency', now(), now(), 1 from core.products p where p.code in ('onlJackpot'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* --------------------------------- */
/* --- onl jackpot hour / minute --- */
/* --------------------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'SC', 0.5, 'hourly', 
'{5000000, 7500000, 9000000, 10500000, 12500000, 5000000, 6500000, 8000000, 10500000, 12500000, 5500000, 7500000, 8500000, 10000000, 12500000}', 
250000, 'real_currency', -- t2  (Match 5 + 0 Bonus Ball)
500,    'real_currency', -- t3  (Match 4 + 1 Bonus Ball)
45,     'real_currency', -- t4  (Match 4 + 0 Bonus Ball)
45,     'real_currency', -- t5  (Match 3 + 1 Bonus Ball)
4.40,   'real_currency', -- t6  (Match 3 + 0 Bonus Ball)
4.40,   'real_currency', -- t7  (Match 2 + 1 Bonus Ball)
0.90,   'real_currency', -- t8  (Match 2 + 0 Bonus Ball)
0.90,   'real_currency', -- t9  (Match 1 + 1 Bonus Ball)
0.20,   'real_currency', -- t10 (Match 1 + 0 Bonus Ball)
0.20,   'real_currency', -- t11 (Match 0 + 1 Bonus Ball)
0.05,   'real_currency', -- t12 (Match 0 + 0 Bonus Ball)
-- undefined
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
'real_currency', now(), now(), 1 from core.products p where p.code in ('onlJackpotHour', 'onlJackpotMinute'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* --------------------------- */
/* --- onl jackpot virtual --- */
/* --------------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'SC', 0.5, 'minutely', 
'{10000000, 15000000, 18000000, 21000000, 25000000, 10000000, 13000000, 16000000, 21000000, 25000000, 11000000, 14000000, 17000000, 20000000, 25000000}', 
250000, 'real_currency', -- t2  (Match 5 + 0 Bonus Ball)
500,    'real_currency', -- t3  (Match 4 + 1 Bonus Ball)
50,     'real_currency', -- t4  (Match 4 + 0 Bonus Ball)
50,     'real_currency', -- t5  (Match 3 + 1 Bonus Ball)
5,      'real_currency', -- t6  (Match 3 + 0 Bonus Ball)
5,      'real_currency', -- t7  (Match 2 + 1 Bonus Ball)
5,      'real_currency', -- t8  (Match 2 + 0 Bonus Ball)
2.5,      'real_currency', -- t9  (Match 1 + 1 Bonus Ball)
2.5,   'real_currency', -- t10 (Match 1 + 0 Bonus Ball)
2,   'real_currency', -- t11 (Match 0 + 1 Bonus Ball)
10,   'real_currency', -- t12 (Match 0 + 0 Bonus Ball)
-- undefined
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
0,      'real_currency',
'real_currency', now(), now(), 1 from core.products p where p.code in ('onlJackpotVirtual'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;


/* ---------------------------- */
/* --- free money lotteries --- */
/* ---------------------------- */
/* ------------------- */
/* --- onl jackpot --- */
/* ------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'GC', 50, 'hourly', 
'{500000000, 750000000, 900000000, 1050000000, 1250000000, 500000000, 650000000, 800000000, 1050000000, 1250000000, 550000000, 700000000, 850000000, 1000000000, 1250000000}', 
25000000, 'free_currency', -- t2  (Match 5 + 0 Bonus Ball)
50000,    'free_currency', -- t3  (Match 4 + 1 Bonus Ball)
5000,     'free_currency', -- t4  (Match 4 + 0 Bonus Ball)
5000,     'free_currency', -- t5  (Match 3 + 1 Bonus Ball)
500,      'free_currency', -- t6  (Match 3 + 0 Bonus Ball)
500,      'free_currency', -- t7  (Match 2 + 1 Bonus Ball)
100,      'free_currency', -- t8  (Match 2 + 0 Bonus Ball)
100,      'free_currency', -- t9  (Match 1 + 1 Bonus Ball)
25,       'free_currency', -- t10 (Match 1 + 0 Bonus Ball)
25,       'free_currency', -- t11 (Match 0 + 1 Bonus Ball)
10,       'free_currency', -- t12 (Match 0 + 0 Bonus Ball)
-- undefined
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
'free_currency', now(), now(), 1 from core.products p where p.code in ('onlJackpot'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* --------------------------------- */
/* --- onl jackpot hour / minute --- */
/* --------------------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'GC', 25, 'hourly', 
'{500000000, 750000000, 900000000, 1050000000, 1250000000, 500000000, 650000000, 800000000, 1050000000, 1250000000, 550000000, 700000000, 850000000, 1000000000, 1250000000}', 
12500000, 'free_currency', -- t2  (Match 5 + 0 Bonus Ball)
25000,    'free_currency', -- t3  (Match 4 + 1 Bonus Ball)
2500,     'free_currency', -- t4  (Match 4 + 0 Bonus Ball)
2500,     'free_currency', -- t5  (Match 3 + 1 Bonus Ball)
250,      'free_currency', -- t6  (Match 3 + 0 Bonus Ball)
250,      'free_currency', -- t7  (Match 2 + 1 Bonus Ball)
50,       'free_currency', -- t8  (Match 2 + 0 Bonus Ball)
50,       'free_currency', -- t9  (Match 1 + 1 Bonus Ball)
12,       'free_currency', -- t10 (Match 1 + 0 Bonus Ball)
12,       'free_currency', -- t11 (Match 0 + 1 Bonus Ball)
5,        'free_currency', -- t12 (Match 0 + 0 Bonus Ball)
-- undefined
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
'free_currency', now(), now(), 1 from core.products p where p.code in ('onlJackpotHour', 'onlJackpotMinute'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;

/* --------------------------- */
/* --- onl jackpot virtual --- */
/* --------------------------- */
insert into lotto.lotto_payouts(product_id, code, currency, amount, jackpot_derivation, jackpots, t2, t2pc, t3, t3pc, t4, t4pc, t5, t5pc, t6, t6pc, t7, t7pc, t8, t8pc, t9, t9pc, t10, t10pc, t11, t11pc, t12, t12pc, t13, t13pc, t14, t14pc, t15, t15pc, t16, t16pc, t17, t17pc, t18, t18pc, t1pc, created_at, modified_at, version) 
(select p.id, 'latest', 'GC', 25, 'minutely', 
'{500000000, 750000000, 900000000, 1050000000, 1250000000, 500000000, 650000000, 800000000, 1050000000, 1250000000, 550000000, 700000000, 850000000, 1000000000, 1250000000}', 
12500000, 'free_currency', -- t2  (Match 5 + 0 Bonus Ball)
25000,    'free_currency', -- t3  (Match 4 + 1 Bonus Ball)
2500,     'free_currency', -- t4  (Match 4 + 0 Bonus Ball)
2500,     'free_currency', -- t5  (Match 3 + 1 Bonus Ball)
250,      'free_currency', -- t6  (Match 3 + 0 Bonus Ball)
250,      'free_currency', -- t7  (Match 2 + 1 Bonus Ball)
50,       'free_currency', -- t8  (Match 2 + 0 Bonus Ball)
50,       'free_currency', -- t9  (Match 1 + 1 Bonus Ball)
12,       'free_currency', -- t10 (Match 1 + 0 Bonus Ball)
12,       'free_currency', -- t11 (Match 0 + 1 Bonus Ball)
5,        'free_currency', -- t12 (Match 0 + 0 Bonus Ball)
-- undefined
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
0,        'free_currency',
'free_currency', now(), now(), 1 from core.products p where p.code in ('onlJackpotVirtual'))
ON CONFLICT (product_id, code, currency) DO UPDATE SET code = EXCLUDED.code, currency = EXCLUDED.currency, amount = EXCLUDED.amount, jackpot_derivation = EXCLUDED.jackpot_derivation, jackpots = EXCLUDED.jackpots, t1pc  = EXCLUDED.t1pc,
t2 =  EXCLUDED.t2,  t2pc =  EXCLUDED.t2pc, 
t3 =  EXCLUDED.t3,  t3pc =  EXCLUDED.t3pc, 
t4 =  EXCLUDED.t4,  t4pc =  EXCLUDED.t4pc,
t5 =  EXCLUDED.t5,  t5pc =  EXCLUDED.t5pc,
t6 =  EXCLUDED.t6,  t6pc =  EXCLUDED.t6pc,
t7 =  EXCLUDED.t7,  t7pc =  EXCLUDED.t7pc,
t8 =  EXCLUDED.t8,  t8pc =  EXCLUDED.t8pc,
t9 =  EXCLUDED.t9,  t9pc =  EXCLUDED.t9pc,
t10 = EXCLUDED.t10, t10pc = EXCLUDED.t10pc,
t11 = EXCLUDED.t11, t11pc = EXCLUDED.t11pc,
t12 = EXCLUDED.t12, t12pc = EXCLUDED.t12pc,
t13 = EXCLUDED.t13, t13pc = EXCLUDED.t13pc,
t14 = EXCLUDED.t14, t14pc = EXCLUDED.t14pc,
t15 = EXCLUDED.t15, t15pc = EXCLUDED.t15pc,
t16 = EXCLUDED.t16, t16pc = EXCLUDED.t16pc,
t17 = EXCLUDED.t17, t17pc = EXCLUDED.t17pc,
t18 = EXCLUDED.t18, t18pc = EXCLUDED.t18pc,
modified_at = EXCLUDED.modified_at;


/* ------------------------ */
/* --- country policies --- */
/* ------------------------ */
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'global', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AD', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AQ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AX', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'AZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BB', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BD', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BJ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BQ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BV', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'BZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CD', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CV', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CX', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'CZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'DE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'DJ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'DK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'DM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'DO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'DZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'EC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'EE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'EG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'EH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ER', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ES', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ET', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'FI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'FJ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'FK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'FM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'FO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'FR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GB', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GD', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GP', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GQ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'GY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'HK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'HM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'HN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'HR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'HT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'HU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ID', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IQ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'IT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'JE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'JM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'JO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'JP', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KP', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'KZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LB', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LV', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'LY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MD', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ME', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ML', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MP', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MQ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MV', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MX', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'MZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NP', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'NZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'OM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'PY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'QA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'RE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'RO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'RS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'RU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'RW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SB', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SD', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SJ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ST', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SV', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SX', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'SZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TD', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TH', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TJ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TK', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TL', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TO', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TR', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TV', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'TZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'UA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'UG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'UM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'US', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'UY', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'UZ', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'VA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'VC', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'VE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'VG', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'VI', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'VN', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'VU', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'WF', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'WS', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'YE', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'YT', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ZA', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ZM', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;
insert into lotto.country_payout_policies(brand_id, code, created_at, modified_at, version) (select b.id, 'ZW', now(), now(), 1 from core.brands b) ON CONFLICT (brand_id, code) DO NOTHING;

insert into lotto.country_payouts(policy_id, payout_id) (select cpp.id, p.id from lotto.country_payout_policies cpp cross join lotto.lotto_payouts p where p.code = 'latest') ON CONFLICT DO NOTHING;

