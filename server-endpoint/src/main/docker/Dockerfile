FROM bellsoft/liberica-runtime-container:jdk-${maven.compiler.source}-slim-glibc

ENV ENV_PORT=${PORT:-8089}
ENV ENV_YOURKIT_PORT=${YOURKIT_PORT:-10000}
ENV ENV_JGROUPS_PORT=${JGROUPS_PORT:-6600}
ENV ENV_INDEX=${INDEX:-0}
ENV ENV_JAVA_OPTIONS="${jvm.gc.options}"
ENV ENV_JAVA_OPTIONS_COMMON="${jvm.common.options}"
ENV ENV_JMX_OPTIONS="${jvm.jmx.options}"
ENV ENV_HEAPDUMPPATH=${HEAPDUMPPATH:-/logs/dump.hprof}

ADD ${project.artifactId}-${project.version}-exec.jar /workspace/uber.jar

CMD ["/bin/sh", "-c", "java -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${ENV_HEAPDUMPPATH} -Duser.timezone=UTC -Dorg.springframework.boot.logging.LoggingSystem=none -Djgroups.bind_port=$ENV_JGROUPS_PORT -Dcloud.application.host=$HOSTNAME -Dcloud.application.port=$ENV_PORT -Dcloud.application.instance_index=$ENV_INDEX $ENV_JAVA_OPTIONS $ENV_JAVA_OPTIONS_COMMON -jar /workspace/uber.jar"]