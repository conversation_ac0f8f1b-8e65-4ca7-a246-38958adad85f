package com.turbospaces.cfg;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.OverridingMethodsMustInvokeSuper;

import org.apache.commons.io.FileUtils;
import org.slf4j.event.Level;

import com.google.common.base.Suppliers;
import com.google.common.collect.Lists;
import com.google.common.collect.Range;
import com.google.common.net.HttpHeaders;
import com.google.common.primitives.Ints;
import com.google.common.reflect.TypeToken;
import com.netflix.archaius.api.Property;
import com.turbospaces.boot.DevMode;

import lombok.extern.slf4j.Slf4j;
import reactor.core.scheduler.Scheduler;
import reactor.util.retry.Retry;

@Slf4j
public class ApplicationProperties implements TypedPropertyFactory, DevMode {
    private final DynamicPropertyFactory pf;

    public ApplicationProperties(DynamicPropertyFactory pf) {
        this.pf = Objects.requireNonNull(pf);

        //
        // ~ cloud
        //
        CLOUD_APP_ID = pf.get(CloudOptions.CLOUD_APP_ID, String.class).orElse(System.getProperty("user.name"));
        CLOUD_APP_INSTANCE_INDEX = pf.get(CloudOptions.CLOUD_APP_INSTANCE_INDEX, String.class).orElse("0");
        CLOUD_APP_NAME = pf.get(CloudOptions.CLOUD_APP_NAME, String.class);
        CLOUD_APP_SPACE_NAME = pf.get(CloudOptions.CLOUD_APP_SPACE_NAME, String.class);
        CLOUD_APP_HOST = pf.get(CloudOptions.CLOUD_APP_HOST, String.class).orElse("localhost");
        CLOUD_APP_PORT = pf.get(CloudOptions.CLOUD_APP_PORT, int.class).orElse(PRIMARY_PORT);
        CLOUD_APP_SECONDARY_PORT = pf.get(CloudOptions.CLOUD_APP_SECONDARY_PORT, int.class).orElse(SECONDARY_PORT);
        CLOUD_APP_TERTIARY_PORT = pf.get(CloudOptions.CLOUD_APP_TERTIARY_PORT, int.class).orElse(TERTIARY_PORT);

        //
        // ~ APP
        //
        APP_DEV_MODE = pf.get("app.dev.mode", boolean.class).orElse(true);
        APP_SYSTEM_EXIT_DELAY = pf.rangeValue("app.system-exit.delay", Duration.ofSeconds(5), Range.closed(Duration.ofSeconds(1), Duration.ofMinutes(1)));
        APP_BLOCKHOUND_ENABLED = pf.get("app.blockhound.enabled", boolean.class).orElse(false);
        APP_DNS_QUERY = pf.get("app.dns.query", String.class);
        APP_BACKOFF_RETRY_FIRST = pf.rangeValue("app.backoff-retry.first", Duration.ofSeconds(1), Range.closed(Duration.ofSeconds(1), Duration.ofMinutes(1)));
        APP_BACKOFF_RETRY_MAX = pf.rangeValue("app.backoff-retry.max", Duration.ofMinutes(1), Range.closed(Duration.ofSeconds(15), Duration.ofMinutes(15)));
        APP_BACKOFF_RETRY_NUM = pf.get("app.backoff-retry.num", int.class).orElse(10);
        APP_SENTRY_ENABLED = pf.get("app.sentry.enabled", boolean.class).orElse(true);
        APP_USE_SELF_SIGNED_CERTIFICATE = pf.get("app.use.self-signed-certificate", boolean.class).orElse(false);
        APP_LOGGING_RESET_TO = pf.get("app.logging.reset-to", String.class);
        APP_LOGGING_DRY_RUN = pf.get("app.logging.dry-run", boolean.class).orElse(false);
        APP_LOGGING_REPORT_TO_SENTRY = pf.get("app.logging.report-to-sentry", boolean.class).orElse(true);
        APP_METRICS_DRY_RUN = pf.get("app.metrics.dry-run", boolean.class).orElse(false);
        APP_METRICS_ELK_REPORTER_ENABLED = pf.get("app.metrics.elk-reporter.enabled", boolean.class).orElse(false);
        APP_METRICS_INFLUX_REPORTER_ENABLED = pf.get("app.metrics.influx-reporter.enabled", boolean.class).orElse(true);
        APP_ALERTS_DRY_RUN = pf.get("app.alerts.dry-run", boolean.class).orElse(false);
        APP_EXTERNAL_IP = pf.get("app.external.ip", String.class);
        APP_JMX_DOMAIN = pf.get("app.jmx.domain", String.class).orElse("metrics");
        APP_NOTIFY_TOPIC = pf.listOfStrings("app.notify.topic").orElse(List.of("notify"));
        APP_EVENTS_TOPIC = pf.get("app.events.topic", String.class).orElse("events");
        APP_DB_MIGRATION_ENABLED = pf.get("app.db-migration.enabled", boolean.class).orElse(true);
        APP_DB_MIGRATION_CONSISTENCY_CHECK_ENABLED = pf.get("app.db-migration.consistency-check.enabled", boolean.class).orElse(true);
        APP_DB_CDC_ENABLED = pf.get("app.db-cdc.enabled", boolean.class).orElse(false);
        APP_DB_MIGRATION_BASELINE_VERSION = pf.get("app.db-migration.baseline-version", String.class);
        APP_DB_MIGRATION_IGNORE_PATTERNS = pf.listOfStrings("app.db-migration.ignore-patterns").orElse(Lists.newArrayList("*:missing", "*:future"));
        APP_DB_MIGRATION_BASELINE = pf.get("app.db-migration.baseline", boolean.class).orElse(false);
        APP_DB_MIGRATION_PATH = pf.get("app.db-migration.path", String.class).orElse("db/sql-migration");
        APP_PLATFORM_MIN_SIZE = pf.get("app.platform.min-size", int.class).orElse(0);
        APP_PLATFORM_MAX_SIZE = pf.get("app.platform.max-size", int.class).orElse(1024);
        APP_PLATFORM_MAX_IDLE = pf.rangeValue("app.platform.max-idle", Duration.ofSeconds(1), Range.closed(Duration.ofSeconds(1), Duration.ofHours(1)));
        APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT = pf.rangeValue("app.platform.graceful-shutdown-timeout", Duration.ofSeconds(30), Range.closed(Duration.ofSeconds(1), Duration.ofMinutes(1)));
        APP_TIMER_INTERVAL = pf.rangeValue("app.timer.interval", Duration.ofMinutes(1), Range.closed(Duration.ofSeconds(1), Duration.ofMinutes(1)));
        APP_METRICS_REPORT_INTERVAL = pf.get("app.metrics.report-interval", Duration.class).orElse(Duration.ofMinutes(1));
        APP_METRICS_BULK_SIZE = pf.get("app.metrics.bulk-size", int.class).orElse(1024);
        APP_WAIT_FOR_HEALTHCHECKS_INTERVAL = pf.rangeValue("app.wait-for-healthchecks.interval", Duration.ofSeconds(1), Range.closed(Duration.ofSeconds(1), Duration.ofMinutes(1)));
        APP_WAIT_FOR_HEALTHCHECKS_ENABLED = pf.get("app.wait-for-healthchecks.enabled", boolean.class).orElse(true);
        APP_WAIT_FOR_HEALTHCHECKS_TIMEOUT = pf.rangeValue("app.wait-for-healthchecks.timeout", Duration.ofSeconds(60), Range.closed(Duration.ofSeconds(15), Duration.ofMinutes(5)));
        APP_DATA_START_CLEAN = pf.get("app.data.start-clean", boolean.class).orElse(false);
        APP_SHUTDOWN_HOOK_ENABLED = pf.get("app.shutdown-hook.enabled", boolean.class).orElse(true);
        APP_CLEAR_CONFIG_AT_SHUTDOWN_ENABLED = pf.get("app.clear-config-at-shutdown.enabled", boolean.class).orElse(false);
        APP_CFG_DYNAMIC_POLLING_ENABLED = pf.get("app.cfg-dynamic-polling.enabled", boolean.class).orElse(true);
        DYNAMIC_PROPERTY_KEYS = pf.listOfStrings("dynamic-property-keys").orElse(List.of());
        LOGGER_LEVEL_CFG_ABUSE = pf.get("logger.level.cfg-abuse", String.class).orElse("WARN");

        API_RATE_LIMIT_OUTGOING_DEFAULT_PERIOD = pf.get("api.rate.limit.outgoing-default.period", Duration.class).orElse(Duration.ofSeconds(30));
        API_RATE_LIMIT_OUTGOING_DEFAULT_COUNT = pf.get("api.rate.limit.outgoing-default.count", int.class).orElse(3000);
        API_RATE_LIMIT_OUTGOING_DEFAULT_TIMEOUT = pf.get("api.rate.limit.outgoing-default.timeout", Duration.class).orElse(Duration.ofSeconds(2));

        //
        // ~ TCP
        //
        TCP_REUSE_ADDRESS = pf.get("tcp.reuse-address", boolean.class).orElse(true);
        TCP_NO_DELAY = pf.get("tcp.no-delay", boolean.class).orElse(true);
        TCP_KEEP_ALIVE = pf.get("tcp.keep-alive", boolean.class).orElse(true);
        TCP_KEEP_ALIVE_TIMEOUT = pf.rangeValue("tcp.keep-alive.timeout", Duration.ofSeconds(30), Range.closed(Duration.ofSeconds(0), Duration.ofMinutes(15)));
        TCP_CONNECTION_TIMEOUT = pf.rangeValue("tcp.connection.timeout", Duration.ofSeconds(15), Range.closed(Duration.ofSeconds(0), Duration.ofMinutes(1)));
        TCP_CONNECTION_REQUEST_TIMEOUT = pf.rangeValue("tcp.connection-request.timeout", Duration.ofSeconds(5), Range.closed(Duration.ofSeconds(1), Duration.ofSeconds(30)));
        TCP_CONNECTION_VALIDATE_AFTER_INACTIVITY_TIMEOUT = pf.rangeValue("tcp.connection.validate-after-inactivity.timeout", Duration.ofSeconds(0), Range.closed(Duration.ofSeconds(0), Duration.ofMinutes(1)));
        TCP_CONNECTION_EVICT_IDLE_TIMEOUT = pf.rangeValue("tcp.connection.evict-idle.timeout", Duration.ofSeconds(0), Range.closed(Duration.ofSeconds(0), Duration.ofMinutes(1)));
        TCP_CONNECTION_CLOSE_IDLE_IMMEDIATELY = pf.get("tcp.connection.close-idle-immediately", boolean.class).orElse(false);
        TCP_SOCKET_TIMEOUT = pf.rangeValue("tcp.socket.timeout", Duration.ofSeconds(60), Range.closed(Duration.ofSeconds(1), Duration.ofMinutes(1)));
        TCP_SOCKET_BACKLOG = pf.get("tcp.socket.backlog", int.class).orElse(1024);

        //
        // ~ CACHE
        //
        CACHE_ENABLED = pf.get("cache.enabled", boolean.class).orElse(true);
        CACHE_RING_BUFFER_SIZE = pf.get("cache.ring-buffer.size", Integer.class).orElse(256 * 1024);
        CACHE_LOCAL_REPLICATE_BY_INVALIDATION = pf.get("cache.local.replicate-by-invalidation", boolean.class).orElse(false);
        CACHE_REMOTE_LOG_LEVEL = pf.get("cache.remote.log-level", String.class).orElse(Level.TRACE.name());
        CACHE_DEFAULT_MAX_TTL = pf.get("cache.default.max-ttl", Duration.class).orElse(Duration.ofMinutes(30));
        CACHE_DEFAULT_MAX_IDLE = pf.get("cache.default.max-idle", Duration.class).orElse(Duration.ofMinutes(1));
        CACHE_DEFAULT_MAX_SIZE = pf.get("cache.default.max-size", int.class).orElse(1024 * 32);

        //
        // ~ NETTY
        //
        NETTY_ACCEPTOR_POOL_SIZE = pf.get("netty.acceptor-pool.size", int.class).orElse(4);
        NETTY_WORKER_POOL_SIZE = pf.get("netty.worker-pool.size", int.class).orElse(32);
        NETTY_VERSION_SEND_HTTP_HEADER = pf.get("netty.version.send-http-header", boolean.class).orElse(false);
        NETTY_VERSION_HTTP_HEADER_NAME = pf.get("netty.version.http-header-name", String.class).orElse("X-Netty-Version");
        NETTY_USE_NATIVE_TRANSPORT = pf.get("netty.use-native-transport", boolean.class).orElse(true);

        //
        // ~ JETTY
        //
        JETTY_POOL_MIN_SIZE = pf.get("jetty.pool.min-size", int.class).orElse(32);
        JETTY_POOL_MAX_SIZE = pf.get("jetty.pool.max-size", int.class).orElse(64);
        JETTY_POOL_QUEUE_MAX_SIZE = pf.get("jetty.pool.queue-max-size", int.class).orElse(16 * 1024);
        JETTY_POOL_MAX_IDLE = pf.get("jetty.pool.max-idle", Duration.class).orElse(Duration.ofMinutes(1));
        JETTY_HTTP_SEND_DATE_HEADER = pf.get("jetty.http.send-date-header", boolean.class).orElse(true);
        JETTY_GZIP_ENABLED = pf.get("jetty.gzip.enabled", boolean.class).orElse(true);

        //
        // ~ CORS
        //
        // @formatter:off
        CORS_ALLOWED_ORIGINS = pf.listOfStrings("cors.allowed-origins");
        CORS_ALLOWED_HEADERS = pf.listOfStrings("cors.allowed-headers").orElse(Lists.newArrayList("access-control-allow-origin", "content-type", "sentry-trace", "x-user-agent", "x-platform", "x-trace-id", "x-message-id", "x-chk", "x-timestamp", "x-site-verify", "x-site-ot-verify", "x-nonce", "x-cf-chk", "x-payload-var", "x-visitor-info", "x-client-coordinates", "x-client-check"));
        CORS_ALLOWED_METHODS = pf.listOfStrings("cors.allowed-methods").orElse(Lists.newArrayList("GET", "POST", "DELETE", "PUT", "PATCH"));
        CORS_EXPOSED_HEADERS = pf.listOfStrings("cors.exposed-headers").orElse(Lists.newArrayList("X-Reason,X-Status,X-Status-Text,X-Trace-Id,X-Payload-Var"));
        CORS_ALLOW_CREDENTIALS = pf.get("cors.allow-credentials", boolean.class).orElse(true);
        CORS_MAX_AGE = pf.rangeValue("cors.max-age", Duration.ofMinutes(1), Range.closed(Duration.ofSeconds(15), Duration.ofDays(1)));
        CORS_MATCH_ORIGINS_BY_PATTERN = pf.get("cors.match-origins-by-pattern", boolean.class).orElse(false);
        // @formatter:on

        //
        // ~ JDBC
        //
        JDBC_DEFAULT_INSTANCE_REGISTER = pf.get("jdbc.default-instance.register", boolean.class).orElse(false);
        JDBC_PERSIST_BATCH_SIZE = pf.get("jdbc.persist-batch.size", int.class).orElse(100);
        JDBC_QUERY_BATCH_SIZE = pf.get("jdbc.query-batch.size", int.class).orElse(250);
        JDBC_LAZY_BATCH_SIZE = pf.get("jdbc.lazy-batch.size", int.class).orElse(100);
        JDBC_LAZY_SEQUENCE_BATCH_SIZE = pf.get("jdbc.lazy-sequence-batch.size", int.class).orElse(100);
        JDBC_POOL_MIN_SIZE = pf.get("jdbc.pool.min-size", int.class).orElse(1);
        JDBC_POOL_MAX_SIZE = pf.get("jdbc.pool.max-size", int.class).orElse(10);
        JDBC_READ_ONLY_POOL_MIN_SIZE = pf.get("jdbc.read-only-pool.min-size", int.class).orElse(1);
        JDBC_READ_ONLY_POOL_MAX_SIZE = pf.get("jdbc.read-only-pool.max-size", int.class).orElse(5);
        JDBC_CONNECTION_TIMEOUT = pf.rangeValue("jdbc.connection.timeout", Duration.ofSeconds(30), Range.closed(Duration.ofSeconds(15), Duration.ofMinutes(1)));
        JDBC_SOCKET_TIMEOUT = pf.rangeValue("jdbc.socket.timeout", Duration.ofMinutes(30), Range.closed(Duration.ofSeconds(15), Duration.ofHours(1)));
        JDBC_LEAK_DETECTION_TIMEOUT = pf.rangeValue("jdbc.leak-detection.timeout", Duration.ofSeconds(5), Range.closed(Duration.ZERO, Duration.ofMinutes(30)));
        JDBC_SLOW_QUERY_LOGGER_ENABLE = pf.get("jdbc.slow-query-logger.enable", Boolean.class).orElse(true);
        JDBC_SLOW_QUERY_LOGGER_MILLIS = pf.get("jdbc.slow-query-logger.millis", Integer.class).orElse(1000);

        //
        // ~ REDIS
        //
        REDIS_SUBSCRIPTION_POOL_CONNECTION_SIZE = pf.get("redis.subscription-pool-connection.size", Integer.class).orElse(128);
        REDIS_MAX_SUBSCRIPTIONS_PER_CONNECTION = pf.get("redis.max-subscriptions-per-connection", Integer.class).orElse(16);

        //
        // ~ ZK
        //
        ZOOKEEPER_SESSION_TIMEOUT = pf.rangeValue("zookeeper.session.timeout", Duration.ofSeconds(30), Range.closed(Duration.ofSeconds(10), Duration.ofMinutes(1)));

        //
        // ~ KAFKA
        //
        KAFKA_MAX_BLOCK = pf.rangeValue("kafka.max-block", Duration.ofSeconds(30), Range.closed(Duration.ofSeconds(15), Duration.ofMinutes(1)));
        KAFKA_IDEMPOTENCE_ENABLE = pf.get("kafka.idempotence.enable", boolean.class).orElse(true);
        KAFKA_ACKS = pf.get("kafka.acks", String.class).orElse("all");
        KAFKA_COMPRESSION_TYPE = pf.get("kafka.compression.type", String.class).orElse("none");
        KAFKA_RECORD_MAX_REQUEST_SIZE = pf.get("kafka.record.max-request-size", int.class).orElse(Ints.checkedCast(4 * FileUtils.ONE_MB));
        KAFKA_AUTO_OFFSET_RESET = pf.get("kafka.auto.offset.reset", String.class);
        KAFKA_HEARTBEAT_INTERVAL = pf.rangeValue("kafka.heartbeat.interval", Duration.ofSeconds(5),Range.closed(Duration.ofSeconds(1), Duration.ofSeconds(15)));
        KAFKA_SESSION_TIMEOUT = pf.rangeValue("kafka.session.timeout", Duration.ofSeconds(30), Range.closed(Duration.ofSeconds(15), Duration.ofMinutes(1)));
        KAFKA_POLL_MAX_INTERVAL = pf.rangeValue("kafka.poll.max-interval", Duration.ofMinutes(10), Range.closed(Duration.ofSeconds(15), Duration.ofMinutes(30)));
        KAFKA_POLL_MAX_RECORDS = pf.get("kafka.poll.max-records", int.class).orElse(256);
        KAFKA_MIN_WORKERS = pf.get("kafka.min-workers", int.class).orElse(0);
        KAFKA_MAX_WORKERS = pf.get("kafka.max-workers", int.class).orElse(256);
        KAFKA_MAX_REQUESTS_PER_ACTOR = pf.get("kafka.max-requests.per-actor", int.class).orElse(4);
        KAFKA_DO_NOT_QUEUE_PER_READ_ONLY_ACTOR = pf.get("kafka.do-not-queue.per-read-only-actor", boolean.class).orElse(false);
        KAFKA_MAX_POLL_CONCURRENCY = pf.get("kafka.max-poll-concurrency", int.class).orElse(2);
        KAFKA_NACK_ON_QUEUE_FULL = pf.get("kafka.nack.on-queue-full", boolean.class).orElse(false);
        KAFKA_SYSTEM_EXIT_ON_QUEUE_FULL = pf.get("kafka.system-exit.on-queue-full", boolean.class).orElse(true);
        KAFKA_SYSTEM_EXIT_CODE = pf.get("kafka.system-exit.code", int.class).orElse(-1);
        BATCH_COMPLETION_TIMEOUT = pf.rangeValue("batch.completion.timeout", Duration.ofMinutes(2), Range.closed(Duration.ofSeconds(30), Duration.ofMinutes(5)));
        KAFKA_SPRING_BACK_OFF_ENABLED = pf.get("kafka.spring-back-off.enabled", boolean.class).orElse(false);
        KAFKA_NACK_POLL_INTERVAL = pf.get("kafka.nack-poll-interval", Duration.class).orElse(Duration.ofMinutes(1));
        KAFKA_MIGRATION_ENABLED = pf.get("kafka.migration.enabled", boolean.class).orElse(true);
        KAFKA_SINK_DEAD_LETTER_ENABLED = pf.getScoped("kafka.sink-dead-letter.enabled", boolean.class, true);

        KAFKA_BATCH_MAX_POLL = pf.get("kafka.batch.max-poll", Integer.class).orElse(100_000);
        KAFKA_BATCH_PARTITION_FETCH_MAX_BYTES = pf.get("kafka.batch.partition.fetch.max-bytes", Integer.class).orElse(Ints.checkedCast(1 * FileUtils.ONE_MB));
        KAFKA_BATCH_FETCH_MAX_WAIT = pf.get("kafka.batch.fetch.max-wait", Duration.class).orElse(Duration.ofMinutes(1));
        KAFKA_BATCH_FETCH_MIN_BYTES = pf.get("kafka.batch.fetch.min-bytes", Integer.class).orElse(Ints.checkedCast(1 * FileUtils.ONE_MB));

        //
        // ~ TEMPORAL
        //
        TEMPORAL_WORKFLOW_WORKERS_MAX = pf.get("temporal.workflow-workers.max", Integer.class).orElse(512);
        TEMPORAL_ACTIVITY_WORKERS_MAX = pf.get("temporal.activity-workers.max", Integer.class).orElse(1024);
        TEMPORAL_LOCK_ACTIVITY_COMPLETION_FROM_SCHEDULE_TIMEOUT = pf.get("temporal.lock_activity.completion_from_schedule.timeout", Duration.class).orElse(Duration.ofMinutes(6));
        TEMPORAL_LOCK_ACTIVITY_MAX_LOCK_TIMEOUT = pf.get("temporal.lock_activity.max_lock.timeout", Duration.class).orElse(Duration.ofMinutes(2));
        TEMPORAL_ACTIVITY_START_TO_CLOSE_TIMEOUT = pf.get("temporal.activity.start-to-close.timeout", Duration.class).orElse(Duration.ofSeconds(90));

        //
        // ~ GRPC
        //
        GRPC_MIN_WORKERS = pf.get("grpc.min-workers", int.class).orElse(1);
        GRPC_MAX_WORKERS = pf.get("grpc.max-workers", int.class).orElse(1024);
        GRPC_REQUEST_MAX_SIZE = pf.get("grpc.request.max-size", int.class).orElse(Ints.checkedCast(16 * FileUtils.ONE_MB));
        GRPC_METADATA_MAX_SIZE = pf.get("grpc.metadata.max-size", int.class).orElse(Ints.checkedCast(4 * FileUtils.ONE_MB));

        //
        // ~ QUARTZ
        //
        QUARTZ_SCHEDULER_ID = pf.get("quartz.scheduler.id", String.class).orElse("AUTO");
        QUARTZ_WORKER_POOL_COUNT = pf.get("quartz.worker-pool.count", Integer.class).orElse(10);
        QUARTZ_JOBSTORE_TABLE_PREFIX = pf.get("quartz.jobstore.table-prefix", String.class).orElse("qrtz_");
        QUARTZ_JOBSTORE_USE_PROPS = pf.get("quartz.jobstore.use-props", Boolean.class).orElse(false);
        QUARTZ_JOBSTORE_ACQUIRE_TRIGGERS_WITHIN_LOCK = pf.get("quartz.jobstore.acquire-triggers-within-lock", Boolean.class).orElse(false);
        QUARTZ_JOBSTORE_IS_CLUSTERED = pf.get("quartz.jobstore.is-clustered", Boolean.class).orElse(false);
        QUARTZ_CONNECTION_POOL_MIN = pf.get("quartz.connection-pool.min", Integer.class).orElse(1);
        QUARTZ_CONNECTION_POOL_MAX = pf.get("quartz.connection-pool.max", Integer.class).orElse(5);
        QUARTZ_INSTANCE_ID = pf.get("quartz.instance.id", String.class);
        QUARTZ_SHUTDOWN_WAIT_FOR_JOBS_COMPLETION = pf.get("quartz.shutdown.wait-for-jobs-completion", boolean.class);
        QUARTZ_ENFORCE_DISABLED_JOBS_ENABLED = pf.get("quartz.enforce-disabled-jobs.enabled", boolean.class).orElse(true);
        QUARTZ_AUTO_REMOVE_JOBS_WITH_MISSING_CLASSES_ENABLED = pf.get("quartz.auto-remove-jobs-with-missing-classes.enabled", boolean.class).orElse(true);

        IP_RATE_LIMITER_ENABLED = pf.get("ip.rate-limiter.enabled", boolean.class).orElse(true);
        IP_RATE_LIMITER_WHITELIST = pf.get("ip.rate-limiter.whitelist", String.class);
        IP_RATE_LIMITER_PERIOD = pf.get("ip.rate-limiter.period", Duration.class).orElse(Duration.ofMinutes(1));
        IP_RATE_LIMITER_COOLDOWN_PERIOD = pf.get("ip.rate-limiter.cooldown-period", Duration.class).orElse(Duration.ofMinutes(30));
        IP_RATE_LIMITER_COUNT = pf.get("ip.rate-limiter.count", int.class).orElse(15);

        API_RATE_LIMIT_ENABLED = pf.get("api.rate.limit.enabled", boolean.class).orElse(true);
        API_RATE_LIMIT_PERIOD = pf.get("api.rate.limit.period", Duration.class).orElse(Duration.ofMinutes(1));
        API_RATE_LIMIT_COUNT = pf.get("api.rate.limit.count", int.class).orElse(6000);
        API_RATE_LIMIT_TIMEOUT = pf.get("api.rate.limit.timeout", Duration.class).orElse(Duration.ofSeconds(0));
        API_RECAPTCHA_BYPASS_ENABLED = pf.get("api.recaptcha.bypass.enabled", boolean.class).orElse(false);
        API_RECAPTCHA_BYPASS_PARAM = pf.get("api.recaptcha.bypass.param", String.class).orElse("bypass-recaptcha");

        RATE_LIMITER_ENABLED = pf.getScoped(String.format("rate-limiter.%s.enabled", ScopedProperty.SCOPE_PLACEHOLDER), boolean.class, false);
        RATE_LIMITER_PERIOD = pf.getScoped(String.format("rate-limiter.%s.period", ScopedProperty.SCOPE_PLACEHOLDER), Duration.class, Duration.ofMinutes(1));
        RATE_LIMITER_COUNT = pf.getScoped(String.format("rate-limiter.%s.count", ScopedProperty.SCOPE_PLACEHOLDER), Integer.class, 6000);
        RATE_LIMITER_TIMEOUT = pf.getScoped(String.format("rate-limiter.%s.timeout", ScopedProperty.SCOPE_PLACEHOLDER), Duration.class, Duration.ofSeconds(0));

        CONNECTION_PUBLIC_DOMAINS = pf.get("connection.public.domains", String.class);
        CONNECTION_SITE_VERIFICATION_REPORT_TO_SENTRY = pf.get("connection.site-verification.report-to-sentry", boolean.class).orElse(false);
        CONNECTION_SITE_VERIFICATION_HEADER_MAX_LENGTH = pf.get("connection.site-verification-header.max-length", int.class).orElse(4096);
        CONNECTION_SITE_VERIFICATION_UNIQUE_IPS_MAX = pf.get("connection.site-verification.unique-ips-max", int.class).orElse(2);
        CONNECTION_SITE_VERIFICATION_UNIQUE_REQUESTS_PER_PATH_MAX = pf.get("connection.site-verification.unique-requests-per-path-max", int.class).orElse(90);
        CONNECTION_SITE_VERIFICATION_PROPAGATE_IO_EXCEPTIONS = pf.get("connection.site-verification.propagate-io-exceptions", boolean.class).orElse(false);
        CONNECTION_USER_AGENT_WHITELIST = pf.get("connection.user-agent.whitelist", String.class);
        CONNECTION_CLIENT_CERT_SHA256_WHITELIST = pf.get("connection.client-cert.sha256-whitelist", String.class);
        CONNECTION_CHALLENGE_ENABLED = pf.get("connection.challenge.enabled", boolean.class).orElse(true);
        CONNECTION_CHALLENGE_ACCEPT_EMPTY = pf.get("connection.challenge.accept-empty", boolean.class).orElse(true);
        CONNECTION_CHALLENGE_MAX_TIMESTAMP_DIFF = pf.rangeValue("connection.challenge.max-timestamp-diff", Duration.ofMinutes(5), Range.closed(Duration.ofSeconds(30), Duration.ofMinutes(30)));
        CONNECTION_CHALLENGE_MAX_LOCK_WAIT = pf.rangeValue("connection.challenge.max-lock-wait", Duration.ofSeconds(5), Range.closed(Duration.ofSeconds(1), Duration.ofSeconds(30)));

        //
        // ~ HTTP
        //
        HTTP_POOL_MAX_PER_ROUTE = pf.get("http.pool.max-per-route", int.class).orElse(256);
        HTTP_POOL_MAX_SIZE = pf.get("http.pool.max-size", int.class).orElse(512);
        HTTP_ASYNC_IO_THREAD_COUNT = pf.get("http.async.io-thread-count", int.class).orElse(0); // 0 means auto-calculate
        HTTP_METRICS_LATENCY_KEY = pf.get("http.metrics.latency-key", String.class).orElse("server-latency");
        HTTP_RATE_BARRIER_ACCEPTED_KEY = pf.get("http.rate-barrier.accepted-key", String.class).orElse("http.rate-barrier.accepted");
        HTTP_RATE_BARRIER_DEBOUNCED_KEY = pf.get("http.rate-barrier.debounced-key", String.class).orElse("http.rate-barrier.debounced");
        HTTP_RETRY_REQUEST_ENABLED = pf.get("http.retry-request.enabled", boolean.class).orElse(false);
        HTTP_RETRY_REQUEST_COUNT = pf.get("http.retry-request.count", int.class).orElse(3);
        HTTP_RETRY_ALREADY_SENT_REQUEST_ENABLED = pf.get("http.retry-already-sent-request.enabled", boolean.class).orElse(false);
        HTTP_HEADERS_TO_MASK = pf.listOfStrings("http.headers.to-mask").orElse(Lists.newArrayList(HttpHeaders.AUTHORIZATION, "X-Api-Key"));
        HTTP_COOKIES_TO_MASK = pf.listOfStrings("http.cookies.to-mask");
        HTTP_QUERY_PARAMS_TO_MASK = pf.listOfStrings("http.query-params.to-mask").orElse(Lists.newArrayList("password"));
        HTTP_METRICS_INBOUND_PATH_MASK = pf.listOfPatterns("http.metrics.inbound-path-mask");
        HTTP_METRICS_OUTBOUND_PATH_MASK = pf.listOfPatterns("http.metrics.outbound-path-mask");
        HTTP_PROXY = pf.get("http.proxy", String.class);
        HTTP_REQUEST_TO_PROXY_PATTERNS = pf.listOfPatterns("http.request-to-proxy.patterns");
        HTTP_REQUEST_ENCRYPTION_ENABLED = pf.get("http.request-encryption.enabled", boolean.class).orElse(true);
        HTTP_INITIAL_LINE_MAX_SIZE = pf.get("http.initial-line.max-size", int.class).orElse(Ints.checkedCast(4 * FileUtils.ONE_KB));
        HTTP_REQUEST_MAX_SIZE = pf.get("http.request.max-size", int.class).orElse(Ints.checkedCast(256 * FileUtils.ONE_KB));
        HTTP_CHUNK_MAX_SIZE = pf.get("http.chunk.max-size", int.class).orElse(Ints.checkedCast(32 * FileUtils.ONE_KB));
        HTTP_HEADER_MAX_SIZE = pf.get("http.header.max-size", int.class).orElse(Ints.checkedCast(16 * FileUtils.ONE_KB));

        IP_WHITELIST = pf.getScoped(String.format("ip.whitelist.%s", ScopedProperty.SCOPE_PLACEHOLDER), new TypeToken<>() {}, Set.of());
        SITE_VERIFY_ENFORCE_ENABLED = pf.getScoped(String.format("site-verify.enforce.%s.enabled", ScopedProperty.SCOPE_PLACEHOLDER), boolean.class, false);
        MOCK_HTTP_PROXY = pf.get("mock.http.proxy", String.class);
        MOCK_HTTP_REQUEST_TO_PROXY_PATTERNS = pf.listOfPatterns("mock.http.request-to-proxy.patterns");
        SKIP_PAYLOAD_CHECKSUM_CHECK = pf.getScoped("skip-payload-checksum-check", boolean.class, false);

        BROADCAST_TOO_LONG_WARNING_PERIOD = pf.get("broadcast.too-long-warning.period", Duration.class).orElse(Duration.ofSeconds(1));
        BROADCAST_TOO_LONG_WARNING_SILENCE_PERIOD = pf.get("broadcast.too-long-warning.silence-period", Duration.class).orElse(Duration.ofSeconds(30));

        //
        // ~ Spanner
        //
        SPANNER_INSTANCE_NAME = pf.get("spanner.instance.name", String.class);
        SPANNER_DATABASE_NAME = pf.get("spanner.database.name", String.class);
        SPANNER_PROJECT_ID = pf.get("spanner.project.id", String.class);
        SPANNER_DATABASE_PORT = pf.get("spanner.database.port", Integer.class);

        FLYWAY_MIGRATION_RUN_ON_START = pf.get("flyway.migration.run-on-start", boolean.class).orElse(false);
        FLYWAY_MIGRATION_USE_AUTOCONFIG_EMULATOR = pf.get("flyway.migration.use-autoconfig-emulator", boolean.class).orElse(false);

        //
        // ~ PubSub
        //
        PUBSUB_MESSAGE_ORDERING_ENABLED = pf.get("pubsub.message-ordering.enabled", boolean.class).orElse(true);
        PUBSUB_SUBSCRIPTION_EXACTLY_ONCE_DELIVERY_ENABLED = pf.get("pubsub.subscription.exactly-once-delivery.enabled", boolean.class).orElse(false);
        PUBSUB_SUBSCRIPTION_RETAIN_ACKED_MESSAGES = pf.get("pubsub.subscription.retain-acked-messages", boolean.class).orElse(false);
        PUBSUB_NETWORK_TRIP_ALERT_THRESHOLD = pf.get("pubsub.network-trip.alert.threshold", Duration.class).orElse(Duration.ofSeconds(1));
        PUBSUB_SUBSCRIPTION_MESSAGE_RETENTION_DURATION = pf.get("pubsub.subscription.message-retention-duration", Duration.class).orElse(Duration.ofMinutes(30));
        PUBSUB_DEADLETTER_DELIVERY_ATTEMPT_MAX = pf.get("pubsub.deadletter.delivery-attempt-max", int.class).orElse(5);
        PUBSUB_SUBSCRIPTION_RETRY_BACKOFF_MIN = pf.rangeValue("pubsub.subscription.retry-backoff-min", Duration.ofSeconds(10), Range.closed(Duration.ofSeconds(0), Duration.ofSeconds(600)));
        PUBSUB_SUBSCRIPTION_RETRY_BACKOFF_MAX = pf.rangeValue("pubsub.subscription.retry-backoff-max", Duration.ofSeconds(600), Range.closed(Duration.ofSeconds(0), Duration.ofSeconds(600)));

        //
        // ~ Sentry
        //
        SENTRY_REQUEST_REPLY_TIMEOUT_REPORT = pf.get("sentry.request-reply-timeout.report", boolean.class).orElse(true);
        SENTRY_ALERTS_LOG_MESSAGES_TO_IGNORE = pf.getProperty("sentry.alerts.log.messages.to.ignore").asType(s -> splitString(s, "~"), ""); // USED PROD
        SENTRY_ALERTS_LOGGERS_TO_IGNORE = pf.getProperty("sentry.alerts.loggers.to.ignore").asType(s -> splitString(s, "~"), ""); // USED PROD

        //
        // ~ Logger
        //
        LOGGER_LEVEL_DEFAULT_RESPONSE_WRAPPER_FACADE = pf.get("logger.level.default-response-wrapper-facade", String.class).orElse("TRACE");

        //
        // ~ Browser cache
        //
        BROWSER_CACHE_SHORT_DURATION = pf.rangeValue("browser.cache.short-duration", Duration.ofMinutes(1), Range.closed(Duration.ofSeconds(30), Duration.ofMinutes(30)));
        BROWSER_CACHE_DEFAULT_DURATION = pf.rangeValue("browser.cache.default-duration", Duration.ofMinutes(30), Range.closed(Duration.ofMinutes(1), Duration.ofDays(1)));

        //
        // ~ geo location
        //
        GEO_LOCATION_TIMEOUT = pf.rangeValue("geo-location.timeout", Duration.ofMillis(100), Range.closed(Duration.ofMillis(1), Duration.ofSeconds(1)));

        //
        // ~ presentation config
        //
        BRAND_GROUP = pf.get("brand.group", String.class).orElse("");

        //
        // ~ Click House
        //
        CLICKHOUSE_SOCKET_TIMEOUT = pf.rangeValue("clickhouse.socket.timeout", Duration.ofMinutes(30), Range.closed(Duration.ofMinutes(1), Duration.ofHours(1)));
        CLICKHOUSE_OPTIMIZE_THROW_IF_NOOP = pf.get("clickhouse.optimize.throw-if-noop", int.class).orElse(1);
        CLICKHOUSE_OPTIMIZE_ALTER_SYNC = pf.get("clickhouse.optimize.alter-sync", int.class).orElse(0);
        CLICKHOUSE_OPTIMIZE_MUTATION_SYNC = pf.get("clickhouse.optimize.mutation-sync", int.class).orElse(0);

        LOCUST_WORKERS = pf.get("locust.workers", int.class).orElse(64);
        LOCUST_RATE_LIMITER = pf.get("locust.rate.limiter", int.class).orElse(512);
    }
    @Override
    public List<FragmentProperties> fragments() {
        return List.of();
    }
    @Override
    public boolean isDevMode() {
        return APP_DEV_MODE.get();
    }
    @Override
    public DynamicPropertyFactory factory() {
        return pf;
    }
    @Override
    public ApplicationConfig cfg() {
        return (ApplicationConfig) TypedPropertyFactory.super.cfg();
    }
    @Override
    public String externalIp() {
        Property<String> prop = APP_EXTERNAL_IP.orElse(ipSupplier.get());
        return prop.get();
    }
    @Override
    public Retry retry(Scheduler scheduler) {
        int numRetries = APP_BACKOFF_RETRY_NUM.get();
        Duration firstBackoff = APP_BACKOFF_RETRY_FIRST.get();
        Duration maxBackoff = APP_BACKOFF_RETRY_MAX.get();
        return Retry.backoff(numRetries, firstBackoff).maxBackoff(maxBackoff).scheduler(scheduler);
    }
    protected List<Pattern> parsePatterns(String patterns) {
        try {
            return patterns.isEmpty()
                    ? Collections.emptyList()
                    : Arrays.stream(patterns.split(",")).map(Pattern::compile).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Error on patter init {}", patterns, e);
            return Collections.emptyList();
        }
    }
    protected List<String> splitString(String value, String separator) {
        try {
            return value.trim().isEmpty() ? Collections.emptyList() : Arrays.asList(value.split(separator));
        } catch (Exception e) {
            log.error("Error on string list parse {}", value);
            return Collections.emptyList();
        }
    }

    //
    // ~ cloud
    //
    public final Property<String> CLOUD_APP_ID;
    public final Property<String> CLOUD_APP_SPACE_NAME;
    public final Property<String> CLOUD_APP_NAME;
    public final Property<String> CLOUD_APP_INSTANCE_INDEX;
    public final Property<String> CLOUD_APP_HOST;
    public final Property<Integer> CLOUD_APP_PORT;
    public final Property<Integer> CLOUD_APP_SECONDARY_PORT;
    public final Property<Integer> CLOUD_APP_TERTIARY_PORT;

    //
    // ~ APP
    //
    public final Property<Boolean> APP_DEV_MODE;
    public final Property<Boolean> APP_BLOCKHOUND_ENABLED;
    public final Property<Duration> APP_SYSTEM_EXIT_DELAY;
    public final Property<String> APP_DNS_QUERY;
    public final Property<Duration> APP_BACKOFF_RETRY_FIRST;
    public final Property<Duration> APP_BACKOFF_RETRY_MAX;
    public final Property<Integer> APP_BACKOFF_RETRY_NUM;
    public final Property<Boolean> APP_SENTRY_ENABLED;
    public final Property<Boolean> APP_USE_SELF_SIGNED_CERTIFICATE;
    public final Property<String> APP_LOGGING_RESET_TO;
    public final Property<Boolean> APP_LOGGING_DRY_RUN;
    public final Property<Boolean> APP_LOGGING_REPORT_TO_SENTRY;
    public final Property<Boolean> APP_ALERTS_DRY_RUN;
    public final Property<Boolean> APP_METRICS_DRY_RUN;
    public final Property<Boolean> APP_METRICS_ELK_REPORTER_ENABLED;
    public final Property<Boolean> APP_METRICS_INFLUX_REPORTER_ENABLED;
    public final Property<String> APP_EXTERNAL_IP;
    public final Property<String> APP_JMX_DOMAIN;
    public final Property<List<String>> APP_NOTIFY_TOPIC;
    public final Property<String> APP_EVENTS_TOPIC;
    public final Property<Boolean> APP_DB_MIGRATION_ENABLED;
    public final Property<Boolean> APP_DB_MIGRATION_CONSISTENCY_CHECK_ENABLED;
    public final Property<Boolean> APP_DB_CDC_ENABLED;
    public final Property<String> APP_DB_MIGRATION_BASELINE_VERSION;
    public final Property<List<String>> APP_DB_MIGRATION_IGNORE_PATTERNS;
    public final Property<Boolean> APP_DB_MIGRATION_BASELINE;
    public final Property<String> APP_DB_MIGRATION_PATH;
    public final Property<Integer> APP_PLATFORM_MIN_SIZE;
    public final Property<Integer> APP_PLATFORM_MAX_SIZE;
    public final Property<Duration> APP_PLATFORM_MAX_IDLE;
    public Property<Duration> APP_PLATFORM_GRACEFUL_SHUTDOWN_TIMEOUT;
    public final Property<Duration> APP_TIMER_INTERVAL;
    public final Property<Integer> APP_METRICS_BULK_SIZE;
    public final Property<Duration> APP_METRICS_REPORT_INTERVAL;
    public final Property<Boolean> APP_DATA_START_CLEAN;
    public final Property<Duration> APP_WAIT_FOR_HEALTHCHECKS_INTERVAL;
    public final Property<Boolean> APP_WAIT_FOR_HEALTHCHECKS_ENABLED;
    public final Property<Duration> APP_WAIT_FOR_HEALTHCHECKS_TIMEOUT;
    public final Property<Boolean> APP_SHUTDOWN_HOOK_ENABLED;
    public final Property<Boolean> APP_CLEAR_CONFIG_AT_SHUTDOWN_ENABLED;
    public final Property<Boolean> APP_CFG_DYNAMIC_POLLING_ENABLED;
    public final Property<List<String>> DYNAMIC_PROPERTY_KEYS;
    public final Property<String> LOGGER_LEVEL_CFG_ABUSE;
    public final Property<Duration> API_RATE_LIMIT_OUTGOING_DEFAULT_PERIOD;
    public final Property<Integer> API_RATE_LIMIT_OUTGOING_DEFAULT_COUNT;
    public final Property<Duration> API_RATE_LIMIT_OUTGOING_DEFAULT_TIMEOUT;

    //
    // ~ TCP
    //
    public final Property<Boolean> TCP_REUSE_ADDRESS;
    public final Property<Boolean> TCP_NO_DELAY;
    public final Property<Boolean> TCP_KEEP_ALIVE;
    public final Property<Duration> TCP_KEEP_ALIVE_TIMEOUT;
    public final Property<Duration> TCP_CONNECTION_TIMEOUT;
    public final Property<Duration> TCP_CONNECTION_REQUEST_TIMEOUT;
    public final Property<Duration> TCP_CONNECTION_VALIDATE_AFTER_INACTIVITY_TIMEOUT;
    public final Property<Duration> TCP_CONNECTION_EVICT_IDLE_TIMEOUT;
    public final Property<Boolean> TCP_CONNECTION_CLOSE_IDLE_IMMEDIATELY;
    public final Property<Duration> TCP_SOCKET_TIMEOUT;
    public final Property<Integer> TCP_SOCKET_BACKLOG;

    //
    // ~ CACHE
    //
    public final Property<Boolean> CACHE_ENABLED;
    public final Property<Duration> CACHE_DEFAULT_MAX_TTL;
    public final Property<Duration> CACHE_DEFAULT_MAX_IDLE;
    public final Property<Integer> CACHE_DEFAULT_MAX_SIZE;
    public final Property<Integer> CACHE_RING_BUFFER_SIZE;
    public final Property<Boolean> CACHE_LOCAL_REPLICATE_BY_INVALIDATION;
    public final Property<String> CACHE_REMOTE_LOG_LEVEL;

    //
    // ~ NETTY
    //
    public final Property<Integer> NETTY_ACCEPTOR_POOL_SIZE;
    public final Property<Integer> NETTY_WORKER_POOL_SIZE;
    public final Property<Boolean> NETTY_VERSION_SEND_HTTP_HEADER;
    public final Property<String> NETTY_VERSION_HTTP_HEADER_NAME;
    public final Property<Boolean> NETTY_USE_NATIVE_TRANSPORT;

    //
    // ~ JETTY
    //
    public final Property<Integer> JETTY_POOL_MAX_SIZE;
    public final Property<Integer> JETTY_POOL_MIN_SIZE;
    public final Property<Integer> JETTY_POOL_QUEUE_MAX_SIZE;
    public final Property<Duration> JETTY_POOL_MAX_IDLE;
    public final Property<Boolean> JETTY_HTTP_SEND_DATE_HEADER;
    public final Property<Boolean> JETTY_GZIP_ENABLED;

    //
    // ~ CORS
    //
    public final Property<List<String>> CORS_ALLOWED_ORIGINS;
    public final Property<List<String>> CORS_ALLOWED_HEADERS;
    public final Property<List<String>> CORS_ALLOWED_METHODS;
    public final Property<List<String>> CORS_EXPOSED_HEADERS;
    public final Property<Boolean> CORS_ALLOW_CREDENTIALS;
    public final Property<Duration> CORS_MAX_AGE;
    public final Property<Boolean> CORS_MATCH_ORIGINS_BY_PATTERN;

    //
    // ~ JDBC
    //
    public final Property<Boolean> JDBC_DEFAULT_INSTANCE_REGISTER;
    public final Property<Integer> JDBC_PERSIST_BATCH_SIZE;
    public final Property<Integer> JDBC_QUERY_BATCH_SIZE;
    public final Property<Integer> JDBC_LAZY_BATCH_SIZE;
    public final Property<Integer> JDBC_LAZY_SEQUENCE_BATCH_SIZE;
    public final Property<Integer> JDBC_POOL_MIN_SIZE;
    public final Property<Integer> JDBC_POOL_MAX_SIZE;
    public final Property<Integer> JDBC_READ_ONLY_POOL_MIN_SIZE;
    public final Property<Integer> JDBC_READ_ONLY_POOL_MAX_SIZE;
    public final Property<Duration> JDBC_CONNECTION_TIMEOUT;
    public final Property<Duration> JDBC_SOCKET_TIMEOUT;
    public final Property<Duration> JDBC_LEAK_DETECTION_TIMEOUT;
    public final Property<Boolean> JDBC_SLOW_QUERY_LOGGER_ENABLE;
    public final Property<Integer> JDBC_SLOW_QUERY_LOGGER_MILLIS;

    //
    // ~ REDIS
    //
    public final Property<Integer> REDIS_SUBSCRIPTION_POOL_CONNECTION_SIZE;
    public final Property<Integer> REDIS_MAX_SUBSCRIPTIONS_PER_CONNECTION;

    //
    // ~ ZK
    //
    public final Property<Duration> ZOOKEEPER_SESSION_TIMEOUT;

    //
    // ~ KAFKA
    //
    public final Property<Duration> KAFKA_MAX_BLOCK;
    public final Property<Boolean> KAFKA_IDEMPOTENCE_ENABLE;
    public final Property<String> KAFKA_ACKS;
    public final Property<String> KAFKA_COMPRESSION_TYPE;
    public final Property<Integer> KAFKA_RECORD_MAX_REQUEST_SIZE;
    public final Property<String> KAFKA_AUTO_OFFSET_RESET;
    public final Property<Duration> KAFKA_HEARTBEAT_INTERVAL;
    public final Property<Duration> KAFKA_SESSION_TIMEOUT;
    public final Property<Duration> KAFKA_POLL_MAX_INTERVAL;
    public final Property<Integer> KAFKA_POLL_MAX_RECORDS;
    public final Property<Integer> KAFKA_MIN_WORKERS;
    public final Property<Integer> KAFKA_MAX_WORKERS;
    public final Property<Integer> KAFKA_MAX_REQUESTS_PER_ACTOR;
    public final Property<Boolean> KAFKA_DO_NOT_QUEUE_PER_READ_ONLY_ACTOR;
    public final Property<Integer> KAFKA_MAX_POLL_CONCURRENCY;
    public final Property<Boolean> KAFKA_SYSTEM_EXIT_ON_QUEUE_FULL;
    public final Property<Boolean> KAFKA_NACK_ON_QUEUE_FULL;
    public final Property<Integer> KAFKA_SYSTEM_EXIT_CODE;
    public final Property<Duration> BATCH_COMPLETION_TIMEOUT;
    public final Property<Boolean> KAFKA_SPRING_BACK_OFF_ENABLED;
    public final Property<Duration> KAFKA_NACK_POLL_INTERVAL;
    public final Property<Boolean> KAFKA_MIGRATION_ENABLED;
    public final ScopedProperty<Boolean> KAFKA_SINK_DEAD_LETTER_ENABLED;

    public final Property<Integer> KAFKA_BATCH_MAX_POLL;
    public final Property<Integer> KAFKA_BATCH_PARTITION_FETCH_MAX_BYTES;
    public final Property<Duration> KAFKA_BATCH_FETCH_MAX_WAIT;
    public final Property<Integer> KAFKA_BATCH_FETCH_MIN_BYTES;

    public final Property<Integer> TEMPORAL_WORKFLOW_WORKERS_MAX;
    public final Property<Integer> TEMPORAL_ACTIVITY_WORKERS_MAX;
    public final Property<Duration> TEMPORAL_LOCK_ACTIVITY_COMPLETION_FROM_SCHEDULE_TIMEOUT;
    public final Property<Duration> TEMPORAL_LOCK_ACTIVITY_MAX_LOCK_TIMEOUT;
    public final Property<Duration> TEMPORAL_ACTIVITY_START_TO_CLOSE_TIMEOUT;

    //
    // ~ GRPC
    //
    public final Property<Integer> GRPC_MIN_WORKERS;
    public final Property<Integer> GRPC_MAX_WORKERS;
    public final Property<Integer> GRPC_REQUEST_MAX_SIZE;
    public final Property<Integer> GRPC_METADATA_MAX_SIZE;

    //
    // ~ QUARTZ
    //
    public final Property<String> QUARTZ_SCHEDULER_ID;
    public final Property<Integer> QUARTZ_WORKER_POOL_COUNT;
    public final Property<String> QUARTZ_JOBSTORE_TABLE_PREFIX;
    public final Property<Boolean> QUARTZ_JOBSTORE_USE_PROPS;
    public final Property<Boolean> QUARTZ_JOBSTORE_ACQUIRE_TRIGGERS_WITHIN_LOCK;
    public final Property<Integer> QUARTZ_CONNECTION_POOL_MIN;
    public final Property<Integer> QUARTZ_CONNECTION_POOL_MAX;
    public final Property<Boolean> QUARTZ_JOBSTORE_IS_CLUSTERED;
    public final Property<String> QUARTZ_INSTANCE_ID;
    public final Property<Boolean> QUARTZ_SHUTDOWN_WAIT_FOR_JOBS_COMPLETION;
    public final Property<Boolean> QUARTZ_ENFORCE_DISABLED_JOBS_ENABLED;
    public final Property<Boolean> QUARTZ_AUTO_REMOVE_JOBS_WITH_MISSING_CLASSES_ENABLED;

    //
    // ~ HTTP
    //
    public final Property<List<Pattern>> HTTP_METRICS_INBOUND_PATH_MASK;
    public final Property<List<Pattern>> HTTP_METRICS_OUTBOUND_PATH_MASK;
    public final Property<String> HTTP_METRICS_LATENCY_KEY;
    public final Property<String> HTTP_RATE_BARRIER_ACCEPTED_KEY;
    public final Property<String> HTTP_RATE_BARRIER_DEBOUNCED_KEY;
    public final Property<Boolean> HTTP_RETRY_REQUEST_ENABLED;
    public final Property<Integer> HTTP_RETRY_REQUEST_COUNT;
    public final Property<Boolean> HTTP_RETRY_ALREADY_SENT_REQUEST_ENABLED;
    public final Property<String> HTTP_PROXY;
    public final Property<Integer> HTTP_POOL_MAX_PER_ROUTE;
    public final Property<List<Pattern>> HTTP_REQUEST_TO_PROXY_PATTERNS;
    public final Property<Boolean> HTTP_REQUEST_ENCRYPTION_ENABLED;
    public final Property<String> MOCK_HTTP_PROXY;
    public final Property<List<Pattern>> MOCK_HTTP_REQUEST_TO_PROXY_PATTERNS;
    public final Property<List<String>> HTTP_HEADERS_TO_MASK;
    public final Property<List<String>> HTTP_QUERY_PARAMS_TO_MASK;
    public final Property<List<String>> HTTP_COOKIES_TO_MASK;
    public final Property<Integer> HTTP_REQUEST_MAX_SIZE;
    public final Property<Integer> HTTP_INITIAL_LINE_MAX_SIZE;
    public final Property<Integer> HTTP_HEADER_MAX_SIZE;
    public final Property<Integer> HTTP_CHUNK_MAX_SIZE;
    public final Property<Integer> HTTP_POOL_MAX_SIZE;
    public final Property<Integer> HTTP_ASYNC_IO_THREAD_COUNT;
    public final ScopedProperty<Boolean> SKIP_PAYLOAD_CHECKSUM_CHECK;
    public final ScopedProperty<Set<String>> IP_WHITELIST;
    public final ScopedProperty<Boolean> SITE_VERIFY_ENFORCE_ENABLED;
    public final Property<Duration> BROADCAST_TOO_LONG_WARNING_PERIOD;
    public final Property<Duration> BROADCAST_TOO_LONG_WARNING_SILENCE_PERIOD;

    //
    // ~ Spanner
    //
    public final Property<String> SPANNER_INSTANCE_NAME;
    public final Property<String> SPANNER_DATABASE_NAME;
    public final Property<String> SPANNER_PROJECT_ID;
    public final Property<Integer> SPANNER_DATABASE_PORT;
    public final Property<Boolean> FLYWAY_MIGRATION_RUN_ON_START;
    public final Property<Boolean> FLYWAY_MIGRATION_USE_AUTOCONFIG_EMULATOR;

    //
    // ~ PubSub
    //
    public final Property<Boolean> PUBSUB_MESSAGE_ORDERING_ENABLED;
    public final Property<Boolean> PUBSUB_SUBSCRIPTION_RETAIN_ACKED_MESSAGES;
    public final Property<Duration> PUBSUB_NETWORK_TRIP_ALERT_THRESHOLD;
    public final Property<Boolean> PUBSUB_SUBSCRIPTION_EXACTLY_ONCE_DELIVERY_ENABLED;
    public final Property<Duration> PUBSUB_SUBSCRIPTION_MESSAGE_RETENTION_DURATION;
    public final Property<Duration> PUBSUB_SUBSCRIPTION_RETRY_BACKOFF_MIN;
    public final Property<Duration> PUBSUB_SUBSCRIPTION_RETRY_BACKOFF_MAX;
    public final Property<Integer> PUBSUB_DEADLETTER_DELIVERY_ATTEMPT_MAX;

    public final Property<Boolean> IP_RATE_LIMITER_ENABLED;
    public final Property<Duration> IP_RATE_LIMITER_PERIOD;
    public final Property<Duration> IP_RATE_LIMITER_COOLDOWN_PERIOD;
    public final Property<String> IP_RATE_LIMITER_WHITELIST;
    public final Property<Integer> IP_RATE_LIMITER_COUNT;

    public final Property<Boolean> API_RATE_LIMIT_ENABLED;
    public final Property<Duration> API_RATE_LIMIT_PERIOD;
    public final Property<Integer> API_RATE_LIMIT_COUNT;
    public final Property<Duration> API_RATE_LIMIT_TIMEOUT;
    public final Property<Boolean> API_RECAPTCHA_BYPASS_ENABLED;
    public final Property<String> API_RECAPTCHA_BYPASS_PARAM;

    public final ScopedProperty<Boolean> RATE_LIMITER_ENABLED;
    public final ScopedProperty<Duration> RATE_LIMITER_PERIOD;
    public final ScopedProperty<Integer> RATE_LIMITER_COUNT;
    public final ScopedProperty<Duration> RATE_LIMITER_TIMEOUT;

    public final Property<String> CONNECTION_PUBLIC_DOMAINS;
    public final Property<String> CONNECTION_USER_AGENT_WHITELIST;
    public final Property<String> CONNECTION_CLIENT_CERT_SHA256_WHITELIST;
    public final Property<Boolean> CONNECTION_SITE_VERIFICATION_REPORT_TO_SENTRY;
    public final Property<Integer> CONNECTION_SITE_VERIFICATION_HEADER_MAX_LENGTH;
    public final Property<Integer> CONNECTION_SITE_VERIFICATION_UNIQUE_IPS_MAX;
    public final Property<Integer> CONNECTION_SITE_VERIFICATION_UNIQUE_REQUESTS_PER_PATH_MAX;
    public final Property<Boolean> CONNECTION_SITE_VERIFICATION_PROPAGATE_IO_EXCEPTIONS;
    public final Property<Boolean> CONNECTION_CHALLENGE_ENABLED;
    public final Property<Boolean> CONNECTION_CHALLENGE_ACCEPT_EMPTY;
    public final Property<Duration> CONNECTION_CHALLENGE_MAX_TIMESTAMP_DIFF;
    public final Property<Duration> CONNECTION_CHALLENGE_MAX_LOCK_WAIT;

    //
    // ~ Browser
    //
    public final Property<Duration> BROWSER_CACHE_SHORT_DURATION;
    public final Property<Duration> BROWSER_CACHE_DEFAULT_DURATION;

    //
    // ~ Sentry
    //
    public final Property<Boolean> SENTRY_REQUEST_REPLY_TIMEOUT_REPORT;
    public final Property<List<String>> SENTRY_ALERTS_LOG_MESSAGES_TO_IGNORE;
    public final Property<List<String>> SENTRY_ALERTS_LOGGERS_TO_IGNORE;

    //
    // ~ Logger
    //
    public final Property<String> LOGGER_LEVEL_DEFAULT_RESPONSE_WRAPPER_FACADE;

    //
    // ~ geo location
    //
    public final Property<Duration> GEO_LOCATION_TIMEOUT;

    //
    // ~ presentation config
    //
    public final Property<String> BRAND_GROUP;


    public final Property<Duration> CLICKHOUSE_SOCKET_TIMEOUT;
    public final Property<Integer> CLICKHOUSE_OPTIMIZE_THROW_IF_NOOP;
    public final Property<Integer> CLICKHOUSE_OPTIMIZE_ALTER_SYNC;
    public final Property<Integer> CLICKHOUSE_OPTIMIZE_MUTATION_SYNC;

    public final Property<Integer> LOCUST_WORKERS;
    public final Property<Integer> LOCUST_RATE_LIMITER;

    protected final com.google.common.base.Supplier<String> ipSupplier = Suppliers.memoize(new com.google.common.base.Supplier<>() {
        @Override
        public String get() {
            try {
                URI uri = new URI("https://checkip.amazonaws.com");
                HttpURLConnection con = (HttpURLConnection) uri.toURL().openConnection();
                con.setRequestMethod("GET");
                con.setConnectTimeout((int) TCP_CONNECTION_TIMEOUT.get().toMillis());
                con.setReadTimeout((int) TCP_SOCKET_TIMEOUT.get().toMillis());

                StringBuilder b = new StringBuilder();
                try (InputStream io = con.getInputStream()) {
                    try (BufferedReader br = new BufferedReader(new InputStreamReader(io))) {
                        String output;
                        while ((output = br.readLine()) != null) {
                            b.append(output);
                        }
                    }
                }

                return b.toString();
            } catch (Exception err) {
                try {
                    return InetAddress.getLocalHost().getHostAddress();
                } catch (UnknownHostException nested) {
                    throw new UndeclaredThrowableException(nested);
                }
            }
        }
    });

    @OverridingMethodsMustInvokeSuper
    public List<String> undeclaredDynamicProperties() {
        return List.of();
    }
}
