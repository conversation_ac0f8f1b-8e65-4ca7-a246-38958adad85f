package admin.validators;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.api.DisplayName;

class PriorityLevelValidatorTest {

    private PriorityLevelValidator validator = new PriorityLevelValidator();

    @ParameterizedTest
    @CsvSource({
            "null, true",
            "1, true",
            "200, true",
            "999, false"
    })
    @DisplayName("Validates priority levels correctly")
    void isValid(String value, boolean expected) {
        Integer intValue = "null".equals(value) ? null : Integer.valueOf(value);
        assertEquals(expected, validator.isValid(intValue, null));
    }
}