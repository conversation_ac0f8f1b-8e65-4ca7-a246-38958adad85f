package admin.models;


import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CustomCreatorTag {
    CREATOR("creator"), CREATOR_DUMMY_FUNDS("creator_dummy_funds");

    private final String nameValue;

    public static boolean isValidTagName(String input) {
        return Arrays.stream(values())
                .anyMatch(tag -> tag.getNameValue().equals(input));
    }
}
