package admin.models;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class Creator {
    @NotNull
    private String code;
    @NotNull
    private String displayName;
    @NotNull
    private String handle;
    private String description;
    private String affiliateId;
    private Double rate;
    private Double flatFee;
    private Integer dealId;
    private boolean active;
    private boolean visible;
    private String avatarImageUrl;
    private String bannerImageUrl;
    private String landingUrl;
    private Integer levelPriority;
    private Boolean creatorTag;
    private Boolean creatorDummyFundsTag;
    private Boolean chatEnabled;
    private String chatGroupCode;
    private Boolean streamingAllowed;
}
