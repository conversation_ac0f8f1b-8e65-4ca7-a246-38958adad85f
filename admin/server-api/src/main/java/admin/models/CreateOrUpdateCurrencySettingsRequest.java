package admin.models;

import java.util.ArrayList;
import java.util.List;

import admin.api.AdminRequest;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreateOrUpdateCurrencySettingsRequest implements AdminRequest {

    @NotNull
    private String currency;

    @NotNull
    @Min(value = 0L, message = "The conversionRate value must be positive")
    private Double conversionRate;

    private boolean redeemable;

    private List<Double> allowedWagers = new ArrayList<>();
}
