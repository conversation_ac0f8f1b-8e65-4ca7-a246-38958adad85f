package admin.models;

import admin.api.AdminRequest;
import admin.validators.ValidPriorityLevel;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class RegisterCreatorRequest implements AdminRequest {
    @NotNull
    private Long accountId;

    @NotEmpty(message = "Display name cannot be empty.")
    @Size(min = 3, max = 32, message = "Display name must be between 3 and 32 characters long.")
    private String displayName;

    @NotEmpty(message = "Handle cannot be empty.")
    @Size(min = 4, max = 16, message = "Handle must be between 4 and 16 characters long.")
    @Pattern(regexp = "^@[a-zA-Z0-9_-]+$", message = "Handle must start with '@' and can contain only alphanumeric characters, underscores, and dashes.")
    private String handle;

    @Size(max = 300, message = "Up to 300 characters is allowed for description field")
    private String description;

    private boolean connectStreams;

    private String affiliateId;

    @Min(value = 0L, message = "The rate value must be positive")
    @Max(value = 100L, message = "The rate value must <= 100")
    private Double rate;

    @Min(value = 0L, message = "The flatFee value must be positive")
    private Double flatFee;

    @Min(value = 0L, message = "The dealId value must be positive")
    private Integer dealId;

    private boolean active;

    private boolean visible;

    @Size(max = 128, message = "Up to 128 characters is allowed for landing url.")
    @Pattern(regexp = "^$|^(/[a-zA-Z0-9_@-]+)+$", message = "If provided, LandingUrl must start with '/' and contain " +
            "only alphanumeric characters, dashes, symbol '@' or underscores in each segment. Empty or missing values will be " +
            "treated as not provided.")
    private String landingUrl;

    @ValidPriorityLevel
    private Integer levelPriority;

    private Boolean creatorTag;
    private Boolean creatorDummyFundsTag;
    private Boolean streamingAllowed;
    private Boolean chatEnabled;

}
