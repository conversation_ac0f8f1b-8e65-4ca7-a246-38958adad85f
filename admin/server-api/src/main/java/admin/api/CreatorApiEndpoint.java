package admin.api;

import org.jboss.resteasy.spi.HttpRequest;

import admin.models.CreateOrUpdateAllowedProductRequest;
import admin.models.CreateOrUpdateCurrencySettingsRequest;
import admin.models.GetAccountInfoRequest;
import admin.models.GetAccountInfoResponse;
import admin.models.GetAllowedProductsRequest;
import admin.models.GetAllowedProductsResponse;
import admin.models.GetBackofficeEventsRequest;
import admin.models.GetBackofficeEventsResponse;
import admin.models.GetCreatorLevelsResponse;
import admin.models.GetCurrencySettingsRequest;
import admin.models.GetCurrencySettingsResponse;
import admin.models.RegisterCreatorRequest;
import admin.models.ResetImagesRequest;
import admin.models.SetLiveSteamStatusRequest;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;

@Path("v1/creator")
@Tag(name = "Creator", description = "Admin - Creator API Endpoints")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityScheme(type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, name = "ApiKeyAuth", paramName = HttpHeaders.AUTHORIZATION)
@SecurityRequirement(name = "ApiKeyAuth")
@ApiResponses({
        @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = AdminOkResponse.class))),
        @ApiResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = AdminErrorResponse.class)))
})
public interface CreatorApiEndpoint {
    @POST
    @Path("/RegisterCreator")
    void registerCreator(@Suspended AsyncResponse async,
                         @Context HttpRequest httpReq,
                         @BeanParam AdminHeader header,
                         @NotNull @Valid RegisterCreatorRequest request);


    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetAccountInfoResponse.class)))
    @POST
    @Path("/GetAccountInfo")
    void getAccountInfo(@Suspended AsyncResponse async,
                        @Context HttpRequest httpReq,
                        @BeanParam AdminHeader header,
                        @NotNull @Valid GetAccountInfoRequest request);

    @POST
    @Path("/SetLiveStreamStatus")
    void setLiveStreamStatus(@Suspended AsyncResponse async,
                             @Context HttpRequest httpReq,
                             @BeanParam AdminHeader header,
                             @NotNull @Valid SetLiveSteamStatusRequest request);


    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetAllowedProductsResponse.class)))
    @POST
    @Path("/GetAllowedProducts")
    void getAllowedProducts(@Suspended AsyncResponse async,
                            @Context HttpRequest httpReq,
                            @BeanParam AdminHeader header,
                            @NotNull @Valid GetAllowedProductsRequest request);

    @POST
    @Path("/UpdateAllowedProduct") // create or update
    void updateAllowedProduct(@Suspended AsyncResponse async,
                              @Context HttpRequest httpReq,
                              @BeanParam AdminHeader header,
                              @NotNull @Valid CreateOrUpdateAllowedProductRequest request);

    @POST
    @Path("/ResetImages")
    void resetImages(@Suspended AsyncResponse async,
                              @Context HttpRequest httpReq,
                              @BeanParam AdminHeader header,
                              @NotNull @Valid ResetImagesRequest request);

    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetCurrencySettingsResponse.class)))
    @POST
    @Path("/GetCurrencySettings")
    void getCurrencySettings(@Suspended AsyncResponse async,
                            @Context HttpRequest httpReq,
                            @BeanParam AdminHeader header,
                            @NotNull @Valid GetCurrencySettingsRequest request);

    @POST
    @Path("/UpdateCurrencySettings") // create or update
    void updateCurrencySettings(@Suspended AsyncResponse async,
                              @Context HttpRequest httpReq,
                              @BeanParam AdminHeader header,
                              @NotNull @Valid CreateOrUpdateCurrencySettingsRequest request);

    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetBackofficeEventsResponse.class)))
    @POST
    @Path("/GetBackofficeEvents")
    void getBackofficeEvents(@Suspended AsyncResponse async,
                             @Context HttpRequest httpReq,
                             @BeanParam AdminHeader header,
                             @NotNull @Valid GetBackofficeEventsRequest request);

    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = GetCreatorLevelsResponse.class)))
    @GET
    @Path("/GetCreatorLevels")
    void getCreatorLevels(@Suspended AsyncResponse async,
                          @Context HttpRequest httpReq,
                          @BeanParam AdminHeader header);

}
