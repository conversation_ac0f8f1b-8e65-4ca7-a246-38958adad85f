package admin.validators;

import java.util.Arrays;

import admin.models.PriorityWithLevel;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class PriorityLevelValidator implements ConstraintValidator<ValidPriorityLevel, Integer> {

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        return value == null || Arrays.stream(PriorityWithLevel.values())
                .anyMatch(level -> level.getPriority() == value);
    }
}
