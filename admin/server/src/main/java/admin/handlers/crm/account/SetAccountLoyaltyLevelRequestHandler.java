package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetAccountLoyaltyLevelRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.model.LoyaltyLevelsSpec;

@Service
@Slf4j
public class SetAccountLoyaltyLevelRequestHandler extends AbstractCrmAdminHandler<SetAccountLoyaltyLevelRequest> {
    @Inject
    public SetAccountLoyaltyLevelRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }
    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetAccountLoyaltyLevelRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request)
                    .setCreatedBy(header.getUserEmail())
                    .setIdentity(identity).build();

            var wrapped = uamServiceApi.setAccountLoyaltyLevel(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Manually set Loyalty Level to: %s. Rewarded: %s. Comment: %s"
                                .formatted(request.getLevel(), request.isReward(), request.getComment());
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_INFO, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        log.error(status.errorText());
                    }
                }
            });
        });
    }

    private uam.api.v1.SetAccountLoyaltyLevelRequest.Builder toUamRequest(SetAccountLoyaltyLevelRequest request) {
        return uam.api.v1.SetAccountLoyaltyLevelRequest.newBuilder()
                .setLevel(LoyaltyLevelsSpec.fromString(request.getLevel()).level())
                .setReward(request.isReward())
                .setComment(request.getComment());
    }
}
