package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.MassRemoteEmailSendRequest;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.Identity;
import uam.api.v1.SendEmailRequest;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MassRemoteEmailSendRequestHandler extends AbstractCrmAdminHandler<MassRemoteEmailSendRequest> {

    public MassRemoteEmailSendRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassRemoteEmailSendRequest request) throws Exception {
        async.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        async.setTimeoutHandler(ar -> {
            log.warn("Mass email send request timed out");
            ar.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                    .build());
        });

        getMassAccountRoutingInfo(header, request.getSegment(), request.getTag(), (routingMap, throwable) -> {
            if (throwable != null) {
                logErrorAndResume(async, throwable);
            } else {
                processRoutingInfo(async, header, request, routingMap);
            }
        });
    }

    private void processRoutingInfo(AsyncResponse async, AdminHeader header, MassRemoteEmailSendRequest request,
                                    Map<Identity, AsciiString> routingMap) {
        log.info("Processing mass email send request for {} accounts", routingMap.size());

        Flux.fromIterable(routingMap.entrySet())
                .buffer(request.getBuffer())
                .flatMap(entries ->
                        Flux.fromIterable(entries)
                                .flatMap(entry ->
                                        sendEmail(header, request, entry.getKey(), entry.getValue())))
                .subscribeOn(Schedulers.fromExecutor(executor))
                .collectList()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .subscribe(result -> {

                    var eventMessage = "Mass email send operation completed: [ %s ] accounts processed".formatted(routingMap.size());
                    log.info(eventMessage);

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.MASS_EMAIL_SEND,
                            BackofficeEventType.UPDATE, request);

                    async.resume(Response.ok(new AdminOkResponse()).build());
                }, throwable -> {
                    if (throwable instanceof TimeoutException) {
                        log.warn("Mass email send request timed out");
                        async.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                                .build());
                    } else {
                        logErrorAndResume(async, throwable);
                    }
                });
    }

    private Flux<Void> sendEmail(AdminHeader header, MassRemoteEmailSendRequest request, Identity identity, AsciiString routingKey) {
        return Flux.create(sink -> {
            var sendEmailRequest = SendEmailRequest.newBuilder()
                    .setIdentity(identity)
                    .setTemplateCode(request.getTemplate())
                    .build();

            var wrapped = uamServiceApi.sendEmail(sendEmailRequest, routingKey);
            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();

                    if (status.isOK()) {
                        var message = String.format("Sent email to account %s template %s",
                                identity.getByAccountId().getAccountId(), request.getTemplate());
                        saveAccountEvent(AccountEventType.SEND_EMAIL_MASS, header.getUserEmail(), message, routingKey, identity);
                    } else {
                        log.error("Failed to send email to account {} template {}: {}",
                                identity.getByAccountId().getAccountId(), request.getTemplate(), status.errorText());
                    }
                    sink.complete();
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Error sending email to account {}: {}", identity.getByAccountId().getAccountId(), t.getMessage());
                    sink.error(t);
                }

            }, executor);
        });
    }
}
