package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.AccountOptOutArbitrationRequest;
import admin.models.account.AccountOptOutArbitrationResponse;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.OptOutArbitrationRejectionReason;
import uam.api.v1.OptOutArbitrationRequest;
import uam.api.v1.OptOutArbitrationResponse;

import java.io.IOException;

@Service
@Slf4j
public class AccountOptOutArbitrationHandler extends AbstractCrmAdminHandler<AccountOptOutArbitrationRequest> {

    @Inject
    public AccountOptOutArbitrationHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, AccountOptOutArbitrationRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = OptOutArbitrationRequest.newBuilder()
                    .setIdentity(identity).build();
            var wrapped = uamServiceApi.optOutArbitration(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();
                    var optOutArbitrationResponse = srespw.unpack(OptOutArbitrationResponse.class);
                    String eventMessage;

                    if (status.isOK()) {

                        var response = new AccountOptOutArbitrationResponse();
                        response.setAccountId(request.getAccountId());
                        if (optOutArbitrationResponse.hasRejectionReason()) {
                            OptOutArbitrationRejectionReason rejectionReason = optOutArbitrationResponse.getRejectionReason();
                            eventMessage = "(%s) Opt-out arbitration is rejected for %s. Reason: %s"
                                    .formatted(
                                            header.getUserEmail(),
                                            request.getAccountId(),
                                            rejectionReason.name()
                                    );

                            response.setRejectionReason(rejectionReason.name());
                            log.warn(eventMessage);
                        } else {
                            eventMessage = "(%s) Opt-out arbitration has been finished successfully for %s."
                                    .formatted(
                                            header.getUserEmail(),
                                            request.getAccountId()
                                    );
                            log.info(eventMessage);
                        }
                        response.setMessage(eventMessage);
                        saveAccountEvent(AccountEventType.ADD_ACCOUNT_COMMENT, header.getUserEmail(), eventMessage, routingKey, identity);
                        async.resume(Response.status(Response.Status.OK).entity(response).build());
                    } else {
                        var errorMessage = "Unable to opt-out arbitration for account %s. Reason: %s"
                                .formatted(
                                request.getAccountId(),
                                status.errorText()
                        );

                        log.error(errorMessage);

                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), errorMessage)
                        ).build());
                    }
                }
            });
        });
    }
}
