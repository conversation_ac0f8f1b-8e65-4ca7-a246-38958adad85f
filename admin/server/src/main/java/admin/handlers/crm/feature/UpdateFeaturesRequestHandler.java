package admin.handlers.crm.feature;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.feature.UpdateFeaturesRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;

@Slf4j
@Service
public class UpdateFeaturesRequestHandler extends AbstractCrmAdminHandler<UpdateFeaturesRequest> {

    public UpdateFeaturesRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory
    ) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateFeaturesRequest req) throws Exception {
        var brandName = header.getBrand();
        var features = req.getFeatures();
        var request = uam.api.v1.UpdateFeaturesRequest.newBuilder()
                .addAllFeatures(features)
                .setBrandName(brandName)
                .build();

        var wrapped = uamServiceApi.updateFeatures(request, AsciiString.cached(brandName));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());
                    sendEvent(
                            header,
                            "Features [ %s ] for brand %s successfully updated".formatted(features, brandName),
                            BackofficeEventStatus.SUCCESSFUL,
                            BackofficeEntityType.FEATURE,
                            BackofficeEventType.UPDATE,
                            req
                    );
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }
}
