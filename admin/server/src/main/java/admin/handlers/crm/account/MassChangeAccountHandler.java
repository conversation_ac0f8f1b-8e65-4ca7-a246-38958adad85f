package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.MassChangeAccountRequest;
import admin.models.account.enums.AccountRestrictionSpec;
import admin.models.account.enums.AccountStatusSpec;
import admin.models.account.enums.AccountTypeSpec;
import admin.models.account.enums.AccountWithdrawLockModeSpec;
import admin.models.account.enums.VipLevelSpec;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import com.turbospaces.rpc.ApiResponse;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.AccountInfo;
import uam.api.v1.AccountStatus;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.RemoteAdminSetAccountInfoRequest;
import uam.api.v1.RemoteAdminSetAccountInfoResponse;
import uam.api.v1.Identity;

import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MassChangeAccountHandler extends AbstractCrmAdminHandler<MassChangeAccountRequest> {

    private static final String DO_NOT_CHANGE = "DO_NOT_CHANGE";

    private static final Map<AccountStatusSpec, uam.api.v1.AccountStatus> accountStatusMap = new HashMap<>();

    static {
        accountStatusMap.put(AccountStatusSpec.DEFAULT, uam.api.v1.AccountStatus.DEFAULT_STATUS);
        accountStatusMap.put(AccountStatusSpec.UNDER_INVESTIGATION, uam.api.v1.AccountStatus.UNDER_INVESTIGATION_STATUS);
        accountStatusMap.put(AccountStatusSpec.RESTRICTED_BY_GEO_POLICY, uam.api.v1.AccountStatus.RESTRICTED_BY_GEO_POLICY);
        accountStatusMap.put(AccountStatusSpec.PENDING_REDEEM_REVIEW, uam.api.v1.AccountStatus.PENDING_REDEEM_REVIEW);
        accountStatusMap.put(AccountStatusSpec.PENDING_VERIFICATION, uam.api.v1.AccountStatus.PENDING_VERIFICATION);
        accountStatusMap.put(AccountStatusSpec.BW_PENDING_REDEEM_REVIEW, uam.api.v1.AccountStatus.BW_PENDING_REDEEM_REVIEW);
        accountStatusMap.put(AccountStatusSpec.PENDING_PM_VERIFICATION, uam.api.v1.AccountStatus.PENDING_PM_VERIFICATION);
        accountStatusMap.put(AccountStatusSpec.PENDING_PM_BW_VERIFICATION, uam.api.v1.AccountStatus.PENDING_PM_BW_VERIFICATION);
        accountStatusMap.put(AccountStatusSpec.FOR_PAYMENT_PROCESSING, uam.api.v1.AccountStatus.FOR_PAYMENT_PROCESSING);
        accountStatusMap.put(AccountStatusSpec.FOR_NSC_PROCESSING, uam.api.v1.AccountStatus.FOR_NSC_PROCESSING);
        accountStatusMap.put(AccountStatusSpec.CLOSED, uam.api.v1.AccountStatus.CLOSED_STATUS);
        accountStatusMap.put(AccountStatusSpec.REQUIRES_JUMIO_KYC, AccountStatus.REQUIRES_JUMIO_KYC);
        accountStatusMap.put(AccountStatusSpec.DO_NOT_CHANGE, uam.api.v1.AccountStatus.DO_NOT_CHANGE_STATUS);
    }

    @Inject
    public MassChangeAccountHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassChangeAccountRequest request) throws Exception {
        async.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        async.setTimeoutHandler(ar -> {
            log.warn("Mass change account request timed out");
            ar.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                    .build());
        });

        // Sanitize and prepare account IDs.
        List<String> sanitizedIds = sanitize(request.getIds()).stream()
                .map(String::toLowerCase)
                .collect(Collectors.toList());
        if (sanitizedIds.isEmpty()) {
            log.warn("No valid account IDs provided for change");
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "No valid account IDs provided"))
                    .build());
            return;
        }

        // Initialize counters.
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);

        // Define a custom scheduler based on the existing executor.
        Scheduler scheduler = Schedulers.fromExecutor(executor);

        // Process account IDs reactively.
        Flux.fromIterable(sanitizedIds)
                .buffer(BUFFER_MAX_SIZE)
                .flatMap(batchIds ->
                        Flux.fromIterable(batchIds)
                                .flatMap(id ->
                                        getAccountRoutingInfoReactive(id, header.getBrand())
                                                .flatMap(pair -> processSingleAccountChange(header, request, pair.getT1(), pair.getT2(), processedCount, failedCount))
                                                .onErrorResume(e -> {
                                                    log.error("Failed to change account {}: {}", id, e.getMessage());
                                                    failedCount.incrementAndGet();
                                                    return Mono.empty();
                                                })
                                )
                )
                .subscribeOn(scheduler)
                .collectList()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .doOnSuccess(_ -> {
                    var eventMessage = "Mass change account completed: [ %s ] accounts processed, [ %s ] accounts failed. Details: [ %s ]"
                            .formatted(processedCount.get(), failedCount.get(), createMassRetoolMessage(request));

                    log.info(eventMessage);

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.MASS_ACCOUNT_CHANGE,
                            BackofficeEventType.UPDATE, request);

                    async.resume(Response.ok(new AdminOkResponse()).build());
                })
                .doOnError(error -> {
                    log.error("Mass change account encountered errors: {} processed, {} failed", processedCount.get(), failedCount.get());
                    if (error instanceof TimeoutException) {
                        async.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                                .build());
                    } else {
                        logErrorAndResume(async, error);
                    }
                })
                .subscribe();
    }

    /**
     * Reactive wrapper for getAccountRoutingInfo that returns a Mono of Tuple2<Identity, AsciiString>.
     *
     * @param id    The account ID.
     * @param brand The brand associated with the account.
     * @return A Mono emitting a tuple containing the account's Identity and routing key.
     */
    private Mono<Tuple2<Identity, AsciiString>> getAccountRoutingInfoReactive(String id, String brand) {
        return Mono.create(sink -> {
            // Note: We pass null for AsyncResponse because we want to handle errors reactively.
            super.getAccountRoutingInfo(Long.parseLong(id), brand, null, (identity, routingKey) -> {
                if (identity != null && routingKey != null) {
                    sink.success(Tuples.of(identity, routingKey));
                } else {
                    sink.error(new RuntimeException("Routing info not found for account ID: " + id));
                }
            });
        });
    }

    private Mono<Void> processSingleAccountChange(AdminHeader header, MassChangeAccountRequest request,
                                                  Identity identity, AsciiString routingKey,
                                                  AtomicInteger processedCount,
                                                  AtomicInteger failedCount) {
        return Mono.create(sink -> {
            // Build the AccountInfo using a helper method.
            RemoteAdminSetAccountInfoRequest setInfoRequest = RemoteAdminSetAccountInfoRequest.newBuilder()
                    .setInfo(createAccountInfo(request))
                    .setIdentity(identity)
                    .build();

            ApiResponse<?> wrapped = uamServiceApi.remoteAdminSetAccountInfo(setInfoRequest, routingKey);
            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();
                    if (status.isOK()) {
                        RemoteAdminSetAccountInfoResponse response = srespw.unpack(RemoteAdminSetAccountInfoResponse.class);
                        AccountInfo before = response.getInfoBeforeChanges();
                        AccountInfo after = response.getInfo();
                        String eventMessage = createAccountRetoolMessage(before, after);
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_INFO, header.getUserEmail(), eventMessage, routingKey, identity);
                        processedCount.incrementAndGet();
                        sink.success();
                    } else {
                        failedCount.incrementAndGet();
                        sink.error(new RuntimeException("Failed to change account info: " + status.errorText()));
                    }
                }

                @Override
                public void onFailure(Throwable t) {
                    failedCount.incrementAndGet();
                    sink.error(t);
                }
            });
        });
    }

    /**
     * Builds the AccountInfo for the change request.
     *
     * @param request The mass change account request.
     * @return A SetAccountInfoRequest.Builder containing the updated account info.
     */
    private AccountInfo.Builder createAccountInfo(MassChangeAccountRequest request) {
        var info = AccountInfo.newBuilder();
        // Set account type if provided
        switch (request.getAccountType()) {
            case ADMIN -> info.setAccountType(uam.api.v1.AccountType.ADMIN_ACCOUNT);
            case REGULAR -> info.setAccountType(uam.api.v1.AccountType.REGULAR_ACCOUNT);
            default -> {}
        }
        // Set withdraw lock mode if provided
        switch (request.getWithdrawLockMode()) {
            case AUTO_LOCK_MODE -> info.setWithdrawLockMode(uam.api.v1.AccountWithdrawLockMode.AUTO_LOCK_MODE);
            case MANUAL_LOCK_MODE -> info.setWithdrawLockMode(uam.api.v1.AccountWithdrawLockMode.MANUAL_LOCK_MODE);
            default -> {}
        }
        // Add restrictions from the request
        info.setModifyRestrictions(true);
        request.getAccountRestrictions().stream()
                .map(this::toRestriction)
                .forEach(info::addRestrictions);
        // Set account status if not DO_NOT_CHANGE
        if (!AccountStatusSpec.DO_NOT_CHANGE.equals(request.getAccountStatus())) {
            info.setAccountStatus(accountStatusMap.get(request.getAccountStatus()));
        }
        // Set VIP level override if applicable
        if (!VipLevelSpec.DO_NOT_CHANGE.equals(request.getVipLevel())) {
            info.setPurchaseVipLevelOverride(toVipLevel(request));
        }
        // Set email verified if provided and not "DO_NOT_CHANGE"
        if (StringUtils.isNotEmpty(request.getEmailVerified()) && !DO_NOT_CHANGE.equals(request.getEmailVerified())) {
            info.setEmailVerified(Boolean.parseBoolean(request.getEmailVerified()));
        }
        return info;
    }

    private uam.api.v1.AccountRestriction toRestriction(AccountRestrictionSpec restriction) {
        return switch (restriction) {
            case DO_NOT_CHANGE -> uam.api.v1.AccountRestriction.DO_NOT_CHANGE;
            case NO_GAME -> uam.api.v1.AccountRestriction.NO_GAME;
            case NO_PURCHASE -> uam.api.v1.AccountRestriction.NO_PURCHASE;
            case NO_REDEEM -> uam.api.v1.AccountRestriction.NO_REDEEM;
            case NO_DAILY_LOGIN_REWARD -> uam.api.v1.AccountRestriction.NO_DAILY_LOGIN_REWARD;
            case NO_SWEEPSTAKE_CODE -> uam.api.v1.AccountRestriction.NO_SWEEPSTAKE_CODE;
            case NO_LEGAL_RULES_UPDATE -> uam.api.v1.AccountRestriction.NO_LEGAL_RULES_UPDATE;
            case NO_CAMPAIGNS -> uam.api.v1.AccountRestriction.NO_CAMPAIGNS;
            case RESTRICTED_CAMPAIGNS -> uam.api.v1.AccountRestriction.RESTRICTED_CAMPAIGNS;
            case LOYALTY_CAMPAIGNS -> uam.api.v1.AccountRestriction.LOYALTY_CAMPAIGNS;
        };
    }

    private uam.api.v1.AccountPurchaseVipLevel toVipLevel(MassChangeAccountRequest request) {
        return switch (request.getVipLevel()) {
            case NONE -> uam.api.v1.AccountPurchaseVipLevel.NONE_PURCHASE_VIP_LEVEL;
            case CLASS_A -> uam.api.v1.AccountPurchaseVipLevel.CLASS_A;
            case CLASS_B -> uam.api.v1.AccountPurchaseVipLevel.CLASS_B;
            case CLASS_C -> uam.api.v1.AccountPurchaseVipLevel.CLASS_C;
            case CLASS_D -> uam.api.v1.AccountPurchaseVipLevel.CLASS_D;
            case CLASS_E -> uam.api.v1.AccountPurchaseVipLevel.CLASS_E;
            case CLASS_F -> uam.api.v1.AccountPurchaseVipLevel.CLASS_F;
            case PORTFOLIO_PLAYER -> uam.api.v1.AccountPurchaseVipLevel.PORTFOLIO_PLAYER;
            default -> uam.api.v1.AccountPurchaseVipLevel.DO_NOT_CHANGE_PURCHASE_VIP_LEVEL;
        };
    }

    /**
     * Compares before and after AccountInfo to create an event message.
     */
    private String createAccountRetoolMessage(AccountInfo before, AccountInfo after) {
        StringBuilder message = new StringBuilder("Account has been updated. ");
        // Compare restrictions
        var beforeSet = new java.util.HashSet<>(before.getRestrictionsList());
        var afterSet = new java.util.HashSet<>(after.getRestrictionsList());
        var added = new java.util.HashSet<>(afterSet);
        added.removeAll(beforeSet);
        var removed = new java.util.HashSet<>(beforeSet);
        removed.removeAll(afterSet);
        if (!added.isEmpty()) {
            message.append("Added restrictions: ")
                    .append(added.stream().map(Enum::name).collect(Collectors.joining(", ")))
                    .append(". ");
        }
        if (!removed.isEmpty()) {
            message.append("Removed restrictions: ")
                    .append(removed.stream().map(Enum::name).collect(Collectors.joining(", ")))
                    .append(". ");
        }
        // Compare other fields
        if (!after.getAccountType().equals(before.getAccountType())) {
            message.append("Account type changed from ")
                    .append(before.getAccountType()).append(" to ").append(after.getAccountType()).append(". ");
        }
        if (!after.getAccountStatus().equals(before.getAccountStatus())) {
            message.append("Account status changed from ")
                    .append(before.getAccountStatus()).append(" to ").append(after.getAccountStatus()).append(". ");
        }
        if (!after.getWithdrawLockMode().equals(before.getWithdrawLockMode())) {
            message.append("Withdraw lock mode changed from ")
                    .append(before.getWithdrawLockMode()).append(" to ").append(after.getWithdrawLockMode()).append(". ");
        }
        if (after.getEmailVerified() != before.getEmailVerified()) {
            message.append("Email verified changed from ")
                    .append(before.getEmailVerified()).append(" to ").append(after.getEmailVerified()).append(". ");
        }
        if (!after.getPurchaseVipLevelOverride().equals(before.getPurchaseVipLevelOverride())) {
            message.append("VIP level changed from ")
                    .append(before.getPurchaseVipLevelOverride()).append(" to ").append(after.getPurchaseVipLevelOverride()).append(". ");
        }
        return message.toString().trim();
    }

    private String createMassRetoolMessage(MassChangeAccountRequest request) {
        StringBuilder message = new StringBuilder("Accounts has been updated. ");
        if (!request.getAccountRestrictions().contains(AccountRestrictionSpec.DO_NOT_CHANGE)) {
            message.append("Added restrictions: ")
                    .append(request.getAccountRestrictions().stream().map(Enum::name).collect(Collectors.joining(", ")))
                    .append(". ");
        }
        if (!request.getAccountType().equals(AccountTypeSpec.DO_NOT_CHANGE)) {
            message.append("Type changed to ").append(request.getAccountType()).append(". ");
        }
        if (!request.getAccountStatus().equals(AccountStatusSpec.DO_NOT_CHANGE)) {
            message.append("Status changed to ").append(request.getAccountStatus()).append(". ");
        }
        if (!request.getWithdrawLockMode().equals(AccountWithdrawLockModeSpec.DO_NOT_CHANGE_LOCK_MODE)) {
            message.append("Withdraw lock mode changed to ").append(request.getWithdrawLockMode()).append(". ");
        }
        if (!DO_NOT_CHANGE.equals(request.getEmailVerified())) {
            message.append("Email verified changed to ").append(request.getEmailVerified()).append(". ");
        }
        if (!request.getVipLevel().equals(VipLevelSpec.DO_NOT_CHANGE)) {
            message.append("VIP level changed to ").append(request.getVipLevel()).append(". ");
        }
        return message.toString().trim();
    }
}
