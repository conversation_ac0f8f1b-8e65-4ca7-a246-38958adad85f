package admin.handlers.crm.common;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.common.ServerConfigRequest;
import admin.models.common.ServerConfigResponse;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.RemoteAdminCRMServerPropsRequest;
import uam.api.v1.RemoteAdminCRMServerPropsResponse;
import uam.model.RewardSourceSystemSpec;

import java.io.IOException;
import java.util.Arrays;

@Service
@Slf4j
public class ServerConfigHandler extends AbstractCrmAdminHandler<ServerConfigRequest> {

    public ServerConfigHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, ServerConfigRequest request) throws Exception {

        var res = new ServerConfigResponse();

        res.setLoyaltyProgramEnabled(false);
        res.setSweepstakeRewardMaxAmount(props.SOCIAL_MEDIA_REWARD_MAX_AMOUNT.get());
        res.setBrandGroup(props.BRAND_GROUP.get());
        res.setRewardSourceSystems(
                Arrays.stream(RewardSourceSystemSpec.values())
                        .filter(spec -> spec.groups().contains(props.BRAND_GROUP.get()))
                        .map(RewardSourceSystemSpec::code)
                        .toList()
        );
        res.setOtpTriggerRulesCountries(props.OTP_TRIGGER_RULES_COUNTRIES.get());

        res.setOfferTemplateMultiCountriesSupport(props.OFFER_TEMPLATE_MULTI_COUNTRIES_SUPPORT_ENABLED.get(header.getBrand()));
        res.setOfferTemplateSweepstakeMaxAmount(props.OFFER_TEMPLATE_SWEEPSTAKE_MAX_AMOUNT.get());
        res.setOfferTemplateSweepstakeFirstMaxAmount(props.OFFER_TEMPLATE_SWEEPSTAKE_FIRST_MAX_AMOUNT.get());
        res.setOfferTemplatePriorityMinValue(props.OFFER_TEMPLATE_PRIORITY_MIN_VALUE.get());
        res.setOfferTemplatePriorityMaxValue(props.OFFER_TEMPLATE_PRIORITY_MAX_VALUE.get());
        res.setOfferTemplatePriorityDefaultValue(props.OFFER_TEMPLATE_PRIORITY_DEFAULT_VALUE.get());
        res.setOfferTemplateXpLevelsEnabled(props.OFFER_TEMPLATE_XP_LEVELS_ENABLED.get());
        res.setOfferTemplateMinWeeklyWageredGcEnabled(props.OFFER_TEMPLATE_MIN_WEEKLY_WAGERED_GC_ENABLED.get());
        res.setMassRewardSweepstakeMaxAmount(props.MASS_ACCOUNT_REWARD_SWEEPSTAKE_MAX_AMOUNT.get());

        var req = RemoteAdminCRMServerPropsRequest.newBuilder().build();

        var wrapped = uamServiceApi.remoteAdminGetCRMServerProps(req, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws IOException {

                var status = srespw.status();

                if (status.isOK()) {

                    var response = srespw.unpack(RemoteAdminCRMServerPropsResponse.class);

                    res.setAccountRewardSweepstakeBlockingEnabled(response.getAccountRewardSweepstakeBlockingEnabled());
                    res.setAccountRewardSweepstakeBlockingTags(response.getAccountRewardSweepstakeBlockingTagsList());
                    res.setEmailTemplateTypes(response.getEmailTemplateTypesList());

                    async.resume(Response.status(Response.Status.OK).entity(res).build());

                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }
}
