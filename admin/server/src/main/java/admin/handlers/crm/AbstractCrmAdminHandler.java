package admin.handlers.crm;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminRequest;
import admin.handler.AdminHandler;
import admin.models.common.BackOfficeMessageRequest;
import api.v1.AccountRoutingInfo;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import api.v1.PlatformSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.rpc.ApiResponse;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEvent;
import uam.api.v1.AccountEventType;
import uam.api.v1.AccountRoutingById;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.GetAccountRoutingInfoRequest;
import uam.api.v1.GetAccountRoutingInfoResponse;
import uam.api.v1.GetMassAccountRoutingInfoRequest;
import uam.api.v1.GetMassAccountRoutingInfoResponse;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;
import uam.api.v1.RemoteAdminCreateBackofficeEventRequest;
import uam.api.v1.SaveAccountEventRequest;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractCrmAdminHandler<T extends AdminRequest> implements InitializingBean, DisposableBean, BeanNameAware, AdminHandler<T> {

    protected static final String ACCOUNT_NOT_FOUND_CODE = "account_not_found";
    public static final int TIMEOUT = 60;
    public static final int BUFFER_MAX_SIZE = 4;

    protected final UamServiceApi uamServiceApi;
    protected final ApiFactory apiFactory;
    protected final DynamicCloud cloud;
    protected final CrmAdminServerProperties props;
    protected final PlatformExecutorService executor;

    protected AbstractCrmAdminHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        this.uamServiceApi = uamServiceApi;
        this.apiFactory = apiFactory;
        this.cloud = cloud;
        this.props = props;
        this.executor = new DefaultPlatformExecutorService(props, meterRegistry);
    }

    @Override
    public void setBeanName(String name) {
        executor.setBeanName(name);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        executor.afterPropertiesSet();
    }

    @Override
    public void destroy() throws Exception {
        executor.destroy();
    }

    protected void sendEvent(AdminHeader header, String message, BackofficeEventStatus status,
                             BackofficeEntityType entityType,
                             BackofficeEventType eventType,
                             BackOfficeMessageRequest request) {
        var backOfficeEvent = RemoteAdminCreateBackofficeEventRequest.newBuilder()
                .setBrandName(header.getBrand())
                        .setCreatedBy(header.getUserEmail())
                        .setMessage(message)
                        .setStatus(status)
                        .setReference(request.getReference())
                        .setEntityType(entityType)
                        .setEventType(eventType)
                .build();

        uamServiceApi.saveBackofficeEvent(backOfficeEvent, AsciiString.cached(request.getReference()));
    }

    protected void saveAccountEvent(AccountEventType type, String createdBy, String message, AsciiString hash, Identity identity) {
        saveAccountEvent(type, null, createdBy, message, hash, identity);
    }
    protected void saveAccountEvent(
            AccountEventType type,
            String reference,
            String createdBy,
            String message,
            AsciiString hash,
            Identity identity) {
        var req = toSaveAccountEventRequest(type, reference, createdBy, message, identity);
        uamServiceApi.saveAccountEvent(req.build(), hash);
    }

    protected static Identity toIdentity(AccountRoutingInfo routing) {
        var byId = IdentityByAccountId.newBuilder()
                .setAccountId(routing.getId())
                .setPlatform(PlatformSpec.WEB.code())
                .setRemoteIp(routing.getRemoteIp());

        return Identity.newBuilder()
                .setByAccountId(byId)
                .build();
    }

    protected void getAccountRoutingInfo(Long accountId, String brand, AsyncResponse async,
                                         BiConsumer<Identity, AsciiString> consumer) {

        var req = GetAccountRoutingInfoRequest.newBuilder()
                .setBrandName(brand)
                .setByAccountId(AccountRoutingById.newBuilder()
                        .setId(accountId.toString())
                );

        var wrappedRouting = uamServiceApi.getAccountRouting(req.build());

        wrappedRouting.addListener(new AbstractSafeResponseConsumer(wrappedRouting, apiFactory) {

            @Override
            public void onFailure(Throwable t) {
                super.onFailure(t);
                async.resume(Response.status(Response.Status.BAD_REQUEST)
                        .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), t.getMessage()))
                        .build());
            }

            @Override
            public void accept(ResponseWrapperFacade srespw) throws Exception {

                var routResp = srespw.unpack(GetAccountRoutingInfoResponse.class);

                if (routResp.hasRouting()) {

                    var routing = routResp.getRouting();
                    var hash = routing.getHash();
                    var identity = toIdentity(routing);
                    var routingKey = AsciiString.cached(hash);

                    consumer.accept(identity, routingKey);

                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(ACCOUNT_NOT_FOUND_CODE, "Account not found"))
                            .build());
                    log.error("Account {} was not found for Brand: {}", accountId, brand);
                }
            }
        });
    }

    protected void getMassAccountRoutingInfo(AdminHeader header, String category, String tag,
                                             BiConsumer<Map<Identity, AsciiString>, Throwable> callback) {
        var requestBuilder = GetMassAccountRoutingInfoRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setCategory(category);

        if (tag != null && !tag.isEmpty()) {
            requestBuilder.setTag(tag);
        }

        ApiResponse<GetMassAccountRoutingInfoResponse> wrappedRouting = uamServiceApi.getMassAccountRoutingInfo(
                requestBuilder.build(),
                AsciiString.cached(header.getBrand()));

        wrappedRouting.addListener(new AbstractSafeResponseConsumer(wrappedRouting, apiFactory) {

            @Override
            public void accept(ResponseWrapperFacade srespw) throws Exception {
                Map<Identity, AsciiString> routingMap = new HashMap<>();
                GetMassAccountRoutingInfoResponse response = srespw.unpack(GetMassAccountRoutingInfoResponse.class);
                for (AccountRoutingInfo routing : response.getRoutingList()) {
                    Identity identity = toIdentity(routing);
                    AsciiString routingKey = AsciiString.cached(routing.getHash());
                    routingMap.put(identity, routingKey);
                }
                log.debug("Got routing info for: {} accounts", routingMap.size());
                callback.accept(routingMap, null);
            }

            @Override
            public void onFailure(Throwable t) {
                log.error("Error getting mass account routing info", t);
                callback.accept(null, t);
            }

        }, executor);
    }

    protected String toJsonString(T obj) {
        return ReflectionToStringBuilder.toString(obj, ToStringStyle.JSON_STYLE, false, false, true, null);
    }

    private static SaveAccountEventRequest.Builder toSaveAccountEventRequest(AccountEventType type, String reference, String createdBy, String message,
                                                                     Identity identity) {
        var event = AccountEvent.newBuilder()
                .setType(type)
                .setReference(Optional.ofNullable(reference).orElse(StringUtils.EMPTY))
                .setCreatedBy(createdBy)
                .setMessage(message);

        return SaveAccountEventRequest.newBuilder()
                .setIdentity(identity)
                .setEvent(event);
    }

    protected Collection<String> sanitize(Collection<String> ids) {
        return ids.stream()
                .filter(Objects::nonNull)
                .map(StringUtils::trim)
                .filter(StringUtils::isNumeric)
                .collect(Collectors.toSet());
    }

    protected void logErrorAndResume(AsyncResponse async, Throwable t) {
        log.error("Error in processing request", t);
        async.resume(Response.status(Response.Status.BAD_REQUEST)
                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), t.getMessage()))
                .build());
    }
}
