package admin.handlers.crm.brand;

import java.time.ZoneOffset;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.CreateHomepageFeatureRequest;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.FeatureType;
import uam.api.v1.RepeatMode;
import uam.api.v1.SetHomepageFeatureRequest;
import uam.api.v1.SetHomepageFeatureResponse;

@Service
@Slf4j
public class CreateHomepageFeatureRequestHandler extends AbstractCrmAdminHandler<CreateHomepageFeatureRequest> {

    public CreateHomepageFeatureRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry, UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateHomepageFeatureRequest request) throws Exception {
        var updateRequest = mapRequest(header, request);

        var wrapped = uamServiceApi.setHomepageFeature(updateRequest, AsciiString.cached(header.getBrand()));
        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void onFailure(Throwable t) {
                super.onFailure(t);
                async.resume(Response.status(Response.Status.BAD_REQUEST)
                        .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), t.getMessage()))
                        .build());
            }

            @Override
            public void accept(ResponseWrapperFacade srespw) throws Throwable {
                var status = srespw.status();
                if (status.isOK()) {
                    var resp = srespw.unpack(SetHomepageFeatureResponse.class);
                    if (resp.getApplied()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Homepage Feature [ %s ] created successfully"
                                .formatted(request.getReference());
                        sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_HOMEPAGE_FEATURE,
                                BackofficeEventType.CREATE, request);
                    } else {
                        async.resume(Response.status(Response.Status.OK).entity(JsonNodeFactory.instance.objectNode()).build());
                    }
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                            .build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static SetHomepageFeatureRequest mapRequest(AdminHeader header, CreateHomepageFeatureRequest request) {
        var setHomepageFeatureRequest = SetHomepageFeatureRequest.newBuilder()
                .setId(request.getId())
                .setFeatureType(FeatureType.valueOf(request.getFeatureType().toString()))
                .setBrandName(header.getBrand())
                .setEnabled(request.isEnabled());

        if (StringUtils.isNotEmpty(request.getUrl())) {
            setHomepageFeatureRequest.setUrl(request.getUrl());
        }

        if (StringUtils.isNotEmpty(request.getUrlMobile())) {
            setHomepageFeatureRequest.setUrlMobile(request.getUrlMobile());
        }

        if (Objects.nonNull(request.getOrder())) {
            setHomepageFeatureRequest.setOrder(request.getOrder());
        }

        if (StringUtils.isNotEmpty(request.getCountry())) {
            setHomepageFeatureRequest.setCountry(request.getCountry());
        }

        if (StringUtils.isNotEmpty(request.getTextAlt())) {
            setHomepageFeatureRequest.setTextAlt(request.getTextAlt());
        }

        if (StringUtils.isNotEmpty(request.getPath())) {
            setHomepageFeatureRequest.setPath(request.getPath());
        }

        if (Objects.nonNull(request.getPriority())) {
            setHomepageFeatureRequest.setPriority(request.getPriority());
        }

        setHomepageFeatureRequest.setTimer(request.isTimer());
        setHomepageFeatureRequest.setProgress(request.isProgress());
        setHomepageFeatureRequest.setSweepstake(request.isSweepstake());
        setHomepageFeatureRequest.setGold(request.isGold());
        setHomepageFeatureRequest.setLoggedIn(request.isLoggedIn());
        setHomepageFeatureRequest.setLoggedOut(request.isLoggedOut());

        if (Objects.nonNull(request.getStartAt())) {
            setHomepageFeatureRequest.setStartAt(request.getStartAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        }

        if (Objects.nonNull(request.getEndAt())) {
            setHomepageFeatureRequest.setEndAt(request.getEndAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        }

        if (Objects.nonNull(request.getRepeatMode())) {
            setHomepageFeatureRequest.setRepeatMode(RepeatMode.valueOf(request.getRepeatMode().toString()));
        }

        if (StringUtils.isNotEmpty(request.getAnimation())) {
            setHomepageFeatureRequest.setAnimation(request.getAnimation());
        }
        if (StringUtils.isNotEmpty(request.getPlacement())) {
            setHomepageFeatureRequest.setPlacement(request.getPlacement());
        }
        if (StringUtils.isNotEmpty(request.getLayout())) {
            setHomepageFeatureRequest.setLayout(request.getLayout());
        }

        return setHomepageFeatureRequest.build();
    }
}
