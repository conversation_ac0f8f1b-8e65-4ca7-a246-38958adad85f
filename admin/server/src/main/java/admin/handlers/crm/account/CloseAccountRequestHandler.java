package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.AccountClosureRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.CloseAccountRequest;

@Service
@Slf4j
public class CloseAccountRequestHandler extends AbstractCrmAdminHandler<AccountClosureRequest> {
    @Inject
    public CloseAccountRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, AccountClosureRequest request) throws Exception {
        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = CloseAccountRequest.newBuilder()
                    .setIdentity(identity)
                    .setReason(request.getReason())
                    .build();

            var wrapped = uamServiceApi.closeAccount(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {
                        String message = "Account has been closed, reason: %s".formatted(request.getReason());
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_INFO, header.getUserEmail(), message, routingKey, identity);
                        log.warn(message);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        log.error(status.errorText());
                    }
                }
            });
        });
    }
}
