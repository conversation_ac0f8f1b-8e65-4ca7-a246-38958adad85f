package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetAccountRestrictionsRequest;
import api.v1.AccountRestrictionSpec;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.AccountRestriction;
import uam.api.v1.RemoteAdminSetAccountRestrictionsRequest;
import uam.api.v1.RemoteAdminSetAccountRestrictionsResponse;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SetAccountRestrictionsRequestHandler extends AbstractCrmAdminHandler<SetAccountRestrictionsRequest> {

    private static final Set<String> VALID_RESTRICTIONS =
            Arrays.stream(AccountRestrictionSpec.values())
                    .map(v -> StringUtils.lowerCase(v.name()))
                    .collect(Collectors.toSet());

    public SetAccountRestrictionsRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetAccountRestrictionsRequest request) throws Exception {
        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = RemoteAdminSetAccountRestrictionsRequest.newBuilder()
                    .setIdentity(identity);

            request.getRestrictions().forEach(restriction -> {
                if (!VALID_RESTRICTIONS.contains(restriction)) {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse("ERR_BAD_REQUEST", "Invalid restriction: " + restriction)
                    ).build());
                    return;
                }
                uamRequest.addRestrictions(uam.api.v1.AccountRestriction.valueOf(restriction.toUpperCase()));
            });

            var wrapped = uamServiceApi.remoteAdminSetAccountRestrictions(uamRequest.build(), routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();

                    if (status.isOK()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());
                        var response = srespw.unpack(RemoteAdminSetAccountRestrictionsResponse.class);
                        var before = response.getBeforeSetRestrictionsList();
                        var after = response.getAfterSetRestrictionsList();

                        var eventMessage = createRetoolMessage(before, after);
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_RESTRICTION, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {

                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        log.error(status.errorText());
                    }
                }
            });
        });
    }

    private String createRetoolMessage(List<uam.api.v1.AccountRestriction> before, List<uam.api.v1.AccountRestriction> after) {
        var message = new StringBuilder("Account Restriction has been updated. ");

        Set<AccountRestriction> beforeSet = new HashSet<>(before);
        Set<AccountRestriction> afterSet = new HashSet<>(after);

        // Determine added and removed restrictions
        Set<AccountRestriction> added = new HashSet<>(afterSet);
        added.removeAll(beforeSet);

        Set<AccountRestriction> removed = new HashSet<>(beforeSet);
        removed.removeAll(afterSet);

        if (!added.isEmpty()) {
            message.append("Restrictions were added: ")
                    .append(added.stream().map(Enum::name).collect(Collectors.joining(", ")))
                    .append(". ");
        }

        if (!removed.isEmpty()) {
            message.append("Restrictions were removed: ")
                    .append(removed.stream().map(Enum::name).collect(Collectors.joining(", ")))
                    .append(". ");
        }

        return message.toString().trim();
    }
}
