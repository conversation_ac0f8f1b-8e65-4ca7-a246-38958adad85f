package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.models.brand.UpdateBannerTemplateRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.BannerTemplateAdminInfo;
import uam.api.v1.SaveBannerTemplateRequest;

import java.time.ZoneOffset;

@Service
@Slf4j
public class UpdateBannerTemplateRequestHandler extends AbstractCrmAdminHandler<UpdateBannerTemplateRequest> {

    public UpdateBannerTemplateRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateBannerTemplateRequest request) throws Exception {

        var updateBannerTemplateRequest = mapRequest(header, request);

        var wrapped = uamServiceApi.addOrUpdateBannerTemplate(updateBannerTemplateRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "Banner Template Code [ %s ], Title [ %s ] Updated Successfully"
                            .formatted(request.getReference(), request.getTitle());
                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_BANNER_TEMPLATE,
                            BackofficeEventType.UPDATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static SaveBannerTemplateRequest mapRequest(AdminHeader header, UpdateBannerTemplateRequest request) {

        var bannerTemplateAdminInfoBuilder = BannerTemplateAdminInfo.newBuilder()
                .setCode(request.getReference())
                .setTitle(request.getTitle())
                .setActive(request.getActive())
                .setBannerImageUrl(request.getBannerImageUrl())
                .setPlatform(request.getPlatform())
                .setExcludeSegment(request.getExcludeSegment())
                .setCtaUrl(request.getCtaUrl())
                .setPriority(request.getPriority())
                .setSegment(request.getSegment())
                .setApplicable(request.getApplicable())
                .addAllSegmentTags(request.getSegmentTags())
                .addAllExcludeSegmentTags(request.getExcludeSegmentTags());

        if (request.getStartAt() != null) {
            bannerTemplateAdminInfoBuilder
                    .setStartAt(request.getStartAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        }

        if (request.getEndAt() != null) {
            bannerTemplateAdminInfoBuilder
                    .setEndAt(request.getEndAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        }

        var saveBannerTemplateRequest = SaveBannerTemplateRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setBanner(bannerTemplateAdminInfoBuilder.build());

        return saveBannerTemplateRequest.build();
    }
}
