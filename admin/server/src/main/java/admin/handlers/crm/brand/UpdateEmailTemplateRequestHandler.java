package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.models.brand.UpdateEmailTemplateRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.*;

@Service
@Slf4j
public class UpdateEmailTemplateRequestHandler extends AbstractCrmAdminHandler<UpdateEmailTemplateRequest> {

    public UpdateEmailTemplateRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateEmailTemplateRequest request) throws Exception {

        var updateRequest = mapRequest(header, request);

        var wrapped = uamServiceApi.setEmailTemplateInfo(updateRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "Email Template Code [ %s ], Description [ %s ], Updated Successfully"
                            .formatted(request.getReference(), request.getDescription());
                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_EMAIL_TEMPLATE,
                            BackofficeEventType.UPDATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static SetEmailTemplateInfoRequest mapRequest(AdminHeader header, UpdateEmailTemplateRequest request) {
        var updateRequest = SetEmailTemplateInfoRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setEmailTemplate(EmailTemplateInfo.newBuilder()
                        .setLocale(request.getLocale())
                        .setCode(request.getReference())
                        .setProvider(request.getProvider())
                        .setEmailTemplateId(request.getEmailTemplateId())
                        .setDescription(request.getDescription())
                        .build());
        return updateRequest.build();
    }
}
