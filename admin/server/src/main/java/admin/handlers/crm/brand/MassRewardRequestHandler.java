package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.MassRewardRequest;
import admin.models.brand.MassRewardResponse;
import api.v1.AccountRoutingFlatten;
import api.v1.AccountRoutingInfo;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.google.common.collect.Sets;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.GetMassAccountRoutingInfoResponse;
import uam.api.v1.RewardAccountManuallyRequest;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Service
@Slf4j
public class MassRewardRequestHandler extends AbstractCrmAdminHandler<MassRewardRequest> {

    public MassRewardRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassRewardRequest request) throws Exception {

        Map<uam.api.v1.RewardAccountManuallyRequest.Builder, AccountRoutingFlatten> routingMap = new HashMap<>();

        FluentFuture.from(executor.submit((CheckedRunnable) () -> handleRoutingInfo(async, header, request, routingMap)))
                .addCallback(new FutureCallback<Object>() {
                    @Override
                    public void onSuccess(Object result) {
                        log.debug("Send emails for: {} accounts", routingMap.size());
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        handleFailure(async, t);
                    }
                }, MoreExecutors.directExecutor());
    }

    private void handleRoutingInfo(AsyncResponse async, AdminHeader header, MassRewardRequest request,
                                   Map<uam.api.v1.RewardAccountManuallyRequest.Builder, AccountRoutingFlatten> routingMap) {
        var getMassAccountRoutingInfoRequestBuilder = uam.api.v1.GetMassAccountRoutingInfoRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setCategory(request.getCategory());

        if (StringUtils.isNotEmpty(request.getTag())) {
            getMassAccountRoutingInfoRequestBuilder.setTag(request.getTag());
        }

        try {
            var wrappedRouting = uamServiceApi.getMassAccountRoutingInfo(
                    getMassAccountRoutingInfoRequestBuilder.build(),
                    AsciiString.cached(header.getBrand()));

            wrappedRouting.addListener(new AbstractSafeResponseConsumer(wrappedRouting, apiFactory) {
                @Override
                public void onFailure(Throwable t) {
                    handleFailure(async, t);
                }

                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    populateRoutingMap(srespw, routingMap, request);
                    log.debug("Got routing info for: {} accounts", routingMap.size());
                    processRoutingInfo(async, header, routingMap, request);
                }
            });
        } catch (Exception err) {
            handleFailure(async, err);
        }
    }

    private void handleFailure(AsyncResponse async, Throwable t) {
        log.error(t.getMessage(), t);
        async.resume(Response.status(Response.Status.BAD_REQUEST)
                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), t.getMessage()))
                .build());
    }

    private void populateRoutingMap(ResponseWrapperFacade srespw,
                                    Map<uam.api.v1.RewardAccountManuallyRequest.Builder, AccountRoutingFlatten> routingMap,
                                    MassRewardRequest request) throws Exception {
        var sresp = srespw.unpack(GetMassAccountRoutingInfoResponse.class);

        for (AccountRoutingInfo routing : sresp.getRoutingList()) {
            var byAccountId = uam.api.v1.IdentityByAccountId.newBuilder()
                    .setAccountId(routing.getId())
                    .setRemoteIp(routing.getRemoteIp());

            // ~ per player ID
            Hasher hasher = Hashing.murmur3_128().newHasher();
            hasher.putString(routing.getBrand(), StandardCharsets.UTF_8);
            hasher.putLong(routing.getId());
            hasher.putString(request.getSessionId(), StandardCharsets.UTF_8);
            String id = hasher.hash().toString();

            var ramreqb = uam.api.v1.RewardAccountManuallyRequest.newBuilder();
            ramreqb.setMode(request.getMode());
            ramreqb.setIdentity(uam.api.v1.Identity.newBuilder().setByAccountId(byAccountId));
            ramreqb.setSessionId(id);
            ramreqb.setSweepstakeAmount(request.getSweepstakeAmount().toString());
            ramreqb.setGoldAmount(request.getGoldAmount().toString());
            if (StringUtils.isNotEmpty(request.getRewardCampaignCode())) {
                ramreqb.setCampaignCode(request.getRewardCampaignCode());
            }
            if (StringUtils.isNotEmpty(request.getCreditorName())) {
                ramreqb.setCreditorName(request.getCreditorName());
            }
            if (StringUtils.isNotEmpty(request.getEmailCode())) {
                ramreqb.setEmailCode(request.getEmailCode());
            }

            routingMap.put(ramreqb, AccountRoutingFlatten.standard(AsciiString.cached(routing.getHash()), routing.getId()));
        }
    }

    private void processRoutingInfo(AsyncResponse async, AdminHeader header,
                                    Map<uam.api.v1.RewardAccountManuallyRequest.Builder, AccountRoutingFlatten> routingMap,
                                    MassRewardRequest request) {
        Flux.fromIterable(routingMap.entrySet())
                .buffer(4)
                .subscribe(t -> {
                    CountDownLatch latch = new CountDownLatch(t.size());
                    Set<Long> applied = Sets.newConcurrentHashSet();
                    Set<Long> notApplied = Sets.newConcurrentHashSet();
                    AtomicLong counter = new AtomicLong();

                    for (Map.Entry<uam.api.v1.RewardAccountManuallyRequest.Builder, AccountRoutingFlatten> entry : t) {
                        processRequest(header, entry, latch, counter, applied, notApplied);
                    }

                    try {
                        latch.await(60, TimeUnit.SECONDS);
                        var message = String.format( "Mass reward manually of %d (applied: %d, not affected by %s: %s) (out of %d) users by: %s sweepstakes and %s golds. mode: %s creditor: %s campaign: %s",
                                counter.get(),
                                applied.size(),
                                request.getSessionId(),
                                notApplied,
                                t.size(),
                                request.getSweepstakeAmount(),
                                request.getGoldAmount(),
                                request.getMode().name(),
                                request.getCreditorName(),
                                request.getRewardCampaignCode());

                        MassRewardResponse response = new MassRewardResponse();
                        response.setMessage(message);

                        async.resume(Response.status(Response.Status.OK).entity(response).build());

                    } catch (Exception err) {
                        log.error(err.getMessage(), err);
                        handleFailure(async, err);
                    }
                });
    }

    private void processRequest(AdminHeader header,
                                Map.Entry<uam.api.v1.RewardAccountManuallyRequest.Builder, AccountRoutingFlatten> entry,
                                CountDownLatch latch, AtomicLong counter, Set<Long> applied, Set<Long> notApplied) {
        var request = entry.getKey();
        var flatten = entry.getValue();

        var wrapped = uamServiceApi.rewardAccountManually(request.build(), flatten.getRoutingKey());
        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws IOException {
                processResponse(srespw, counter, applied, request, flatten, header, notApplied, latch);
            }
        }, MoreExecutors.directExecutor());
    }

    private void processResponse(ResponseWrapperFacade srespw, AtomicLong counter, Set<Long> applied, RewardAccountManuallyRequest.Builder request,
                                 AccountRoutingFlatten flatten, AdminHeader header, Set<Long> notApplied, CountDownLatch latch) throws IOException {
        try {
            var status = srespw.status();

            if (status.isOK()) {
                var resp = srespw.unpack(uam.api.v1.RewardAccountManuallyResponse.class);
                counter.incrementAndGet();
                if (resp.getApplied()) {
                    applied.add(request.getIdentity().getByAccountId().getAccountId());

                    var message = String.format(
                            "Reward manually account %s by: %s sweepstakes and %s golds. mode: %s. creditor: %s. campaign: %s",
                            flatten.getAccountId(),
                            request.getSweepstakeAmount(), request.getGoldAmount(),
                            request.getMode().name(), request.getCreditorName(), request.getCampaignCode());
                    saveAccountEvent(AccountEventType.ACCOUNT_REWARD_MASS,
                            header.getUserEmail(),
                            message,
                            flatten.getRoutingKey(),
                            request.getIdentity());
                } else {
                    notApplied.add(request.getIdentity().getByAccountId().getAccountId());
                }
            } else {
                var message = String.format("Unable to reward account: %d reason: %s",
                        request.getIdentity().getByAccountId().getAccountId(),
                        status.errorText());
                log.error(message);
            }
        } finally {
            latch.countDown();
        }
    }
}
