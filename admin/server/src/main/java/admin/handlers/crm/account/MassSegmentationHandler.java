package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.MassSegmentationRequest;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import uam.api.UamServiceApi;
import uam.api.v1.*;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class MassSegmentationHandler extends AbstractCrmAdminHandler<MassSegmentationRequest> {

    @Inject
    public MassSegmentationHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassSegmentationRequest request) throws Exception {
        async.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        async.setTimeoutHandler(ar -> {
            log.warn("Mass segmentation request timed out");
            ar.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(new AdminErrorResponse("timeout", "Request timed out"))
                    .build());
        });

        // Sanitize and prepare IDs
        List<String> sanitizedIds = sanitize(request.getIds()).stream()
                .map(String::toLowerCase)
                .toList();

        if (sanitizedIds.isEmpty()) {
            log.warn("No valid account IDs provided for status update");
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_BAD_REQUEST.name(), "No valid account IDs provided"))
                    .build());
            return;
        }

        // Initialize counters
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);

        Flux.fromIterable(sanitizedIds)
                .buffer(BUFFER_MAX_SIZE) // Batch processing of 4 IDs at a time
                .flatMap(batch -> Flux.fromIterable(batch)
                        .flatMap(id -> getAccountRoutingInfoReactive(id, header.getBrand())
                                .flatMap(pair -> processSingleSegmentation(header, request, id, pair.getT1(), pair.getT2(), processedCount, failedCount))
                                .onErrorResume(e -> {
                                    log.error("Failed to segment account {}: {}", id, e.getMessage());
                                    failedCount.incrementAndGet();
                                    return Mono.empty();
                                })))
                .subscribeOn(Schedulers.fromExecutor(executor))
                .collectList()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .doOnSuccess(list -> {
                    // Completed successfully

                    var eventMessage = "Mass segmentation completed: [ %s ] accounts processed, [ %s ] accounts failed"
                            .formatted(processedCount.get(), failedCount.get());

                    log.info(eventMessage);

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.MASS_ACCOUNT_SEGMENTATION,
                            BackofficeEventType.UPDATE, request);

                    async.resume(Response.ok(new AdminOkResponse()).build());
                })
                .doOnError(throwable -> {
                    // Global pipeline error
                    log.error("Mass segmentation encountered errors: {} accounts processed, {} accounts failed",
                            processedCount.get(), failedCount.get());
                    if (throwable instanceof TimeoutException) {
                        log.warn("Mass segmentation request timed out");
                        async.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                                .entity(new AdminErrorResponse("timeout", "Request timed out"))
                                .build());
                    } else {
                        logErrorAndResume(async, throwable);
                    }
                })
                .subscribe();
    }

    /**
     * Wraps the callback-based getAccountRoutingInfo method into a Mono,
     * enabling integration into a reactive pipeline.
     */
    private Mono<reactor.util.function.Tuple2<Identity, AsciiString>> getAccountRoutingInfoReactive(String id, String brand) {
        return Mono.create(sink -> {
            super.getAccountRoutingInfo(Long.parseLong(id), brand, null, (identity, routingKey) -> {
                if (identity != null && routingKey != null) {
                    sink.success(reactor.util.function.Tuples.of(identity, routingKey));
                } else {
                    sink.error(new RuntimeException("Account not found or routingKey null for ID " + id));
                }
            });
        });
    }

    /**
     * Processes segmentation (setting category info) for a single account.
     */
    private Mono<Void> processSingleSegmentation(AdminHeader header, MassSegmentationRequest request,
                                                 String id, Identity identity, AsciiString routingKey,
                                                 AtomicInteger processedCount, AtomicInteger failedCount) {
        return Mono.create(sink -> {
            var uamRequest = SetAccountCategoryInfoRequest.newBuilder()
                    .setIdentity(identity)
                    .setCategory(request.getCategory())
                    .addAllToAdd(request.getToAdd())
                    .addAllToRemove(request.getToRemove())
                    .build();

            var wrapped = uamServiceApi.setAccountCategoryInfo(uamRequest, routingKey);
            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {

                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {
                        log.debug("Successfully segmented account {}", id);

                        var eventMessage = String.format(
                                "Updated account %s in category: %s (tags toAdd: %s, toRemove: %s)",
                                id, request.getCategory(), request.getToAdd(), request.getToRemove()
                        );
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_SEGMENTATION_MASS, header.getUserEmail(), eventMessage, routingKey, identity);

                        processedCount.incrementAndGet();
                        sink.success();
                    } else {
                        log.error("Failed to segment account {}: {}", id, status.errorText());
                        failedCount.incrementAndGet();
                        sink.error(new RuntimeException("Failed to segment account: " + status.errorText()));
                    }
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Error segmenting account {}: {}", id, t.getMessage());
                    failedCount.incrementAndGet();
                    sink.error(t);
                }
            });
        });
    }
}
