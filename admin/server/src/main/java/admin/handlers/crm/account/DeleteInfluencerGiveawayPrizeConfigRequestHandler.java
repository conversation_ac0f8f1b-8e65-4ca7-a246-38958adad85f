package admin.handlers.crm.account;

import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.DeleteInfluencerGiveawayPrizeConfigRequest;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.RemoteAdminDeleteInfluencerGiveawayPrizeConfigRequest;

@Service
@Slf4j
public class DeleteInfluencerGiveawayPrizeConfigRequestHandler extends AbstractCrmAdminHandler<DeleteInfluencerGiveawayPrizeConfigRequest> {

    @Inject
    public DeleteInfluencerGiveawayPrizeConfigRequestHandler(CrmAdminServerProperties props,
                                                             DynamicCloud cloud,
                                                             MeterRegistry meterRegistry,
                                                             UamServiceApi uamServiceApi,
                                                             ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, DeleteInfluencerGiveawayPrizeConfigRequest request) throws Exception {

        var uamRequest = toUamRequest(request);
        var wrapped = uamServiceApi.remoteAdminDeleteInfluencerGiveawayPrizeConfig(uamRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    log.info("[{}] Influencer Giveaway Prize Config removed successfully", request.getConfigId());
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText())).build());
                    log.warn(status.errorText());
                }
            }
        });
    }

    private RemoteAdminDeleteInfluencerGiveawayPrizeConfigRequest toUamRequest(DeleteInfluencerGiveawayPrizeConfigRequest request) {
        var uamRequest = RemoteAdminDeleteInfluencerGiveawayPrizeConfigRequest.newBuilder();
        uamRequest.setConfigId(request.getConfigId());
        return uamRequest.build();
    }
}
