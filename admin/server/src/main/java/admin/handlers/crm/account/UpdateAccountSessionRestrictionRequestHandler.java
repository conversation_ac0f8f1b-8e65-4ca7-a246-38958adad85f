package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.UpdateAccountSessionRestrictionRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.Identity;
import uam.api.v1.RemoteAdminUpdateAccountSessionRestrictionRequest;

@Service
public class UpdateAccountSessionRestrictionRequestHandler extends AbstractCrmAdminHandler<UpdateAccountSessionRestrictionRequest> {

    protected UpdateAccountSessionRestrictionRequestHandler(CrmAdminServerProperties props,
                                                            DynamicCloud cloud,
                                                            MeterRegistry meterRegistry,
                                                            UamServiceApi uamServiceApi,
                                                            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateAccountSessionRestrictionRequest request) throws Exception {
        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {
            RemoteAdminUpdateAccountSessionRestrictionRequest uamRequest = toUamRequest(request, identity);
            var response = uamServiceApi.updateAccountSessionRestriction(uamRequest, routingKey);
            response.addListener(new AbstractSafeResponseConsumer(response, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade wrapper) {
                    var status = wrapper.status();
                    if (status.isOK()) {
                        async.resume(
                                Response.status(Response.Status.OK)
                                        .entity(new AdminOkResponse())
                                        .build()
                        );
                    } else {
                        async.resume(
                                Response.status(Response.Status.BAD_REQUEST)
                                        .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                                        .build()
                        );
                    }
                }
            });
        });
    }

    private static RemoteAdminUpdateAccountSessionRestrictionRequest toUamRequest(UpdateAccountSessionRestrictionRequest request, Identity identity) {
        return RemoteAdminUpdateAccountSessionRestrictionRequest.newBuilder()
                .setIdentity(identity)
                .setId(request.getId())
                .setStartAt(request.getStartAt().toInstant().toEpochMilli())
                .setEndAt(request.getEndAt().toInstant().toEpochMilli())
                .setReason(request.getReason())
                .build();
    }
}
