package admin.handlers.crm.account;

import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.CreateAccountSessionRestrictionRequest;
import api.v1.ApiFactory;
import common.utils.ProtoUtils;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.AccountSessionRestrictionType;
import uam.api.v1.Identity;
import uam.api.v1.RemoteAdminCreateAccountSessionRestrictionRequest;

@Service
public class CreateAccountSessionRestrictionRequestHandler extends AbstractCrmAdminHandler<CreateAccountSessionRestrictionRequest> {

    protected CreateAccountSessionRestrictionRequestHandler(CrmAdminServerProperties props,
                                                            DynamicCloud cloud,
                                                            MeterRegistry meterRegistry,
                                                            UamServiceApi uamServiceApi,
                                                            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateAccountSessionRestrictionRequest request) throws Exception {
        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {
            RemoteAdminCreateAccountSessionRestrictionRequest uamRequest = toUamRequest(request, identity, header.getUserEmail());
            var response = uamServiceApi.createAccountSessionRestriction(uamRequest, routingKey);
            response.addListener(new AbstractSafeResponseConsumer(response, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade wrapper) {
                    var status = wrapper.status();
                    if (status.isOK()) {
                        String message = createMessage(request);
                        saveAccountEvent(AccountEventType.ACCOUNT_SESSION_RESTRICTION, header.getUserEmail(), message, routingKey, identity);
                        async.resume(
                                Response.status(Response.Status.OK)
                                        .entity(new AdminOkResponse())
                                        .build()
                        );
                    } else {
                        async.resume(
                                Response.status(Response.Status.BAD_REQUEST)
                                        .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                                        .build()
                        );
                    }
                }
            });
        });
    }

    private static RemoteAdminCreateAccountSessionRestrictionRequest toUamRequest(CreateAccountSessionRestrictionRequest request, Identity identity, String createdBy) {
        RemoteAdminCreateAccountSessionRestrictionRequest.Builder remoteRequest = RemoteAdminCreateAccountSessionRestrictionRequest.newBuilder()
                .setIdentity(identity)
                .setType(AccountSessionRestrictionType.valueOf(request.getType().name()))
                .setStartAt(request.getStartAt().toInstant().toEpochMilli())
                .setEndAt(request.getEndAt().toInstant().toEpochMilli())
                .setCreatedBy(createdBy);
        ProtoUtils.applyIfNotEmpty(request.getReason(), remoteRequest::setReason);

        return remoteRequest.build();
    }

    private static String createMessage(CreateAccountSessionRestrictionRequest request) {
        return "Created account session restriction with type: %s, startAt: %s and endAt: %s"
                .formatted(request.getType(), request.getStartAt(), request.getEndAt());
    }
}
