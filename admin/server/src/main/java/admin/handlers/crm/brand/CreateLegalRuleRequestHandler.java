package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.CreateLegalRuleRequest;
import api.v1.ApiFactory;
import api.v1.CommonMappers;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.RuleSetting;
import uam.api.v1.RuleType;

import java.util.Optional;

@Service
@Slf4j
public class CreateLegalRuleRequestHandler extends AbstractCrmAdminHandler<CreateLegalRuleRequest> {

    public CreateLegalRuleRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateLegalRuleRequest request) throws Exception {

        var builder = uam.api.v1.CreateLegalRuleRequest.newBuilder();

        Optional.ofNullable(request.getText()).ifPresent(builder::setText);

        uam.api.v1.CreateLegalRuleRequest createLegalRuleRequest = builder
                .setBrand(header.getBrand())
                .setRuleVersion(request.getRuleVersion())
                .setSetting(RuleSetting.valueOf(request.getSetting()))
                .setVersionDate(CommonMappers.toDate(request.getVersionDate()))
                .setType(RuleType.valueOf(request.getType()))
                .setGoldOnly(request.getGoldOnly())
                .setCountry(request.getCountry())
                .build();

        log.debug("A new legal rule is being created: {} by user: {}", request, header.getUserEmail());

        var wrapped = uamServiceApi.createLegalRule(createLegalRuleRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {

                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "Legal Rule [ %s ] is Created Successfully"
                            .formatted(request.getReference());
                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_LEGAL_RULE,
                            BackofficeEventType.CREATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }
}
