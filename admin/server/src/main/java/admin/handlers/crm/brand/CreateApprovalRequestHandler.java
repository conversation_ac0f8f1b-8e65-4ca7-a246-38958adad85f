package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.ApprovalActionRequest;
import admin.models.account.ApprovalActionResponse;
import admin.models.account.enums.ApprovalFlowStatusSpec;
import api.v1.ApiFactory;
import com.google.common.base.Strings;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.RemoteAdminSubmitApprovalRequest;
import uam.api.v1.RemoteAdminSubmitApprovalResponse;

import java.io.IOException;
import java.time.ZoneOffset;

@Service
@Slf4j
public class CreateApprovalRequestHandler extends AbstractCrmAdminHandler<ApprovalActionRequest> {

    public CreateApprovalRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry, UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, ApprovalActionRequest request) throws Exception {

        var approvalRequest = mapRequest(header, request);

        var wrapped = uamServiceApi.createApproval(approvalRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws IOException {
                var status = srespw.status();
                if (status.isOK()) {

                    RemoteAdminSubmitApprovalResponse response = srespw.unpack(RemoteAdminSubmitApprovalResponse.class);

                    async.resume(Response.status(Response.Status.OK)
                            .entity(ApprovalActionResponse.builder().requestId(response.getRequestId()).build()).build());
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static RemoteAdminSubmitApprovalRequest mapRequest(AdminHeader header,
                                                        ApprovalActionRequest request) {
        var approvalRequest = RemoteAdminSubmitApprovalRequest.newBuilder();
        approvalRequest.setBrandName(header.getBrand());
        approvalRequest.addAllReviewers(request.getReviewers());
        approvalRequest.setRequestedBy(header.getUserEmail());
        approvalRequest.setRequestJson(request.getRequestJson());
        approvalRequest.setAccountCount(request.getAccountCount());
        approvalRequest.setOperationType(request.getOperationType());
        approvalRequest.setStatus(ApprovalFlowStatusSpec.PENDING.name());

        if (!Strings.isNullOrEmpty(request.getChangeSummary())) {
            approvalRequest.setChangeSummary(request.getChangeSummary());
        }

        if (!Strings.isNullOrEmpty(request.getComment())) {
            approvalRequest.setComment(request.getComment());
        }

        if (request.getExpireAt() != null) {
            approvalRequest.setExpireAt(request.getExpireAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        }

        return approvalRequest.build();
    }
}
