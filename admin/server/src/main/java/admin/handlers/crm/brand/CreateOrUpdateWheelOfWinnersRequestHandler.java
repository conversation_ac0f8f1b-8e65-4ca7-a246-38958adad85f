package admin.handlers.crm.brand;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.CreateOrUpdateWheelOfWinnersRequest;
import admin.models.brand.CreateOrUpdateWheelOfWinnersResponse;
import api.v1.ApiFactory;
import api.v1.CommonMappers;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.FreeSpins;
import uam.api.v1.GoldSweepstakeCoins;
import uam.api.v1.PrizeDraw;
import uam.api.v1.RemoteAdminCreateOrUpdateWheelOfWinnersRequest;
import uam.api.v1.RemoteAdminWheelOfWinnersSection;
import uam.api.v1.WheelOfWinnersSectionTypeSpec;

@Service
@Slf4j
public class CreateOrUpdateWheelOfWinnersRequestHandler extends AbstractCrmAdminHandler<CreateOrUpdateWheelOfWinnersRequest> {

    public CreateOrUpdateWheelOfWinnersRequestHandler(CrmAdminServerProperties props,
                                                      DynamicCloud cloud,
                                                      MeterRegistry meterRegistry,
                                                      UamServiceApi uamServiceApi,
                                                      ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateOrUpdateWheelOfWinnersRequest request) throws Exception {

        var uamRequest = toUamRequest(header, request);
        var wrapped = uamServiceApi.remoteAdminCreateOrUpdateWheelOfWinners(uamRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws IOException {
                var status = srespw.status();
                if (status.isOK()) {

                    var resp = srespw.unpack(uam.api.v1.RemoteAdminCreateOrUpdateWheelOfWinnersResponse.class);

                    async.resume(Response.ok().entity(new CreateOrUpdateWheelOfWinnersResponse(resp.hasCreatedId() ? resp.getCreatedId() : null)).build());

                    var eventMessage = "Wheel Of Winners created/updated successfully. %s"
                            .formatted(ReflectionToStringBuilder.toString(request, ToStringStyle.MULTI_LINE_STYLE, false, false, true, null));

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_WHEEL_OF_WINNERS,
                            (Objects.isNull(request.getId()) ? BackofficeEventType.CREATE : BackofficeEventType.UPDATE), request);

                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static RemoteAdminCreateOrUpdateWheelOfWinnersRequest toUamRequest(final AdminHeader header, final CreateOrUpdateWheelOfWinnersRequest request) {

        var build = RemoteAdminCreateOrUpdateWheelOfWinnersRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setCode(request.getCode())
                .setActive(request.getActive())
                .setHours(request.getHours())
                .setEndDate(CommonMappers.toDate(request.getEndDate()));

        if (!Objects.isNull(request.getId())) {
            build.setId(request.getId());
        }

        if (!Objects.isNull(request.getSections()) && !request.getSections().isEmpty()) {
            build.addAllSections(toUamSections(request.getSections()));
        }

        if (StringUtils.isNotEmpty(request.getSegment())) {
            build.setSegment(request.getSegment());
        }
        if (!Objects.isNull(request.getSegmentTags()) && !request.getSegmentTags().isEmpty()) {
            build.addAllSegmentTags(request.getSegmentTags());
        }

        if (StringUtils.isNotEmpty(request.getExcludeSegment())) {
            build.setExcludeSegment(request.getExcludeSegment());
        }
        if (!Objects.isNull(request.getExcludeSegmentTags()) && !request.getExcludeSegmentTags().isEmpty()) {
            build.addAllExcludeSegmentTags(request.getExcludeSegmentTags());
        }

        return build.build();
    }

    private static List<RemoteAdminWheelOfWinnersSection> toUamSections(final List<CreateOrUpdateWheelOfWinnersRequest.WheelOfWinnersSection> sections) {

        var sectionList = Lists.<RemoteAdminWheelOfWinnersSection>newArrayList();

        for (var section : sections) {

            var sectionBuilder = RemoteAdminWheelOfWinnersSection.newBuilder()
                    .setCode(section.getCode())
                    .setChance(section.getChance().toString())
                    .setLabel(section.getLabel());
            Optional.ofNullable(section.getId()).ifPresent(sectionBuilder::setId);

            switch (section) {
                case CreateOrUpdateWheelOfWinnersRequest.GoldSweepstakeCoins goldSweepstakeCoins -> sectionBuilder
                        .setType(WheelOfWinnersSectionTypeSpec.GOLD_SWEEPSTAKE_COINS)
                        .setGoldSweepstakeCoins(GoldSweepstakeCoins.newBuilder()
                                .setGcAmount(goldSweepstakeCoins.getGcAmount().toString())
                                .setScAmount(goldSweepstakeCoins.getScAmount().toString())
                                .build());
                case CreateOrUpdateWheelOfWinnersRequest.FreeSpins freeSpins -> sectionBuilder
                        .setType(WheelOfWinnersSectionTypeSpec.FREE_SPINS)
                        .setFreeSpins(FreeSpins.newBuilder()
                                .setCampaignCode(freeSpins.getCampaignCode())
                                .setFallbackCampaignCode(freeSpins.getFallbackCampaignCode())
                                .build());
                case CreateOrUpdateWheelOfWinnersRequest.PrizeDraw prizeDraw -> sectionBuilder
                        .setType(WheelOfWinnersSectionTypeSpec.PRIZE_DRAW)
                        .setPrizeDraw(PrizeDraw.newBuilder()
                                .setEntries(prizeDraw.getEntries())
                                .build());
            }

            sectionList.add(sectionBuilder.build());
        }

        return sectionList;
    }
}