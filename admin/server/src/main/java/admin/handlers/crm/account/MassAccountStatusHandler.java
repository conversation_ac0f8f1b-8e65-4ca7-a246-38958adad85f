package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.MassAccountStatusRequest;
import api.v1.AccountStatusSpec;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.AccountRestriction;
import uam.api.v1.AccountStatus;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.Identity;
import uam.api.v1.SetAccountInfoRequest;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class MassAccountStatusHandler extends AbstractCrmAdminHandler<MassAccountStatusRequest> {

    private static final Map<AccountStatusSpec, uam.api.v1.AccountStatus> accountStatusMap = new HashMap<>();

    static {
        accountStatusMap.put(AccountStatusSpec.DEFAULT, uam.api.v1.AccountStatus.DEFAULT_STATUS);
        accountStatusMap.put(AccountStatusSpec.UNDER_INVESTIGATION, uam.api.v1.AccountStatus.UNDER_INVESTIGATION_STATUS);
        accountStatusMap.put(AccountStatusSpec.RESTRICTED_BY_GEO_POLICY, uam.api.v1.AccountStatus.RESTRICTED_BY_GEO_POLICY);
        accountStatusMap.put(AccountStatusSpec.PENDING_REDEEM_REVIEW, uam.api.v1.AccountStatus.PENDING_REDEEM_REVIEW);
        accountStatusMap.put(AccountStatusSpec.PENDING_VERIFICATION, uam.api.v1.AccountStatus.PENDING_VERIFICATION);
        accountStatusMap.put(AccountStatusSpec.BW_PENDING_REDEEM_REVIEW, uam.api.v1.AccountStatus.BW_PENDING_REDEEM_REVIEW);
        accountStatusMap.put(AccountStatusSpec.PENDING_PM_VERIFICATION, uam.api.v1.AccountStatus.PENDING_PM_VERIFICATION);
        accountStatusMap.put(AccountStatusSpec.PENDING_PM_BW_VERIFICATION, uam.api.v1.AccountStatus.PENDING_PM_BW_VERIFICATION);
        accountStatusMap.put(AccountStatusSpec.FOR_PAYMENT_PROCESSING, uam.api.v1.AccountStatus.FOR_PAYMENT_PROCESSING);
        accountStatusMap.put(AccountStatusSpec.FOR_NSC_PROCESSING, uam.api.v1.AccountStatus.FOR_NSC_PROCESSING);
        accountStatusMap.put(AccountStatusSpec.REQUIRES_JUMIO_KYC, uam.api.v1.AccountStatus.REQUIRES_JUMIO_KYC);
    }

    @Inject
    public MassAccountStatusHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassAccountStatusRequest request) throws Exception {
        async.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        async.setTimeoutHandler(ar -> {
            log.warn("Mass account status request timed out");
            ar.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                    .build());
        });

        List<String> sanitizedIds = sanitize(request.getIds()).stream()
                .map(String::toLowerCase)
                .toList();

        if (sanitizedIds.isEmpty()) {
            log.warn("No valid account IDs provided for status update");
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_BAD_REQUEST.name(), "No valid account IDs provided"))
                    .build());
            return;
        }

        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);

        Flux.fromIterable(sanitizedIds)
                .buffer(BUFFER_MAX_SIZE)
                .flatMap(batchIds ->
                                Flux.fromIterable(batchIds)
                                        .flatMap(id -> getAccountRoutingInfoReactive(id, header.getBrand(), async)
                                                .flatMap(pair -> processSingleAccountStatus(header, request, id, pair.getT1(), pair.getT2(), processedCount, failedCount))
                                                .onErrorResume(e -> {
                                                    // Log individual account failure
                                                    log.error("Failed to update status for account {}: {}", id, e.getMessage());
                                                    failedCount.incrementAndGet();
                                                    return Mono.empty(); // Continue processing other accounts
                                                })))
                .subscribeOn(Schedulers.fromExecutor(executor))
                .collectList()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .doOnSuccess(list -> {

                    var eventMessage = "Mass account status update completed: [ %s ] accounts processed, [ %s ] accounts failed."
                            .formatted(processedCount.get(), failedCount.get());

                    log.info(eventMessage);

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.MASS_ACCOUNT_STATUS,
                            BackofficeEventType.UPDATE, request);

                    async.resume(Response.ok(new AdminOkResponse()).build());
                })
                .doOnError(throwable -> {
                    log.error("Mass account status update encountered errors: {} accounts processed, {} accounts failed",
                            processedCount.get(), failedCount.get());
                    if (throwable instanceof TimeoutException) {
                        log.warn("Mass account status request timed out");
                        async.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                                .build());
                    } else {
                        logErrorAndResume(async, throwable);
                    }
                })
                .subscribe();
    }

    /**
     * Reactive wrapper for getAccountRoutingInfo that returns a Mono of Tuple2<Identity, AsciiString>.
     * Uses a custom AsyncResponse to capture success/failure.
     */
    private Mono<Tuple2<Identity, AsciiString>> getAccountRoutingInfoReactive(String id, String brand, AsyncResponse async) {
        return Mono.create(sink ->
                getAccountRoutingInfo(Long.parseLong(id), brand, async, (identity, routingKey) -> {
                    if (identity != null && routingKey != null) {
                        sink.success(Tuples.of(identity, routingKey));
                    } else {
                        sink.error(new RuntimeException("Failed to retrieve routing info for account ID: " + id));
                    }
                })
        );
    }

    private Mono<Void> processSingleAccountStatus(AdminHeader header, MassAccountStatusRequest request,
                                                  String id, Identity identity, AsciiString routingKey,
                                                  AtomicInteger processedCount,
                                                  AtomicInteger failedCount) {
        return Mono.create(sink -> {
            AccountStatus status = getAccountStatus(request.getStatus());
            SetAccountInfoRequest setInfoRequest = SetAccountInfoRequest.newBuilder()
                    .setIdentity(identity)
                    .setInfo(uam.api.v1.AccountInfo.newBuilder()
                            .setAccountStatus(status)
                            .addRestrictions(AccountRestriction.DO_NOT_CHANGE))
                    .build();
            var wrapped = uamServiceApi.setAccountInfo(setInfoRequest, routingKey);
            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var responseStatus = srespw.status();
                    if (responseStatus.isOK()) {
                        log.debug("Successfully updated status for account {}", id);
                        String eventMessage = String.format("Account Status Info has been changed: %s, status: %s", id, request.getStatus().name());

                        saveAccountEvent(AccountEventType.SET_ACCOUNT_INFO, header.getUserEmail(), eventMessage, routingKey, identity);
                        processedCount.incrementAndGet();
                        sink.success();
                    } else {
                        log.error("Failed to update status for account {}: {}", id, responseStatus.errorText());
                        failedCount.incrementAndGet();
                        sink.error(new RuntimeException("Failed to update account status: " + responseStatus.errorText()));
                    }
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Error updating status for account {}: {}", id, t.getMessage());
                    failedCount.incrementAndGet();
                    sink.error(t);
                }
            });
        });
    }

    protected static uam.api.v1.AccountStatus getAccountStatus(AccountStatusSpec accountStatus) {
        if (accountStatus == null) {
            return uam.api.v1.AccountStatus.DO_NOT_CHANGE_STATUS;
        }

        uam.api.v1.AccountStatus status = accountStatusMap.get(accountStatus);
        return status != null ? status : uam.api.v1.AccountStatus.DO_NOT_CHANGE_STATUS;
    }
}
