package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.MassAccountTagRequest;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import com.turbospaces.rpc.ApiResponse;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import uam.api.UamServiceApi;
import uam.api.v1.*;
import uam.model.AccountTagSpec;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class MassAccountTagHandler extends AbstractCrmAdminHandler<MassAccountTagRequest> {

    public static final List<AccountTagSpec> PERMANENT_TAG_LIST = List.of(AccountTagSpec.ADVANTAGE_PLAY);

    @Inject
    public MassAccountTagHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassAccountTagRequest request) throws Exception {
        async.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        async.setTimeoutHandler(ar -> {
            log.warn("Mass account tag request timed out");
            ar.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                    .build());
        });

        // Sanitize and prepare IDs
        List<String> sanitizedIds = sanitize(request.getIds()).stream()
                .map(String::toLowerCase)
                .toList();

        if (sanitizedIds.isEmpty()) {
            log.warn("No valid account IDs provided for status update");
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_BAD_REQUEST.name(), "No valid account IDs provided"))
                    .build());
            return;
        }

        // Initialize counters for successes and failures
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);

        // Process the IDs in a reactive pipeline
        Flux.fromIterable(sanitizedIds)
                .buffer(BUFFER_MAX_SIZE) // Batch IDs in groups of 4
                .flatMap(batch -> Flux.fromIterable(batch)
                                .flatMap(id -> getAccountRoutingInfoReactive(id, header.getBrand())
                                        .flatMap(pair -> processTagging(header, request, id, pair.getT1(), pair.getT2(), processedCount, failedCount))
                                        .onErrorResume(e -> {
                                            log.error("Failed to tag account {}: {}", id, e.getMessage());
                                            failedCount.incrementAndGet();
                                            return Mono.empty();
                                        })))
                .subscribeOn(Schedulers.fromExecutor(executor))
                .collectList()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .doOnSuccess(list -> {
                    // Once processing completes successfully

                    var eventMessage = "Mass account tagging completed: [ %s ] accounts processed, [ %s ] accounts failed"
                            .formatted(processedCount, failedCount.get());

                    log.info(eventMessage);

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.MASS_ACCOUNT_TAG,
                            BackofficeEventType.UPDATE, request);

                    async.resume(Response.ok(new AdminOkResponse()).build());
                })
                .doOnError(throwable -> {
                    // Handle global errors
                    log.error("Mass account tagging encountered errors: {} accounts processed, {} accounts failed",
                            processedCount.get(), failedCount.get());
                    if (throwable instanceof TimeoutException) {
                        log.warn("Mass account tag request timed out");
                        async.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                                .build());
                    } else {
                        logErrorAndResume(async, throwable);
                    }
                })
                .subscribe();
    }

    /**
     * Wraps the callback-based getAccountRoutingInfo method into a reactive Mono.
     *
     * @param id    The account ID to look up.
     * @param brand The brand.
     * @return A Mono of Tuple2<Identity, AsciiString> containing identity and routing key.
     */
    private Mono<reactor.util.function.Tuple2<Identity, AsciiString>> getAccountRoutingInfoReactive(String id, String brand) {
        return Mono.create(sink -> {
            // Reuse the existing getAccountRoutingInfo method, but do not call async.resume here
            super.getAccountRoutingInfo(Long.parseLong(id), brand, null, (identity, routingKey) -> {
                if (identity != null && routingKey != null) {
                    sink.success(reactor.util.function.Tuples.of(identity, routingKey));
                } else {
                    sink.error(new RuntimeException("Account routing info not found for ID " + id));
                }
            });
        });
    }

    /**
     * Processes the tagging for a single account.
     *
     * @param header         The admin header.
     * @param request        The MassAccountTagRequest containing the tag info.
     * @param id             The account ID.
     * @param identity       The Identity object for the account.
     * @param routingKey     The AsciiString routing key for the account.
     * @param processedCount An AtomicInteger tracking successful account operations.
     * @param failedCount    An AtomicInteger tracking failed account operations.
     * @return A Mono<Void> that completes when the tagging operation finishes.
     */
    private Mono<Void> processTagging(AdminHeader header, MassAccountTagRequest request,
                                      String id, Identity identity, AsciiString routingKey,
                                      AtomicInteger processedCount, AtomicInteger failedCount) {
        return Mono.create(sink -> {
            SetMassAccountTagRequest.Builder uamRequest = SetMassAccountTagRequest.newBuilder()
                    .setIdentity(identity)
                    .addAllAccountTags(getAccountTags(request, header.getUserEmail()))
                    .setAction(TagAction.valueOf(request.getAction().name()));

            ApiResponse<?> wrapped = uamServiceApi.setAccountTags(uamRequest.build(), routingKey);
            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {
                        log.debug("Successfully tagged account {}", id);

                        var eventMessage = String.format("Account Tags have been changed: %s",
                                toJsonString(request));
                        // Ensure correct parameter order for saveAccountEvent
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_TAG, header.getUserEmail(), eventMessage, routingKey, identity);

                        processedCount.incrementAndGet();
                        sink.success();
                    } else {
                        log.error("Failed to tag account {}: {}", id, status.errorText());
                        failedCount.incrementAndGet();
                        sink.error(new RuntimeException("Failed to tag account: " + status.errorText()));
                    }
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Error tagging account {}: {}", id, t.getMessage());
                    failedCount.incrementAndGet();
                    sink.error(t);
                }
            });
        });
    }

    /**
     * Builds the list of AccountTag objects for the given MassAccountTagRequest.
     */
    private static List<AccountTag> getAccountTags(MassAccountTagRequest request, String username) {
        return request.getTags().stream()
                .map(tag -> {
                    var accountTag = AccountTag.newBuilder()
                            .setType(tag.code())
                            .setCreatedBy(username);
                    Optional.ofNullable(request.getExpiration())
                            .ifPresent(exp -> setExpireAfter(accountTag, exp.duration()));
                    return accountTag.build();
                }).toList();
    }

    /**
     * Sets the expireAfter field unless the tag is permanent (in PERMANENT_TAG_LIST).
     */
    private static void setExpireAfter(AccountTag.Builder accountTag, long duration) {
        if (!PERMANENT_TAG_LIST.contains(AccountTagSpec.fromString(accountTag.getType()))) {
            accountTag.setExpireAfter(duration);
        }
    }
}