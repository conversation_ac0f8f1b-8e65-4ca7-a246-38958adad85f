package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetAccountGrantRewardsRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.RewardAccountManuallyRequest;
import uam.api.v1.RewardMode;

import java.util.Objects;

@Service
@Slf4j
public class SetAccountGrantRewardsRequestHandler extends AbstractCrmAdminHandler<SetAccountGrantRewardsRequest> {
    @Inject
    public SetAccountGrantRewardsRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetAccountGrantRewardsRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request, header)
                    .setIdentity(identity).build();

            var wrapped = uamServiceApi.rewardAccountManually(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();

                    if (status.isOK()) {

                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var fiat = Objects.requireNonNullElse(request.getFiatAmount(), 0);

                        if (Objects.equals(fiat, 0)) {
                            var eventMessage = "Manually Rewarded. Sweepstake: %s (redeemable: %s), Gold Coins: %s. Mode: %s. Reward Reason: %s. Department: %s."
                                    .formatted(
                                            request.getSweepstakeAmount(),
                                            request.isRedeemable(),
                                            request.getGoldAmount(),
                                            request.getMode().name(),
                                            request.getCampaignCode(),
                                            request.getCreditorName()
                                    );
                            saveAccountEvent(AccountEventType.ACCOUNT_REWARD, header.getUserEmail(), eventMessage, routingKey, identity);
                            return;
                        }

                        var eventMessage = "Manually Rewarded. Fiat: %s (redeemable: %s). Mode: %s. Reward Reason: %s. Department: %s."
                                .formatted(
                                        fiat,
                                        request.isRedeemable(),
                                        request.getMode().name(),
                                        request.getCampaignCode(),
                                        request.getCreditorName());

                        saveAccountEvent(AccountEventType.ACCOUNT_REWARD, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())).build());

                        log.error(status.errorText());
                    }
                }
            });

        });
    }

    private static RewardAccountManuallyRequest.Builder toUamRequest(SetAccountGrantRewardsRequest request, AdminHeader header) {

        var builder = RewardAccountManuallyRequest.newBuilder()
                .setSessionId(header.getMessageId().toString())
                .setMode(request.getMode())
                .setGoldAmount(request.getGoldAmount().toString())
                .setSweepstakeAmount(request.getSweepstakeAmount().toString())
                .setCampaignCode(request.getCampaignCode())
                .setCreditorName(request.getCreditorName());

        if (request.getMode() != RewardMode.IMMEDIATE) {
            builder.setRedeemable(false);
        } else {
            builder.setRedeemable(request.isRedeemable());
        }

        if (!Objects.isNull(request.getFiatAmount())) {
            builder.setFiatAmount(request.getFiatAmount().toString());
        }

        return builder;
    }
}
