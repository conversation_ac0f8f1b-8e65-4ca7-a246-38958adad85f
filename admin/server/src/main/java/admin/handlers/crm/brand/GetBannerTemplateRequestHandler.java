package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.GetBannerTemplateRequest;
import admin.models.brand.GetBannerTemplateResponse;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BannerTemplateAdminInfo;
import uam.api.v1.RemoteAdminGetBannerTemplateRequest;
import uam.api.v1.RemoteAdminGetBannerTemplateResponse;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Service
@Slf4j
public class GetBannerTemplateRequestHandler extends AbstractCrmAdminHandler<GetBannerTemplateRequest> {

    public GetBannerTemplateRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry, UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, GetBannerTemplateRequest request) throws Exception {

        var req = RemoteAdminGetBannerTemplateRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setCode(request.getCode())
                .build();

        var wrapped = uamServiceApi.remoteAdminGetBannerTemplate(req, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {

            @Override
            public void onFailure(Throwable t) {
                super.onFailure(t);
                async.resume(Response.status(Response.Status.BAD_REQUEST)
                        .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), t.getMessage()))
                        .build());
            }

            @Override
            public void accept(ResponseWrapperFacade srespw) throws Throwable {

                var resp = srespw.unpack(RemoteAdminGetBannerTemplateResponse.class);

                var status = srespw.status();
                if (status.isOK()) {
                    if (!resp.hasBanner()) {
                        async.resume(Response.status(Response.Status.OK).entity(JsonNodeFactory.instance.objectNode()).build());
                    } else {
                        async.resume(Response.status(Response.Status.OK).entity(toResponse(resp.getBanner())).build());
                    }
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                            .build());
                    log.error(status.errorText());
                }

            }
        });
    }

    private static GetBannerTemplateResponse toResponse(final BannerTemplateAdminInfo banner) {

        var builder = GetBannerTemplateResponse.builder()
                .code(banner.getCode())
                .platform(banner.getPlatform())
                .active(banner.getActive())
                .title(banner.getTitle())
                .segment(banner.getSegment())
                .excludeSegment(banner.getExcludeSegment())
                .priority(banner.getPriority())
                .segmentTags(banner.getSegmentTagsList())
                .excludeSegmentTags(banner.getExcludeSegmentTagsList())
                .bannerImageUrl(banner.getBannerImageUrl());

        if (banner.getStartAt() > 0) {
            builder.startAt(LocalDateTime.ofInstant(new Date(banner.getStartAt()).toInstant(), ZoneId.systemDefault()));
        }

        if (banner.getEndAt() > 0) {
            builder.endAt(LocalDateTime.ofInstant(new Date(banner.getEndAt()).toInstant(), ZoneId.systemDefault()));
        }

        if (StringUtils.isNotEmpty(banner.getCtaUrl())) {
            builder.ctaUrl(banner.getCtaUrl());
        }

        return builder.build();
    }
}
