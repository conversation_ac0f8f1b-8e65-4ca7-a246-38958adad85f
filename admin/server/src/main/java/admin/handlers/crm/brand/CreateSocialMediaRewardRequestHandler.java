package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.CreateSocialMediaRewardRequest;
import api.v1.ApiFactory;
import api.v1.ProductModeSpec;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;

import java.math.BigDecimal;

@Service
@Slf4j
public class CreateSocialMediaRewardRequestHandler extends AbstractCrmAdminHandler<CreateSocialMediaRewardRequest> {

    public CreateSocialMediaRewardRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateSocialMediaRewardRequest request) throws Exception {

        var req = createRequest(header, request);

        var error = validate(req);

        if (StringUtils.isNotEmpty(error)) {
            async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                    new AdminErrorResponse(Response.Status.BAD_REQUEST.toString(), error)).build());
        }

        executor.submit((CheckedRunnable) () -> {
            for (int i = 0; i < request.getQuantity(); i++) {
                var wrapped = uamServiceApi.createSocialMediaReward(req, AsciiString.cached(header.getBrand()));
                var response = wrapped.get();
                if (response.status().isOK()) {

                    var message = String.format("Created social reward url: %s", response.unpack().getUrl());
                    sendEvent(header, message, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_SOCIAL_MEDIA_REWARD,
                            BackofficeEventType.CREATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(response.status().errorCode().toString(), response.status().errorText())
                    ).build());
                    log.error(response.status().errorText());
                }
            }
            async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());
        });
    }

    private String validate(uam.api.v1.CreateSocialMediaRewardRequest request) {

        var sweepstakeMaxAmount = props.SOCIAL_MEDIA_REWARD_MAX_AMOUNT.get();

        if (!request.getScAmount().isBlank()) {
            try {
                var scAmount = new BigDecimal(request.getScAmount());
                if (scAmount.compareTo(sweepstakeMaxAmount) > 0) {
                    return String.format("Maximum %s %s can be rewarded", sweepstakeMaxAmount, ProductModeSpec.SWEEPSTAKE.code());
                }
            } catch (NumberFormatException e) {
                return "Invalid amount";
            }
        }
        return StringUtils.EMPTY;
    }

    private static uam.api.v1.CreateSocialMediaRewardRequest createRequest(AdminHeader header, CreateSocialMediaRewardRequest request) {
        return uam.api.v1.CreateSocialMediaRewardRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setSocialMedia(request.getSocialMedia())
                .setGcAmount(request.getGcAmount())
                .setScAmount(request.getScAmount())
                .build();
    }
}
