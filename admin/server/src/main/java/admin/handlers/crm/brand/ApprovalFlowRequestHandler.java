package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.ApprovalFlowRequest;
import admin.models.account.enums.ApprovalFlowStatusSpec;
import admin.models.brand.MassRemoteRewardRequest;
import api.v1.ApiFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Strings;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import com.turbospaces.json.CommonObjectMapper;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.RemoteAdminApprovalFlowRequest;

@Service
@Slf4j
public class ApprovalFlowRequestHandler extends AbstractCrmAdminHandler<ApprovalFlowRequest> {

    private final MassRemoteRewardRequestHandler massRemoteRewardRequestHandler;
    private final CommonObjectMapper objectMapper;

    public ApprovalFlowRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry, UamServiceApi uamServiceApi, ApiFactory apiFactory,
            MassRemoteRewardRequestHandler massRemoteRewardRequestHandler, CommonObjectMapper objectMapper) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
        this.massRemoteRewardRequestHandler = massRemoteRewardRequestHandler;
        this.objectMapper = objectMapper;
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, ApprovalFlowRequest request) throws Exception {

        var approvalFlowRequest = mapRequest(header, request);

        var wrapped = uamServiceApi.updateApproval(approvalFlowRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws Exception {
                var status = srespw.status();
                if (status.isOK()) {

                    if (request.getStatus() == ApprovalFlowStatusSpec.APPROVED) {

                        // 1) Parse the outer JSON
                        JsonNode root = objectMapper.readTree(request.getRequestJson());

                        // 2) Extract and normalize the "body" node
                        JsonNode bodyNode = root.path("body");
                        JsonNode realBody;
                        if (bodyNode.isTextual()) {
                            // if body is a quoted JSON string, un‐escape it
                            realBody = objectMapper.readTree(bodyNode.asText());
                        } else {
                            // body is already JSON
                            realBody = bodyNode;
                        }

                        // 3) Load the class from the "type" field
                        String typeName = root.path("type").asText(null);
                        if (typeName == null) {
                            throw new IllegalArgumentException("Missing or empty 'type' in requestJson");
                        }
                        Class<?> reqClass = Class.forName(typeName);

                        // 4) Convert the JSON body into the target class
                        Object reqObj = objectMapper.treeToValue(realBody, reqClass);

                        massRemoteRewardRequestHandler.accept(async, header, (MassRemoteRewardRequest) reqObj);
                    } else {
                        async.resume(Response.ok(new AdminOkResponse()).build());
                    }
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static RemoteAdminApprovalFlowRequest mapRequest(AdminHeader header,
                                                               ApprovalFlowRequest request) {
        var approvalRequest = RemoteAdminApprovalFlowRequest.newBuilder();
        approvalRequest.setBrandName(header.getBrand());
        approvalRequest.setRequestId(request.getId());
        approvalRequest.addAllApprovedBy(request.getApprovedBy());
        approvalRequest.addAllRejectedBy(request.getRejectedBy());
        approvalRequest.setStatus(request.getStatus().name());

        if (!Strings.isNullOrEmpty(request.getComment())) {
            approvalRequest.setChangeSummary(request.getComment());
        }

        return approvalRequest.build();
    }
}
