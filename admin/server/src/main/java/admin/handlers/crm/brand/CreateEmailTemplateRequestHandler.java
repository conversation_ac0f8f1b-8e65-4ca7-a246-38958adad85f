package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.CreateEmailTemplateRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.EmailTemplateInfo;
import uam.api.v1.SetEmailTemplateInfoRequest;

@Service
@Slf4j
public class CreateEmailTemplateRequestHandler extends AbstractCrmAdminHandler<CreateEmailTemplateRequest> {

    public CreateEmailTemplateRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry, UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateEmailTemplateRequest request) throws Exception {

        var updateRequest = mapRequest(header, request);

        var wrapped = uamServiceApi.setEmailTemplateInfo(updateRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "Email Template Code [ %s ], Description [ %s ], Created Successfully"
                            .formatted(request.getReference(), request.getDescription());
                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_EMAIL_TEMPLATE,
                            BackofficeEventType.CREATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static SetEmailTemplateInfoRequest mapRequest(AdminHeader header,
                                                          CreateEmailTemplateRequest request) {

        var emailTemplateInfo = EmailTemplateInfo.newBuilder()
                .setCode(request.getReference())
                .setProvider(request.getProvider())
                .setEmailTemplateId(request.getEmailTemplateId());

        if (StringUtils.isNotEmpty(request.getDescription())) {
            emailTemplateInfo.setDescription(request.getDescription());
        }
        if (StringUtils.isNotEmpty(request.getLocale())) {
            emailTemplateInfo.setLocale(request.getLocale());
        }

        var updateRequest = SetEmailTemplateInfoRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setEmailTemplate(emailTemplateInfo.build());
        return updateRequest.build();
    }
}
