package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.CreateInfluencerRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import api.v1.Reason;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.RemoteAdminCreateInfluencerRequest;

@Service
@Slf4j
public class CreateInfluencerRequestHandler extends AbstractCrmAdminHandler<CreateInfluencerRequest> {

    @Inject
    public CreateInfluencerRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory
    ) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateInfluencerRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request)
                    .build();

            var wrapped = uamServiceApi.remoteAdminCreateInfluencer(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {

                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Create Account as Influencer. Nickname: %s. Active: %s"
                                .formatted(
                                        request.getNickname(),
                                        request.getActive()
                                );

                        saveAccountEvent(uam.api.v1.AccountEventType.SET_ACCOUNT_INFLUENCER, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        if (status.errorReason() == Reason.UNSPECIFIED) {
                            log.error("Creating influencer failed. Message: {}, body: {}", status.errorText(), request);
                        } else {
                            log.warn("Creating influencer failed. Message: {}, body: {}", status.errorText(), request);
                        }
                    }
                }
            });
        });
    }

    private RemoteAdminCreateInfluencerRequest.Builder toUamRequest(CreateInfluencerRequest request) {
        return RemoteAdminCreateInfluencerRequest.newBuilder()
                .setAccountId(request.getAccountId())
                .setNickname(request.getNickname())
                .setActive(request.getActive());
    }
}
