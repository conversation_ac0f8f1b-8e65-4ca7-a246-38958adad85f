package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.models.brand.UpdateSocialMediaRewardRequest;
import api.v1.ApiFactory;
import api.v1.ProductModeSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;

import java.io.IOException;
import java.math.BigDecimal;

@Service
@Slf4j
public class UpdateSocialMediaRewardRequestHandler extends AbstractCrmAdminHandler<UpdateSocialMediaRewardRequest> {

    public UpdateSocialMediaRewardRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateSocialMediaRewardRequest request) throws Exception {

        var req = createRequest(header, request);

        var error = validate(req);

        if (StringUtils.isNotEmpty(error)) {
            async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                    new AdminErrorResponse(Response.Status.BAD_REQUEST.toString(), error)).build());
        } else {
            var wrapped = uamServiceApi.updateSocialMediaReward(req, AsciiString.cached(header.getBrand()));

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();
                    if (status.isOK()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var resp = srespw.unpack(uam.api.v1.UpdateSocialMediaRewardResponse.class);
                        var eventMessage = "Social Media Reward is Updated Successfully, URL [ %s ]"
                                .formatted(resp.getUrl());
                        sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_SOCIAL_MEDIA_REWARD,
                                BackofficeEventType.UPDATE, request);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        log.error(status.errorText());
                    }
                }
            });
        }
    }

    private String validate(uam.api.v1.UpdateSocialMediaRewardRequest request) {

        var sweepstakeMaxAmount = props.SOCIAL_MEDIA_REWARD_MAX_AMOUNT.get();

        if (!request.getScAmount().isBlank()) {
            try {
                var scAmount = new BigDecimal(request.getScAmount());
                if (scAmount.compareTo(sweepstakeMaxAmount) > 0) {
                    return String.format("Maximum %s %s can be rewarded", sweepstakeMaxAmount, ProductModeSpec.SWEEPSTAKE.code());
                }
            } catch (NumberFormatException e) {
                return "Invalid amount";
            }
        }
        return StringUtils.EMPTY;
    }

    private static uam.api.v1.UpdateSocialMediaRewardRequest createRequest(AdminHeader header, UpdateSocialMediaRewardRequest request) {
        return uam.api.v1.UpdateSocialMediaRewardRequest.newBuilder()
                .setCode(request.getCode())
                .setActive(request.getActive())
                .setBrandName(header.getBrand())
                .setSocialMedia(request.getSocialMedia())
                .setGcAmount(request.getGcAmount())
                .setScAmount(request.getScAmount())
                .build();
    }
}
