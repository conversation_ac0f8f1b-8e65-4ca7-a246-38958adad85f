package admin.handlers.crm.brand;

import java.util.Objects;
import java.util.function.BiConsumer;

import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.rpc.QueuePostTemplate;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.SendSystemMessageRequest;
import api.v1.ApiFactory;
import api.v1.PlainTextInfo;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.ShowSystemMessageNotification;

@Service
@Slf4j
public class SendSystemMessageRequestHandler extends AbstractCrmAdminHandler<SendSystemMessageRequest> {

    private final QueuePostTemplate<?> postTemplate;

    public SendSystemMessageRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
        this.postTemplate = postTemplate;
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SendSystemMessageRequest request) throws Exception {
        var message = request.getMessage();

        ShowSystemMessageNotification.Builder notify = ShowSystemMessageNotification.newBuilder();
        PlainTextInfo.Builder pti = PlainTextInfo.newBuilder();
        pti.setMessage(message);
        pti.setEmoji(true);
        notify.setText(pti);

        postTemplate.sendNotify(apiFactory.notificationMapper().pack(notify))
                .whenComplete((BiConsumer<Object, Throwable>) (value, err) -> {
            if (Objects.nonNull(err)) {
                async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                        new AdminErrorResponse(Response.Status.BAD_REQUEST.toString(), err.getMessage())
                ).build());

                log.error(err.getMessage());
            } else {
                async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                var eventMessage = "Send System Message [ %s ], Send Successfully"
                        .formatted(request.getMessage());
                sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.PRODUCT_CATEGORY,
                        BackofficeEventType.UPDATE, request);
            }
        });
    }
}
