package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.GetSocialMediaRewardRequest;
import admin.models.brand.GetSocialMediaRewardResponse;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;

import java.io.IOException;

@Service
@Slf4j
public class GetSocialMediaRewardRequestHandler extends AbstractCrmAdminHandler<GetSocialMediaRewardRequest> {

    public GetSocialMediaRewardRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, GetSocialMediaRewardRequest request) throws Exception {

        var req = createRequest(header, request);
        var wrapped = uamServiceApi.getSocialMediaReward(req, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws IOException {
                var status = srespw.status();
                if (status.isOK()) {
                    var resp = srespw.unpack(uam.api.v1.RemoteAdminGetSocialMediaRewardResponse.class);
                    async.resume(Response.status(Response.Status.OK).entity(createResponse(resp)).build());
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private GetSocialMediaRewardResponse createResponse(uam.api.v1.RemoteAdminGetSocialMediaRewardResponse resp) {
        return GetSocialMediaRewardResponse.builder()
                .socialMedia(resp.getSocialMedia())
                .active(resp.getActive())
                .code(resp.getCode())
                .gcAmount(resp.getGcAmount())
                .scAmount(resp.getScAmount())
                .url(resp.getUrl())
                .build();
    }

    private static uam.api.v1.RemoteAdminGetSocialMediaRewardRequest createRequest(AdminHeader header, GetSocialMediaRewardRequest request) {
        return uam.api.v1.RemoteAdminGetSocialMediaRewardRequest.newBuilder()
                .setCode(request.getCode())
                .setBrandName(header.getBrand())
                .build();
    }
}
