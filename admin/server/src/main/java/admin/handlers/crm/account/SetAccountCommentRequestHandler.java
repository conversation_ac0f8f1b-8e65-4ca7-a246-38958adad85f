package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetAccountCommentRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.RemoteAdminSetAccountCommentRequest;
import uam.api.v1.RemoteAdminSetAccountCommentResponse;

import java.io.IOException;

@Service
@Slf4j
public class SetAccountCommentRequestHandler extends AbstractCrmAdminHandler<SetAccountCommentRequest> {

    @Inject
    public SetAccountCommentRequestHandler(CrmAdminServerProperties props,
                                           DynamicCloud cloud,
                                           MeterRegistry meterRegistry,
                                           UamServiceApi uamServiceApi,
                                           ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetAccountCommentRequest request) throws Exception {
        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = RemoteAdminSetAccountCommentRequest.newBuilder()
                    .setIdentity(identity)
                    .setComment(request.getComment())
                    .setCreatedBy(header.getUserEmail())
                    .build();

            var wrapped = uamServiceApi.remoteAdminSetAccountComment(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();
                    if (status.isOK()) {
                        var resp = srespw.unpack(RemoteAdminSetAccountCommentResponse.class);

                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        if (resp.getCommentAdded()) {
                            var msg = "Account Comment been added: %s".formatted(request.getComment());
                            saveAccountEvent(AccountEventType.ADD_ACCOUNT_COMMENT, header.getUserEmail(), msg, routingKey, identity);
                        }
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());

                        log.error(status.errorText());
                    }
                }
            });
        });
    }
}
