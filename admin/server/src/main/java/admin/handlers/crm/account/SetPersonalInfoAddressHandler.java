package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetPersonalInfoAddressRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.RemoteAdminSetAccountPersonalInfoAddressRequest;
import uam.api.v1.RemoteAdminSetAccountPersonalInfoAddressResponse;

import java.io.IOException;

@Service
@Slf4j
public class SetPersonalInfoAddressHandler extends AbstractCrmAdminHandler<SetPersonalInfoAddressRequest> {
    @Inject
    public SetPersonalInfoAddressHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }
    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetPersonalInfoAddressRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(header, request).setIdentity(identity).build();

            var wrapped = uamServiceApi.remoteAdminSetAccountPersonalAddressInfo(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();
                    if (status.isOK()) {
                        var resp = srespw.unpack(RemoteAdminSetAccountPersonalInfoAddressResponse.class);

                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Personal Address changed: %s"
                                .formatted(toJsonString(request));
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_INFO, header.getUserEmail(), eventMessage, routingKey, identity);

                        if (resp.getCommentAdded()) {
                            var msg = "Account Comment been added: %s".formatted(request.getComment());
                            saveAccountEvent(AccountEventType.ADD_ACCOUNT_COMMENT, header.getUserEmail(), msg, routingKey, identity);
                        }
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());

                        log.error(status.errorText());
                    }
                }
            });
        });
    }

    private static RemoteAdminSetAccountPersonalInfoAddressRequest.Builder toUamRequest(AdminHeader header, SetPersonalInfoAddressRequest req) {
        var builder = RemoteAdminSetAccountPersonalInfoAddressRequest.newBuilder()
                .setCountry(req.getCountry())
                .setCity(req.getCity())
                .setAddress(req.getAddress())
                .setZip(req.getZip());

        if (StringUtils.isNotEmpty(req.getState())) {
            builder.setState(req.getState());
        }

        if (StringUtils.isNotEmpty(req.getComment())) {
            builder.setCreatedBy(header.getUserEmail())
                    .setComment(req.getComment());
        }

        return builder;
    }
}
