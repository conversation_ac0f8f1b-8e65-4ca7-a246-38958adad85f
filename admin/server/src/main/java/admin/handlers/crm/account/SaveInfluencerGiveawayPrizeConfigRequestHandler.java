package admin.handlers.crm.account;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SaveInfluencerGiveawayPrizeConfigRequest;
import admin.models.brand.GiveawayPrizeConfigType;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.CoinConfig;
import uam.api.v1.FreeSpinConfig;
import uam.api.v1.RemoteAdminSaveInfluencerGiveawayPrizeConfigRequest;

@Service
@Slf4j
public class SaveInfluencerGiveawayPrizeConfigRequestHandler extends AbstractCrmAdminHandler<SaveInfluencerGiveawayPrizeConfigRequest> {

    @Inject
    public SaveInfluencerGiveawayPrizeConfigRequestHandler(CrmAdminServerProperties props,
                                                           DynamicCloud cloud,
                                                           MeterRegistry meterRegistry,
                                                           UamServiceApi uamServiceApi,
                                                           ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SaveInfluencerGiveawayPrizeConfigRequest request) throws Exception {

        var error = validate(request);

        if (StringUtils.isNotEmpty(error)) {
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new AdminErrorResponse(Response.Status.BAD_REQUEST.toString(), error)).build());
        }

        var uamRequest = toUamRequest(request);
        var wrapped = uamServiceApi.remoteAdminSaveInfluencerGiveawayPrizeConfig(uamRequest, AsciiString.cached(String.valueOf(request.getInfluencerId())));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    log.info(" [{}] Influencer giveaway prize config created successfully", request.getInfluencerId());

                } else {
                    async.resume(Response.status(Response.Status.BAD_GATEWAY)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText())).build());
                    log.warn("[{}] Can not save influencer giveaway prize config.", request.getInfluencerId());
                }
            }
        });
    }

    private String validate(SaveInfluencerGiveawayPrizeConfigRequest request) {
        if (request == null || request.getType() == null) {
            return "Invalid request: Type cannot be null";
        }

        try {
            GiveawayPrizeConfigType type = GiveawayPrizeConfigType.fromString(request.getType());

            return switch (type) {
                case COIN -> (request.getGcPrize() == null || request.getScPrize() == null)
                        ? "Invalid request: Both GC Prize and SC Prize are required for COIN type"
                        : StringUtils.EMPTY;
                case FREE_SPIN -> (request.getFsCountPrize() == null)
                        ? "Invalid request: FS Count Prize is required for FREE_SPIN type"
                        : StringUtils.EMPTY;
            };
        } catch (IllegalArgumentException e) {
            return "Invalid request: Unsupported prize type";
        }
    }

    private RemoteAdminSaveInfluencerGiveawayPrizeConfigRequest toUamRequest(SaveInfluencerGiveawayPrizeConfigRequest request) {
        RemoteAdminSaveInfluencerGiveawayPrizeConfigRequest.Builder builder = RemoteAdminSaveInfluencerGiveawayPrizeConfigRequest.newBuilder()
                .setInfluencerId(request.getInfluencerId())
                .addAllWinners(request.getWinners());

        if (request.getConfigId() != null) {
            builder.setConfigId(request.getConfigId());
        }
        var scPrize = request.getScPrize();
        var gcPrize = request.getGcPrize();

        if (scPrize != null && gcPrize != null) {
            builder.setCoinConfig(CoinConfig.newBuilder()
                    .setGcPrize(gcPrize)
                    .setScPrize(scPrize)
                    .build());
        } else {
            var fsCountPrize = request.getFsCountPrize();
            if (fsCountPrize != null) {
                builder.setFreeSpinConfig(FreeSpinConfig.newBuilder()
                        .setFsCountPrize(fsCountPrize)
                        .build());
            }
        }

        return builder.build();
    }
}
