package admin.handlers.crm.brand;

import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.DeleteBrandGiveawayPrizeConfigRequest;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.RemoteAdminDeleteBrandGiveawayPrizeConfigRequest;

@Service
@Slf4j
public class DeleteBrandGiveawayPrizeConfigRequestHandler extends AbstractCrmAdminHandler<DeleteBrandGiveawayPrizeConfigRequest> {

    @Inject
    public DeleteBrandGiveawayPrizeConfigRequestHandler(CrmAdminServerProperties props,
                                                        DynamicCloud cloud,
                                                        MeterRegistry meterRegistry,
                                                        UamServiceApi uamServiceApi,
                                                        ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, DeleteBrandGiveawayPrizeConfigRequest request) throws Exception {

        var uamRequest = toUamRequest(request);
        var wrapped = uamServiceApi.remoteAdminDeleteBrandGiveawayPrizeConfig(uamRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "[%s] Brand Giveaway Prize Config removed Successfully".formatted(request.getConfigId());

                    sendEvent(header,
                            eventMessage,
                            BackofficeEventStatus.SUCCESSFUL,
                            BackofficeEntityType.BRAND_GIVEAWAY_PRIZE_CONFIG,
                            BackofficeEventType.DELETE,
                            request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText())).build());
                    log.warn(status.errorText());
                }
            }
        });
    }

    private RemoteAdminDeleteBrandGiveawayPrizeConfigRequest toUamRequest(DeleteBrandGiveawayPrizeConfigRequest request) {
        var uamRequest = RemoteAdminDeleteBrandGiveawayPrizeConfigRequest.newBuilder();
        uamRequest.setConfigId(request.getConfigId());
        return uamRequest.build();
    }
}
