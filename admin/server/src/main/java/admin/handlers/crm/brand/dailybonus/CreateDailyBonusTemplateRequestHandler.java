package admin.handlers.crm.brand.dailybonus;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.dailybonus.CreateDailyBonusTemplateRequest;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.DailyBonusTemplateInfo;
import uam.api.v1.SetDailyBonusTemplateInfoRequest;

@Service
@Slf4j
public class CreateDailyBonusTemplateRequestHandler extends AbstractCrmAdminHandler<CreateDailyBonusTemplateRequest> {

    public CreateDailyBonusTemplateRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateDailyBonusTemplateRequest request) throws Exception {

        var updateRequest = mapRequest(header, request);

        var wrapped = uamServiceApi.setDailyBonusTemplateInfo(updateRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "Daily Bonus Template Code [ %s ] Created Successfully"
                            .formatted(request.getReference());

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_DAILY_BONUS_TEMPLATE,
                            BackofficeEventType.CREATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static SetDailyBonusTemplateInfoRequest mapRequest(AdminHeader header,
                                                               CreateDailyBonusTemplateRequest request) {

        var dailyBonusTemplateInfo = DailyBonusTemplateInfo.newBuilder()
                .setCode(request.getReference())
                .setGoldAmount(request.getGoldAmount())
                .setSweepstakeAmount(request.getSweepstakeAmount())
                .setInactive(request.getInactive());

        if (StringUtils.isNotEmpty(request.getExternalRewardCode())) {
            dailyBonusTemplateInfo.setExternalRewardCode(request.getExternalRewardCode());
        }

        if (StringUtils.isNotEmpty(request.getCountry())) {
            dailyBonusTemplateInfo.setCountry(request.getCountry());
        }

        if (StringUtils.isNotEmpty(request.getSegment())) {
            dailyBonusTemplateInfo.setSegment(request.getSegment());
        }

        var dailyBonusRequest = SetDailyBonusTemplateInfoRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setTemplate(dailyBonusTemplateInfo.build());
        return dailyBonusRequest.build();
    }
}
