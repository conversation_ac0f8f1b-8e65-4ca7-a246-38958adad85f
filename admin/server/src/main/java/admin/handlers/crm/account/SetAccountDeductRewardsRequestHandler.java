package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetAccountDeductRewardsRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.RewardAccountManuallyRequest;
import uam.api.v1.RewardMode;

import java.math.BigDecimal;
import java.util.Objects;

@Service
@Slf4j
public class SetAccountDeductRewardsRequestHandler extends AbstractCrmAdminHandler<SetAccountDeductRewardsRequest> {
    @Inject
    public SetAccountDeductRewardsRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetAccountDeductRewardsRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request).setIdentity(identity).build();

            var wrapped = uamServiceApi.rewardAccountManually(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();

                    if (status.isOK()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = getEventMessage(request);

                        saveAccountEvent(AccountEventType.ACCOUNT_DEDUCT, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        log.error(status.errorText());
                    }
                }
            });
        });
    }

    private static String getEventMessage(SetAccountDeductRewardsRequest request) {

        var fiatValue = Objects.requireNonNullElse(request.getFiatAmount(), BigDecimal.ZERO);

        var eventMessage = "Manually Deducted. Sweepstakes: %s, Gold Coins: %s"
                .formatted(
                        request.getSweepstakeAmount(),
                        request.getGoldAmount());

        if (fiatValue.compareTo(BigDecimal.ZERO) > 0) {
            eventMessage += ", Fiat: " + fiatValue;
        }

        return eventMessage;
    }

    private static RewardAccountManuallyRequest.Builder toUamRequest(SetAccountDeductRewardsRequest request) {
        var builder = RewardAccountManuallyRequest.newBuilder()
                .setMode(RewardMode.IMMEDIATE)
                .setToForce(true)
                .setSweepstakeAmount(request.getSweepstakeAmount().abs().negate().toString())
                .setGoldAmount(request.getGoldAmount().abs().negate().toString());

        if (!Objects.isNull(request.getFiatAmount())) {
            builder.setFiatAmount(request.getFiatAmount().abs().negate().toString());
        }

        return builder;
    }
}
