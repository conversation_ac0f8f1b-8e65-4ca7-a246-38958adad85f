package admin.handlers.crm.brand;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.GiveawayPrizeConfigType;
import admin.models.brand.SaveBrandGiveawayPrizeConfigRequest;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.CoinConfig;
import uam.api.v1.FreeSpinConfig;
import uam.api.v1.RemoteAdminSaveBrandGiveawayPrizeConfigRequest;

@Service
@Slf4j
public class SaveBrandGiveawayPrizeConfigRequestHandler extends AbstractCrmAdminHandler<SaveBrandGiveawayPrizeConfigRequest> {

    @Inject
    public SaveBrandGiveawayPrizeConfigRequestHandler(CrmAdminServerProperties props,
                                                      DynamicCloud cloud,
                                                      MeterRegistry meterRegistry,
                                                      UamServiceApi uamServiceApi,
                                                      ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SaveBrandGiveawayPrizeConfigRequest request) throws Exception {

        var error = validate(request);

        if (StringUtils.isNotEmpty(error)) {
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new AdminErrorResponse(Response.Status.BAD_REQUEST.toString(), error)).build());
        }

        var uamRequest = toUamRequest(request, header.getBrand());
        var wrapped = uamServiceApi.remoteAdminSaveBrandGiveawayPrizeConfig(uamRequest, AsciiString.cached(header.getBrand()));



        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());
                    log.debug("[{}] Brand giveaway prize config created successfully", header.getBrand());
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText())).build());
                    log.warn("[{}] Can not save brand giveaway prize config.", header.getBrand());
                }
            }
        });
    }

    private String validate(SaveBrandGiveawayPrizeConfigRequest request) {
        if (request == null || request.getType() == null) {
            return "Invalid request: Type cannot be null";
        }

        try {
            GiveawayPrizeConfigType type = GiveawayPrizeConfigType.fromString(request.getType());

            return switch (type) {
                case COIN -> (request.getGcPrize() == null || request.getScPrize() == null)
                        ? "Invalid request: Both GC Prize and SC Prize are required for COIN type"
                        : StringUtils.EMPTY;
                case FREE_SPIN -> (request.getFsCountPrize() == null)
                        ? "Invalid request: FS Count Prize is required for FREE_SPIN type"
                        : StringUtils.EMPTY;
            };
        } catch (IllegalArgumentException e) {
            return "Invalid request: Unsupported prize type";
        }
    }

    private RemoteAdminSaveBrandGiveawayPrizeConfigRequest toUamRequest(SaveBrandGiveawayPrizeConfigRequest request, String brandName ) {
        RemoteAdminSaveBrandGiveawayPrizeConfigRequest.Builder builder = RemoteAdminSaveBrandGiveawayPrizeConfigRequest.newBuilder()
                .setBrandName(brandName)
                .addAllWinners(request.getWinners().stream().sorted().toList());

        if (request.getConfigId() != null) {
            builder.setConfigId(request.getConfigId());
        }
        var scPrize = request.getScPrize();
        var gcPrize = request.getGcPrize();

        if (scPrize != null && gcPrize != null) {
            builder.setCoinConfig(CoinConfig.newBuilder()
                    .setGcPrize(gcPrize)
                    .setScPrize(scPrize)
                    .build());
        } else {
            var fsCountPrize = request.getFsCountPrize();
            if (fsCountPrize != null) {
                builder.setFreeSpinConfig(FreeSpinConfig.newBuilder()
                        .setFsCountPrize(fsCountPrize)
                        .build());
            }
        }

        return builder.build();
    }
}
