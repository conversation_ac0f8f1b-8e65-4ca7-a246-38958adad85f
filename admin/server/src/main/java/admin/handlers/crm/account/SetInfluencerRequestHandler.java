package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetInfluencerRequest;
import api.v1.ApiFactory;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import api.v1.Reason;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import uam.api.UamServiceApi;
import uam.api.v1.RemoteAdminUpdateInfluencerLimitsRequest;
import uam.api.v1.RemoteAdminUpdateInfluencerPermissionsRequest;
import uam.api.v1.RemoteAdminUpdateInfluencerRequest;

@Service
@Slf4j
public class SetInfluencerRequestHandler extends AbstractCrmAdminHandler<SetInfluencerRequest> {

    @Inject
    public SetInfluencerRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory
    ) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetInfluencerRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request);

            var wrapped = uamServiceApi.remoteAdminUpdateInfluencer(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {

                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Update Influencer: %s".formatted(toJsonString(request));
                        saveAccountEvent(uam.api.v1.AccountEventType.SET_ACCOUNT_INFLUENCER, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        if (status.errorReason() == Reason.UNSPECIFIED) {
                            log.error("Creating influencer failed. Message: {}, body: {}", status.errorText(), request);
                        } else {
                            log.warn("Creating influencer failed. Message: {}, body: {}", status.errorText(), request);
                        }
                    }
                }
            });
        });
    }

    private RemoteAdminUpdateInfluencerRequest toUamRequest(SetInfluencerRequest request) {

        var builder = RemoteAdminUpdateInfluencerRequest.newBuilder()
                .setAccountId(request.getAccountId())
                .setNickname(request.getNickname())
                .setActive(request.getActive());

        if (StringUtils.isNoneEmpty(request.getBannerUrl())) {
            builder.setBannerUrl(request.getBannerUrl());
        }

        var limits = RemoteAdminUpdateInfluencerLimitsRequest.newBuilder();

        if (StringUtils.isNoneEmpty(request.getRewardInUsdWeekly())) {
            limits.setRewardInUsdWeekly(request.getRewardInUsdWeekly());
        }

        if (request.getCreatedGiveawaysDaily() != null) {
            limits.setCreatedGiveawaysDaily(request.getCreatedGiveawaysDaily());
        }

        builder.setLimits(limits);

        var permissions = RemoteAdminUpdateInfluencerPermissionsRequest.newBuilder();

        if (request.getAllowedToCreateCoinCampaign() != null) {
            permissions.setAllowedToCreateCoinCampaign(request.getAllowedToCreateCoinCampaign());
        }

        if (request.getAllowedToCreateFreeSpinCampaign() != null) {
            permissions.setAllowedToCreateFreeSpinCampaign(request.getAllowedToCreateFreeSpinCampaign());
        }

        builder.setPermissions(permissions);

        return builder.build();
    }
}
