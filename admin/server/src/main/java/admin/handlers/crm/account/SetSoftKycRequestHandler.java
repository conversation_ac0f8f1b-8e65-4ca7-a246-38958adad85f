package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetSoftKycRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.RemoteAdminSetSoftKycRequest;

@Service
@Slf4j
public class SetSoftKycRequestHandler extends AbstractCrmAdminHandler<SetSoftKycRequest> {

    @Inject
    public SetSoftKycRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetSoftKycRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request)
                    .setIdentity(identity)
                    .build();

            var wrapped = uamServiceApi.remoteAdminSetSoftKyc(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {

                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Change Account Soft Kyc: %s"
                                .formatted(toJsonString(request));
                        saveAccountEvent(AccountEventType.SET_ACCOUNT_SOFT_KYC_INFO, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());

                        log.error(status.errorText());
                    }
                }
            });
        });
    }

    private static RemoteAdminSetSoftKycRequest.Builder toUamRequest(SetSoftKycRequest request) {
        var builder = RemoteAdminSetSoftKycRequest
                .newBuilder()
                .setCountry(request.getCountry());

        if (StringUtils.isNotEmpty(request.getState())) {
            builder.setState(request.getState());
        }

        if (StringUtils.isNotEmpty(request.getCity())) {
            builder.setCity(request.getCity());
        }

        if (StringUtils.isNotEmpty(request.getAddress())) {
            builder.setAddress(request.getAddress());
        }

        if (StringUtils.isNotEmpty(request.getAddress2())) {
            builder.setAddress2(request.getAddress2());
        }

        if (StringUtils.isNotEmpty(request.getPostal())) {
            builder.setPostal(request.getPostal());
        }

        return builder;
    }
}
