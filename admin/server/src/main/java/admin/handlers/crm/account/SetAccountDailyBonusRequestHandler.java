package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetAccountDailyBonusRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.RemoteAdminSetAccountDailyBonusRequest;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@Service
@Slf4j
public class SetAccountDailyBonusRequestHandler extends AbstractCrmAdminHandler<SetAccountDailyBonusRequest> {

    @Inject
    public SetAccountDailyBonusRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetAccountDailyBonusRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request).setIdentity(identity).build();

            var wrapped = uamServiceApi.remoteAdminSetAccountDailyBonus(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {

                    var status = srespw.status();

                    if (status.isOK()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Set daily bonus: %s accepted, next bonus day: %s, next bonus at: %s"
                                .formatted(
                                        request.getAcceptedCount(),
                                        request.getNextBonusDay(),
                                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                                                .withZone(ZoneId.systemDefault())
                                                .format(request.getNextBonusAt())
                                );

                        saveAccountEvent(AccountEventType.ACCOUNT_REWARD, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        log.error(status.errorText());
                    }
                }
            });
        });
    }

    private static RemoteAdminSetAccountDailyBonusRequest.Builder toUamRequest(SetAccountDailyBonusRequest request) {
        var builder = RemoteAdminSetAccountDailyBonusRequest.newBuilder()
                .setAcceptedCount(request.getAcceptedCount())
                .setNextBonusDay(request.getNextBonusDay())
                .setNextBonusAt(request.getNextBonusAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        if (StringUtils.isNotEmpty(request.getSegment())) {
            builder.setSegment(request.getSegment());
        }
        return builder;
    }
}
