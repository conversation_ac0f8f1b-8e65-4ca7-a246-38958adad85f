package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.MassAccountCommentRequest;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.AddAccountCommentRequest;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.Identity;
import uam.api.v1.RemoteAdminSetAccountCommentRequest;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class MassAccountCommentHandler extends AbstractCrmAdminHandler<MassAccountCommentRequest> {
    private static final int BUFFER_MAX_SIZE = 1000;

    @Inject
    public MassAccountCommentHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassAccountCommentRequest request) throws Exception {
        async.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        async.setTimeoutHandler(ar -> {
            log.warn("Mass account comment request timed out");
            ar.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                    .build());
        });

        List<String> sanitizedIds = sanitize(request.getAccountsIds()).stream()
                .map(String::toLowerCase)
                .toList();

        if (sanitizedIds.isEmpty()) {
            log.warn("No valid account IDs provided for comment");
            async.resume(Response.status(Response.Status.BAD_REQUEST)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_BAD_REQUEST.name(), "No valid account IDs provided"))
                    .build());
            return;
        }

        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);

        Flux.fromIterable(sanitizedIds)
                .buffer(BUFFER_MAX_SIZE)
                .flatMap(batchIds ->
                                Flux.fromIterable(batchIds)
                                        .flatMap(id -> getAccountRoutingInfoReactive(id, header.getBrand(), async)
                                                .flatMap(pair -> processSingleAccountComment(header, request, id, pair.getT1(), pair.getT2(), processedCount, failedCount))
                                                .onErrorResume(e -> {
                                                    // Log individual account failure
                                                    log.error("Failed to add comment for account {}: {}", id, e.getMessage());
                                                    failedCount.incrementAndGet();
                                                    return Mono.empty(); // Continue processing other accounts
                                                })))
                .subscribeOn(Schedulers.fromExecutor(executor))
                .collectList()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .doOnSuccess(list -> {

                    var eventMessage = "Mass account comment completed: [ %s ] accounts processed, [ %s ] accounts failed."
                            .formatted(processedCount.get(), failedCount.get());

                    log.info(eventMessage);

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.MASS_ACCOUNT_COMMENT,
                            BackofficeEventType.UPDATE, request);

                    async.resume(Response.ok(new AdminOkResponse()).build());
                })
                .doOnError(throwable -> {
                    log.error("Mass account comment encountered errors: {} accounts processed, {} accounts failed",
                            processedCount.get(), failedCount.get());
                    if (throwable instanceof TimeoutException) {
                        log.warn("Mass account comment request timed out");
                        async.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                                .build());
                    } else {
                        logErrorAndResume(async, throwable);
                    }
                })
                .subscribe();
    }

    /**
     * Reactive wrapper for getAccountRoutingInfo that returns a Mono of Tuple2<Identity, AsciiString>.
     * Uses a custom AsyncResponse to capture success/failure.
     */
    private Mono<Tuple2<Identity, AsciiString>> getAccountRoutingInfoReactive(String id, String brand, AsyncResponse async) {
        return Mono.create(sink ->
                getAccountRoutingInfo(Long.parseLong(id), brand, async, (identity, routingKey) -> {
                    if (identity != null && routingKey != null) {
                        sink.success(Tuples.of(identity, routingKey));
                    } else {
                        sink.error(new RuntimeException("Failed to retrieve routing info for account ID: " + id));
                    }
                })
        );
    }

    private Mono<Void> processSingleAccountComment(AdminHeader header, MassAccountCommentRequest request,
                                                   String accountId, Identity identity, AsciiString routingKey,
                                                   AtomicInteger processedCount,
                                                   AtomicInteger failedCount) {
        return Mono.create(sink -> {
            RemoteAdminSetAccountCommentRequest addCommentRequest = RemoteAdminSetAccountCommentRequest.newBuilder()
                    .setIdentity(identity)
                    .setCreatedBy(header.getUserEmail())
                    .setComment(request.getComment())
                    .build();

            var wrapped = uamServiceApi.remoteAdminSetAccountComment(addCommentRequest, routingKey);
            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var responseStatus = srespw.status();
                    if (responseStatus.isOK()) {
                        log.debug("Successfully added comment for account {}", accountId);
                        String eventMessage = String.format("Account comment has been added: %s, comment: %s", accountId, request.getComment());

                        saveAccountEvent(AccountEventType.ADD_ACCOUNT_COMMENT, header.getUserEmail(), eventMessage, routingKey, identity);
                        processedCount.incrementAndGet();
                        sink.success();
                    } else {
                        log.error("Failed to add comment for account {}: {}", accountId, responseStatus.errorText());
                        failedCount.incrementAndGet();
                        sink.error(new RuntimeException("Failed to add comment for account: " + responseStatus.errorText()));
                    }
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Error adding comment for account {}: {}", accountId, t.getMessage());
                    failedCount.incrementAndGet();
                    sink.error(t);
                }
            });
        });
    }
}
