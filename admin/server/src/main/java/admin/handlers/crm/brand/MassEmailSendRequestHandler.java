package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.MassEmailSendRequest;
import api.v1.AccountRoutingInfo;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.GetMassAccountRoutingInfoResponse;
import uam.api.v1.Identity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Service
@Slf4j
public class MassEmailSendRequestHandler extends AbstractCrmAdminHandler<MassEmailSendRequest> {

    public MassEmailSendRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassEmailSendRequest request) throws Exception {

        Map<Identity, AsciiString> routingMap = new HashMap<>();

        FluentFuture.from(executor.submit((CheckedRunnable) () -> handleRoutingInfo(async, header, request, routingMap)))
                .addCallback(new FutureCallback<Object>() {
                    @Override
                    public void onSuccess(Object result) {
                        log.debug("Send emails for: {} accounts", routingMap.size());
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        logErrorAndResume(async, t);
                    }
                }, MoreExecutors.directExecutor());
    }

    private void handleRoutingInfo(AsyncResponse async, AdminHeader header, MassEmailSendRequest request, Map<Identity, AsciiString> routingMap) {
        var getMassAccountRoutingInfoRequestBuilder = uam.api.v1.GetMassAccountRoutingInfoRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setCategory(request.getSegment());

        if (StringUtils.isNotEmpty(request.getTag())) {
            getMassAccountRoutingInfoRequestBuilder.setTag(request.getTag());
        }

        try {
            var wrappedRouting = uamServiceApi.getMassAccountRoutingInfo(
                    getMassAccountRoutingInfoRequestBuilder.build(),
                    AsciiString.cached(header.getBrand()));

            wrappedRouting.addListener(new AbstractSafeResponseConsumer(wrappedRouting, apiFactory) {

                @Override
                public void onFailure(Throwable t) {
                    handleFailure(async, t);
                }

                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    populateRoutingMap(srespw, routingMap);
                    log.debug("Got routing info for: {} accounts", routingMap.size());
                    processRoutingInfo(async, header, request, routingMap);
                }
            });
        } catch (Exception err) {
            logErrorAndResume(async, err);
            throw new RuntimeException(err);
        }
    }

    private void handleFailure(AsyncResponse async, Throwable t) {
        log.error(t.getMessage(), t);
        async.resume(Response.status(Response.Status.BAD_REQUEST)
                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), t.getMessage()))
                .build());
    }

    private void populateRoutingMap(ResponseWrapperFacade srespw, Map<Identity, AsciiString> routingMap) throws Exception {
        var sresp = srespw.unpack(GetMassAccountRoutingInfoResponse.class);

        for (AccountRoutingInfo routing : sresp.getRoutingList()) {
            var hash = routing.getHash();
            var identity = toIdentity(routing);
            var routingKey = AsciiString.cached(hash);
            routingMap.put(identity, routingKey);
        }
    }

    private void processRoutingInfo(AsyncResponse async, AdminHeader header, MassEmailSendRequest request, Map<Identity, AsciiString> routingMap) {
        Flux.fromIterable(routingMap.entrySet())
                .buffer(request.getBuffer())
                .subscribe(t -> {
                    CountDownLatch latch = new CountDownLatch(t.size());
                    AtomicLong counter = new AtomicLong();

                    for (Map.Entry<Identity, AsciiString> entry : t) {
                        processEmailRequest(header, request, entry, latch, counter);
                    }

                    awaitLatch(async, request, t, latch, counter);
                });
    }

    private void processEmailRequest(AdminHeader header, MassEmailSendRequest request, Map.Entry<Identity, AsciiString> entry, CountDownLatch latch, AtomicLong counter) {
        var identity = entry.getKey();
        var routingKey = entry.getValue();

        var sendEmailRequestBuilder = uam.api.v1.SendEmailRequest.newBuilder();
        sendEmailRequestBuilder.setIdentity(identity);
        sendEmailRequestBuilder.setTemplateCode(request.getTemplate());

        var wrapped = uamServiceApi.sendEmail(sendEmailRequestBuilder.build(), routingKey);
        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                try {
                    handleEmailResponse(srespw, header, identity, routingKey, counter, request);
                } finally {
                    latch.countDown();
                }
            }
        }, MoreExecutors.directExecutor());
    }

    private void handleEmailResponse(ResponseWrapperFacade srespw, AdminHeader header, Identity identity, AsciiString routingKey, AtomicLong counter, MassEmailSendRequest request) {
        var status = srespw.status();

        if (status.isOK()) {
            counter.incrementAndGet();
            var message = String.format("Sent email to account %s template %s",
                    identity.getByAccountId().getAccountId(), request.getTemplate());
            saveAccountEvent(AccountEventType.SEND_EMAIL_MASS, header.getUserEmail(), message, routingKey, identity);
        } else {
            log.error("Failed to send email to account {} template {}: {}",
                    identity.getByAccountId().getAccountId(), request.getTemplate(), status.errorText());
        }
    }

    @SuppressWarnings("java:S899")
    private void awaitLatch(AsyncResponse async, MassEmailSendRequest request, List<Map.Entry<Identity, AsciiString>> t, CountDownLatch latch, AtomicLong counter) {
        try {
            latch.await(request.getTimeout(), TimeUnit.SECONDS);
            async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());
        } catch (Exception err) {
            logErrorAndResume(async, err);
        }
    }
}
