package admin.handlers.crm.brand.features;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.features.CreateBrandFeatureRequest;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.BrandFeatureInfo;
import uam.api.v1.SetBrandFeatureRequest;
import uam.api.v1.SetBrandFeatureResponse;

@Service
@Slf4j
public class CreateBrandFeatureRequestHandler extends AbstractCrmAdminHandler<CreateBrandFeatureRequest> {

    public CreateBrandFeatureRequestHandler(CrmAdminServerProperties props,
                                            DynamicCloud cloud,
                                            MeterRegistry meterRegistry,
                                            UamServiceApi uamServiceApi,
                                            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreateBrandFeatureRequest request) throws Exception {
        var wrapped = uamServiceApi.setBrandFeature(mapRequest(header, request), AsciiString.cached(header.getBrand()));
        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void onFailure(Throwable t) {
                super.onFailure(t);
                async.resume(Response.status(Response.Status.BAD_REQUEST)
                        .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), t.getMessage()))
                        .build());
            }

            @Override
            public void accept(ResponseWrapperFacade srespw) throws Throwable {
                var status = srespw.status();
                if (status.isOK()) {
                    var resp = srespw.unpack(SetBrandFeatureResponse.class);
                    if (resp.getApplied()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Brand Feature [ %s ] created successfully"
                                .formatted(request.getReference());
                        sendEvent(header,
                                eventMessage,
                                BackofficeEventStatus.SUCCESSFUL,
                                BackofficeEntityType.BRAND_FEATURE,
                                BackofficeEventType.CREATE,
                                request);
                    } else {
                        async.resume(Response.status(Response.Status.OK).entity(JsonNodeFactory.instance.objectNode()).build());
                    }
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                            .build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private SetBrandFeatureRequest mapRequest(AdminHeader header, CreateBrandFeatureRequest request) {
        SetBrandFeatureRequest.Builder crmRequest = SetBrandFeatureRequest.newBuilder()
                .setBrandName(header.getBrand());

        BrandFeatureInfo.Builder feature = BrandFeatureInfo.newBuilder()
                .setCode(request.getCode())
                .setEnabled(request.isEnabled());

        if (StringUtils.isNotEmpty(request.getCountry())) {
            crmRequest.setCountry(request.getCountry());
        }

        if (StringUtils.isNotEmpty(request.getValue())) {
            feature.setValue(request.getValue());
        }

        return crmRequest.setFeature(feature).build();
    }
}
