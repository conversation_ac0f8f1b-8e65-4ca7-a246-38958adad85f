package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.models.brand.UpdateRewardCampaignRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.SetRewardCampaignInfoRequest;

@Service
@Slf4j
public class UpdateRewardCampaignRequestHandler extends AbstractCrmAdminHandler<UpdateRewardCampaignRequest> {

    public UpdateRewardCampaignRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi, ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateRewardCampaignRequest request) throws Exception {

        var wrapped = uamServiceApi.setRewardCampaignInfo(
                getRequest(header, request).build(),
                AsciiString.cached(header.getBrand())
        );

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {

                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "Reward Campaign [ %s ] is Updated Successfully"
                            .formatted(request.getReference());
                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_REWARD_CAMPAIGN,
                            BackofficeEventType.UPDATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                            new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                    ).build());
                    log.error(status.errorText());
                }
            }
        });
    }

    private static SetRewardCampaignInfoRequest.Builder getRequest(AdminHeader header, UpdateRewardCampaignRequest request) {

        var sreqb = SetRewardCampaignInfoRequest.newBuilder();
        sreqb.setBrandName(header.getBrand());

        var info = uam.api.v1.RewardCampaignInfo.newBuilder();
        info.setBrandName(header.getBrand());
        info.setCode(request.getCode());
        info.setTitle(request.getTitle());
        info.setCategoryName(request.getCategoryName());
        info.setType(request.getType());
        info.setObjective(request.getObjective());
        info.setChannelIssued(request.getChannelIssued());
        sreqb.setRewardCampaign(info);
        return sreqb;
    }
}
