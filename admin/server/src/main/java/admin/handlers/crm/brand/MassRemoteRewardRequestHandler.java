package admin.handlers.crm.brand;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.brand.MassRemoteRewardRequest;
import api.v1.ApiFactory;
import api.v1.CodeSpec;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;
import uam.api.v1.Identity;
import uam.api.v1.RewardAccountManuallyResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@Slf4j
public class MassRemoteRewardRequestHandler extends AbstractCrmAdminHandler<MassRemoteRewardRequest> {

    public MassRemoteRewardRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, MassRemoteRewardRequest request) throws Exception {
        async.setTimeout(TIMEOUT, TimeUnit.SECONDS);
        async.setTimeoutHandler(ar -> {
            log.warn("Mass reward request timed out");
            ar.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                    .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                    .build());
        });

        getMassAccountRoutingInfo(header, request.getCategory(), request.getTag(), (routingMap, throwable) -> {
            if (throwable != null) {
                logErrorAndResume(async, throwable);
            } else {
                processRoutingInfo(async, header, request, routingMap);
            }
        });
    }

    private void processRoutingInfo(AsyncResponse async, AdminHeader header, MassRemoteRewardRequest request,
                                    Map<Identity, AsciiString> routingMap) {
        int totalAccounts = routingMap.size();
        log.info("Processing mass reward request for {} accounts", totalAccounts);

        Scheduler customScheduler = Schedulers.fromExecutor(executor);

        // Initialize counters
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);

        Flux.fromIterable(routingMap.entrySet())
                .buffer(BUFFER_MAX_SIZE)
                .flatMap(entries -> Flux.fromIterable(entries)
                        .flatMap(entry ->
                                rewardAccount(header, request, entry.getKey(), entry.getValue(), processedCount, failedCount)))
                .subscribeOn(customScheduler)
                .collectList()
                .timeout(Duration.ofSeconds(TIMEOUT))
                .subscribe(result -> {
                    var eventMessage = "Mass reward request completed: [ %s ] accounts processed, [ %s ] accounts failed"
                            .formatted(processedCount.get(), failedCount.get());

                    log.info(eventMessage);

                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.MASS_REWARD,
                            BackofficeEventType.UPDATE, request);

                    async.resume(Response.ok(new AdminOkResponse()).build());
                }, throwable -> {
                    log.info("Mass reward request completed with errors: {} accounts processed, {} accounts failed",
                            processedCount.get(), failedCount.get());
                    if (throwable instanceof TimeoutException) {
                        log.warn("Mass reward request timed out");
                        async.resume(Response.status(Response.Status.SERVICE_UNAVAILABLE)
                                .entity(new AdminErrorResponse(CodeSpec.ERR_SYSTEM.name(), "Request timed out"))
                                .build());
                    } else {
                        logErrorAndResume(async, throwable);
                    }
                });
    }

    private Flux<Void> rewardAccount(AdminHeader header, MassRemoteRewardRequest request,
                                     Identity identity, AsciiString routingKey,
                                     AtomicInteger processedCount, AtomicInteger failedCount) {
        return Flux.create(sink -> {
            // ~ per player ID
            Hasher hasher = Hashing.murmur3_128().newHasher();
            hasher.putString(header.getBrand(), StandardCharsets.UTF_8);
            hasher.putLong(identity.getByAccountId().getAccountId());
            hasher.putString(request.getSessionId(), StandardCharsets.UTF_8);
            String id = hasher.hash().toString();

            var rewardRequest = uam.api.v1.RewardAccountManuallyRequest.newBuilder();
            rewardRequest.setMode(request.getMode());
            rewardRequest.setIdentity(identity);
            rewardRequest.setSessionId(id);
            rewardRequest.setSweepstakeAmount(request.getSweepstakeAmount().toString());
            rewardRequest.setGoldAmount(request.getGoldAmount().toString());
            if (StringUtils.isNotEmpty(request.getRewardCampaignCode())) {
                rewardRequest.setCampaignCode(request.getRewardCampaignCode());
            }
            if (StringUtils.isNotEmpty(request.getCreditorName())) {
                rewardRequest.setCreditorName(request.getCreditorName());
            }
            if (StringUtils.isNotEmpty(request.getEmailCode())) {
                rewardRequest.setEmailCode(request.getEmailCode());
            }

            var wrapped = uamServiceApi.rewardAccountManually(rewardRequest.build(), routingKey);
            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();
                    var resp = srespw.unpack(RewardAccountManuallyResponse.class);

                    if (status.isOK() && resp.getApplied()) {
                        var message = String.format(
                                "Reward manually account %s by: %s sweepstakes and %s golds. mode: %s. creditor: %s. campaign: %s",
                                identity.getByAccountId().getAccountId(),
                                request.getSweepstakeAmount(), request.getGoldAmount(),
                                request.getMode().name(), request.getCreditorName(), request.getRewardCampaignCode());
                        processedCount.incrementAndGet();
                        saveAccountEvent(AccountEventType.ACCOUNT_REWARD_MASS,
                                header.getUserEmail(),
                                message,
                                routingKey,
                                identity);
                    } else {
                        log.error("Failed to reward account {} with reward {}: {}",
                                identity.getByAccountId().getAccountId(), request.getRewardCampaignCode(), status.errorText());
                        failedCount.incrementAndGet();
                    }
                    sink.complete();
                }

                @Override
                public void onFailure(Throwable t) {
                    log.error("Error rewarding account {}: {}", identity.getByAccountId().getAccountId(), t.getMessage());
                    failedCount.incrementAndGet();
                    sink.error(t);
                }

            }, executor);
        });
    }
}
