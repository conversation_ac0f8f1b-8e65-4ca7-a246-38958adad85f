package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.SetAccountPreferencesRequest;
import api.v1.ApiFactory;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;
import uam.api.v1.RemoteAdminUpdateAccountPreferencesRequest;

@Service
@Slf4j
public class SetAccountPreferencesRequestHandler extends AbstractCrmAdminHandler<SetAccountPreferencesRequest> {
    @Inject
    public SetAccountPreferencesRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, SetAccountPreferencesRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var uamRequest = toUamRequest(request).setIdentity(identity).build();

            var wrapped = uamServiceApi.remoteAdminSetAccountPreferences(uamRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {

                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Update Preferences: %s"
                                .formatted(toJsonString(request));

                        saveAccountEvent(AccountEventType.SET_ACCOUNT_INFO, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
                        ).build());
                        log.error(status.errorText());
                    }
                }
            });
        });
    }

    private RemoteAdminUpdateAccountPreferencesRequest.Builder toUamRequest(SetAccountPreferencesRequest request) {
        return RemoteAdminUpdateAccountPreferencesRequest.newBuilder()
                .setAutoLockWithdraw(request.getAutoLockWithdraw())
                .setEmailHardBounce(request.getEmailHardBounce())
                .setEmailSpamReport(request.getEmailSpamReport())
                .setDoNotSendEmails(request.getDoNotSendEmails())
                .setDoNotSendSms(request.getDoNotSendSms())
                .setDoNotSendPushes(request.getDoNotSendPushes())
                .setDoNotCall(request.getDoNotCall());
    }
}
