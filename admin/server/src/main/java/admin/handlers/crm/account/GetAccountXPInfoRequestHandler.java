package admin.handlers.crm.account;

import admin.CrmAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.handlers.crm.AbstractCrmAdminHandler;
import admin.models.account.GetAccountXPInfoRequest;
import admin.models.account.GetAccountXPInfoResponse;
import admin.models.account.LevelInfo;
import api.v1.ApiFactory;
import api.v1.CommonMappers;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import uam.api.UamServiceApi;

import java.io.IOException;
import java.time.Instant;
import java.time.ZoneOffset;

@Service
@Slf4j
public class GetAccountXPInfoRequestHandler extends AbstractCrmAdminHandler<GetAccountXPInfoRequest> {
    @Inject
    public GetAccountXPInfoRequestHandler(
            CrmAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }
    @Override
    public void accept(AsyncResponse async, AdminHeader header, GetAccountXPInfoRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            uam.api.v1.GetAccountInfoRequest getAccountInfoRequest = uam.api.v1.GetAccountInfoRequest.newBuilder()
                    .setIdentity(identity)
                    .build();

            var wrapped = uamServiceApi.getAccountInfo(getAccountInfoRequest, routingKey);

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws IOException {
                    var status = srespw.status();
                    if (status.isOK()) {
                        var resp = srespw.unpack(uam.api.v1.GetAccountInfoResponse.class);
                        if (resp.hasInfo()) {
                            uam.api.v1.AccountInfo info = resp.getInfo();
                            GetAccountXPInfoResponse response = new GetAccountXPInfoResponse();
                            response.setLevelInfo(mapInfo(info.getXpLevel()));
                            async.resume(Response.status(Response.Status.OK).entity(response).build());
                        } else {
                            badRequest(async, status);
                        }
                    } else {
                        badRequest(async, status);
                    }
                }
            });
        });
    }

    private LevelInfo mapInfo(uam.api.v1.LevelInfo levelInfo) {
        return LevelInfo.builder()
                .level(levelInfo.getLevel())
                .threshold(levelInfo.getThreshold())
                .current(levelInfo.getCurrent())
                .range(levelInfo.getRange())
                .category(levelInfo.getCategory())
                .name(levelInfo.getName())
                .annualLevel(levelInfo.getAnnualLevel())
                .annualRange(levelInfo.getAnnualRange())
                .annualLevel(levelInfo.getAnnualLevel())
                .levelValidUntil(CommonMappers.toLocalDate(levelInfo.getLevelValidUntil()))
                .monthThreshold(levelInfo.getMonthThreshold())
                .monthLevel(levelInfo.getMonthLevel())
                .monthRange(levelInfo.getMonthRange())
                .currentMonthPoints(levelInfo.getCurrentMonthPoints())
                .currentYearPoints(levelInfo.getCurrentYearPoints())
                .baseMonthlyLevel(levelInfo.getBaseMonthlyLevel())
                .baseAnnualLevel(levelInfo.getBaseAnnualLevel())
                .nextMonthlyLevel(levelInfo.getNextMonthlyLevel())
                .nextAnnualLevel(levelInfo.getNextAnnualLevel())
                .monthlyLevelProgress(levelInfo.getMonthlyLevelProgress())
                .annualLevelProgress(levelInfo.getAnnualLevelProgress())
                .levelType(levelInfo.getLevelType())
                .remainAmountPoints(levelInfo.getRemainAmountPoints())
                .build();
    }

    private void badRequest(AsyncResponse async, com.turbospaces.api.facade.ResponseStatusFacade status) {
        async.resume(Response.status(Response.Status.BAD_REQUEST).entity(
                new AdminErrorResponse(status.errorCode().toString(), status.errorText())
        ).build());
        log.error(status.errorText());
    }
}
