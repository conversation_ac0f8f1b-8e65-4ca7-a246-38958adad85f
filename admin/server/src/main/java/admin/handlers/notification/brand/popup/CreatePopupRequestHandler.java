package admin.handlers.notification.brand.popup;

import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.NotificationAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.notification.AbstractNotificationAdminHandler;
import admin.models.brand.popup.CreatePopupRequest;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import notification.NotificationServiceApi;
import uam.api.UamServiceApi;
import uam.api.v1.BackofficeEntityType;
import uam.api.v1.BackofficeEventStatus;
import uam.api.v1.BackofficeEventType;

@Service
@Slf4j
public class CreatePopupRequestHandler extends AbstractNotificationAdminHandler<CreatePopupRequest> {
    private final NotificationServiceApi notificationServiceApi;
    private final Mapper mapper;

    public CreatePopupRequestHandler(
            NotificationAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            NotificationServiceApi notificationServiceApi,
            ApiFactory apiFactory,
            Mapper mapper) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
        this.notificationServiceApi = notificationServiceApi;
        this.mapper = mapper;
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, CreatePopupRequest request) throws Exception {
        var createRequest = mapper.mapRequest(header, request);

        var wrapped = notificationServiceApi.createOrUpdatePopup(createRequest, AsciiString.cached(header.getBrand()));

        wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
            @Override
            public void accept(ResponseWrapperFacade srespw) {
                var status = srespw.status();
                if (status.isOK()) {
                    async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                    var eventMessage = "Popup [brand=%s, country=%s, code=%s] created successfully".formatted(
                            header.getBrand(), request.getCountry(), request.getCode());
                    sendEvent(header, eventMessage, BackofficeEventStatus.SUCCESSFUL, BackofficeEntityType.BRAND_POPUP,
                            BackofficeEventType.CREATE, request);
                } else {
                    async.resume(Response.status(Response.Status.BAD_REQUEST)
                            .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                            .build());
                    if (status.isBadRequest()) {
                        log.warn(status.errorText());
                    } else {
                        log.error(status.errorText());
                    }
                }
            }
        });
    }
}