package admin.handlers.notification.common;

import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import admin.NotificationAdminServerProperties;
import admin.api.AdminHeader;
import admin.handlers.notification.AbstractNotificationAdminHandler;
import admin.models.common.NotificationAdminServerConfigRequest;
import admin.models.common.NotificationAdminServerConfigResponse;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import uam.api.UamServiceApi;

// Ping Service
@Service
public class NotificationAdminServerConfigRequestHandler extends AbstractNotificationAdminHandler<NotificationAdminServerConfigRequest> {

    public NotificationAdminServerConfigRequestHandler(
            NotificationAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            ApiFactory apiFactory) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, NotificationAdminServerConfigRequest request) throws Exception {

        var res = new NotificationAdminServerConfigResponse();

        async.resume(Response.status(Response.Status.OK).entity(res).build());
    }
}
