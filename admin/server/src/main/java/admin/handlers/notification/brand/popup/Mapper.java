package admin.handlers.notification.brand.popup;

import java.time.ZoneOffset;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import admin.api.AdminHeader;
import admin.models.brand.popup.CreatePopupRequest;
import admin.models.brand.popup.PopupScheduleRule;
import admin.models.brand.popup.UpdateAccountPopupRequest;
import admin.models.brand.popup.UpdatePopupRequest;
import api.v1.CommonMappers;
import notification.api.v1.CreateOrUpdatePopupRequest;
import notification.api.v1.PopupSchedule;

@Component
public class Mapper {
    public CreateOrUpdatePopupRequest mapRequest(AdminHeader header, CreatePopupRequest request) {
        var dtoRules = request.getRules().stream()
                .map(this::mapPopupScheduleRule)
                .toList();
        return CreateOrUpdatePopupRequest.newBuilder()
                .setBrandName(header.getBrand())
                .setCountry(request.getCountry())
                .setCode(request.getCode())
                .setEnabled(request.getEnabled())
                .setSchedule(PopupSchedule.newBuilder().addAllRules(dtoRules))
                .build();
    }

    public CreateOrUpdatePopupRequest mapRequest(UpdatePopupRequest request) {
        var dtoRules = request.getRules().stream()
                .map(this::mapPopupScheduleRule)
                .toList();
        return CreateOrUpdatePopupRequest.newBuilder()
                .setId(request.getId())
                .setEnabled(request.getEnabled())
                .setSchedule(PopupSchedule.newBuilder().addAllRules(dtoRules))
                .build();
    }


    public notification.api.v1.UpdateAccountPopupRequest mapRequest(UpdateAccountPopupRequest request) {
        return notification.api.v1.UpdateAccountPopupRequest.newBuilder()
                .setAccountId(request.getAccountId())
                .setPopupId(request.getPopupId())
                .setTimestamp(request.getTimestamp().toInstant(ZoneOffset.UTC).toEpochMilli())
                .build();
    }

    private notification.api.v1.PopupScheduleRule mapPopupScheduleRule(PopupScheduleRule rule) {
        var builder = notification.api.v1.PopupScheduleRule.newBuilder().setType(rule.getType());
        if (rule.getHours() != null) {
            builder.setHours(rule.getHours());
        }
        if (rule.getDate() != null) {
            builder.setDate(CommonMappers.toDate(rule.getDate()));
        }
        if (CollectionUtils.isNotEmpty(rule.getWeekDays())) {
            builder.addAllWeekDays(rule.getWeekDays());
        }
        return builder.build();
    }
}