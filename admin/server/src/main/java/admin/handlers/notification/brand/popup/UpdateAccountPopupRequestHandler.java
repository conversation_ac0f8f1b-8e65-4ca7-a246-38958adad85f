package admin.handlers.notification.brand.popup;

import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.dispatch.AbstractSafeResponseConsumer;

import admin.NotificationAdminServerProperties;
import admin.api.AdminErrorResponse;
import admin.api.AdminHeader;
import admin.api.AdminOkResponse;
import admin.handlers.notification.AbstractNotificationAdminHandler;
import admin.models.brand.popup.UpdateAccountPopupRequest;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import notification.NotificationServiceApi;
import uam.api.UamServiceApi;
import uam.api.v1.AccountEventType;

@Service
@Slf4j
public class UpdateAccountPopupRequestHandler extends AbstractNotificationAdminHandler<UpdateAccountPopupRequest> {
    private final NotificationServiceApi notificationServiceApi;
    private final Mapper mapper;

    public UpdateAccountPopupRequestHandler(
            NotificationAdminServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamServiceApi uamServiceApi,
            NotificationServiceApi notificationServiceApi,
            ApiFactory apiFactory,
            Mapper mapper) {
        super(props, cloud, meterRegistry, uamServiceApi, apiFactory);
        this.notificationServiceApi = notificationServiceApi;
        this.mapper = mapper;
    }

    @Override
    public void accept(AsyncResponse async, AdminHeader header, UpdateAccountPopupRequest request) throws Exception {

        super.getAccountRoutingInfo(request.getAccountId(), header.getBrand(), async, (identity, routingKey) -> {

            var updateRequest = mapper.mapRequest(request);
            var wrapped = notificationServiceApi.updateAccountPopup(updateRequest, AsciiString.cached(header.getBrand()));

            wrapped.addListener(new AbstractSafeResponseConsumer(wrapped, apiFactory) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    var status = srespw.status();
                    if (status.isOK()) {
                        async.resume(Response.status(Response.Status.OK).entity(new AdminOkResponse()).build());

                        var eventMessage = "Account popup successfully updated: %s".formatted(toJsonString(request));
                        saveAccountEvent(AccountEventType.UPDATE_ACCOUNT_POPUP, header.getUserEmail(), eventMessage, routingKey, identity);
                    } else {
                        async.resume(Response.status(Response.Status.BAD_REQUEST)
                                .entity(new AdminErrorResponse(status.errorCode().toString(), status.errorText()))
                                .build());
                        if (status.isBadRequest()) {
                            log.warn(status.errorText());
                        } else {
                            log.error(status.errorText());
                        }
                    }
                }
            });
        });
    }
}