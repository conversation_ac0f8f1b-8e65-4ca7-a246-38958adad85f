package admin.di;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.QueuePostTemplate;

import admin.AdminTopics;
import admin.NotificationAdminServerProperties;
import admin.api.NotificationAdminBrandApiEndpoint;
import admin.endpoints.DefaultNotificationAdminBrandApiEndpoint;
import admin.handler.AdminMessageDispatcher;
import admin.handlers.notification.AbstractNotificationAdminHandler;
import api.DefaultApiFactory;
import api.v1.ApiFactory;
import notification.DefaultNotificationServiceApi;
import notification.NotificationServiceApi;
import uam.api.DefaultUamServiceApi;
import uam.api.UamServiceApi;

@Configuration
@EnableKafka
@Import({AdminDiModule.class, AdminAcceptorsDiModule.class, AdminKafkaDiModule.class})
@ComponentScan(basePackageClasses = {AbstractNotificationAdminHandler.class})
public class NotificationAdminDiModule {
    @Bean
    public ApiFactory apiFactory(ApplicationProperties props, ObjectMapper mapper) {
        return new DefaultApiFactory(props, mapper);
    }

    @Bean
    public NotificationServiceApi notificationApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultNotificationServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, AdminTopics.RESP));
    }

    @Bean
    public UamServiceApi uamServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultUamServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, AdminTopics.RESP));
    }

    @Bean
    public NotificationAdminBrandApiEndpoint notificationAdminBrandApiEndpoint(
            DynamicCloud cloud,
            ApiFactory apiFactory,
            AdminMessageDispatcher messageDispatcher,
            NotificationAdminServerProperties props) {
        return new DefaultNotificationAdminBrandApiEndpoint(cloud, apiFactory, messageDispatcher, props);
    }

}
