package admin.di;

import admin.api.AdminFeatureApiEndpoint;
import admin.endpoints.DefaultAdminFeaturesApiEndpoint;
import auth.api.AuthServiceApi;
import auth.api.DefaultAuthServiceApi;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.QueuePostTemplate;

import admin.AdminTopics;
import admin.CrmAdminServerProperties;
import admin.api.CrmAdminAccountApiEndpoint;
import admin.api.CrmAdminBrandApiEndpoint;
import admin.endpoints.DefaultCrmAdminAccountApiEndpoint;
import admin.endpoints.DefaultCrmAdminBrandApiEndpoint;
import admin.handler.AdminMessageDispatcher;
import admin.handlers.crm.AbstractCrmAdminHandler;
import api.DefaultApiFactory;
import api.v1.ApiFactory;
import uam.api.DefaultUamServiceApi;
import uam.api.UamServiceApi;

@Configuration
@EnableKafka
@Import({AdminDiModule.class, AdminAcceptorsDiModule.class, AdminKafkaDiModule.class})
@ComponentScan(basePackageClasses = {AbstractCrmAdminHandler.class})
public class CrmAdminDiModule {
    @Bean
    public ApiFactory apiFactory(ApplicationProperties props, ObjectMapper mapper) {
        return new DefaultApiFactory(props, mapper);
    }

    @Bean
    public UamServiceApi uamServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultUamServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, AdminTopics.RESP));
    }

    @Bean
    public AuthServiceApi authServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> kafka, ApiFactory apiFactory) {
        return new DefaultAuthServiceApi(props, kafka, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, AdminTopics.RESP));
    }

    @Bean
    public CrmAdminAccountApiEndpoint remoteAccountApiEndpoint(
            DynamicCloud cloud,
            ApiFactory apiFactory,
            AdminMessageDispatcher messageDispatcher,
            CrmAdminServerProperties props) {
        return new DefaultCrmAdminAccountApiEndpoint(cloud, apiFactory, messageDispatcher, props);
    }

    @Bean
    public CrmAdminBrandApiEndpoint remoteBrandApiEndpoint(
            DynamicCloud cloud,
            ApiFactory apiFactory,
            AdminMessageDispatcher messageDispatcher,
            CrmAdminServerProperties props) {
        return new DefaultCrmAdminBrandApiEndpoint(cloud, apiFactory, messageDispatcher, props);
    }

    @Bean
    public AdminFeatureApiEndpoint defaultAdminFeaturesApiEndpoint(
            DynamicCloud cloud,
            ApiFactory apiFactory,
            AdminMessageDispatcher messageDispatcher,
            CrmAdminServerProperties props) {
        return new DefaultAdminFeaturesApiEndpoint(cloud, apiFactory, messageDispatcher, props);
    }
}
