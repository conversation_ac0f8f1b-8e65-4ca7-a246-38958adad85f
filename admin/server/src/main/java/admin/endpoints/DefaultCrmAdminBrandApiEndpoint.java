package admin.endpoints;

import admin.models.account.ApprovalActionRequest;
import admin.models.account.ApprovalFlowRequest;
import admin.models.brand.CreateBannerTemplateRequest;
import admin.models.brand.dailybonus.CreateDailyBonusTemplateRequest;
import admin.models.brand.CreateEmailTemplateRequest;
import admin.models.brand.CreateHomepageFeatureRequest;
import admin.models.brand.CreateLegalRuleRequest;
import admin.models.brand.CreateOfferTemplateRequest;
import admin.models.brand.CreateOrUpdateWheelOfWinnersRequest;
import admin.models.brand.CreateRewardCampaignRequest;
import admin.models.brand.CreateRewardCreditorRequest;
import admin.models.brand.CreateSocialMediaRewardRequest;
import admin.models.brand.DeleteBrandGiveawayPrizeConfigRequest;
import admin.models.brand.DeleteHomepageFeatureRequest;
import admin.models.brand.GetBannerTemplateRequest;
import admin.models.brand.GetSocialMediaRewardRequest;
import admin.models.brand.InvalidateCacheRequest;
import admin.models.brand.MassEmailSendRequest;
import admin.models.brand.MassRemoteEmailSendRequest;
import admin.models.brand.MassRemoteRewardRequest;
import admin.models.brand.MassRewardRequest;
import admin.models.brand.SaveBrandGiveawayPrizeConfigRequest;
import admin.models.brand.SendSystemMessageRequest;
import admin.models.brand.UpdateBannerTemplateRequest;
import admin.models.brand.dailybonus.UpdateDailyBonusTemplateRequest;
import admin.models.brand.UpdateEmailTemplateRequest;
import admin.models.brand.UpdateHomepageFeatureRequest;
import admin.models.brand.UpdateOfferTemplateRequest;
import admin.models.brand.UpdateRewardCampaignRequest;
import admin.models.brand.UpdateRewardCreditorRequest;
import admin.models.brand.UpdateSocialMediaRewardRequest;
import org.jboss.resteasy.spi.HttpRequest;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import admin.CrmAdminServerProperties;
import admin.api.AdminHeader;
import admin.api.CrmAdminBrandApiEndpoint;
import admin.endpoint.AbstractApiEndpoint;
import admin.handler.AdminMessageDispatcher;
import admin.models.brand.features.CreateBrandFeatureRequest;
import admin.models.brand.features.DeleteBrandFeatureRequest;
import admin.models.brand.features.UpdateBrandFeatureRequest;
import api.v1.ApiFactory;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;

@Component
public class DefaultCrmAdminBrandApiEndpoint extends AbstractApiEndpoint implements CrmAdminBrandApiEndpoint {

    @Inject
    public DefaultCrmAdminBrandApiEndpoint(DynamicCloud cloud, ApiFactory apiFactory, AdminMessageDispatcher messageDispatcher, CrmAdminServerProperties props) {
        super(cloud, apiFactory, messageDispatcher, props);
    }

    @Override
    public void updateBannerTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateBannerTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createBannerTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateBannerTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateDailyBonusTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateDailyBonusTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createDailyBonusTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateDailyBonusTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateEmailTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateEmailTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createEmailTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateEmailTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateOfferTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateOfferTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createOfferTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateOfferTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void invalidateCache(AsyncResponse async, HttpRequest httpReq, AdminHeader header, InvalidateCacheRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void sendSystemMessage(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SendSystemMessageRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    @Deprecated
    public void massEmailSendRequest(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassEmailSendRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createSocialMediaReward(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateSocialMediaRewardRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateSocialMediaReward(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateSocialMediaRewardRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void getSocialMediaReward(AsyncResponse async, HttpRequest httpReq, AdminHeader header, GetSocialMediaRewardRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createRewardCreditor(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateRewardCreditorRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateRewardCreditor(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateRewardCreditorRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createRewardCampaign(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateRewardCampaignRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateRewardCampaign(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateRewardCampaignRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createLegalRule(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateLegalRuleRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void getBannerTemplate(AsyncResponse async, HttpRequest httpReq, AdminHeader header, GetBannerTemplateRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    @Deprecated
    public void massReward(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassRewardRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massRemoteReward(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassRemoteRewardRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massRemoteEmailSendRequest(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassRemoteEmailSendRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createHomepageFeature(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateHomepageFeatureRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateHomepageFeature(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateHomepageFeatureRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void deleteHomepageFeature(AsyncResponse async, HttpRequest httpReq, AdminHeader header, DeleteHomepageFeatureRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void saveBrandGiveawayPrizeConfig(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SaveBrandGiveawayPrizeConfigRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void deleteBrandGiveawayPrizeConfig(AsyncResponse async, HttpRequest httpReq, AdminHeader header, DeleteBrandGiveawayPrizeConfigRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createOrUpdateWheelOfWinners(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateOrUpdateWheelOfWinnersRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createApproval(AsyncResponse async, HttpRequest httpReq, AdminHeader header, ApprovalActionRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateApproval(AsyncResponse async, HttpRequest httpReq, AdminHeader header, ApprovalFlowRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createBrandFeature(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateBrandFeatureRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateBrandFeature(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateBrandFeatureRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void deleteBrandFeature(AsyncResponse async, HttpRequest httpReq, AdminHeader header, DeleteBrandFeatureRequest request) {
        this.send(async, httpReq, header, request);
    }
}
