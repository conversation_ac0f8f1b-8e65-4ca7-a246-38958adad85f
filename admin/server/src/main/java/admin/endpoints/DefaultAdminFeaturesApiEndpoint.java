package admin.endpoints;

import admin.CrmAdminServerProperties;
import admin.api.AdminHeader;
import admin.api.AdminFeatureApiEndpoint;
import admin.endpoint.AbstractApiEndpoint;
import admin.handler.AdminMessageDispatcher;
import admin.models.feature.UpdateFeaturesRequest;
import api.v1.ApiFactory;
import jakarta.ws.rs.container.AsyncResponse;
import org.jboss.resteasy.spi.HttpRequest;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

@Component
public class DefaultAdminFeaturesApiEndpoint extends AbstractApiEndpoint implements AdminFeatureApiEndpoint {

    public DefaultAdminFeaturesApiEndpoint(
            DynamicCloud cloud,
            ApiFactory apiFactory,
            AdminMessageDispatcher messageDispatcher,
            CrmAdminServerProperties props
    ) {
        super(cloud, apiFactory, messageDispatcher, props);
    }

    @Override
    public void updateFeatures(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateFeaturesRequest request) {
        this.send(async, httpReq, header, request);
    }
}
