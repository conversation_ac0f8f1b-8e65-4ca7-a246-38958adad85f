package admin.endpoints;

import org.jboss.resteasy.spi.HttpRequest;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import admin.NotificationAdminServerProperties;
import admin.api.AdminHeader;
import admin.api.NotificationAdminBrandApiEndpoint;
import admin.endpoint.AbstractApiEndpoint;
import admin.handler.AdminMessageDispatcher;
import admin.models.brand.popup.CreatePopupRequest;
import admin.models.brand.popup.UpdatePopupRequest;
import api.v1.ApiFactory;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;

@Component
public class DefaultNotificationAdminBrandApiEndpoint extends AbstractApiEndpoint implements NotificationAdminBrandApiEndpoint {

    @Inject
    public DefaultNotificationAdminBrandApiEndpoint(DynamicCloud cloud, ApiFactory apiFactory, AdminMessageDispatcher messageDispatcher, NotificationAdminServerProperties props) {
        super(cloud, apiFactory, messageDispatcher, props);
    }

    @Override
    public void createPopup(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreatePopupRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updatePopup(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdatePopupRequest request) {
        this.send(async, httpReq, header, request);
    }
}
