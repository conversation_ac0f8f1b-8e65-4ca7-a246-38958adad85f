package admin.endpoints;

import admin.models.account.ChangeAccountEmailRequest;
import admin.models.account.MassAccountCommentRequest;
import org.jboss.resteasy.spi.HttpRequest;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import admin.CrmAdminServerProperties;
import admin.api.AdminHeader;
import admin.api.CrmAdminAccountApiEndpoint;
import admin.endpoint.AbstractApiEndpoint;
import admin.handler.AdminMessageDispatcher;
import admin.models.account.AccountClosureRequest;
import admin.models.account.AccountOptOutArbitrationRequest;
import admin.models.account.CreateAccountSessionRestrictionRequest;
import admin.models.account.CreateAccountTagsRequest;
import admin.models.account.CreateInfluencerRequest;
import admin.models.account.CreateKycIdVerificationRequest;
import admin.models.account.CreateKycPoaVerificationRequest;
import admin.models.account.CreateSegmentationTagsRequest;
import admin.models.account.DeleteAccountRequest;
import admin.models.account.DeleteInfluencerGiveawayPrizeConfigRequest;
import admin.models.account.DeleteSegmentationCategoryRequest;
import admin.models.account.GetAccountXPInfoRequest;
import admin.models.account.MassAccountStatusRequest;
import admin.models.account.MassAccountTagRequest;
import admin.models.account.MassChangeAccountRequest;
import admin.models.account.MassLockAccountRequest;
import admin.models.account.MassSegmentationRequest;
import admin.models.account.MassUnlockAccountRequest;
import admin.models.account.RemoveAccountTagsRequest;
import admin.models.account.SaveInfluencerGiveawayPrizeConfigRequest;
import admin.models.account.SetAccountCommentRequest;
import admin.models.account.SetAccountDailyBonusRequest;
import admin.models.account.SetAccountDeductRewardsRequest;
import admin.models.account.SetAccountGrantRewardsRequest;
import admin.models.account.SetAccountLoyaltyLevelRequest;
import admin.models.account.SetAccountPreferencesRequest;
import admin.models.account.SetAccountRestrictionsRequest;
import admin.models.account.SetAccountVipLevelRequest;
import admin.models.account.SetInfluencerRequest;
import admin.models.account.SetJackpotWinnerRequest;
import admin.models.account.SetKycAttemptsRequest;
import admin.models.account.SetLockAccountRequest;
import admin.models.account.SetPersonalInfoAddressRequest;
import admin.models.account.SetPersonalInfoRequest;
import admin.models.account.SetSegmentationTagsRequest;
import admin.models.account.SetSoftKycRequest;
import admin.models.account.SetUnlockAccountRequest;
import admin.models.account.UpdateAccountSessionRestrictionRequest;
import admin.models.common.ServerConfigRequest;
import api.v1.ApiFactory;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;

@Component
public class DefaultCrmAdminAccountApiEndpoint extends AbstractApiEndpoint implements CrmAdminAccountApiEndpoint {

    @Inject
    public DefaultCrmAdminAccountApiEndpoint(DynamicCloud cloud, ApiFactory apiFactory, AdminMessageDispatcher messageDispatcher, CrmAdminServerProperties props) {
        super(cloud, apiFactory, messageDispatcher, props);
    }

    @Override
    public void setPersonalInfo(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetPersonalInfoRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setPersonalInfoAddress(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetPersonalInfoAddressRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setKycAttempts(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetKycAttemptsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createKycIdVerification(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateKycIdVerificationRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createKycPoaVerification(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateKycPoaVerificationRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createSegmentationTags(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateSegmentationTagsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createTags(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateAccountTagsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void removeTags(AsyncResponse async, HttpRequest httpReq, AdminHeader header, RemoveAccountTagsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setSegmentationTags(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetSegmentationTagsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void deleteSegmentationCategory(AsyncResponse async, HttpRequest httpReq, AdminHeader header, DeleteSegmentationCategoryRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountPreferences(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountPreferencesRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setLockAccount(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetLockAccountRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setUnlockAccount(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetUnlockAccountRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountVipLevel(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountVipLevelRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountGrantRewards(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountGrantRewardsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountDeductRewards(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountDeductRewardsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setSoftKyc(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetSoftKycRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountLoyaltyLevel(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountLoyaltyLevelRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountRestrictions(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountRestrictionsRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void deleteAccount(AsyncResponse async, HttpRequest httpReq, AdminHeader header, DeleteAccountRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void serverConfig(AsyncResponse async, HttpRequest httpReq, AdminHeader header) {
        this.send(async, httpReq, header, new ServerConfigRequest());
    }

    @Override
    public void setJackpotWinner(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetJackpotWinnerRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createInfluencer(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateInfluencerRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setInfluencer(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetInfluencerRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountDailyBonus(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountDailyBonusRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void getAccountXPInfo(AsyncResponse async, HttpRequest httpReq, AdminHeader header, GetAccountXPInfoRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massAccountStatus(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassAccountStatusRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massAccountTag(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassAccountTagRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massChangeAccount(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassChangeAccountRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massLockAccount(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassLockAccountRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massSegmentation(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassSegmentationRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massUnlockAccount(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassUnlockAccountRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void optOutArbitration(AsyncResponse async, HttpRequest httpReq, AdminHeader header, AccountOptOutArbitrationRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void createAccountSessionRestriction(AsyncResponse async, HttpRequest httpReq, AdminHeader header, CreateAccountSessionRestrictionRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void updateAccountSessionRestriction(AsyncResponse async, HttpRequest httpReq, AdminHeader header, UpdateAccountSessionRestrictionRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void setAccountComment(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SetAccountCommentRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void closeAccount(AsyncResponse async, HttpRequest httpReq, AdminHeader header, AccountClosureRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void saveInfluencerGiveawayPrizeConfig(AsyncResponse async, HttpRequest httpReq, AdminHeader header, SaveInfluencerGiveawayPrizeConfigRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void deleteInfluencerGiveawayPrizeConfig(AsyncResponse async, HttpRequest httpReq, AdminHeader header, DeleteInfluencerGiveawayPrizeConfigRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void changeAccountEmail(AsyncResponse async, HttpRequest httpReq, AdminHeader header, ChangeAccountEmailRequest request) {
        this.send(async, httpReq, header, request);
    }

    @Override
    public void massAccountComment(AsyncResponse async, HttpRequest httpReq, AdminHeader header, MassAccountCommentRequest request) {
        this.send(async, httpReq, header, request);
    }
}
