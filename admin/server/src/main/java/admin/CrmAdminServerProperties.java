package admin;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.cfg.ScopedProperty;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;

public class CrmAdminServerProperties extends AdminServerProperties {

    public final Property<BigDecimal> SOCIAL_MEDIA_REWARD_MAX_AMOUNT;
    public final Property<String> BRAND_GROUP;
    public final Property<List<String>> OTP_TRIGGER_RULES_COUNTRIES;
    public final ScopedProperty<Boolean> OFFER_TEMPLATE_MULTI_COUNTRIES_SUPPORT_ENABLED;
    public final Property<BigDecimal> OFFER_TEMPLATE_SWEEPSTAKE_MAX_AMOUNT;
    public final Property<BigDecimal> OFFER_TEMPLATE_SWEEPSTAKE_FIRST_MAX_AMOUNT;
    public final Property<Integer> OFFER_TEMPLATE_PRIORITY_MAX_VALUE;
    public final Property<Integer> OFFER_TEMPLATE_PRIORITY_MIN_VALUE;
    public final Property<Integer> OFFER_TEMPLATE_PRIORITY_DEFAULT_VALUE;
    public final Property<Boolean> OFFER_TEMPLATE_XP_LEVELS_ENABLED;
    public final Property<Boolean> OFFER_TEMPLATE_MIN_WEEKLY_WAGERED_GC_ENABLED;
    public final Property<BigDecimal> MASS_ACCOUNT_REWARD_SWEEPSTAKE_MAX_AMOUNT;

    public CrmAdminServerProperties(DynamicPropertyFactory factory) {
        super(factory);

        SOCIAL_MEDIA_REWARD_MAX_AMOUNT = factory.get("social.media.reward.max-amount", BigDecimal.class).orElse(new BigDecimal(300));

        OTP_TRIGGER_RULES_COUNTRIES = factory.listOfStrings("otp.trigger.rules.countries").orElse(List.of(Locale.US.getCountry()));
        BRAND_GROUP = factory.get("brand.group", String.class).orElse(StringUtils.EMPTY);

        OFFER_TEMPLATE_MULTI_COUNTRIES_SUPPORT_ENABLED = factory.getScoped("offer-template.multi-countries-support.enabled", boolean.class, false);
        OFFER_TEMPLATE_SWEEPSTAKE_MAX_AMOUNT = factory.get("offer-template.sweepstake.max-amount", BigDecimal.class).orElse(new BigDecimal(300));
        OFFER_TEMPLATE_SWEEPSTAKE_FIRST_MAX_AMOUNT = factory.get("offer-template.sweepstake-first.max-amount", BigDecimal.class).orElse(new BigDecimal(300));
        OFFER_TEMPLATE_PRIORITY_MAX_VALUE = factory.get("offer-template.priority.max-value", int.class).orElse(100);
        OFFER_TEMPLATE_PRIORITY_MIN_VALUE = factory.get("offer-template.priority.min-value", int.class).orElse(0);
        OFFER_TEMPLATE_PRIORITY_DEFAULT_VALUE = factory.get("offer-template.priority.default-value", int.class).orElse(50);
        OFFER_TEMPLATE_XP_LEVELS_ENABLED = factory.get("offer-template.xp-levels.enabled", boolean.class).orElse(false);
        OFFER_TEMPLATE_MIN_WEEKLY_WAGERED_GC_ENABLED = factory.get("offer-template.min-weekly-wagered-gc.enabled", boolean.class).orElse(false);
        MASS_ACCOUNT_REWARD_SWEEPSTAKE_MAX_AMOUNT = factory.get("mass-account-reward.sweepstake.max-amount", BigDecimal.class).orElse(new BigDecimal(300));
    }
}
