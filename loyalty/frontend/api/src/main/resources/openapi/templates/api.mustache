package {{package}};

{{#imports}}import {{import}};
{{/imports}}

import {{javaxPackage}}.ws.rs.*;
import {{javaxPackage}}.ws.rs.core.Response;
import jakarta.ws.rs.core.Context;
import org.jboss.resteasy.spi.HttpRequest;
import io.netty.channel.ChannelHandlerContext;


{{#supportAsync}}
import java.util.concurrent.CompletionStage;
import java.util.concurrent.CompletableFuture;
{{/supportAsync}}

import java.io.InputStream;
import java.util.Map;
import java.util.List;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
{{#useBeanValidation}}import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;{{/useBeanValidation}}

@Path("{{commonPath}}")
{{#hasConsumes}}
@Consumes({ {{#consumes}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/consumes}} }){{/hasConsumes}}{{#hasProduces}}
@Produces({ {{#produces}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/produces}} }){{/hasProduces}}
{{>generatedAnnotation}}
public {{#interfaceOnly}}interface{{/interfaceOnly}}{{^interfaceOnly}}class{{/interfaceOnly}} {{classname}} {
{{#operations}}
{{#operation}}
    @{{httpMethod}}
    @Path("{{path}}")
    {{#hasConsumes}}
    @Consumes({ {{#consumes}}"{{{mediaType}}}"{{#hasMore}}, {{/hasMore}}{{/consumes}} })
    {{/hasConsumes}}
    {{#hasProduces}}
    @Produces({ {{#produces}}"{{{mediaType}}}"{{#hasMore}}, {{/hasMore}}{{/produces}} })
    {{/hasProduces}}
    {{#vendorExtensions.x-async-enabled}}void{{/vendorExtensions.x-async-enabled}}{{^vendorExtensions.x-async-enabled}}Response{{/vendorExtensions.x-async-enabled}} {{nickname}}({{#vendorExtensions.x-async-enabled}}@Context HttpRequest httpReq, @Context ChannelHandlerContext ctx, @Suspended final AsyncResponse asyncResponse{{/vendorExtensions.x-async-enabled}}) throws Throwable;
{{/operation}}
}
{{/operations}}