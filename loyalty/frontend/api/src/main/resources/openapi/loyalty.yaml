openapi: 3.0.1
info:
  title: Loyalty API
  version: '1.0'

tags:
  - name: 'Loyalty'
paths:
  /v1/loyalty/account/list:
    post:
      tags:
        - 'Loyalty'
      operationId: 'getAccountLoyaltySystems'
      summary: 'Get account loyalty systems.'
      description: >
        Get account loyalty systems.
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAccountLoyaltySystemsRequestBody'
      responses:
        '200':
          description: 'Loyalty System successfully get.'
          headers:
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountLoyaltySystemsResponseBody'
  /v1/loyalty/variant/list:
    post:
      tags:
        - 'Variant'
      operationId: 'getAccountVariants'
      summary: 'Get account variants.'
      description: >
        Get account variants.
      x-async-enabled: true
      parameters:
        - $ref: '#/components/parameters/X-Message-Id'
        - $ref: '#/components/parameters/X-Timestamp'
        - $ref: '#/components/parameters/brandName'
        - $ref: '#/components/parameters/platform'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAccountVariantsRequestBody'
      responses:
        '200':
          description: 'Variant successfully get.'
          headers:
            X-Message-Id:
              $ref: '#/components/headers/X-Message-Id'
            X-Took-Id:
              $ref: '#/components/headers/X-Took'
            X-Status-Text:
              $ref: '#/components/headers/X-Status-Text'
            X-Status:
              $ref: '#/components/headers/X-Status'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountVariantsResponseBody'
components:
  headers:
    X-Message-Id:
      description: UUID of original request
      schema:
        type: string
        format: uuid
    X-Took:
      description: Processing time
      schema:
        type: integer
    X-Status:
      description: 'Operation status with possible values (err_ok, err_auth, etc.)'
      schema:
        $ref: '#/components/schemas/Status'
    X-Status-Text:
      description: Operation text
      schema:
        type: string

  parameters:
    X-Message-Id:
      in: header
      name: X-Message-Id
      description: 'UUID of the original request'
      required: true
      schema:
        type: string
        format: uuid
    X-Timestamp:
      in: header
      name: X-Timestamp
      description: 'Unix request timestamp'
      required: true
      schema:
        type: integer
        format: timestamp
    brandName:
      in: query
      name: brandName
      description: 'Brand name'
      required: true
      schema:
        type: string
    platform:
      in: query
      name: platform
      description: 'Platform'
      required: true
      schema:
        type: string
        enum: [ web, android, ios ]

  schemas:
    Status:
      type: string
      enum: [ err_ok, err_auth, err_system, err_too_many_request, err_bad_request, err_duplicate, err_not_found, err_denied ]
    GetAccountLoyaltySystemsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]
    GetAccountLoyaltySystemsResponseBody:
      type: object
      properties:
        loyaltySystems:
          type: array
          items:
            $ref: '#/components/schemas/AccountLoyaltySystem'
    AccountLoyaltySystem:
      type: object
      description: 'Loyalty system assigned to a player.'
      properties:
        accountLoyaltySystemCode:
          type: string
          description: 'Code of the account loyalty system.'
        xpCurrent:
          type: number
        detailsUrl:
          type: string
        popupReminderIntervalDays:
          type: integer
        xpVariant:
          $ref: '#/components/schemas/AccountVariants'
        levelCurrent:
          $ref: '#/components/schemas/LoyaltySystemLevel'
        levelNext:
          $ref: '#/components/schemas/LoyaltySystemLevel'
        levelType:
          $ref: '#/components/schemas/LoyaltySystemLevelType'
        status:
          $ref: '#/components/schemas/LoyaltySystemStatus'
        subLevelNumber:
          type: integer
          description: 'Current sub-level number within the main level. Starts from 1.'
        subLevelsInLevel:
          type: integer
          description: 'Total number of sub-levels in the current main level.'
        subLevelNextXpRequired:
          type: number
          description: 'XP required to reach the next sub-level.'
    LoyaltySystemStatus:
      type: string
      enum:
        - ACTIVE
    LoyaltySystemLevelType:
      type: string
      enum:
        - NAMED
        - NUMBERED
    LoyaltyAccount:
      type: object
      properties:
        id:
          type: string
          format: uuid
    LoyaltySystemLevel:
      type: object
      description: 'Base schema for loyalty system levels.'
      required:
        - scope
        - xpRequired
      properties:
        scope:
          type: string
          description: 'Scope of the loyalty system level.'
        xpRequired:
          type: number
          description: 'XP required to reach this level.'
        isSubLevel:
          type: boolean
          description: 'Indicates if this level is a sub-level of a main level.'
      discriminator:
        propertyName: type
        mapping:
          NAMED: '#/components/schemas/NamedLoyaltySystemLevel'
          NUMBERED: '#/components/schemas/NumberedLoyaltySystemLevel'
    NamedLoyaltySystemLevel:
      allOf:
        - $ref: '#/components/schemas/LoyaltySystemLevel'
        - type: object
          description: 'Named loyalty system level.'
          required:
            - name
            - icon
          properties:
            name:
              type: string
              description: 'Name of the loyalty level.'
            icon:
              type: string
              description: 'Small icon of the loyalty level.'
    NumberedLoyaltySystemLevel:
      allOf:
        - $ref: '#/components/schemas/LoyaltySystemLevel'
        - type: object
          description: 'Numbered loyalty system level.'
          required:
            - rank
          properties:
            rank:
              type: integer
              description: 'Rank of the loyalty level.'
    GetAccountVariantsRequestBody:
      x-implements: [ 'gateway.api.RequestBody' ]
      type: object
      allOf: [ ]
    GetAccountVariantsResponseBody:
      type: object
      properties:
        variants:
          type: array
          items:
            $ref: '#/components/schemas/AccountVariants'
    AccountVariants:
      type: object
      description: 'Variants assigned to a player.'
      properties:
        balanceCode:
          type: string
        variantCode:
          type: string
        variantName:
          type: string
        variantAbbreviation:
          type: string
        iconUrlSm:
          type: string
        iconUrlLg:
          type: string
        points:
          type: number
    # ~~~ Notifications
    LoyaltyXpPointsUpdateNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This account-specific websocket notification is sent when loyalty XP points are updated.
      required:
        - accountVariantCode
        - accountLoyaltySystemCode
        - xpBefore
        - xpCurrent
      properties:
        accountVariantCode:
          type: string
          description: 'Code of the xp variant system.'
        accountLoyaltySystemCode:
          type: string
          description: 'Code of the account loyalty system.'
        xpBefore:
          type: number
          description: 'XP points before the update.'
        xpCurrent:
          type: number
          description: 'XP points after the update.'
    LoyaltyXpLevelUpdateNotification:
      x-implements: [ 'gateway.api.ResponseOrNotification' ]
      type: object
      description: >
        This account-specific websocket notification is sent when loyalty XP level is updated.
      required:
        - accountLoyaltySystemCode
      properties:
        accountLoyaltySystemCode:
          type: string
          description: 'Code of the account loyalty system.'
        levelCurrent:
          $ref: '#/components/schemas/LoyaltySystemLevel'
        levelNext:
          $ref: '#/components/schemas/LoyaltySystemLevel'
        subLevelNumber:
          type: integer
          description: 'Current sub-level number within the main level. Starts from 1.'
        subLevelsInLevel:
          type: integer
          description: 'Total number of sub-levels in the current main level.'
        subLevelNextXpRequired:
          type: number
          description: 'XP required to reach the next sub-level.'

