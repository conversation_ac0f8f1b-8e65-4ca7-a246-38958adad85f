package offerchain.gateway.di;

import api.v1.ApiFactory;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.QueuePostTemplate;

import offerchain.api.DefaultOfferchainServiceApi;
import offerchain.api.OfferchainServiceApi;
import offerchain.gateway.OfferChainGatewayTopics;
import offerchain.worker.DefaultOfferChainWorkerServiceApi;
import offerchain.worker.OfferChainWorkerServiceApi;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OfferChainGatewayServiceApiDiModule {

    @Bean
    public OfferchainServiceApi offerChainServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultOfferchainServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, OfferChainGatewayTopics.RESP));
    }

    @Bean
    public OfferChainWorkerServiceApi workerServiceApi(QueuePostTemplate<?> postTemplate) {
        return new DefaultOfferChainWorkerServiceApi(postTemplate);
    }
}
