package offerchain.gateway.endpoints;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.http.HttpProto;
import io.netty.channel.ChannelHandlerContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import offerchain.gateway.dto.AssignOfferChainRequest;

import static engagement.provider.AbstractBloomreachApiEndpoint.BLOOMREACH_PATH;
import static engagement.provider.AbstractBloomreachApiEndpoint.OFFER_CHAIN_PATH;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Offer chain", description = "Offer chain API")
@SecurityScheme(type = SecuritySchemeType.APIKEY, name = "BloomreachOfferChainSecurity", in = SecuritySchemeIn.HEADER,
        description = "Enter the token with the `Bearer: ` prefix", paramName = HttpHeaders.AUTHORIZATION)
@SecurityRequirement(name = "BloomreachOfferChainSecurity")
@ApiResponse(responseCode = "202", description = "Success")
@ApiResponse(responseCode = "304", description = "Missing request id or uuids")
@ApiResponse(responseCode = "500", description = "Internal error")
@Path(HttpProto.V1)
public interface OfferChainBloomreachApiEndpoint {

    @POST
    @Path(OFFER_CHAIN_PATH + "/assign")
    @ApiEndpoint(rateLimiterKey = "offerchain.bloomreach")
    @Operation(
            summary = "Offer-chain",
            description = "Assign offer chain instances")
    void createOfferChainInstances(@Suspended AsyncResponse async,
                                   @Context ChannelHandlerContext ctx,
                                   @Context HttpHeaders headers,
                                   @NotNull @Valid AssignOfferChainRequest offerChainRequest) throws Exception;

    @POST
    @Path(BLOOMREACH_PATH + "/offer-chain")
    @ApiEndpoint(rateLimiterKey = "offerchain.bloomreach")
    @Operation(
            summary = "Offer-chain",
            description = "Assign offer chain instances")
    void createOfferChainInstancesDeprecated(@Suspended AsyncResponse async,
                                             @Context ChannelHandlerContext ctx,
                                             @Context HttpHeaders headers,
                                             @NotNull @Valid AssignOfferChainRequest offerChainRequest) throws Exception;
}
