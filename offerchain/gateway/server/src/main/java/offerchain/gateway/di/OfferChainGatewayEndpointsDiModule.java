package offerchain.gateway.di;

import api.v1.ApiFactory;
import com.turbospaces.cfg.ApplicationProperties;
import offerchain.gateway.endpoints.OfferChainBloomreachApiEndpoint;
import offerchain.gateway.endpoints.OfferChainBloomreachApiEndpointImpl;
import offerchain.worker.OfferChainWorkerServiceApi;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OfferChainGatewayEndpointsDiModule {

    @Bean
    public OfferChainBloomreachApiEndpoint bloomreachOfferChainApiEndpoint(
            ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            OfferChainWorkerServiceApi workerApi) {
        return new OfferChainBloomreachApiEndpointImpl(props, apiFactory, cloud, workerApi);
    }
}
