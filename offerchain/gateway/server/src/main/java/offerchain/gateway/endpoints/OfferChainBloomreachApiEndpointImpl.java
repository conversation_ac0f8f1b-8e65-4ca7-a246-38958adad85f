package offerchain.gateway.endpoints;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.function.BiConsumer;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;

import com.google.protobuf.Any;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.AccountRoutingFlatten;
import api.v1.ApiFactory;
import engagement.provider.AbstractBloomreachApiEndpoint;

import io.netty.channel.ChannelHandlerContext;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

import offerchain.gateway.dto.AssignOfferChainRequest;
import offerchain.api.v1.CreateOfferChainInstanceRequest;
import offerchain.worker.OfferChainWorkerServiceApi;
import offerchain.worker.api.v1.OfferChainBackgroundRequest;

@Slf4j
@Hidden
public class OfferChainBloomreachApiEndpointImpl extends AbstractBloomreachApiEndpoint implements OfferChainBloomreachApiEndpoint {

    private final OfferChainWorkerServiceApi offerchainWorkerApi;

    @Inject
    public OfferChainBloomreachApiEndpointImpl(
            ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            OfferChainWorkerServiceApi offerchainWorkerApi) {
        super(props, apiFactory, cloud);
        this.offerchainWorkerApi = Objects.requireNonNull(offerchainWorkerApi);
    }

    @Override
    public void createOfferChainInstances(AsyncResponse async, ChannelHandlerContext ctx,
            HttpHeaders headers, @NotNull @Valid AssignOfferChainRequest req) {
        log.info("Received createOfferChainInstances request for brand '{}' with requestId '{}' uuid count {}",
                req.brand, req.requestId, req.uuids.size());
        if (isForbidden(headers, req.brand, async)) {
            return;
        }
        if (req.uuids.isEmpty()) {
            log.warn("No account UUIDs provided");
            async.resume(Response.notModified().build());
            return;
        }
        if (!req.uuids.stream().allMatch(AbstractBloomreachApiEndpoint::validUUID)) {
            log.warn("Invalid [Account Id] format in one of provided uuids");
            async.resume(Response.notModified().build());
            return;
        }

        if (StringUtils.isEmpty(req.requestId)) {
            log.warn("Missing [Request Id] for Offer Chain batch processing");
            async.resume(Response.notModified().build());
            return;
        }

        List<? extends CompletableFuture<?>> futures = req.uuids.stream()
                .map(userId -> createOfferChainInstance(userId, req).toCompletableFuture())
                .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .whenComplete((BiConsumer<Object, Throwable>) (_, err) -> {
                    if (Objects.nonNull(err)) {
                        log.error("Failed to create offer chain batch of {} uuids", req.uuids.size(), err);
                        async.resume(Response.serverError().entity(Map.of(
                                "error", err.getMessage())).build());
                    } else {
                        log.info("Offer chain batch created successfully");
                        async.resume(Response.accepted().build());
                    }
                });
    }

    @Override
    public void createOfferChainInstancesDeprecated(AsyncResponse async, ChannelHandlerContext ctx,
            HttpHeaders headers, AssignOfferChainRequest offerChainRequest) {
        createOfferChainInstances(async, ctx, headers, offerChainRequest);
    }

    private CompletionStage<?> createOfferChainInstance(String uuid, AssignOfferChainRequest request) {
        var flatten = AccountRoutingFlatten.standard().readExternal(uuid);
        var externalAccount = offerchain.api.v1.ExternalAccount.newBuilder()
                .setAccountId(flatten.getAccountId())
                .setBrandName(request.brand)
                .setHash(flatten.getRoutingKey().toString())
                .build();

        var builder = CreateOfferChainInstanceRequest.newBuilder();
        builder.setExternalAccount(externalAccount);
        builder.setBrandName(request.brand);
        builder.setRequestId(request.requestId);
        builder.setOverwriteExistingInstance(request.overwriteExistingInstance);
        builder.setOfferChainTemplateCode(request.offerChainTemplateCode);
        if (StringUtils.isNotBlank(request.availableFrom)) {
            builder.setAvailableFrom(request.availableFrom);
        }

        var req = OfferChainBackgroundRequest.newBuilder()
                .setRequest(Any.pack(builder.build()))
                .setRoutingKey(flatten.getRoutingKey().toString());

        log.debug("Created offer chain instance request for userId '{}'", uuid);
        return offerchainWorkerApi.processInBackground(req.build());
    }
}
