package offerchain.gateway.dto;

import engagement.provider.model.BloomreachRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Setter;
import lombok.ToString;

@ToString
@Setter
public class AssignOfferChainRequest extends BloomreachRequest {

    @Schema(
            description = "Code of the offer chain template to be applied",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotBlank
    public String offerChainTemplateCode;

    @Schema(
            description = "Flag indicating whether to overwrite existing instances. If set to true, existing instances will be overwritten.",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    public Boolean overwriteExistingInstance = false;

    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$",
            message = "availableFrom must be in ISO 8601 format (e.g., 2025-12-01T00:00:00Z)")
    @Schema(
            description = "Date and time when the offer becomes available, in ISO 8601 format (e.g., 2025-12-01T00:00:00Z)",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    public String availableFrom;
}
