package admin.models.offer.chain.common;

import java.util.List;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class AdminOfferChainMapItem {

    private Long id;

    @NotNull
    @Size(min = 1, max = 256)
    private String name;

    @Size(min = 36, max = 36)
    private String code;

    @NotNull
    @Max(64)
    private Integer position;

    @NotNull
    private AdminOfferChainMapItemType type;

    @Size(max = 32)
    private String displayTagline;

    @Size(max = 256)
    private String displayTitle;

    @Size(max = 2000)
    private String displayDescription;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String backgroundImageUrl;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String smallOpenIconUrl;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String largeOpenIconUrl;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String smallDoneIconUrl;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String largeDoneIconUrl;

    private List<AdminOfferChainMapItemOffer> content;
}
