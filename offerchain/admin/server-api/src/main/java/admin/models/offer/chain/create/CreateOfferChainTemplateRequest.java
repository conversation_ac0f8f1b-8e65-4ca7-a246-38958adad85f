package admin.models.offer.chain.create;

import java.util.List;

import admin.api.AdminRequest;
import admin.models.offer.chain.common.AdminOfferChainMapItem;
import admin.models.offer.chain.common.AdminOfferChainMapTheme;
import admin.models.offer.chain.common.AdminOfferChainMapType;
import admin.models.offer.chain.common.AdminOfferChainTemplatePlacementTypeInfo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CreateOfferChainTemplateRequest implements AdminRequest {
    private Long id;

    @NotNull
    @Size(min = 3, max = 128)
    @Pattern(regexp = "^[a-zA-Z0-9_\\-\\s'?!.]*$")
    private String name;

    @NotNull
    @Size(min = 3, max = 128)
    private String displayName;

    @Size(min = 3, max = 96)
    private String displayNameShort;

    @Size(max = 16)
    private String displayTagline;

    @Size(max = 2048)
    private String displayDescription;

    @Size(max = 512)
    private String displayDescriptionShort;

    @NotNull
    private Boolean isActive;

    @NotNull
    private Boolean isTest;

    @NotNull
    private Boolean isHidden;

    @NotNull
    private Boolean isTimeLimited;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String iconSmall;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String iconLarge;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String iconInbox;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String arrowImgUrl;

    @Pattern(regexp = "^(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z)?$")
    private String availableFrom;

    @Pattern(regexp = "^(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z)?$")
    private String availableTo;

    @NotNull
    @Min(1)
    @Max(1095)
    private Integer expiresAfterDays;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String termUrl;

    @NotNull
    @Valid
    @NotEmpty
    private List<AdminOfferChainTemplatePlacementTypeInfo> placements;

    @NotNull
    @Valid
    private AdminOfferChainMapType mapType;

    @NotNull
    @Valid
    private AdminOfferChainMapTheme mapTheme;

    @Valid
    @NotEmpty
    private List<AdminOfferChainMapItem> items;

    private Boolean rewardOfferOnly;
}

