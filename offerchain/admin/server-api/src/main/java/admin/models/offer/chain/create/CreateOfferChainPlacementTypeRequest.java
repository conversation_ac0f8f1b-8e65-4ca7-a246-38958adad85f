package admin.models.offer.chain.create;

import admin.api.AdminRequest;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateOfferChainPlacementTypeRequest implements AdminRequest {

    @NotNull
    @Size(min = 3, max = 256)
    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-%_.]+$")
    private String name;

    @NotNull
    @Size(min = 3, max = 256)
    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-%_.]+$")
    private String featureKey;
}
