package admin.models.offer.chain.common;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OfferChainMapItemTypeItem {

    @NotNull
    @Size(min = 1, max = 128)
    private String code;

    @Size(max = 256)
    private String name;
}
