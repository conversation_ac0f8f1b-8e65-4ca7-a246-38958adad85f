package admin.models.offer.chain.common;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AdminOfferChainMapItemOffer {
    private Long id;

    @NotNull
    private AdminOfferChainMapItemOfferType type;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String backgroundUrl;

    @Size(max = 16)
    private String displayTagline;

    @Size(max = 2048)
    private String displayDescription;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String smallOpenIconUrl;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String largeOpenIconUrl;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String smallDoneIconUrl;

    @Size(max = 2048)
    @Pattern(regexp = "^https://.*")
    private String largeDoneIconUrl;

    private UUID code;

    private UUID rewardCode;

    // ~ drop after migration
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer gcAmount;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer scAmount;

    @Deprecated(since = "drop after migration", forRemoval = true)
    private BigDecimal freeSpinsValue;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer freeSpinsAmount;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer freeSpinsBetLevel;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer freeSpinsGameId;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private BigDecimal fallback1Value;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer fallback1Game;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer fallback1Bet;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private BigDecimal fallback2Value;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer fallback2Game;
    @Deprecated(since = "drop after migration", forRemoval = true)
    private Integer fallback2Bet;

    private List<String> randomRewardIds;

    private String purchaseOfferTemplateCode;
}
