package admin.api;

import static io.swagger.v3.oas.annotations.enums.SecuritySchemeType.APIKEY;

import org.jboss.resteasy.spi.HttpRequest;

import admin.models.common.OfferChainServerConfigResponse;
import admin.models.offer.chain.create.CreateOfferChainInstanceRequest;
import admin.models.offer.chain.create.CreateOfferChainPlacementTypeRequest;
import admin.models.offer.chain.create.CreateOfferChainPlacementTypeResponse;
import admin.models.offer.chain.create.CreateOfferChainTemplateRequest;
import admin.models.offer.chain.create.CreateOfferChainTemplateResponse;
import admin.models.offer.chain.delete.DeleteOfferChainInstanceByTemplateRequest;
import admin.models.offer.chain.delete.DeleteOfferChainInstanceRequest;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;

@Path(OfferChainApiEndpoint.V_1_ADMIN)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityScheme(type = APIKEY, in = SecuritySchemeIn.HEADER, name = "ApiKeyAuth", paramName = HttpHeaders.AUTHORIZATION)
@SecurityRequirement(name = "ApiKeyAuth")
@ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = AdminOkResponse.class)))
@ApiResponse(responseCode = "400", content = @Content(schema = @Schema(implementation = AdminErrorResponse.class)))
public interface OfferChainApiEndpoint {
    String V_1_ADMIN = "v1/admin";

    String ENGAGEMENT = "/engagement";
    String OFFER_CHAIN_CREATE_TEMPLATE = "/CreateOfferChainTemplate";
    String OFFER_CHAIN_CREATE_PLACEMENT_TYPE = "/CreateOfferChainPlacementType";
    String OFFER_CHAIN_CREATE_INSTANCE = "/CreateOfferChainInstance";
    String OFFER_CHAIN_DELETE_INSTANCE = "/DeleteOfferChainInstance";
    String OFFER_CHAIN_DELETE_INSTANCE_BY_TEMPLATE = "/DeleteOfferChainInstanceByTemplate";
    String OFFER_CHAIN_SERVER_CONFIG = "/server/config";

    String OFFER_CHAIN = "/offerchain";
    String CREATE_TEMPLATE = "/template/create";
    String CREATE_PLACEMENT_TYPE = "/placement/create";
    String CREATE_INSTANCE = "/instance/create";
    String DELETE_INSTANCE = "/instance/delete";
    String DELETE_INSTANCE_BY_TEMPLATE = "/instance/deleteByTemplate";
    String SERVER_CONFIG = "/server/config";


    /**
     * Creates a new offer chain template based on the provided request details.
     */
    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CreateOfferChainTemplateResponse.class)))
    @POST
    @Path(ENGAGEMENT + OFFER_CHAIN_CREATE_TEMPLATE)
    void createOfferChainTemplateDeprecated(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid CreateOfferChainTemplateRequest request);

    /**
     * Creates a new offer chain placement type using the specified request data.
     */
    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @ApiResponse(responseCode = "200",
            content = @Content(
                    schema = @Schema(
                            implementation = CreateOfferChainPlacementTypeResponse.class)))
    @POST
    @Path(ENGAGEMENT + OFFER_CHAIN_CREATE_PLACEMENT_TYPE)
    void createOfferChainPlacementTypeDeprecated(@Suspended AsyncResponse async,
                                       @Context HttpRequest httpReq,
                                       @BeanParam AdminHeader header,
                                       @NotNull @Valid CreateOfferChainPlacementTypeRequest request);

    /**
     * Creates a new offer chain instance based on the provided request.
     */
    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @POST
    @Path(ENGAGEMENT + OFFER_CHAIN_CREATE_INSTANCE)
    void createOfferChainInstanceDeprecated(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid CreateOfferChainInstanceRequest request);

    /**
     * Deletes an existing offer chain instance as specified in the request.
     */
    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @POST
    @Path(ENGAGEMENT + OFFER_CHAIN_DELETE_INSTANCE)
    void deleteOfferChainInstanceDeprecated(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid DeleteOfferChainInstanceRequest request);

    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @ApiResponse(responseCode = "500", content = @Content(schema = @Schema(implementation = AdminErrorResponse.class)))
    @POST
    @Path(ENGAGEMENT + OFFER_CHAIN_DELETE_INSTANCE_BY_TEMPLATE)
    void deleteOfferChainInstanceByTemplateDeprecated(@Suspended AsyncResponse async,
                                            @Context HttpRequest httpReq,
                                            @BeanParam AdminHeader header,
                                            @NotNull @Valid DeleteOfferChainInstanceByTemplateRequest request);

    @Tag(name = "Other", description = "Retool helper endpoint")
    @POST
    @Path(ENGAGEMENT + OFFER_CHAIN_SERVER_CONFIG)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = OfferChainServerConfigResponse.class)))
    void serverConfigDeprecated(@Suspended AsyncResponse async,
                      @Context HttpRequest httpReq,
                      @BeanParam AdminHeader header);

    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = CreateOfferChainTemplateResponse.class)))
    @POST
    @Path(OFFER_CHAIN + CREATE_TEMPLATE)
    void createOfferChainTemplate(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid CreateOfferChainTemplateRequest request);

    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @ApiResponse(responseCode = "200",
            content = @Content(
                    schema = @Schema(
                            implementation = CreateOfferChainPlacementTypeResponse.class)))
    @POST
    @Path(OFFER_CHAIN + CREATE_PLACEMENT_TYPE)
    void createOfferChainPlacementType(@Suspended AsyncResponse async,
                                       @Context HttpRequest httpReq,
                                       @BeanParam AdminHeader header,
                                       @NotNull @Valid CreateOfferChainPlacementTypeRequest request);

    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @POST
    @Path(OFFER_CHAIN + CREATE_INSTANCE)
    void createOfferChainInstance(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid CreateOfferChainInstanceRequest request);

    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @POST
    @Path(OFFER_CHAIN + DELETE_INSTANCE)
    void deleteOfferChainInstance(@Suspended AsyncResponse async,
                                  @Context HttpRequest httpReq,
                                  @BeanParam AdminHeader header,
                                  @NotNull @Valid DeleteOfferChainInstanceRequest request);

    @Tag(name = "Offer chain", description = "Admin - Offer chain API Endpoints")
    @ApiResponse(responseCode = "500", content = @Content(schema = @Schema(implementation = AdminErrorResponse.class)))
    @POST
    @Path(OFFER_CHAIN + DELETE_INSTANCE_BY_TEMPLATE)
    void deleteOfferChainInstanceByTemplate(@Suspended AsyncResponse async,
                                            @Context HttpRequest httpReq,
                                            @BeanParam AdminHeader header,
                                            @NotNull @Valid DeleteOfferChainInstanceByTemplateRequest request);

    @Tag(name = "Other", description = "Retool helper endpoint")
    @POST
    @Path(OFFER_CHAIN + SERVER_CONFIG)
    @ApiResponse(responseCode = "200", content = @Content(schema = @Schema(implementation = OfferChainServerConfigResponse.class)))
    void serverConfig(@Suspended AsyncResponse async,
                      @Context HttpRequest httpReq,
                      @BeanParam AdminHeader header);
}
