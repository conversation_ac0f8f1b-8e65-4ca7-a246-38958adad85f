<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="back_office"/>
        <createSchema name="game_hub"/>
        <createSchema name="reward"/>
        <createSchema name="worker"/>
        <createTable name="uam.accepted_coins" pkName="pk_accepted_coins">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_accepted_coins_account_id"/>
            <column name="acceptance_time" type="localdatetime"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="localdatetime" notnull="true"/>
            <column name="modified_at" type="localdatetime" notnull="true"/>
            <uniqueConstraint name="uq_accepted_coins_account_id" columnNames="account_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.accepted_payment_terms" pkName="pk_accepted_payment_terms">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_accepted_payment_terms_account_id" foreignKeyIndex="ix_accepted_payment_terms_account_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="accepted_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_accepted_payment_terms_account_id_code" columnNames="account_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <addColumn tableName="uam.accounts">
            <column name="country" type="varchar(2) DEFAULT 'US'" notnull="true"/>
            <column name="internal" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <createTable name="uam.account_auth_info" pkName="pk_account_auth_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_auth_info_id"/>
            <column name="first_utm_id" type="bigint" references="uam.utm_templates.id" foreignKeyName="fk_account_auth_info_first_utm_id" foreignKeyIndex="ix_account_auth_info_first_utm_id"/>
            <column name="sign_up" type="timestamp" notnull="true"/>
            <column name="last_sign_in_country" type="varchar(2)"/>
            <column name="last_sign_in_state" type="varchar"/>
            <column name="sign_up_country" type="varchar(2)"/>
            <column name="sign_up_state" type="varchar"/>
        </createTable>
        <addColumn tableName="uam.account_balances">
            <column name="guest_lotto_winning" type="decimal(16,2)" notnull="true"/>
            <column name="redeemable" type="decimal(16,2)" notnull="true"/>
            <column name="unplayed" type="decimal(16,2)" notnull="true"/>
            <column name="pending_withdraw" type="decimal(16,2)" notnull="true"/>
            <column name="owed" type="decimal(16,2)" notnull="true"/>
            <column name="bonus" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <createTable name="payment.account_card_aggregation" pkName="pk_account_card_aggregation">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="fingerprint" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_card_aggregation_account_id" foreignKeyIndex="ix_account_card_aggregation_account_id"/>
            <column name="at" type="date"/>
            <column name="purchase_sum_base_amount" type="decimal"/>
            <column name="purchase_sum_local_currency_amount" type="decimal"/>
            <column name="purchase_count" type="bigint" notnull="true"/>
            <column name="deposit_sum_base_amount" type="decimal"/>
            <column name="deposit_sum_local_currency_amount" type="decimal"/>
            <column name="deposit_count" type="bigint" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_account_card_aggregation_fingerprint_at_account_id" columnNames="fingerprint,at,account_id" oneToOne="false" nullableColumns="at"/>
        </createTable>
        <createTable name="fraud.account_card_verification_info" pkName="pk_account_card_verification_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" uniqueOneToOne="uq_account_card_verification_info_account_id" references="fraud.accounts.id" foreignKeyName="fk_account_card_verification_info_account_id"/>
            <column name="email_sent_at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_closures" pkName="pk_account_closures">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="reason" type="varchar"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_closures_account_id" foreignKeyIndex="ix_account_closures_account_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="deleted" type="boolean" defaultValue="false" notnull="true"/>
        </createTable>
        <createTable name="uam.account_comments" pkName="pk_account_comments">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_comments_account_id" foreignKeyIndex="ix_account_comments_account_id"/>
            <column name="comment" type="varchar" notnull="true"/>
            <column name="created_by" type="varchar(50)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="uam.account_credentials">
            <column name="nonce" type="varchar(32)"/>
            <column name="provider" type="varchar(8)" checkConstraint="check ( provider in ('internal','auth0'))" checkConstraintName="ck_account_credentials_provider"/>
        </addColumn>
        <addColumn tableName="uam.account_credentials_history">
            <column name="nonce" type="varchar(32)"/>
        </addColumn>
        <alterColumn columnName="next_bonus_at" tableName="uam.account_daily_bonuses" type="timestamp" currentType="localdatetime" currentNotnull="false"/>
        <addColumn tableName="uam.account_daily_bonuses">
            <column name="initial_segment" type="varchar(20)"/>
            <column name="segment" type="varchar(20)"/>
            <column name="segment_updated_at" type="timestamp"/>
        </addColumn>
        <createTable name="payment.account_deferred_purchase_limit" pkName="pk_account_deferred_purchase_limit">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="threshold" type="decimal(16,2)" notnull="true"/>
            <column name="period" type="varchar(7)" notnull="true" checkConstraint="check ( period in ('daily','weekly','monthly'))" checkConstraintName="ck_account_deferred_purchase_limit_period"/>
            <column name="type" type="varchar(6)" notnull="true" checkConstraint="check ( type in ('update','reset'))" checkConstraintName="ck_account_deferred_purchase_limit_type"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_deferred_purchase_limit_account_id" foreignKeyIndex="ix_account_deferred_purchase_limit_account_id"/>
            <column name="applied" type="boolean" defaultValue="false" notnull="true"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="start_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="gold_points" tableName="uam.account_engagement_info" type="decimal(20,2)" currentType="decimal(16,2)" currentNotnull="true"/>
        <alterColumn columnName="sweepstake_points" tableName="uam.account_engagement_info" type="decimal(20,2)" currentType="decimal(16,2)" currentNotnull="true"/>
        <addColumn tableName="uam.account_engagement_info">
            <column name="monthly_xp_level_id" type="bigint" references="core.xp_levels.id" foreignKeyName="fk_account_engagement_info_monthly_xp_level_id" foreignKeyIndex="ix_account_engagement_info_monthly_xp_level_id"/>
            <column name="annual_xp_level_id" type="bigint" references="core.xp_levels.id" foreignKeyName="fk_account_engagement_info_annual_xp_level_id" foreignKeyIndex="ix_account_engagement_info_annual_xp_level_id"/>
            <column name="monthly_gold_points" type="decimal(20,2)" notnull="true"/>
            <column name="annual_gold_points" type="decimal(20,2)" notnull="true"/>
            <column name="points_updated_at" type="date" notnull="true"/>
        </addColumn>
        <alterColumn columnName="event_type" tableName="uam.account_events" checkConstraint="check ( event_type in ('set_kyc_confirmation_address','set_account_info','set_account_kyc','set_account_mode','set_account_soft_kyc_info','set_account_purchase_limit','set_account_segmentation','set_account_segmentation_mass','set_account_vip_level','account_status_mass','account_email_settings_mass','account_deduct','account_reward','account_reward_mass','account_payment_method','account_jackpot_win','account_lock','account_unlock','account_unlock_mass','account_lock_mass','redeem_confirm_mass','redeem_decline_mass','redeem_lock_mass','redeem_unlock_mass','reset_account_purchase_withdraw_methods','reset_account_purchase_limit','send_email_mass','mass_chargeback','set_account_tag','add_account_comment','set_account_free_spin','account_email_update','set_account_influencer','set_account_restriction','set_account_random_reward_instance','account_session_restriction','unknown'))" checkConstraintName="ck_account_events_event_type"/>
        <alterColumn columnName="points" tableName="uam.account_experience_level_history" type="decimal(20)" currentType="decimal(16)" currentNotnull="true"/>
        <createTable name="fraud.account_fraud_info" pkName="pk_account_fraud_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" uniqueOneToOne="uq_account_fraud_info_account_id" references="fraud.accounts.id" foreignKeyName="fk_account_fraud_info_account_id"/>
            <column name="last_sign_in_fraud_score" type="integer"/>
            <column name="sign_up_device_session" type="varchar"/>
            <column name="sign_up_fraud_score" type="integer"/>
            <column name="last_fraud_score" type="integer"/>
            <column name="sign_up_cookie_hash" type="varchar"/>
            <column name="last_sign_in_cookie_hash" type="varchar"/>
            <column name="last_withdraw_cookie_hash" type="varchar"/>
            <column name="seon_session" type="varchar"/>
            <column name="offer_purchase_total_count" type="bigint" notnull="true"/>
            <column name="offer_purchase_total_amount" type="decimal"/>
            <column name="first_otp_show" type="timestamp"/>
            <column name="last_otp_show" type="timestamp"/>
            <column name="kyc" type="varchar(15)" notnull="true" checkConstraint="check ( kyc in ('initial','in_review','confirmed','id_confirmed','doc_review','doc_declined','session_expired','declined','blocked'))" checkConstraintName="ck_account_fraud_info_kyc"/>
            <column name="kyc_email_sent" type="boolean" defaultValue="false" notnull="true"/>
            <column name="jumio_account_reference" type="varchar" unique="uq_account_fraud_info_jumio_account_reference"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_free_spins" pkName="pk_account_free_spins">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_free_spins_account_id" foreignKeyIndex="ix_account_free_spins_account_id"/>
            <column name="free_spin_campaign_id" type="bigint" notnull="true" references="uam.free_spin_campaigns.id" foreignKeyName="fk_account_free_spins_free_spin_campaign_id" foreignKeyIndex="ix_account_free_spins_free_spin_campaign_id"/>
            <column name="free_spin_id" type="varchar"/>
            <column name="request_id" type="varchar"/>
            <column name="left_spins" type="integer" notnull="true"/>
            <column name="status" type="varchar" notnull="true"/>
            <column name="credited" type="timestamp" notnull="true"/>
            <column name="expired" type="timestamp" notnull="true"/>
            <column name="used" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_free_spins_account_id_free_spin_campaign_id_fr_1" columnNames="account_id,free_spin_campaign_id,free_spin_id" oneToOne="false" nullableColumns="free_spin_id"/>
            <uniqueConstraint name="uq_account_free_spins_account_id_free_spin_campaign_id_re_2" columnNames="account_id,free_spin_campaign_id,request_id" oneToOne="false" nullableColumns="request_id"/>
        </createTable>
        <createTable name="uam.account_gameplay_aggregation" pkName="pk_account_gameplay_aggregation">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_gameplay_aggregation_account_id" foreignKeyIndex="ix_account_gameplay_aggregation_account_id"/>
            <column name="gc_wager_amount" type="decimal(16,2)" notnull="true"/>
            <column name="gc_win_amount" type="decimal(16,2)" notnull="true"/>
            <column name="at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="id" tableName="uam.account_gameplay_info" uniqueOneToOne="uq_account_gameplay_info_id" references="uam.account_meta_info.id" foreignKeyName="fk_account_gameplay_info_id" dropForeignKey="fk_account_gameplay_info_id"/>
        <addColumn tableName="uam.account_gameplay_info">
            <column name="last_gameplay" type="timestamp"/>
        </addColumn>
        <addColumn tableName="uam.account_invitation_dropdowns">
            <column name="fiat_rewarded_lvl_1" type="boolean" defaultValue="false" notnull="true"/>
            <column name="fiat_rewarded_lvl_2" type="boolean" defaultValue="false" notnull="true"/>
            <column name="fiat_bonus" type="decimal(16,2)" notnull="true"/>
            <column name="fiat_bonus_first_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="fiat_bonus_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="fiat_purchase_aggr" type="decimal(16,2)" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.account_invitation_info">
            <column name="fiat_invited_lvl_1" type="integer" notnull="true"/>
            <column name="fiat_invited_lvl_2" type="integer" notnull="true"/>
            <column name="fiat_bonus" type="decimal(16,2)" notnull="true"/>
            <column name="fiat_bonus_first_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="fiat_bonus_purchase" type="decimal(16,2)" notnull="true"/>
            <column name="fiat_purchase_aggr" type="decimal(16,2)" notnull="true"/>
            <column name="confirmed_gc" type="decimal(16,2)"/>
            <column name="confirmed_sc" type="decimal(16,2)"/>
            <column name="pending_gc_lvl1" type="decimal(16,2)"/>
            <column name="pending_sc_lvl1" type="decimal(16,2)"/>
            <column name="pending_gc_lvl2" type="decimal(16,2)"/>
            <column name="pending_sc_lvl2" type="decimal(16,2)"/>
        </addColumn>
        <createTable name="uam.account_kyc_info" pkName="pk_account_kyc_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_kyc_info_id"/>
            <column name="first_kyc" type="timestamp"/>
            <column name="last_kyc" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_legal_rule" pkName="pk_account_legal_rule">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_legal_rule_account_id" foreignKeyIndex="ix_account_legal_rule_account_id"/>
            <column name="rule_id" type="bigint" notnull="true" references="uam.legal_rules.id" foreignKeyName="fk_account_legal_rule_rule_id" foreignKeyIndex="ix_account_legal_rule_rule_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_legal_rule_account_id_rule_id" columnNames="account_id,rule_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.account_loyalty_level_history" pkName="pk_account_loyalty_level_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_loyalty_level_history_account_id" foreignKeyIndex="ix_account_loyalty_level_history_account_id"/>
            <column name="level_id" type="bigint" notnull="true" references="core.xp_levels.id" foreignKeyName="fk_account_loyalty_level_history_level_id" foreignKeyIndex="ix_account_loyalty_level_history_level_id"/>
            <column name="type" type="varchar(6)" notnull="true" checkConstraint="check ( type in ('month','annual'))" checkConstraintName="ck_account_loyalty_level_history_type"/>
            <column name="at" type="date" notnull="true"/>
            <column name="expire_at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_marketing_info" pkName="pk_account_marketing_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_marketing_info_id"/>
            <column name="initial_c_ids" type="varchar(4000)"/>
            <column name="initial_u_ids" type="varchar(4000)"/>
            <column name="last_c_ids" type="varchar(4000)"/>
            <column name="last_u_ids" type="varchar(4000)"/>
            <column name="optanon_consent" type="varchar(4000)"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="first_payment_order_id" tableName="payment.account_meta_info" dropUnique="uq_account_meta_info_first_payment_order_id" dropForeignKey="fk_account_meta_info_first_payment_order_id"/>
        <alterColumn columnName="first_successful_payment_order_id" tableName="payment.account_meta_info" dropUnique="uq_account_meta_info_first_successful_payment_order_id" dropForeignKey="fk_account_meta_info_first_successful_payment_order_id"/>
        <alterColumn columnName="last_payment_order_id" tableName="payment.account_meta_info" dropUnique="uq_account_meta_info_last_payment_order_id" dropForeignKey="fk_account_meta_info_last_payment_order_id"/>
        <addColumn tableName="payment.account_meta_info">
            <column name="first_payment_order_at" type="date"/>
            <column name="first_deposit_order_id" type="bigint"/>
            <column name="first_deposit_order_at" type="date"/>
            <column name="first_successful_payment_order_at" type="date"/>
            <column name="first_successful_deposit_order_id" type="bigint"/>
            <column name="first_successful_deposit_order_at" type="date"/>
            <column name="last_failed_on_provider_payment_order_id" type="bigint"/>
            <column name="last_failed_on_provider_payment_order_at" type="date"/>
            <column name="last_successful_payment_order_at" type="date"/>
            <column name="last_deposit_order_id" type="bigint"/>
            <column name="last_successful_deposit_order_at" type="date"/>
            <column name="fiat_deposit_count" type="integer" notnull="true"/>
            <column name="total_fiat_deposit_amount" type="decimal(16,2)"/>
            <column name="total_deposit_base_amount" type="decimal(16,2)"/>
            <column name="total_fiat_deposit_base_amount" type="decimal(16,2)"/>
            <column name="total_withdraw_base_amount" type="decimal(16,2)"/>
        </addColumn>
        <alterForeignKey name="fk_account_meta_info_firstpaymentorder" columnNames="first_payment_order_id,first_payment_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_firstdepositorder" columnNames="first_deposit_order_id,first_deposit_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_firstsuccessfulpaymentorder" columnNames="first_successful_payment_order_id,first_successful_payment_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_firstsuccessfuldepositorder" columnNames="first_successful_deposit_order_id,first_successful_deposit_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_lastfailedonproviderpaymentorder" columnNames="last_failed_on_provider_payment_order_id,last_failed_on_provider_payment_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_lastsuccessfulpaymentorder" columnNames="last_payment_order_id,last_successful_payment_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.account_meta_info"/>
        <alterForeignKey name="fk_account_meta_info_lastsuccessfuldepositorder" columnNames="last_deposit_order_id,last_successful_deposit_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.account_meta_info"/>
        <addUniqueConstraint constraintName="uq_account_meta_info_firstpaymentorder" tableName="payment.account_meta_info" columnNames="first_payment_order_id,first_payment_order_at" oneToOne="true" nullableColumns="first_payment_order_id,first_payment_order_at"/>
        <addUniqueConstraint constraintName="uq_account_meta_info_firstdepositorder" tableName="payment.account_meta_info" columnNames="first_deposit_order_id,first_deposit_order_at" oneToOne="true" nullableColumns="first_deposit_order_id,first_deposit_order_at"/>
        <addUniqueConstraint constraintName="uq_account_meta_info_firstsuccessfulpaymentorder" tableName="payment.account_meta_info" columnNames="first_successful_payment_order_id,first_successful_payment_order_at" oneToOne="true" nullableColumns="first_successful_payment_order_id,first_successful_payment_order_at"/>
        <addUniqueConstraint constraintName="uq_account_meta_info_firstsuccessfuldepositorder" tableName="payment.account_meta_info" columnNames="first_successful_deposit_order_id,first_successful_deposit_order_at" oneToOne="true" nullableColumns="first_successful_deposit_order_id,first_successful_deposit_order_at"/>
        <addUniqueConstraint constraintName="uq_account_meta_info_lastfailedonproviderpaymentorder" tableName="payment.account_meta_info" columnNames="last_failed_on_provider_payment_order_id,last_failed_on_provider_payment_order_at" oneToOne="true" nullableColumns="last_failed_on_provider_payment_order_id,last_failed_on_provider_payment_order_at"/>
        <addUniqueConstraint constraintName="uq_account_meta_info_lastsuccessfulpaymentorder" tableName="payment.account_meta_info" columnNames="last_payment_order_id,last_successful_payment_order_at" oneToOne="true" nullableColumns="last_payment_order_id,last_successful_payment_order_at"/>
        <addUniqueConstraint constraintName="uq_account_meta_info_lastsuccessfuldepositorder" tableName="payment.account_meta_info" columnNames="last_deposit_order_id,last_successful_deposit_order_at" oneToOne="true" nullableColumns="last_deposit_order_id,last_successful_deposit_order_at"/>
        <addColumn tableName="uam.account_meta_info">
            <column name="deposit_count" type="integer" notnull="true"/>
        </addColumn>
        <createTable name="uam.account_missions" pkName="pk_account_missions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="mission_id" type="bigint" notnull="true" references="uam.missions.id" foreignKeyName="fk_account_missions_mission_id" foreignKeyIndex="ix_account_missions_mission_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_missions_account_id" foreignKeyIndex="ix_account_missions_account_id"/>
            <column name="status" type="varchar(20)" notnull="true"/>
            <column name="expires_at" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_mission_steps" pkName="pk_account_mission_steps">
            <column name="account_mission_id" type="bigint" notnull="true" primaryKey="true" references="uam.account_missions.id" foreignKeyName="fk_account_mission_steps_account_mission_id" foreignKeyIndex="ix_account_mission_steps_account_mission_id"/>
            <column name="mission_step_id" type="bigint" notnull="true" primaryKey="true" references="uam.mission_steps.id" foreignKeyName="fk_account_mission_steps_mission_step_id" foreignKeyIndex="ix_account_mission_steps_mission_step_id"/>
            <column name="status" type="varchar(20)" notnull="true"/>
            <column name="expires_at" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_mission_steps_account_mission_id_mission_step_id" columnNames="account_mission_id,mission_step_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <addColumn tableName="uam.account_oauths">
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_account_oauths_brand_id" foreignKeyIndex="ix_account_oauths_brand_id"/>
            <column name="link" type="varchar(4000)"/>
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_account_oauths_brand_id_uuid" tableName="uam.account_oauths" columnNames="brand_id,uuid" oneToOne="false" nullableColumns="uuid"/>
        <addUniqueConstraint constraintName="uq_account_oauths_account_id_provider" tableName="uam.account_oauths" columnNames="account_id,provider" oneToOne="false" nullableColumns=""/>
        <createTable name="payment.account_offer_meta_info" pkName="pk_account_offer_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="offer_id" type="bigint" notnull="true" references="payment.offer_templates.id" foreignKeyName="fk_account_offer_meta_info_offer_id" foreignKeyIndex="ix_account_offer_meta_info_offer_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_offer_meta_info_account_id" foreignKeyIndex="ix_account_offer_meta_info_account_id"/>
            <column name="last_purchase_date" type="timestamp" notnull="true"/>
            <column name="purchase_count" type="bigint" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.account_offer_reward_log" pkName="pk_account_offer_reward_log">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_offer_reward_log_account_id" foreignKeyIndex="ix_account_offer_reward_log_account_id"/>
            <column name="offer_id" type="bigint" notnull="true" references="payment.offer_templates.id" foreignKeyName="fk_account_offer_reward_log_offer_id" foreignKeyIndex="ix_account_offer_reward_log_offer_id"/>
            <column name="source" type="varchar" notnull="true"/>
            <column name="reference" type="varchar" notnull="true"/>
            <column name="request_id" type="varchar" notnull="true"/>
            <column name="max_purchase_count" type="integer" notnull="true"/>
            <column name="purchase_count" type="integer" notnull="true"/>
            <column name="available_from" type="timestamp"/>
            <column name="expire_at" type="timestamp"/>
            <column name="status" type="varchar(9)" notnull="true" checkConstraint="check ( status in ('created','cancelled','used','expired'))" checkConstraintName="ck_account_offer_reward_log_status"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_offer_reward_log_account_id_offer_id_request_id" columnNames="account_id,offer_id,request_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.account_otp_limit" pkName="pk_account_otp_limit">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" uniqueOneToOne="uq_account_otp_limit_account_id" references="fraud.accounts.id" foreignKeyName="fk_account_otp_limit_account_id"/>
            <column name="limit_exceeded" type="boolean" defaultValue="false" notnull="true"/>
            <column name="attempts" type="integer" notnull="true"/>
            <column name="last_attempt_at" type="timestamp"/>
            <column name="limit_exceeded_at" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.account_payment_aggregation" pkName="pk_account_payment_aggregation">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="integration_type" type="varchar(21)" notnull="true" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_account_payment_aggregation_integration_type"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_payment_aggregation_account_id" foreignKeyIndex="ix_account_payment_aggregation_account_id"/>
            <column name="purchase_sum_base_amount" type="decimal"/>
            <column name="purchase_sum_local_currency_amount" type="decimal"/>
            <column name="deposit_sum_base_amount" type="decimal"/>
            <column name="deposit_sum_local_currency_amount" type="decimal"/>
            <column name="purchase_count" type="bigint" notnull="true"/>
            <column name="deposit_count" type="bigint" notnull="true"/>
            <column name="refund_sum_base_amount" type="decimal"/>
            <column name="refund_sum_local_currency_amount" type="decimal"/>
            <column name="refund_count" type="bigint" notnull="true"/>
            <column name="chargeback_sum_base_amount" type="decimal"/>
            <column name="chargeback_sum_local_currency_amount" type="decimal"/>
            <column name="chargeback_count" type="bigint" notnull="true"/>
            <column name="at" type="date"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_account_payment_aggregation_account_id_at_integration__1" columnNames="account_id,at,integration_type" oneToOne="false" nullableColumns="at"/>
        </createTable>
        <addUniqueConstraint constraintName="uq_account_payment_info_customer_id_provider" tableName="payment.account_payment_info" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_account_payment_info_customer_id_provider_account_id" tableName="payment.account_payment_info" columnNames="customer_id,provider,account_id" oneToOne="false" nullableColumns=""/>
        <addColumn tableName="payment.payment_methods">
            <column name="e_wallet_payment_method_id" type="integer" references="payment.e_wallet_payment_method.id" foreignKeyName="fk_payment_methods_e_wallet_payment_method_id" foreignKeyIndex="ix_payment_methods_e_wallet_payment_method_id"/>
            <column name="e_transfer_payment_method_id" type="integer" references="payment.e_transfer_payment_method.id" foreignKeyName="fk_payment_methods_e_transfer_payment_method_id" foreignKeyIndex="ix_payment_methods_e_transfer_payment_method_id"/>
            <column name="crypto_payment_method_id" type="integer" references="payment.crypto_payment_methods.id" foreignKeyName="fk_payment_methods_crypto_payment_method_id" foreignKeyIndex="ix_payment_methods_crypto_payment_method_id"/>
            <column name="confirm_token" type="varchar(4000)"/>
        </addColumn>
        <createTable name="payment.account_payment_settings" withHistory="true" pkName="pk_account_payment_settings">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_payment_settings_account_id"/>
            <column name="secure3d_action" type="varchar(5)" checkConstraint="check ( secure3d_action in ('skip','force'))" checkConstraintName="ck_account_payment_settings_secure3d_action"/>
            <column name="rtp_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="modified_by" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_account_payment_settings_account_id" columnNames="account_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.account_personal_info" pkName="pk_account_personal_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_personal_info_id"/>
            <column name="email" type="varchar" notnull="true"/>
            <column name="first_name" type="varchar"/>
            <column name="last_name" type="varchar"/>
            <column name="date_of_birth" type="date"/>
            <column name="address" type="varchar(4000)"/>
            <column name="city" type="varchar"/>
            <column name="state" type="varchar"/>
            <column name="country" type="varchar(2)" notnull="true"/>
            <column name="zip" type="varchar(50)"/>
            <column name="currency" type="varchar(3) default 'USD'" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <addHistoryTable baseTable="uam.account_preferences"/>
        <addColumn tableName="uam.account_preferences" withHistory="true">
            <column name="do_not_call" type="boolean" defaultValue="false" notnull="true"/>
            <column name="locale" type="varchar(30) DEFAULT 'en-US'" notnull="true"/>
            <column name="modified_source" type="varchar(50)"/>
        </addColumn>
        <createTable name="uam.account_promotions" pkName="pk_account_promotions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_promotions_account_id" foreignKeyIndex="ix_account_promotions_account_id"/>
            <column name="promotion_id" type="integer" notnull="true" references="uam.promotions.id" foreignKeyName="fk_account_promotions_promotion_id" foreignKeyIndex="ix_account_promotions_promotion_id"/>
            <column name="status" type="varchar(7)" notnull="true" checkConstraint="check ( status in ('active','deleted'))" checkConstraintName="ck_account_promotions_status"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_promotions_account_id_promotion_id" columnNames="account_id,promotion_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.account_provider_black_list" pkName="pk_account_provider_black_list">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_account_provider_black_list_account_id"/>
            <column name="payment_methods" type="varchar[]"/>
            <column name="withdraw_methods" type="varchar[]"/>
            <column name="comments" type="varchar"/>
            <column name="updated_by" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_provider_black_list_account_id" columnNames="account_id" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <addHistoryTable baseTable="payment.account_purchase_limit"/>
        <alterColumn columnName="reason" tableName="payment.account_purchase_limit" currentType="varchar(22)" notnull="true" currentNotnull="false" checkConstraint="check ( reason in ('member_request','suspicious_behaviour','verification_needed','chb_mitigation','verification_needed_pm','chargeback_monitoring'))" checkConstraintName="ck_account_purchase_limit_reason"/>
        <addColumn tableName="payment.account_purchase_limit" withHistory="true">
            <column name="modified_by" type="varchar"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_account_purchase_limit_days_account_id" tableName="payment.account_purchase_limit" columnNames="DROP CONSTRAINT" nullableColumns="account_id"/>
        <addUniqueConstraint constraintName="uq_account_purchase_limit_days_account_id_reason" tableName="payment.account_purchase_limit" columnNames="days,account_id,reason" oneToOne="false" nullableColumns="account_id"/>
        <createTable name="uam.account_session_limits" pkName="pk_account_session_limits">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_session_limits_account_id" foreignKeyIndex="ix_account_session_limits_account_id"/>
            <column name="duration_hours" type="integer" notnull="true"/>
            <column name="start_at" type="timestamp" notnull="true"/>
            <column name="end_at" type="timestamp"/>
            <column name="notified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_by" type="varchar(255) default 'system'" notnull="true"/>
            <column name="version" type="bigint" notnull="true"/>
        </createTable>
        <createTable name="uam.account_session_restrictions" pkName="pk_account_session_restrictions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="type" type="varchar(14)" notnull="true" checkConstraint="check ( type in ('self_exclusion','cool_off'))" checkConstraintName="ck_account_session_restrictions_type"/>
            <column name="start_at" type="timestamp" notnull="true"/>
            <column name="end_at" type="timestamp"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_session_restrictions_account_id" foreignKeyIndex="ix_account_session_restrictions_account_id"/>
            <column name="reason" type="varchar"/>
            <column name="created_by" type="varchar(255) default 'system'" notnull="true"/>
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
        </createTable>
        <createTable name="uam.account_tags" pkName="pk_account_tags">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_tags_account_id" foreignKeyIndex="ix_account_tags_account_id"/>
            <column name="tag" type="varchar(31)" notnull="true" checkConstraint="check ( tag in ('high_fraud_score','monitor','complaint_received','refund_made','big_winner','refund_next_pending_rd','part_of_the_promotion_list','removed_from_the_promotion_list','pm_verified','refund_request','rd_fail_issues','concerning_comments_received','purchase_rec_done','high_turnover','high_rd_cancellation','high_purchase_in_24h','high_purchase_in_7d','free_sc_misuse','legal_threat','gameplay_adv','promo_adv','amoe_adv','confirmed_adv','advantage_play','risky_t','risky_s','payment_plan','nsc','charcgeback_monitoring','suspicious_behaviour','legal_arbitration','top_spender','docs_requested_manual_check','docs_submitted_manual_check'))" checkConstraintName="ck_account_tags_tag"/>
            <column name="created_by" type="varchar" notnull="true"/>
            <column name="expire_at" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="level" tableName="uam.account_vip_level_history" type="varchar(16)" currentType="varchar(7)" currentNotnull="false" checkConstraint="check ( level in ('class_a','class_b','class_c','class_d','class_e','class_f','whale','portfolio_player'))" checkConstraintName="ck_account_vip_level_history_level"/>
        <alterColumn columnName="source" tableName="uam.account_vip_level_history" checkConstraint="check ( source in ('auto','manual','ggr'))" checkConstraintName="ck_account_vip_level_history_source"/>
        <createTable name="uam.account_wheel_of_winners" pkName="pk_account_wheel_of_winners">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_wheel_of_winners_account_id" foreignKeyIndex="ix_account_wheel_of_winners_account_id"/>
            <column name="wheel_of_winners_id" type="bigint" notnull="true" references="uam.wheel_of_winners.id" foreignKeyName="fk_account_wheel_of_winners_wheel_of_winners_id" foreignKeyIndex="ix_account_wheel_of_winners_wheel_of_winners_id"/>
            <column name="spins" type="integer" notnull="true"/>
            <column name="last_given_spin_at" type="timestamp"/>
            <column name="last_spin_at" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_wheel_of_winners_account_id_wheel_of_winners_id" columnNames="account_id,wheel_of_winners_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <alterColumn columnName="type" tableName="payment.withdraw_methods" checkConstraint="check ( type in ('ach','sci','skrill','skrill_ach','nuvei_mazooma_ach','masspay_ach','prizeout','trustly','payper','standard_ach','crypto','orbital','airwallex_ach','aeropay_ach','aeropay','checkbook_ach','paynearme_ach','nsc'))" checkConstraintName="ck_withdraw_methods_type"/>
        <addColumn tableName="payment.withdraw_methods">
            <column name="last_used_at" type="timestamp"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_withdraw_methods_account_id_type" tableName="payment.withdraw_methods" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addColumn tableName="payment.ach_payment_method" withHistory="true">
            <column name="usernames" type="varchar[]"/>
            <column name="emails" type="varchar[]"/>
            <column name="phones" type="varchar[]"/>
        </addColumn>
        <createTable name="payment.ach_payment_method_address" pkName="pk_ach_payment_method_address">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="ach_payment_method_id" type="integer" notnull="true" references="payment.ach_payment_method.id" foreignKeyName="fk_ach_payment_method_address_ach_payment_method_id" foreignKeyIndex="ix_ach_payment_method_address_ach_payment_method_id"/>
            <column name="address1" type="varchar"/>
            <column name="address2" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="state" type="varchar"/>
            <column name="zip" type="varchar"/>
            <column name="country" type="varchar"/>
        </createTable>
        <alterColumn columnName="account_id" tableName="fraud.aml_check" withHistory="true" uniqueOneToOne="uq_aml_check_account_id" references="fraud.accounts.id" foreignKeyName="fk_aml_check_account_id"/>
        <addColumn tableName="fraud.aml_check" withHistory="true">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_aml_check_account_id" tableName="fraud.aml_check" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <createTable name="back_office.approval" pkName="pk_approval">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="operation_type" type="varchar" notnull="true"/>
            <column name="request_json" type="jsonb" notnull="true"/>
            <column name="requested_by" type="varchar" notnull="true"/>
            <column name="reviewers" type="varchar[]" notnull="true"/>
            <column name="approved_by" type="varchar[]"/>
            <column name="rejected_by" type="varchar[]"/>
            <column name="status" type="varchar(8)" notnull="true" checkConstraint="check ( status in ('PENDING','APPROVED','REJECTED','EXECUTED','FAILED'))" checkConstraintName="ck_approval_status"/>
            <column name="comment" type="varchar"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_approval_brand_id" foreignKeyIndex="ix_approval_brand_id"/>
            <column name="account_count" type="bigint"/>
            <column name="slack_message_ts" type="varchar"/>
            <column name="change_summary" type="varchar"/>
            <column name="expire_at" type="timestamp"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.backoffice_events" pkName="pk_backoffice_events">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_backoffice_events_brand_id" foreignKeyIndex="ix_backoffice_events_brand_id"/>
            <column name="entity_type" type="varchar(41)" notnull="true" checkConstraint="check ( entity_type in ('product','product_category','brand_banner_template','brand_daily_bonus_template','brand_email_template','brand_offer_template','brand_social_media_reward','brand_reward_creditor','brand_reward_campaign','brand_legal_rule','brand_otp_trigger_rules','brand_free_spins_campaign','game_lobby_feature_setting','game_lobby_group','game_lobby_group_assign','game_lobby_group_unassign','game_lobby_game_theme','brand_game_allowance_by_location','brand_game_allowance_by_registration','brand_mission_update','brand_mission_reward_update','brand_mission_freespin_reward_update','brand_mission_step_update','brand_mission_step_reward_update','brand_mission_step_freespin_reward_update','brand_mission_step_order_update','mass_account_status','mass_account_change','mass_account_tag','mass_account_lock','mass_account_unlock','mass_account_segmentation','mass_email_send','mass_reward','mass_account_comment','mass_account_free_spin','supplier','rnd_audience','rnd_discounted_line','rnd_pick_em_pack_config','rnd_pick_em_options_order','rnd_reward_bundle','rnd_reward_template','rnd_trading_alert_config','rnd_issue_user_reward','rnd_server_config_modify','rnd_leagues','rnd_pick_em_markets','rnd_pick_em_user_limits','rnd_player','rnd_teams','rnd_matches','rnd_unrecognized','rnd_user_segment','brand_wheel_of_winners','brand_feature_settings','unknown'))" checkConstraintName="ck_backoffice_events_entity_type"/>
            <column name="event_type" type="varchar(7)" notnull="true" checkConstraint="check ( event_type in ('create','update','unknown'))" checkConstraintName="ck_backoffice_events_event_type"/>
            <column name="status_type" type="varchar(10)" notnull="true" checkConstraint="check ( status_type in ('successful','error','unknown'))" checkConstraintName="ck_backoffice_events_status_type"/>
            <column name="message" type="varchar(2000)"/>
            <column name="reference" type="varchar"/>
            <column name="created_by" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.banner_templates" pkName="pk_banner_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_banner_templates_brand_id" foreignKeyIndex="ix_banner_templates_brand_id"/>
            <column name="segment" type="varchar"/>
            <column name="segment_tags" type="varchar[]" notnull="true"/>
            <column name="exclude_segment" type="varchar"/>
            <column name="exclude_segment_tags" type="varchar[]" notnull="true"/>
            <column name="start_at" type="timestamp"/>
            <column name="end_at" type="timestamp"/>
            <column name="priority" type="integer" notnull="true"/>
            <column name="cta_url" type="varchar(4000)"/>
            <column name="banner_image_url" type="varchar(4000)"/>
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="supported_platform" type="varchar(7)" notnull="true" checkConstraint="check ( supported_platform in ('web','android','ios'))" checkConstraintName="ck_banner_templates_supported_platform"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_banner_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.big_wins" pkName="pk_big_wins">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="winner" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_big_wins_account_id" foreignKeyIndex="ix_big_wins_account_id"/>
            <column name="product_id" type="integer" notnull="true" references="core.products.id" foreignKeyName="fk_big_wins_product_id" foreignKeyIndex="ix_big_wins_product_id"/>
            <column name="win_type" type="varchar" notnull="true"/>
            <column name="currency" type="varchar" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="spin_currency" type="varchar"/>
            <column name="spin_amount" type="decimal(16,2)" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.blacklist" withHistory="true" pkName="pk_blacklist">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_blacklist_brand_id" foreignKeyIndex="ix_blacklist_brand_id"/>
            <column name="country" type="varchar"/>
            <column name="type" type="varchar(16)" notnull="true" checkConstraint="check ( type in ('bin','bin_type','bin_level','bin_country_code'))" checkConstraintName="ck_blacklist_type"/>
            <column name="excluded_value" type="varchar" notnull="true"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_by" type="varchar" notnull="true"/>
            <column name="modified_by" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="uam.bonus_rewards">
            <column name="fiat_bonus_amount" type="decimal(16,2)"/>
            <column name="reference" type="varchar(36)"/>
        </addColumn>
        <addColumn tableName="core.brands">
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_brands_mode"/>
            <column name="domain" type="varchar"/>
        </addColumn>
        <createTable name="game_hub.brand_bet_limits" pkName="pk_brand_bet_limits">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="min_bet" type="decimal(16,2)"/>
            <column name="default_bet" type="decimal(16,2)"/>
            <column name="max_bet_limit" type="decimal(16,2)"/>
            <column name="max_win_limit" type="decimal(16,2)"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_brand_bet_limits_brand_id" foreignKeyIndex="ix_brand_bet_limits_brand_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_brand_bet_limits_brand_id_currency" columnNames="brand_id,currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.feature_settings_brand" pkName="pk_feature_settings_brand">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="country_code" type="varchar(2)"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_feature_settings_brand_brand_id" foreignKeyIndex="ix_feature_settings_brand_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="feature_value" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_feature_settings_brand_brand_id_code_country_code" columnNames="brand_id,code,country_code" oneToOne="false" nullableColumns="brand_id,country_code"/>
        </createTable>
        <createTable name="uam.brand_giveaway_prize_configs" pkName="pk_brand_giveaway_prize_configs">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_brand_giveaway_prize_configs_brand_id" foreignKeyIndex="ix_brand_giveaway_prize_configs_brand_id"/>
            <column name="type" type="varchar(9)" notnull="true" checkConstraint="check ( type in ('coin','free_spin'))" checkConstraintName="ck_brand_giveaway_prize_configs_type"/>
            <column name="fs_count_prize" type="integer" notnull="true"/>
            <column name="gc_prize" type="decimal(16,2)" notnull="true"/>
            <column name="sc_prize" type="decimal(16,2)" notnull="true"/>
            <column name="winners" type="integer[]" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_brand_giveaway_prize_configs_brand_id_type_fs_count_pr_1" columnNames="brand_id,type,fs_count_prize,gc_prize,sc_prize" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.brand_settings" pkName="pk_brand_settings">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" uniqueOneToOne="uq_brand_settings_brand_id" references="core.brands.id" foreignKeyName="fk_brand_settings_brand_id"/>
            <column name="title" type="varchar"/>
            <column name="payment_webhook_url" type="varchar"/>
        </createTable>
        <createTable name="fraud.brand_settings" pkName="pk_brand_settings">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer"/>
            <column name="fraud_brand_id" type="integer" notnull="true" uniqueOneToOne="uq_brand_settings_fraud_brand_id" references="fraud.brands.id" foreignKeyName="fk_brand_settings_fraud_brand_id"/>
            <column name="fraud_webhook_url" type="varchar"/>
            <column name="card_verification_success_page" type="varchar"/>
            <column name="card_verification_failed_page" type="varchar"/>
            <column name="kyc_success_page" type="varchar"/>
            <column name="kyc_error_page" type="varchar"/>
            <column name="doc_upload_success_page" type="varchar"/>
            <column name="doc_upload_error_page" type="varchar"/>
            <column name="ethoca_refund_min_amount" type="bigint"/>
            <column name="ethoca_refund_max_amount" type="bigint"/>
        </createTable>
        <addColumn tableName="payment.card_bin_info" withHistory="true">
            <column name="version" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="payment.card_payment_method" withHistory="true">
            <column name="first_eight" type="varchar"/>
            <column name="payment_account_reference" type="varchar"/>
            <column name="secure3d_verified" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <createTable name="payment.card_payment_method_meta_info" withHistory="true" pkName="pk_card_payment_method_meta_info">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="bin" type="varchar" notnull="true"/>
            <column name="last_four" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_card_payment_method_meta_info_account_id" foreignKeyIndex="ix_card_payment_method_meta_info_account_id"/>
            <column name="blocked" type="boolean" defaultValue="false" notnull="true"/>
            <column name="third_party_card_verification_status" type="varchar(19)" checkConstraint="check ( third_party_card_verification_status in ('verified','verification_failed'))" checkConstraintName="ck_card_payment_method_meta_info_third_party_card_verific_1"/>
            <column name="updated_by" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_card_payment_method_meta_info_account_id_bin_last_four" columnNames="account_id,bin,last_four" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <addColumn tableName="payment.card_transaction_details">
            <column name="scheme_transaction_id" type="varchar"/>
            <column name="acquirer_reference_number" type="varchar"/>
            <column name="merchant_advice_code" type="varchar"/>
        </addColumn>
        <createTable name="fraud.card_verification_meta_info" withHistory="true" pkName="pk_card_verification_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_card_verification_meta_info_account_id" foreignKeyIndex="ix_card_verification_meta_info_account_id"/>
            <column name="fingerprint" type="varchar" notnull="true"/>
            <column name="card_bin" type="varchar"/>
            <column name="last_four" type="varchar"/>
            <column name="status" type="varchar(21)" notnull="true" checkConstraint="check ( status in ('unverified','require_verification','in_progress','verified','attempt_limit_reached','verification_failed','unverifiable'))" checkConstraintName="ck_card_verification_meta_info_status"/>
            <column name="status_updated_at" type="timestamp"/>
            <column name="manually_updated" type="boolean"/>
            <column name="agent_name" type="varchar"/>
            <column name="manually_verified" type="boolean"/>
            <column name="verification_attempts" type="integer" notnull="true"/>
            <column name="require_verification_at" type="timestamp"/>
            <column name="email_sent" type="boolean" defaultValue="false" notnull="true"/>
            <column name="automated_trigger_at" type="timestamp"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_card_verification_meta_info_account_id_fingerprint" columnNames="account_id,fingerprint" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.card_verification_request" withHistory="true" pkName="pk_card_verification_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="scan_reference" type="varchar"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_card_verification_request_account_id" foreignKeyIndex="ix_card_verification_request_account_id"/>
            <column name="meta_info_id" type="bigint" notnull="true" references="fraud.card_verification_meta_info.id" foreignKeyName="fk_card_verification_request_meta_info_id" foreignKeyIndex="ix_card_verification_request_meta_info_id"/>
            <column name="status" type="varchar(9)" notnull="true" checkConstraint="check ( status in ('initiated','verified','failed','expired'))" checkConstraintName="ck_card_verification_request_status"/>
            <column name="redirect_url" type="varchar(4000)"/>
            <column name="client_ip" type="varchar"/>
            <column name="timestamp" type="varchar"/>
            <column name="doc_type" type="varchar"/>
            <column name="country" type="varchar"/>
            <column name="images" type="varchar[]"/>
            <column name="extracted_name" type="varchar"/>
            <column name="extracted_pan" type="varchar"/>
            <column name="extracted_issue_date" type="varchar"/>
            <column name="extracted_expiry_date" type="varchar"/>
            <column name="api_error" type="varchar"/>
            <column name="fail_reason" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_card_verification_request_provider_scan_reference" columnNames="provider,scan_reference" oneToOne="false" nullableColumns="scan_reference"/>
        </createTable>
        <createTable name="payment.chargeback" pkName="pk_chargeback">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_chargeback_account_id" foreignKeyIndex="ix_chargeback_account_id"/>
            <column name="order_id" type="bigint" notnull="true"/>
            <column name="order_at" type="date" notnull="true"/>
            <column name="status" type="varchar(19)" notnull="true" checkConstraint="check ( status in ('pending','won','lost','chargeback','alert','rdr','chargeback_reversal','partial_match','representment','unknown','bad_debt'))" checkConstraintName="ck_chargeback_status"/>
            <column name="collection_status" type="varchar(22)" notnull="true" checkConstraint="check ( collection_status in ('complete_uncollected','complete','partial','not_collected','waiting_for_collection'))" checkConstraintName="ck_chargeback_collection_status"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="fees" type="decimal(16,2)" notnull="true"/>
            <column name="collected_amount" type="decimal(16,2)" notnull="true"/>
            <column name="purchase_at" type="timestamp" notnull="true"/>
            <column name="chargeback_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_chargeback_order" columnNames="order_id,order_at" oneToOne="true" nullableColumns=""/>
            <foreignKey name="fk_chargeback_order" columnNames="order_id,order_at" refColumnNames="id,at" refTableName="payment.payment_orders"/>
        </createTable>
        <createTable name="payment.chargeback_config" pkName="pk_chargeback_config">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_chargeback_config_brand_id" foreignKeyIndex="ix_chargeback_config_brand_id"/>
            <column name="integration_type" type="varchar(21)" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_chargeback_config_integration_type"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="fee" type="decimal"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_chargeback_config_brand_id_integration_type_currency" columnNames="brand_id,integration_type,currency" oneToOne="false" nullableColumns="brand_id,integration_type"/>
        </createTable>
        <alterColumn columnName="status" tableName="payment.chargeback_history" type="varchar(19)" currentType="varchar(13)" currentNotnull="true" checkConstraint="check ( status in ('pending','won','lost','chargeback','alert','rdr','chargeback_reversal','partial_match','representment','unknown','bad_debt'))" checkConstraintName="ck_chargeback_history_status"/>
        <alterColumn columnName="order_id" tableName="payment.chargeback_history" dropForeignKey="fk_chargeback_history_order_id" dropForeignKeyIndex="ix_chargeback_history_order_id"/>
        <addColumn tableName="payment.chargeback_history">
            <column name="reason_code" type="varchar"/>
            <column name="order_at" type="date"/>
            <column name="chargeback_id" type="bigint" references="payment.chargeback.id" foreignKeyName="fk_chargeback_history_chargeback_id" foreignKeyIndex="ix_chargeback_history_chargeback_id"/>
            <column name="transaction_id" type="varchar"/>
            <column name="transaction_type" type="varchar(11)" checkConstraint="check ( transaction_type in ('chb_debit','chb_credit','owed_debit','owed_credit'))" checkConstraintName="ck_chargeback_history_transaction_type"/>
            <column name="collection_status" type="varchar(22)" checkConstraint="check ( collection_status in ('complete_uncollected','complete','partial','not_collected','waiting_for_collection'))" checkConstraintName="ck_chargeback_history_collection_status"/>
            <column name="collected_amount" type="decimal(16,2)"/>
            <column name="owed_amount" type="decimal(16,2)"/>
            <column name="total_collected_amount" type="decimal(16,2)"/>
        </addColumn>
        <alterForeignKey name="fk_chargeback_history_order" columnNames="order_id,order_at" refColumnNames="id,at" refTableName="payment.payment_orders" tableName="payment.chargeback_history"/>
        <createTable name="fraud.chat_events" pkName="pk_chat_events">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_chat_events_account_id" foreignKeyIndex="ix_chat_events_account_id"/>
            <column name="action" type="varchar(11)" notnull="true" checkConstraint="check ( action in ('chat_shown','chat_opened'))" checkConstraintName="ck_chat_events_action"/>
            <column name="flow" type="varchar(13)" notnull="true" checkConstraint="check ( flow in ('purchase_flow','redeem_flow'))" checkConstraintName="ck_chat_events_flow"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="uam.citizens">
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <createTable name="uam.completed_kyc_verifications_requests" pkName="pk_completed_kyc_verifications_requests">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="kyc_verification_request_id" type="bigint" notnull="true" uniqueOneToOne="uq_completed_kyc_verifications_requests_kyc_verification__1" references="uam.kyc_verification_requests.id" foreignKeyName="fk_completed_kyc_verifications_requests_kyc_verification__1"/>
            <column name="processed_to_box" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.countries" pkName="pk_countries">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_countries_brand_id" foreignKeyIndex="ix_countries_brand_id"/>
            <column name="code" type="varchar(2)" notnull="true"/>
            <column name="name" type="varchar"/>
            <column name="allowed_states" type="varchar[]" notnull="true"/>
            <column name="currency" type="varchar(3) default 'USD'" notnull="true"/>
        </createTable>
        <addUniqueConstraint constraintName="uq_country_operation_policies_brand_id_code" tableName="uam.country_operation_policies" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <createTable name="payment.crypto_payment_methods" pkName="pk_crypto_payment_methods">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="currency" type="varchar(4)" notnull="true" checkConstraint="check ( currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_payment_methods_currency"/>
            <column name="network" type="varchar(7)" notnull="true" checkConstraint="check ( network in ('trx','sol','eth','bnb','btc','ltc','polygon','bch','avax','ton','teth'))" checkConstraintName="ck_crypto_payment_methods_network"/>
            <column name="wallet" type="varchar" notnull="true"/>
        </createTable>
        <createTable name="payment.crypto_purchase_details" pkName="pk_crypto_purchase_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="source_currency" type="varchar(4)" notnull="true" checkConstraint="check ( source_currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_purchase_details_source_currency"/>
            <column name="source_network" type="varchar(7)" notnull="true" checkConstraint="check ( source_network in ('trx','sol','eth','bnb','btc','ltc','polygon','bch','avax','ton','teth'))" checkConstraintName="ck_crypto_purchase_details_source_network"/>
            <column name="status" type="varchar" notnull="true"/>
            <column name="source_amount" type="decimal(32,16)"/>
            <column name="kyt_alert_level" type="varchar"/>
            <column name="kyt_status" type="varchar"/>
            <column name="block_chains" type="jsonb"/>
            <column name="threshold_data" type="jsonb"/>
            <column name="refund_data" type="jsonb"/>
            <column name="fees" type="jsonb"/>
            <column name="rates" type="jsonb"/>
            <column name="paid_amount" type="decimal(32,16)"/>
            <column name="refund_status" type="varchar"/>
            <column name="paid_amount_in_target_currency" type="decimal(16,2)"/>
            <column name="paid_amount_in_local_currency" type="decimal(16,2)"/>
            <column name="paid_rate" type="decimal(6,2)"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.crypto_rates" pkName="pk_crypto_rates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="from_currency" type="varchar" notnull="true"/>
            <column name="to_currency" type="varchar(4)" notnull="true" checkConstraint="check ( to_currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_rates_to_currency"/>
            <column name="buy_price" type="decimal(32,16)"/>
            <column name="sell_price" type="decimal(32,16)"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_crypto_rates_provider_from_currency_to_currency" columnNames="provider,from_currency,to_currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.crypto_withdrawal_details" pkName="pk_crypto_withdrawal_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="target_currency" type="varchar(4)" notnull="true" checkConstraint="check ( target_currency in ('ltc','usdt','usdc','eth','bch','btc','tst','cro','ape','uni','pol','xrp'))" checkConstraintName="ck_crypto_withdrawal_details_target_currency"/>
            <column name="target_network" type="varchar(7)" notnull="true" checkConstraint="check ( target_network in ('trx','sol','eth','bnb','btc','ltc','polygon','bch','avax','ton','teth'))" checkConstraintName="ck_crypto_withdrawal_details_target_network"/>
            <column name="status" type="varchar" notnull="true"/>
            <column name="target_amount" type="decimal(32,16)"/>
            <column name="kyt_alert_level" type="varchar"/>
            <column name="kyt_status" type="varchar"/>
            <column name="block_chains" type="jsonb"/>
            <column name="fees" type="jsonb"/>
            <column name="rates" type="jsonb"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.currency_operation_policy" identityType="identity" pkName="pk_currency_operation_policy">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_currency_operation_policy_brand_id" foreignKeyIndex="ix_currency_operation_policy_brand_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="country_code" type="varchar(2)" notnull="true"/>
            <column name="precision" type="varchar(10)" notnull="true"/>
            <column name="type" type="varchar(100)" notnull="true"/>
            <column name="allowed_states" type="varchar[](2)" notnull="true"/>
        </createTable>
        <createTable name="payment.currency_rate" withHistory="true" pkName="pk_currency_rate">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_currency_rate_brand_id" foreignKeyIndex="ix_currency_rate_brand_id"/>
            <column name="currency_code" type="varchar" notnull="true"/>
            <column name="rate" type="decimal" notnull="true"/>
            <column name="modified_by" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_currency_rate_brand_id_currency_code" columnNames="brand_id,currency_code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <alterColumn columnName="brand_id" tableName="uam.daily_bonus_templates" currentType="integer" notnull="false" currentNotnull="true"/>
        <addColumn tableName="uam.daily_bonus_templates">
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="country_code" type="varchar(2)"/>
            <column name="external_reward_code" type="varchar"/>
            <column name="segment" type="varchar(20)"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_daily_bonus_templates_brand_id_code" tableName="uam.daily_bonus_templates" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_daily_bonus_templates_brand_id_country_code_segment_code" tableName="uam.daily_bonus_templates" columnNames="brand_id,country_code,segment,code" oneToOne="false" nullableColumns="brand_id,country_code,segment"/>
        <createTable name="payment.daily_redeem_limits" pkName="pk_daily_redeem_limits">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_daily_redeem_limits_brand_id" foreignKeyIndex="ix_daily_redeem_limits_brand_id"/>
            <column name="state" type="varchar"/>
            <column name="max" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="payment.deactivate_provider_policy">
            <column name="custom_error_codes" type="varchar[]"/>
        </addColumn>
        <createTable name="uam.debezium_signal" identityType="external" pkName="pk_debezium_signal">
            <column name="id" type="varchar(42)" primaryKey="true"/>
            <column name="type" type="varchar(42)" notnull="true"/>
            <column name="data" type="varchar(2048)"/>
        </createTable>
        <createTable name="game_hub.debezium_signal" identityType="external" pkName="pk_debezium_signal">
            <column name="id" type="varchar(42)" primaryKey="true"/>
            <column name="type" type="varchar(42)" notnull="true"/>
            <column name="data" type="varchar(2048)"/>
        </createTable>
        <createTable name="payment.debezium_signal" identityType="external" pkName="pk_debezium_signal">
            <column name="id" type="varchar(42)" primaryKey="true"/>
            <column name="type" type="varchar(42)" notnull="true"/>
            <column name="data" type="varchar(2048)"/>
        </createTable>
        <createTable name="fraud.debezium_signal" identityType="external" pkName="pk_debezium_signal">
            <column name="id" type="varchar(42)" primaryKey="true"/>
            <column name="type" type="varchar(42)" notnull="true"/>
            <column name="data" type="varchar(2048)"/>
        </createTable>
        <createTable name="fraud.dispute_decisions" pkName="pk_dispute_decisions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="decision_id" type="varchar"/>
            <column name="arn" type="varchar" notnull="true"/>
            <column name="transaction_id" type="varchar"/>
            <column name="card_bin" type="varchar"/>
            <column name="card_last4" type="varchar"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="currency" type="varchar" notnull="true"/>
            <column name="case_date" type="localdatetime" notnull="true"/>
            <column name="reason_code" type="varchar" notnull="true"/>
            <column name="outcome" type="varchar" notnull="true"/>
            <column name="status_code" type="integer" notnull="true"/>
            <column name="reason" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_dispute_decisions_decision_id" columnNames="decision_id" oneToOne="false" nullableColumns="decision_id"/>
            <uniqueConstraint name="uq_dispute_decisions_arn" columnNames="arn" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.doc_upload_requests" withHistory="true" pkName="pk_doc_upload_requests">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true" unique="uq_doc_upload_requests_transaction_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_doc_upload_requests_account_id" foreignKeyIndex="ix_doc_upload_requests_account_id"/>
            <column name="doc_type" type="varchar(18)" notnull="true" checkConstraint="check ( doc_type in ('bank_statement','tax_return','purchase_agreement','sale_of_an_asset','payslips','others'))" checkConstraintName="ck_doc_upload_requests_doc_type"/>
            <column name="status" type="varchar(15)" notnull="true" checkConstraint="check ( status in ('initiated','session_expired','failed','pending_review','approved','rejected','in_review'))" checkConstraintName="ck_doc_upload_requests_status"/>
            <column name="uploaded_at" type="timestamp"/>
            <column name="in_review_at" type="timestamp"/>
            <column name="final_actioned_at" type="timestamp"/>
            <column name="agent_name" type="varchar"/>
            <column name="comment" type="varchar"/>
            <column name="custom_doc_type" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_doc_upload_requests_account_id_code" columnNames="account_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.dynamic_secure_3d_checks" pkName="pk_dynamic_secure_3d_checks">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_dynamic_secure_3d_checks_brand_id" foreignKeyIndex="ix_dynamic_secure_3d_checks_brand_id"/>
            <column name="secure3d_action" type="varchar(5)" checkConstraint="check ( secure3d_action in ('skip','force'))" checkConstraintName="ck_dynamic_secure_3d_checks_secure3d_action"/>
            <column name="condition" type="varchar" notnull="true"/>
            <column name="allow_high_fraud_score" type="boolean" defaultValue="false" notnull="true"/>
            <column name="countries" type="varchar[]"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="payment.e_transfer_payment_method" pkName="pk_e_transfer_payment_method">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="email" type="varchar"/>
            <column name="phone" type="varchar"/>
            <column name="bank_names" type="varchar[]"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.e_wallet_payment_method" withHistory="true" pkName="pk_e_wallet_payment_method">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="firstname" type="varchar"/>
            <column name="lastname" type="varchar"/>
            <column name="customer_id" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="fraud.email_log" pkName="pk_email_log">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="identifier" type="varchar" notnull="true"/>
            <column name="source" type="varchar(10)" notnull="true" checkConstraint="check ( source in ('tc40','chargeback'))" checkConstraintName="ck_email_log_source"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_email_log_source_identifier" columnNames="source,identifier" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.email_otp_request" identityType="identity" pkName="pk_email_otp_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_email_otp_request_account_id"/>
            <column name="otp" type="varchar" notnull="true"/>
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="expire_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="uam.email_templates">
            <column name="locale" type="varchar(5)"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_email_templates_brand_id_code" tableName="uam.email_templates" columnNames="DROP CONSTRAINT" nullableColumns="brand_id"/>
        <addUniqueConstraint constraintName="uq_email_templates_brand_id_code_locale" tableName="uam.email_templates" columnNames="brand_id,code,locale" oneToOne="false" nullableColumns="brand_id,locale"/>
        <createTable name="payment.error_mapping" pkName="pk_error_mapping">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="error_code" type="varchar" notnull="true"/>
            <column name="type" type="varchar(15)" notnull="true" checkConstraint="check ( type in ('card_network','seon_rule','provider_custom','http_code'))" checkConstraintName="ck_error_mapping_type"/>
            <column name="message" type="varchar"/>
            <column name="description" type="varchar"/>
            <column name="mapped_error_code" type="varchar"/>
            <uniqueConstraint name="uq_error_mapping_error_code_type" columnNames="error_code,type" oneToOne="false" nullableColumns=""/>
        </createTable>
        <addColumn tableName="core.xp_levels">
            <column name="name" type="varchar"/>
            <column name="sweepstake_bonus" type="decimal(16,2)"/>
            <column name="cashback_rate" type="decimal(10,7)"/>
            <column name="type" type="varchar(9)" notnull="true" checkConstraint="check ( type in ('permanent','month','annual'))" checkConstraintName="ck_xp_levels_type"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_xp_levels_brand_id_level" tableName="core.xp_levels" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_xp_levels_brand_id_level_type" tableName="core.xp_levels" columnNames="brand_id,level,type" oneToOne="false" nullableColumns=""/>
        <createTable name="payment.external_reward" pkName="pk_external_reward">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="reward_code" type="varchar" notnull="true"/>
            <column name="source" type="varchar(26)" notnull="true" checkConstraint="check ( source in ('UNKNOWN_SOURCE','QUEST_SOURCE','RANDOM_REWARD_SOURCE','INTERNAL_SOURCE','PLATFORM_TOURNAMENT_SOURCE','CHAIN_OFFERS_REWARD_SOURCE','UNRECOGNIZED'))" checkConstraintName="ck_external_reward_source"/>
            <column name="status" type="varchar(25)" notnull="true" checkConstraint="check ( status in ('UNKNOWN_REWARD_ASSIGNMENT','REWARD_ASSIGNED','REWARD_NOT_APPLICABLE','REWARD_ASSIGNMENT_FAILED','UNRECOGNIZED'))" checkConstraintName="ck_external_reward_status"/>
            <column name="bonus_amount" type="decimal"/>
            <column name="order_id" type="bigint"/>
            <column name="order_at" type="date"/>
            <foreignKey name="fk_external_reward_order" columnNames="order_id,order_at" refColumnNames="id,at" refTableName="payment.payment_orders"/>
        </createTable>
        <createTable name="payment.fx_currency_rate" pkName="pk_fx_currency_rate">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="currency_code" type="varchar" notnull="true"/>
            <column name="fx_rate" type="decimal" notnull="true"/>
            <column name="at" type="date" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_fx_currency_rate_currency_code_at" columnNames="currency_code,at" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="core.features" pkName="pk_features">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_features_brand_id" foreignKeyIndex="ix_features_brand_id"/>
            <column name="enabled_for_web" type="boolean" defaultValue="false" notnull="true"/>
            <column name="enabled_for_ios" type="boolean" defaultValue="false" notnull="true"/>
            <column name="enabled_for_android" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_features_brand_id_name" columnNames="brand_id,name" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.accounts" pkName="pk_accounts">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="hash" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="fraud.brands.id" foreignKeyName="fk_accounts_brand_id" foreignKeyIndex="ix_accounts_brand_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.fraud_applied_rules" pkName="pk_fraud_applied_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="rule_id" type="varchar" notnull="true"/>
            <column name="score" type="integer" notnull="true"/>
            <column name="operation" type="varchar"/>
            <column name="fraud_response_id" type="bigint" notnull="true" references="fraud.fraud_response.id" foreignKeyName="fk_fraud_applied_rules_fraud_response_id" foreignKeyIndex="ix_fraud_applied_rules_fraud_response_id"/>
            <column name="at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="fraud.brands" pkName="pk_brands">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true" unique="uq_brands_name"/>
            <column name="fraud_lock_threshold" type="integer"/>
            <column name="fraud_suspect_threshold" type="integer"/>
            <column name="phone_expiration" type="integer"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules" type="varchar(37)" currentType="varchar(25)" currentNotnull="true" checkConstraint="check ( decline_code in ('err_ok','err_ok_no_content','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_kyc_required_low_risk','err_kyc_required_mid_risk','err_kyc_required_high_risk','err_tc_required','err_sr_required','err_pp_required','err_pt_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_password_set_on_existing_password','err_cardholder_name_mismatch','unrecognized','err_card_blocked','err_card_not_verified','err_payment_routing_empty_chain','err_3ds_payment_routing','err_network','err_payment_inactive_provider','err_payment_email_not_verified','err_payment_purchase_limit','err_payment_method_blocked','err_payment_3ds_required','err_payment_in_progress','err_3ds_failed','err_otp_limit','err_otp_required','err_promo_sms_consent_otp_required','err_restricted_location','err_pickem_limit','err_3ds_not_supported','err_soft_required_gps','err_soft_restricted_location'))" checkConstraintName="ck_fraud_decline_rules_decline_code"/>
        <createTable name="fraud.fraud_response" pkName="pk_fraud_response">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="response_id" type="varchar" notnull="true"/>
            <column name="cookie_hash" type="varchar"/>
            <column name="action_type" type="varchar(16)" checkConstraint="check ( action_type in ('account_login','account_register','withdrawal','purchase','aml_check','verification','deposit'))" checkConstraintName="ck_fraud_response_action_type"/>
            <column name="score" type="integer"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_fraud_response_account_id" foreignKeyIndex="ix_fraud_response_account_id"/>
            <column name="ip_details_id" type="bigint" references="fraud.ip_details.id" foreignKeyName="fk_fraud_response_ip_details_id" foreignKeyIndex="ix_fraud_response_ip_details_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.fraud_rules" identityType="external" pkName="pk_fraud_rules">
            <column name="id" type="varchar" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="fraud.fraud_rule_category_details" pkName="pk_fraud_rule_category_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="category_id" type="bigint" notnull="true"/>
            <column name="category_name" type="varchar"/>
            <column name="category_score_test_score" type="integer"/>
            <column name="category_score_test_state" type="varchar"/>
            <column name="fraud_response_id" type="bigint" notnull="true" references="fraud.fraud_response.id" foreignKeyName="fk_fraud_rule_category_details_fraud_response_id" foreignKeyIndex="ix_fraud_rule_category_details_fraud_response_id"/>
        </createTable>
        <createTable name="uam.free_spin_campaigns" pkName="pk_free_spin_campaigns">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_free_spin_campaigns_brand_id" foreignKeyIndex="ix_free_spin_campaigns_brand_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="code" type="varchar" notnull="true" unique="uq_free_spin_campaigns_code"/>
            <column name="status" type="varchar" notnull="true"/>
            <column name="total_spins" type="integer" notnull="true"/>
            <column name="bet_value" type="decimal" notnull="true"/>
            <column name="bet_type" type="varchar(12)" notnull="true" checkConstraint="check ( bet_type in ('bet_per_line','total_bet'))" checkConstraintName="ck_free_spin_campaigns_bet_type"/>
            <column name="start_date" type="timestamp"/>
            <column name="end_date" type="timestamp"/>
            <column name="bet_level" type="integer"/>
            <column name="type" type="varchar(15)" notnull="true" checkConstraint="check ( type in ('free_spins','awarded_feature'))" checkConstraintName="ck_free_spin_campaigns_type"/>
            <column name="product_id" type="integer" references="core.products.id" foreignKeyName="fk_free_spin_campaigns_product_id" foreignKeyIndex="ix_free_spin_campaigns_product_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.free_spin_campaign_product" pkName="pk_free_spin_campaign_product">
            <column name="free_spin_campaign_id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="product_id" type="integer" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_free_spin_campaign_product_free_spin_campaigns" columnNames="free_spin_campaign_id" refColumnNames="id" refTableName="uam.free_spin_campaigns" indexName="ix_free_spin_campaign_product_free_spin_campaigns"/>
            <foreignKey name="fk_free_spin_campaign_product_products" columnNames="product_id" refColumnNames="id" refTableName="core.products" indexName="ix_free_spin_campaign_product_products"/>
        </createTable>
        <createTable name="uam.free_spin_wallet_sessions" pkName="pk_free_spin_wallet_sessions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_free_spin_id" type="bigint" notnull="true" references="uam.account_free_spins.id" foreignKeyName="fk_free_spin_wallet_sessions_account_free_spin_id" foreignKeyIndex="ix_free_spin_wallet_sessions_account_free_spin_id"/>
            <column name="wallet_session_id" type="bigint"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_free_spin_wallet_sessions_wallet_session_id" columnNames="wallet_session_id" oneToOne="false" nullableColumns="wallet_session_id"/>
        </createTable>
        <createTable name="uam.game_allowance_by_registration_policies" pkName="pk_game_allowance_by_registration_policies">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_game_allowance_by_registration_policies_brand_id" foreignKeyIndex="ix_game_allowance_by_registration_policies_brand_id"/>
            <column name="supplier" type="varchar" notnull="true"/>
            <column name="code" type="varchar(2)" notnull="true"/>
            <column name="mode" type="varchar(10)" notnull="true" checkConstraint="check ( mode in ('prohibited','full'))" checkConstraintName="ck_game_allowance_by_registration_policies_mode"/>
            <column name="precision" type="varchar(7)" notnull="true" checkConstraint="check ( precision in ('country','state'))" checkConstraintName="ck_game_allowance_by_registration_policies_precision"/>
            <column name="blocked" type="varchar[]" notnull="true"/>
            <column name="allowed" type="varchar[]" notnull="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_game_allowance_by_registration_policies_brand_id_code__1" columnNames="brand_id,code,supplier" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.game_allowance_policies" pkName="pk_game_allowance_policies">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_game_allowance_policies_brand_id" foreignKeyIndex="ix_game_allowance_policies_brand_id"/>
            <column name="supplier" type="varchar" notnull="true"/>
            <column name="code" type="varchar(2)" notnull="true"/>
            <column name="mode" type="varchar(10)" notnull="true" checkConstraint="check ( mode in ('prohibited','full'))" checkConstraintName="ck_game_allowance_policies_mode"/>
            <column name="precision" type="varchar(7)" notnull="true" checkConstraint="check ( precision in ('country','state'))" checkConstraintName="ck_game_allowance_policies_precision"/>
            <column name="blocked" type="varchar[]" notnull="true"/>
            <column name="allowed" type="varchar[]" notnull="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_game_allowance_policies_brand_id_code_supplier" columnNames="brand_id,code,supplier" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.feature_settings_game_lobby" pkName="pk_feature_settings_game_lobby">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="country_id" type="integer" notnull="true" uniqueOneToOne="uq_feature_settings_game_lobby_country_id" references="uam.countries.id" foreignKeyName="fk_feature_settings_game_lobby_country_id"/>
            <column name="start_date_time" type="localdatetime" notnull="true"/>
            <column name="excluded_from_response_category_codes" type="varchar[]" notnull="true"/>
            <column name="excluded_from_alternation_category_codes" type="varchar[]" notnull="true"/>
            <column name="gold_mode_only_category_codes" type="varchar[]" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.giveaway" pkName="pk_giveaway">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="owner_id" type="bigint" notnull="true" references="uam.influencer.id" foreignKeyName="fk_giveaway_owner_id" foreignKeyIndex="ix_giveaway_owner_id"/>
            <column name="code" type="uuid" notnull="true" unique="uq_giveaway_code"/>
            <column name="status" type="varchar" notnull="true"/>
            <column name="prize" type="jsonb"/>
            <column name="sc_prize" type="decimal(16,2)"/>
            <column name="gc_prize" type="decimal(16,2)"/>
            <column name="winners_number" type="integer" notnull="true"/>
            <column name="ended_at" type="timestamp"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.giveaway_subscriber" pkName="pk_giveaway_subscriber">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="giveaway_id" type="bigint" notnull="true" references="uam.giveaway.id" foreignKeyName="fk_giveaway_subscriber_giveaway_id" foreignKeyIndex="ix_giveaway_subscriber_giveaway_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_giveaway_subscriber_account_id" foreignKeyIndex="ix_giveaway_subscriber_account_id"/>
            <column name="winner" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_giveaway_subscriber_giveaway_id_account_id" columnNames="giveaway_id,account_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.feature_settings_gold_coin_generator" pkName="pk_feature_settings_gold_coin_generator">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="country_code" type="varchar(2)"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_feature_settings_gold_coin_generator_brand_id" foreignKeyIndex="ix_feature_settings_gold_coin_generator_brand_id"/>
            <column name="claim_interval" type="bigint" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_feature_settings_gold_coin_generator_country_code_bran_1" columnNames="country_code,brand_id" oneToOne="false" nullableColumns="country_code,brand_id"/>
        </createTable>
        <createTable name="uam.gps_history" pkName="pk_gps_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_gps_history_account_id" foreignKeyIndex="ix_gps_history_account_id"/>
            <column name="journey" type="varchar" notnull="true"/>
            <column name="restricted_by_ip" type="boolean" defaultValue="false" notnull="true"/>
            <column name="restricted_by_gps" type="boolean" defaultValue="false" notnull="true"/>
            <column name="ip" type="varchar"/>
            <column name="ip_country" type="varchar"/>
            <column name="ip_state" type="varchar"/>
            <column name="gps_country" type="varchar"/>
            <column name="gps_state" type="varchar"/>
            <column name="reason" type="varchar(20)"/>
            <column name="payload" type="varchar(4000)"/>
            <column name="platform" type="varchar(7)" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_gps_history_platform"/>
            <column name="app_name" type="varchar"/>
            <column name="created_at" type="localdatetime" notnull="true"/>
        </createTable>
        <createTable name="uam.homepage_features" pkName="pk_homepage_features">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="country_code" type="varchar(2)"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_homepage_features_brand_id" foreignKeyIndex="ix_homepage_features_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="feature_type" type="varchar(10)" notnull="true" checkConstraint="check ( feature_type in ('icon','background','widget','settings'))" checkConstraintName="ck_homepage_features_feature_type"/>
            <column name="url" type="varchar"/>
            <column name="url_mobile" type="varchar"/>
            <column name="feature_order" type="integer"/>
            <column name="text_alt" type="varchar"/>
            <column name="path" type="varchar"/>
            <column name="priority" type="integer"/>
            <column name="timer" type="boolean" defaultValue="false" notnull="true"/>
            <column name="progress" type="boolean" defaultValue="false" notnull="true"/>
            <column name="sweepstake" type="boolean" defaultValue="false" notnull="true"/>
            <column name="gold" type="boolean" defaultValue="false" notnull="true"/>
            <column name="logged_in" type="boolean" defaultValue="false" notnull="true"/>
            <column name="logged_out" type="boolean" defaultValue="false" notnull="true"/>
            <column name="start_at" type="timestamp"/>
            <column name="end_at" type="timestamp"/>
            <column name="repeat_mode" type="varchar(5)" checkConstraint="check ( repeat_mode in ('NEVER','DAILY'))" checkConstraintName="ck_homepage_features_repeat_mode"/>
            <column name="animation" type="varchar"/>
            <column name="placement" type="varchar"/>
            <column name="layout" type="varchar"/>
            <column name="exclude_states" type="varchar[]"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="account_id" tableName="uam.kyc_verification_requests" references="fraud.accounts.id" foreignKeyName="fk_kyc_verification_requests_account_id" foreignKeyIndex="ix_kyc_verification_requests_account_id" dropForeignKey="fk_kyc_verification_requests_account_id" dropForeignKeyIndex="ix_kyc_verification_requests_account_id"/>
        <addColumn tableName="uam.kyc_verification_requests">
            <column name="transaction_id" type="uuid" notnull="true" unique="uq_kyc_verification_requests_transaction_id"/>
            <column name="phase" type="varchar(3)" checkConstraint="check ( phase in ('id','poa'))" checkConstraintName="ck_kyc_verification_requests_phase"/>
            <column name="scan_reference" type="varchar"/>
            <column name="client_redirect_url" type="varchar(4000)"/>
            <column name="api_error" type="jsonb"/>
            <column name="id_country" type="varchar(2)"/>
            <column name="id_number_mask" type="varbinary"/>
            <column name="id_number_hash" type="varchar"/>
            <column name="id_type" type="varchar(16)" checkConstraint="check ( id_type in ('passport','driving_license','id_card','visa','residence_permit','other'))" checkConstraintName="ck_kyc_verification_requests_id_type"/>
            <column name="id_state" type="varchar"/>
            <column name="similarity" type="varchar"/>
            <column name="validity" type="boolean" defaultValue="false" notnull="true"/>
            <column name="validity_reason" type="varchar(30)" checkConstraint="check ( validity_reason in ('selfie_cropped_from_id','entire_id_used_as_selfie','multiple_people','selfie_is_screen_paper_video','selfie_manipulated','age_difference_too_big','no_face_present','face_not_fully_visible','bad_quality','black_and_white','liveness_failed','liveness_failed_unknown_reason'))" checkConstraintName="ck_kyc_verification_requests_validity_reason"/>
            <column name="doc_address_mask" type="varbinary"/>
            <column name="doc_name" type="varchar"/>
            <column name="doc_issue_date" type="date"/>
            <column name="expire_at" type="date"/>
            <column name="platform" type="varchar(7)" notnull="true" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_kyc_verification_requests_platform"/>
            <column name="reason" type="varchar(4000)"/>
            <column name="reason_desc" type="varchar(4000)"/>
            <column name="reason_code" type="integer"/>
            <column name="at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <alterColumn columnName="provider_id" tableName="core.products" currentType="integer" notnull="true" currentNotnull="false"/>
        <addColumn tableName="core.products">
            <column name="supplier_id" type="integer" notnull="true" references="core.suppliers.id" foreignKeyName="fk_products_supplier_id" foreignKeyIndex="ix_products_supplier_id"/>
            <column name="exclusivity_end_date" type="timestamp"/>
            <column name="animated_tile" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <alterColumn columnName="min_bet" tableName="uam.product_bet_limits" type="decimal(16,2)" currentType="decimal" currentNotnull="false"/>
        <addColumn tableName="uam.product_bet_limits">
            <column name="max_bet_limit" type="decimal(16,2)"/>
            <column name="max_win_limit" type="decimal(16,2)"/>
        </addColumn>
        <createTable name="core.suppliers" pkName="pk_suppliers">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="search_code" type="varchar" notnull="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="rank" type="integer" notnull="true"/>
            <column name="icon" type="varchar" notnull="true"/>
            <column name="is_new" type="boolean" defaultValue="false" notnull="true"/>
            <column name="show_in_side_menu" type="boolean" defaultValue="false" notnull="true"/>
            <column name="provider_id" type="integer" notnull="true" references="core.providers.id" foreignKeyName="fk_suppliers_provider_id" foreignKeyIndex="ix_suppliers_provider_id"/>
            <column name="freespins_available" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_suppliers_provider_id_code" columnNames="provider_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <addColumn tableName="uam.utm_templates">
            <column name="source" type="varchar"/>
            <column name="medium" type="varchar"/>
            <column name="term" type="varchar"/>
            <column name="content" type="varchar"/>
            <column name="utm_id" type="varchar"/>
            <column name="query" type="varchar(4000)"/>
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_utm_templates_mode"/>
            <column name="vip_points" type="decimal(16,2)"/>
            <column name="gold_money" type="decimal(16,2)"/>
            <column name="sweepstake_money" type="decimal(16,2)"/>
            <column name="fiat_money" type="decimal(16,2)"/>
            <column name="require_otp" type="boolean" defaultValue="false" notnull="true"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_utm_templates_brand_id_campaign" tableName="uam.utm_templates" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_utm_templates_brand_id_campaign_source_medium_term_con_1" tableName="uam.utm_templates" columnNames="brand_id,campaign,source,medium,term,content,utm_id" oneToOne="false" nullableColumns="source,medium,term,content,utm_id"/>
        <createTable name="payment.inbox_notifications" withHistory="true" pkName="pk_inbox_notifications">
            <column name="notification_type" type="varchar(31)" notnull="true"/>
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="token" type="uuid" notnull="true" unique="uq_inbox_notifications_token"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_inbox_notifications_account_id" foreignKeyIndex="ix_inbox_notifications_account_id"/>
            <column name="status" type="varchar(11)" notnull="true" checkConstraint="check ( status in ('unread','read','claimed','expired','removed','unavailable'))" checkConstraintName="ck_inbox_notifications_status"/>
            <column name="expires_at" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="crypto_payment_order_id" type="bigint"/>
            <column name="crypto_payment_order_at" type="date"/>
            <column name="offer_template_id" type="bigint" references="payment.offer_templates.id" foreignKeyName="fk_inbox_notifications_offer_template_id" foreignKeyIndex="ix_inbox_notifications_offer_template_id"/>
            <foreignKey name="fk_inbox_notifications_cryptopaymentorder" columnNames="crypto_payment_order_id,crypto_payment_order_at" refColumnNames="id,at" refTableName="payment.payment_orders" indexName="ix_inbox_notifications_cryptopaymentorder"/>
        </createTable>
        <createTable name="fraud.inbox_notifications" withHistory="true" pkName="pk_inbox_notifications">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="token" type="uuid" notnull="true" unique="uq_inbox_notifications_token"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_inbox_notifications_account_id" foreignKeyOnDelete="RESTRICT" foreignKeyOnUpdate="RESTRICT"/>
            <column name="type" type="varchar(25)" notnull="true" checkConstraint="check ( type in ('require_card_verification'))" checkConstraintName="ck_inbox_notifications_type"/>
            <column name="status" type="varchar(7)" notnull="true" checkConstraint="check ( status in ('read','removed','unread'))" checkConstraintName="ck_inbox_notifications_status"/>
            <column name="active_from" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="uam.influencer" pkName="pk_influencer">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="nickname" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" uniqueOneToOne="uq_influencer_account_id" references="uam.accounts.id" foreignKeyName="fk_influencer_account_id"/>
            <column name="limits" type="jsonb"/>
            <column name="permissions" type="jsonb"/>
            <column name="banner_url" type="varchar"/>
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="uam.influencer_budget_spend" pkName="pk_influencer_budget_spend">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="influencer_id" type="bigint" notnull="true" references="uam.influencer.id" foreignKeyName="fk_influencer_budget_spend_influencer_id" foreignKeyIndex="ix_influencer_budget_spend_influencer_id"/>
            <column name="reward_in_usd" type="decimal(16,2)"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="allocated_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.influencer_giveaway_prize_configs" pkName="pk_influencer_giveaway_prize_configs">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="influencer_id" type="bigint" notnull="true" references="uam.influencer.id" foreignKeyName="fk_influencer_giveaway_prize_configs_influencer_id" foreignKeyIndex="ix_influencer_giveaway_prize_configs_influencer_id"/>
            <column name="type" type="varchar(9)" notnull="true" checkConstraint="check ( type in ('coin','free_spin'))" checkConstraintName="ck_influencer_giveaway_prize_configs_type"/>
            <column name="fs_count_prize" type="integer" notnull="true"/>
            <column name="gc_prize" type="decimal(16,2)" notnull="true"/>
            <column name="sc_prize" type="decimal(16,2)" notnull="true"/>
            <column name="winners" type="integer[]" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_influencer_giveaway_prize_configs_influencer_id_type_f_1" columnNames="influencer_id,type,fs_count_prize,gc_prize,sc_prize" oneToOne="false" nullableColumns=""/>
        </createTable>
        <alterColumn columnName="action" tableName="uam.invitation_reward_policies" checkConstraint="check ( action in ('sign_up','first_purchase','purchase','purchase_aggr','deposit_aggr','first_withdraw','withdraw'))" checkConstraintName="ck_invitation_reward_policies_action"/>
        <addColumn tableName="uam.invitation_reward_policies">
            <column name="fiat_bonus_amount" type="decimal(16,2)"/>
            <column name="fiat_currency" type="varchar(3) default 'USD'" checkConstraint="check ( fiat_currency in ('USD','CAD','AUD'))" checkConstraintName="ck_invitation_reward_policies_fiat_currency"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_invitation_reward_policies_brand_id_action_level" tableName="uam.invitation_reward_policies" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_invitation_reward_policies_brand_id_action_level_fiat__1" tableName="uam.invitation_reward_policies" columnNames="brand_id,action,level,fiat_currency" oneToOne="false" nullableColumns="fiat_currency"/>
        <createTable name="fraud.ip_details" pkName="pk_ip_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="ip" type="varchar"/>
            <column name="type" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_ip_details_ip" columnNames="ip" oneToOne="false" nullableColumns="ip"/>
        </createTable>
        <createTable name="uam.jackpot_account_free_contributions" pkName="pk_jackpot_account_free_contributions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_jackpot_account_free_contributions_account_id" foreignKeyIndex="ix_jackpot_account_free_contributions_account_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="used_amount" type="decimal(16,2)" notnull="true"/>
            <column name="reward_campaign_id" type="bigint" references="uam.reward_campaigns.id" foreignKeyName="fk_jackpot_account_free_contributions_reward_campaign_id" foreignKeyIndex="ix_jackpot_account_free_contributions_reward_campaign_id"/>
            <column name="reward_creditor_id" type="bigint" references="uam.reward_creditors.id" foreignKeyName="fk_jackpot_account_free_contributions_reward_creditor_id" foreignKeyIndex="ix_jackpot_account_free_contributions_reward_creditor_id"/>
            <column name="expire_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_jackpot_account_free_contributions_account_id_code" columnNames="account_id,code" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <addUniqueConstraint constraintName="uq_jackpot_preferences_brand_id_currency" tableName="uam.jackpot_preferences" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <createTable name="uam.jackpot_brand_preferences" pkName="pk_jackpot_brand_preferences">
            <column name="preference_id" type="bigint" notnull="true" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_jackpot_brand_preferences_jackpot_preferences" columnNames="preference_id" refColumnNames="id" refTableName="uam.jackpot_preferences" indexName="ix_jackpot_brand_preferences_jackpot_preferences"/>
            <foreignKey name="fk_jackpot_brand_preferences_brands" columnNames="brand_id" refColumnNames="id" refTableName="core.brands" indexName="ix_jackpot_brand_preferences_brands"/>
        </createTable>
        <createTable name="fraud.kyc_risk_spend_policy" pkName="pk_kyc_risk_spend_policy">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="fraud_brand_id" type="integer" references="fraud.brands.id" foreignKeyName="fk_kyc_risk_spend_policy_fraud_brand_id" foreignKeyIndex="ix_kyc_risk_spend_policy_fraud_brand_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="low_risk_spend_threshold" type="decimal(16,2)" notnull="true"/>
            <column name="mid_risk_spend_threshold" type="decimal(16,2)" notnull="true"/>
            <column name="high_risk_spend_threshold" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="fraud.kyc_risk_spend_policy_history" identityType="identity" pkName="pk_kyc_risk_spend_policy_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="fraud_brand_id" type="integer" references="fraud.brands.id" foreignKeyName="fk_kyc_risk_spend_policy_history_fraud_brand_id" foreignKeyIndex="ix_kyc_risk_spend_policy_history_fraud_brand_id"/>
            <column name="limit_type" type="varchar(10)" notnull="true" checkConstraint="check ( limit_type in ('LOW','MID','HIGH'))" checkConstraintName="ck_kyc_risk_spend_policy_history_limit_type"/>
            <column name="old_value" type="decimal(20,6)"/>
            <column name="new_value" type="decimal(20,6)" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="updated_by" type="varchar" notnull="true"/>
            <column name="updated_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="setting" tableName="uam.legal_rules" checkConstraint="check ( setting in ('Tickbox','Popup'))" checkConstraintName="ck_legal_rules_setting"/>
        <alterColumn columnName="type" tableName="uam.legal_rules" checkConstraint="check ( type in ('tc','sr','pp','pt'))" checkConstraintName="ck_legal_rules_type"/>
        <addColumn tableName="uam.legal_rules">
            <column name="gold_only" type="boolean" defaultValue="false" notnull="true"/>
            <column name="country" type="varchar"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_legal_rules_brand_id_type_rule_version" tableName="uam.legal_rules" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_legal_rules_brand_id_type_rule_version_gold_only_country" tableName="uam.legal_rules" columnNames="brand_id,type,rule_version,gold_only,country" oneToOne="false" nullableColumns="country"/>
        <createTable name="uam.location_status_history" pkName="pk_location_status_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_location_status_history_account_id" foreignKeyIndex="ix_location_status_history_account_id"/>
            <column name="location_status_old" type="varchar(12)" notnull="true" checkConstraint="check ( location_status_old in ('ok','required_gps','restricted'))" checkConstraintName="ck_location_status_history_location_status_old"/>
            <column name="location_status_new" type="varchar(12)" notnull="true" checkConstraint="check ( location_status_new in ('ok','required_gps','restricted'))" checkConstraintName="ck_location_status_history_location_status_new"/>
            <column name="ip" type="varchar"/>
            <column name="ip_country" type="varchar"/>
            <column name="ip_state" type="varchar"/>
            <column name="coordinates" type="varchar"/>
            <column name="gps_country" type="varchar"/>
            <column name="gps_state" type="varchar"/>
            <column name="operation" type="varchar"/>
            <column name="created_at" type="localdatetime" notnull="true"/>
        </createTable>
        <createTable name="uam.lottery_results" pkName="pk_lottery_results">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="date_fetched" type="date"/>
            <column name="results" type="varchar(4000)"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_lottery_results_date_fetched" columnNames="date_fetched" oneToOne="false" nullableColumns="date_fetched"/>
        </createTable>
        <createTable name="uam.loyalty_level_bonus" pkName="pk_loyalty_level_bonus">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="xp_level_id" type="bigint" notnull="true" references="core.xp_levels.id" foreignKeyName="fk_loyalty_level_bonus_xp_level_id" foreignKeyIndex="ix_loyalty_level_bonus_xp_level_id"/>
            <column name="net_gaming_revenue" type="decimal(16,2)" notnull="true"/>
            <column name="bonus_gc" type="decimal(16,2)" notnull="true"/>
            <column name="bonus_sc" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.missions" pkName="pk_missions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_missions_brand_id" foreignKeyIndex="ix_missions_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="type" type="varchar(20)" notnull="true" checkConstraint="check ( type in ('quest','modular'))" checkConstraintName="ck_missions_type"/>
            <column name="time_to_complete" type="integer"/>
            <column name="start_at" type="timestamp"/>
            <column name="end_at" type="timestamp"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="priority" type="integer"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_missions_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.mission_free_spin_rewards" pkName="pk_mission_free_spin_rewards">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="mission_id" type="bigint" notnull="true" references="uam.missions.id" foreignKeyName="fk_mission_free_spin_rewards_mission_id"/>
            <column name="bonus_code" type="varchar" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.mission_rewards" pkName="pk_mission_rewards">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="mission_id" type="bigint" notnull="true" references="uam.missions.id" foreignKeyName="fk_mission_rewards_mission_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.mission_steps" pkName="pk_mission_steps">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="mission_id" type="bigint" notnull="true" references="uam.missions.id" foreignKeyName="fk_mission_steps_mission_id" foreignKeyIndex="ix_mission_steps_mission_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="mission_step_action_id" type="bigint" notnull="true" references="uam.mission_step_actions.id" foreignKeyName="fk_mission_steps_mission_step_action_id" foreignKeyIndex="ix_mission_steps_mission_step_action_id"/>
            <column name="base_unit" type="decimal(16,5)"/>
            <column name="units_to_complete" type="integer"/>
            <column name="time_to_complete" type="integer"/>
            <column name="step_order" type="integer"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_mission_steps_mission_id_code" columnNames="mission_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.mission_step_actions" pkName="pk_mission_step_actions">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="action_type" type="varchar" notnull="true"/>
            <column name="action_value" type="varchar" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_mission_step_actions_action_type_action_value" columnNames="action_type,action_value" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.mission_step_free_spin_rewards" pkName="pk_mission_step_free_spin_rewards">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="mission_step_id" type="bigint" notnull="true" references="uam.mission_steps.id" foreignKeyName="fk_mission_step_free_spin_rewards_mission_step_id"/>
            <column name="bonus_code" type="varchar" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.mission_step_rewards" pkName="pk_mission_step_rewards">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="mission_step_id" type="bigint" notnull="true" references="uam.mission_steps.id" foreignKeyName="fk_mission_step_rewards_mission_step_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <addHistoryTable baseTable="payment.offer_templates"/>
        <alterColumn columnName="title" tableName="payment.offer_templates" currentType="varchar" notnull="true" currentNotnull="false"/>
        <alterColumn columnName="type" tableName="payment.offer_templates" checkConstraint="check ( type in ('one_time','subscription','permanent','personalized','daily','weekly','fixed_daily','fixed_weekly','reward'))" checkConstraintName="ck_offer_templates_type"/>
        <alterColumn columnName="price" tableName="payment.offer_templates" currentType="decimal(16,2)" notnull="true" currentNotnull="false"/>
        <alterColumn columnName="supported_platform" tableName="payment.offer_templates" checkConstraint="check ( supported_platform in ('web','android','ios','native'))" checkConstraintName="ck_offer_templates_supported_platform"/>
        <addColumn tableName="payment.offer_templates" withHistory="true">
            <column name="old_price" type="decimal(16,2)"/>
            <column name="free_spins" type="integer"/>
            <column name="xp_levels" type="integer[]"/>
            <column name="min_weekly_wagered_gold_coins" type="decimal(16,2)"/>
            <column name="show_time_left" type="boolean" defaultValue="false" notnull="true"/>
            <column name="special_offer_url" type="varchar(4000)"/>
            <column name="icon_image_url" type="varchar(4000)"/>
            <column name="background_image_url" type="varchar(4000)"/>
            <column name="homepage_banner_image_url" type="varchar(4000)"/>
            <column name="background_border" type="varchar"/>
            <column name="is_inbox_notification" type="boolean" defaultValue="false" notnull="true"/>
            <column name="capacity_per_player" type="bigint"/>
            <column name="capacity_per_offer" type="bigint"/>
            <column name="countries" type="varchar[]"/>
            <column name="upgrade_id" type="bigint" references="payment.offer_templates.id" foreignKeyName="fk_offer_templates_upgrade_id" foreignKeyIndex="ix_offer_templates_upgrade_id"/>
            <column name="external_reward_code" type="varchar"/>
            <column name="approval_status" type="varchar(17)" checkConstraint="check ( approval_status in ('sent_for_approval','auto_approved','approved','rejected','draft'))" checkConstraintName="ck_offer_templates_approval_status"/>
            <column name="creator" type="varchar"/>
            <column name="approver" type="varchar"/>
            <column name="approver_comment" type="varchar"/>
            <column name="approver_actioned_at" type="timestamp"/>
            <column name="welcome_offer_type" type="varchar(13)" notnull="true" checkConstraint="check ( welcome_offer_type in ('none','default','utm','affiliate','referral_code','raf'))" checkConstraintName="ck_offer_templates_welcome_offer_type"/>
            <column name="utm_source" type="varchar"/>
            <column name="utm_medium" type="varchar"/>
            <column name="utm_campaign" type="varchar"/>
            <column name="utm_content" type="varchar"/>
            <column name="utm_term" type="varchar"/>
            <column name="affiliate_cxd" type="varchar"/>
            <column name="referral_code" type="varchar"/>
        </addColumn>
        <createTable name="payment.offer_template_change_log" pkName="pk_offer_template_change_log">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="offer_template_id" type="bigint" notnull="true" references="payment.offer_templates.id" foreignKeyName="fk_offer_template_change_log_offer_template_id" foreignKeyIndex="ix_offer_template_change_log_offer_template_id"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_offer_template_change_log_brand_id" foreignKeyIndex="ix_offer_template_change_log_brand_id"/>
            <column name="updater" type="varchar" notnull="true"/>
            <column name="status" type="varchar(17)" notnull="true" checkConstraint="check ( status in ('sent_for_approval','auto_approved','approved','rejected','draft'))" checkConstraintName="ck_offer_template_change_log_status"/>
            <column name="approver" type="varchar"/>
            <column name="changes_json" type="TEXT" notnull="true"/>
            <column name="approver_comment" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.opt_out_arbitration_request" pkName="pk_opt_out_arbitration_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_opt_out_arbitration_request_account_id" foreignKeyIndex="ix_opt_out_arbitration_request_account_id"/>
            <column name="status" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.otp_trigger_audit" withHistory="true" pkName="pk_otp_trigger_audit">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" uniqueOneToOne="uq_otp_trigger_audit_account_id" references="fraud.accounts.id" foreignKeyName="fk_otp_trigger_audit_account_id"/>
            <column name="triggered_at" type="timestamp" notnull="true"/>
            <column name="otp_trigger_rule" type="varchar(28)" checkConstraint="check ( otp_trigger_rule in ('signed_up_trigger','bonus_trigger','utm_source_trigger','set_up_notifications_trigger','bonus_acceptance_trigger','account_closure_trigger'))" checkConstraintName="ck_otp_trigger_audit_otp_trigger_rule"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="fraud.otp_trigger_events" pkName="pk_otp_trigger_events">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="fraud.accounts.id" foreignKeyName="fk_otp_trigger_events_account_id" foreignKeyIndex="ix_otp_trigger_events_account_id"/>
            <column name="trigger" type="varchar(28)" notnull="true" checkConstraint="check ( trigger in ('signed_up_trigger','bonus_trigger','utm_source_trigger','set_up_notifications_trigger','bonus_acceptance_trigger','account_closure_trigger'))" checkConstraintName="ck_otp_trigger_events_trigger"/>
            <column name="source" type="varchar(18)" checkConstraint="check ( source in ('my_profile','inbox_notification'))" checkConstraintName="ck_otp_trigger_events_source"/>
            <column name="at" type="date" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="fraud.otp_trigger_rules" pkName="pk_otp_trigger_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer"/>
            <column name="fraud_brand_id" type="integer" notnull="true" references="fraud.brands.id" foreignKeyName="fk_otp_trigger_rules_fraud_brand_id" foreignKeyIndex="ix_otp_trigger_rules_fraud_brand_id"/>
            <column name="country" type="varchar(2)"/>
            <column name="bonus_trigger_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="sign_up_fraud_score_trigger_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="utm_source_trigger_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="daily_bonus_accepted_threshold" type="integer" notnull="true"/>
            <column name="sign_up_fraud_score_threshold" type="integer" notnull="true"/>
            <column name="utm_sources" type="varchar[]" notnull="true"/>
            <column name="bonus_acceptance_trigger_threshold" type="integer" notnull="true"/>
            <column name="bonus_acceptance_trigger_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="payment.payment_aggregated_meta_info" pkName="pk_payment_aggregated_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="provider_id" type="integer" references="payment.providers.id" foreignKeyName="fk_payment_aggregated_meta_info_provider_id" foreignKeyIndex="ix_payment_aggregated_meta_info_provider_id"/>
            <column name="country" type="varchar" notnull="true"/>
            <column name="currency" type="varchar" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="pre_confirmed_amount" type="decimal(16,2)"/>
            <column name="purchase_count" type="bigint" notnull="true"/>
            <column name="withdraw_count" type="bigint"/>
            <column name="at" type="date" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_payment_aggregated_meta_info_provider_id_country_curre_1" columnNames="provider_id,country,currency,at" oneToOne="false" nullableColumns="provider_id"/>
        </createTable>
        <createTable name="payment.payment_method_meta_info" pkName="pk_payment_method_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="type" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_payment_method_meta_info_account_id" foreignKeyIndex="ix_payment_method_meta_info_account_id"/>
            <column name="fingerprint" type="varchar" notnull="true"/>
            <column name="card_bin" type="varchar"/>
            <column name="last_four" type="varchar"/>
            <column name="verification_status" type="varchar(21)" notnull="true" checkConstraint="check ( verification_status in ('unverified','require_verification','in_progress','verified','attempt_limit_reached','verification_failed','unverifiable'))" checkConstraintName="ck_payment_method_meta_info_verification_status"/>
            <column name="secure3d_action" type="varchar(5)" checkConstraint="check ( secure3d_action in ('skip','force'))" checkConstraintName="ck_payment_method_meta_info_secure3d_action"/>
            <column name="verification_attempts" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_payment_method_meta_info_account_id_fingerprint" columnNames="account_id,fingerprint" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.payment_method_verification_request" pkName="pk_payment_method_verification_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="scan_reference" type="varchar"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_payment_method_verification_request_account_id" foreignKeyIndex="ix_payment_method_verification_request_account_id"/>
            <column name="meta_info_id" type="bigint" notnull="true" references="payment.payment_method_meta_info.id" foreignKeyName="fk_payment_method_verification_request_meta_info_id" foreignKeyIndex="ix_payment_method_verification_request_meta_info_id"/>
            <column name="status" type="varchar(9)" notnull="true" checkConstraint="check ( status in ('initiated','verified','failed','expired'))" checkConstraintName="ck_payment_method_verification_request_status"/>
            <column name="redirect_url" type="varchar(4000)"/>
            <column name="client_ip" type="varchar"/>
            <column name="timestamp" type="varchar"/>
            <column name="doc_type" type="varchar"/>
            <column name="country" type="varchar"/>
            <column name="images" type="varchar[]"/>
            <column name="extracted_name" type="varchar"/>
            <column name="extracted_pan" type="varchar"/>
            <column name="extracted_issue_date" type="varchar"/>
            <column name="extracted_expiry_date" type="varchar"/>
            <column name="api_error" type="varchar"/>
            <column name="fail_reason" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_payment_method_verification_request_provider_scan_refe_1" columnNames="provider,scan_reference" oneToOne="false" nullableColumns="scan_reference"/>
            <uniqueConstraint name="uq_payment_method_verification_request_provider_scan_refe_2" columnNames="provider,scan_reference" oneToOne="false" nullableColumns="scan_reference"/>
        </createTable>
        <alterColumn columnName="id" tableName="payment.payment_orders" currentType="bigint" notnull="true" currentNotnull="false"/>
        <alterColumn columnName="secure3d" tableName="payment.payment_orders" currentType="boolean" defaultValue="DROP DEFAULT" notnull="false" currentNotnull="true"/>
        <alterColumn columnName="offer_id" tableName="payment.payment_orders" currentType="bigint" notnull="false" currentNotnull="true"/>
        <alterColumn columnName="chargeback_status" tableName="payment.payment_orders" type="varchar(19)" currentType="varchar(13)" currentNotnull="false" checkConstraint="check ( chargeback_status in ('pending','won','lost','chargeback','alert','rdr','chargeback_reversal','partial_match','representment','unknown','bad_debt'))" checkConstraintName="ck_payment_orders_chargeback_status"/>
        <addColumn tableName="payment.payment_orders">
            <column name="provider_id" type="integer" references="payment.providers.id" foreignKeyName="fk_payment_orders_provider_id" foreignKeyIndex="ix_payment_orders_provider_id"/>
            <column name="base_amount" type="decimal(16,2)"/>
            <column name="gc_amount" type="decimal(16,2)"/>
            <column name="sc_amount" type="decimal(16,2)"/>
            <column name="initial_base_amount" type="decimal(16,2)"/>
            <column name="initial_amount" type="decimal(16,2)"/>
            <column name="initial_gc_amount" type="decimal(16,2)"/>
            <column name="initial_sc_amount" type="decimal(16,2)"/>
            <column name="extended_error_code" type="varchar"/>
            <column name="sca_authentication_id" type="bigint" references="payment.sca_authentications.id" foreignKeyName="fk_payment_orders_sca_authentication_id" foreignKeyIndex="ix_payment_orders_sca_authentication_id"/>
            <column name="crypto_purchase_detail_id" type="bigint" references="payment.crypto_purchase_details.id" foreignKeyName="fk_payment_orders_crypto_purchase_detail_id" foreignKeyIndex="ix_payment_orders_crypto_purchase_detail_id"/>
            <column name="refund_error_code" type="varchar"/>
            <column name="refund_error" type="varchar"/>
            <column name="retry_condition_triggered" type="boolean" defaultValue="false" notnull="true"/>
            <column name="original_chargeback_at" type="date"/>
            <column name="tc40_received_at" type="timestamp"/>
            <column name="quick_purchase_supported_type" type="varchar(24)" checkConstraint="check ( quick_purchase_supported_type in ('regular','quick_purchase_get_coins','quick_purchase','quick_deposit'))" checkConstraintName="ck_payment_orders_quick_purchase_supported_type"/>
            <column name="requested_at" type="timestamp"/>
            <column name="reconciled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="reconciled_at" type="date"/>
            <column name="secure3d_reason" type="varchar"/>
            <column name="is_fraud" type="boolean"/>
            <column name="internal_status" type="varchar(7)" notnull="true" checkConstraint="check ( internal_status in ('created','pending','success','failed'))" checkConstraintName="ck_payment_orders_internal_status"/>
            <column name="applied_routing_rules" type="varchar[]"/>
            <column name="refunded_by" type="varchar"/>
            <column name="app_name" type="varchar"/>
            <column name="app_version" type="varchar"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_payment_orders_provider_code" tableName="payment.payment_orders" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_payment_orders_fraud_request_id" tableName="payment.payment_orders" columnNames="DROP CONSTRAINT" nullableColumns="fraud_request_id"/>
        <addUniqueConstraint constraintName="uq_payment_orders_provider_code_at" tableName="payment.payment_orders" columnNames="provider,code,at" oneToOne="false" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_payment_orders_transaction_id_at" tableName="payment.payment_orders" columnNames="transaction_id,at" oneToOne="false" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_payment_orders_order_sn_at" tableName="payment.payment_orders" columnNames="order_sn,at" oneToOne="false" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_payment_orders_id_at" tableName="payment.payment_orders" columnNames="id,at" oneToOne="false" nullableColumns=""/>
        <createTable name="payment.payment_orders_bonus_abuse" pkName="pk_payment_orders_bonus_abuse">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="order_id" type="bigint" notnull="true"/>
            <column name="order_at" type="date" notnull="true"/>
            <column name="reason" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_payment_orders_bonus_abuse_order" columnNames="order_id,order_at" oneToOne="true" nullableColumns=""/>
            <foreignKey name="fk_payment_orders_bonus_abuse_order" columnNames="order_id,order_at" refColumnNames="id,at" refTableName="payment.payment_orders"/>
        </createTable>
        <createTable name="payment.payment_order_meta_info" pkName="pk_payment_order_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="order_id" type="bigint" notnull="true"/>
            <column name="order_at" type="date" notnull="true"/>
            <column name="status" type="varchar(7)" checkConstraint="check ( status in ('created','pending','success','failed'))" checkConstraintName="ck_payment_order_meta_info_status"/>
            <column name="aggregated" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_payment_order_meta_info_order" columnNames="order_id,order_at" oneToOne="true" nullableColumns=""/>
            <foreignKey name="fk_payment_order_meta_info_order" columnNames="order_id,order_at" refColumnNames="id,at" refTableName="payment.payment_orders"/>
        </createTable>
        <createTable name="payment.payment_orders_seq" identityType="sequence" identityIncrement="1" sequenceName="payment.payment_orders_id_seq" pkName="pk_payment_orders_seq">
            <column name="id" type="bigint" primaryKey="true" identity="true"/>
            <column name="start_interval" type="bigint" notnull="true"/>
            <column name="end_interval" type="bigint" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.phone_number_request" withHistory="true" pkName="pk_phone_number_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="phone_number" type="varchar(25)" notnull="true"/>
            <column name="sid" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" references="fraud.accounts.id" foreignKeyName="fk_phone_number_request_account_id" foreignKeyIndex="ix_phone_number_request_account_id"/>
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="last_attempt" type="timestamp" notnull="true"/>
            <column name="carrier_name" type="varchar"/>
            <column name="country_code" type="varchar"/>
            <column name="network_code" type="varchar"/>
            <column name="type" type="varchar(12)" checkConstraint="check ( type in ('landline','mobile','fixedVoip','nonFixedVoip','personal','tollFree','premium','sharedCost','uan','voicemail','pager','unknown'))" checkConstraintName="ck_phone_number_request_type"/>
            <column name="error_code" type="varchar"/>
            <column name="error_reason" type="varchar(28)" checkConstraint="check ( error_reason in ('duplicate_phone_number_error','otp_not_entered_error','invalid_phone_number_error','country_is_not_matched_error','incorrect_otp_error'))" checkConstraintName="ck_phone_number_request_error_reason"/>
            <column name="source" type="varchar(18)" checkConstraint="check ( source in ('my_profile','inbox_notification'))" checkConstraintName="ck_phone_number_request_source"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_phone_number_request_account_id_sid_phone_number" columnNames="account_id,sid,phone_number" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <createTable name="worker.postback_filters" pkName="pk_postback_filters">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_postback_filters_account_id" foreignKeyIndex="ix_postback_filters_account_id"/>
            <column name="postback_type" type="varchar(14)" notnull="true" checkConstraint="check ( postback_type in ('appsflyer','bankrolls','bloomreach','box','clickhouse','converttologic','facebook','flows','graphyte','gtm','intelitics','moonshoot','pinterest','prodege','rapid-client','reddit','sendgrid','sidelines','snapchat','tiktok','twitter','voluum','xe-rate'))" checkConstraintName="ck_postback_filters_postback_type"/>
            <column name="event_name" type="varchar" notnull="true"/>
            <column name="jsonpath_query" type="varchar(4000)"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_postback_filters_account_id_postback_type_event_name" columnNames="account_id,postback_type,event_name" oneToOne="false" nullableColumns=""/>
        </createTable>
        <alterColumn columnName="style" tableName="core.product_categories" type="varchar(10)" currentType="varchar(7)" currentNotnull="true" checkConstraint="check ( style in ('small','large','heading','highlights','default'))" checkConstraintName="ck_product_categories_style"/>
        <alterColumn columnName="type" tableName="core.product_categories" type="varchar(11)" currentType="varchar(9)" currentNotnull="true" checkConstraint="check ( type in ('jackpot','providers','bingo','ref-frnd','highlights','big-wins','top-wins','default','themes','gameotweek','tournaments','banner'))" checkConstraintName="ck_product_categories_type"/>
        <addColumn tableName="core.product_categories">
            <column name="link_to_promotion" type="varchar"/>
        </addColumn>
        <createTable name="uam.product_category_rule" identityType="identity" pkName="pk_product_category_rule">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="category_id" type="bigint" notnull="true" references="core.product_categories.id" foreignKeyName="fk_product_category_rule_category_id" foreignKeyIndex="ix_product_category_rule_category_id"/>
            <column name="product_id" type="integer" notnull="true" references="core.products.id" foreignKeyName="fk_product_category_rule_product_id" foreignKeyIndex="ix_product_category_rule_product_id"/>
            <column name="rule_type" type="varchar(7)" notnull="true" checkConstraint="check ( rule_type in ('INCLUDE','EXCLUDE','PINNED'))" checkConstraintName="ck_product_category_rule_rule_type"/>
            <column name="pinned_rank" type="integer"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="core.product_groups" identityType="identity" pkName="pk_product_groups">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="is_active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="game_lobby_feature_setting_id" type="bigint" notnull="true" references="uam.feature_settings_game_lobby.id" foreignKeyName="fk_product_groups_game_lobby_feature_setting_id" foreignKeyIndex="ix_product_groups_game_lobby_feature_setting_id"/>
            <column name="initial_product_mode" type="varchar(15)" notnull="true" checkConstraint="check ( initial_product_mode in ('gold','sweepstake','fiat','gold_sweepstake','gold_fiat','free'))" checkConstraintName="ck_product_groups_initial_product_mode"/>
            <column name="mode_change_frequency" type="bigint" notnull="true"/>
            <column name="start_date_time" type="localdatetime" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="updated_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="core.product_group_product" pkName="pk_product_group_product">
            <column name="product_group_id" type="integer" notnull="true" primaryKey="true"/>
            <column name="product_id" type="integer" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_product_group_product_product_groups" columnNames="product_group_id" refColumnNames="id" refTableName="core.product_groups" indexName="ix_product_group_product_product_groups"/>
            <foreignKey name="fk_product_group_product_products" columnNames="product_id" refColumnNames="id" refTableName="core.products" indexName="ix_product_group_product_products"/>
        </createTable>
        <createTable name="uam.promotions" pkName="pk_promotions">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="description" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_promotions_brand_id" foreignKeyIndex="ix_promotions_brand_id"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="start_date" type="timestamp"/>
            <column name="expire_date" type="timestamp"/>
            <column name="url" type="varchar(4000)" notnull="true"/>
            <column name="image" type="varchar(4000)"/>
            <column name="tag" type="varchar"/>
            <column name="tag2" type="varchar"/>
            <column name="important" type="boolean" defaultValue="false" notnull="true"/>
            <column name="shimmer" type="boolean" defaultValue="false" notnull="true"/>
            <column name="style" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_promotions_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <alterColumn columnName="description" tableName="payment.providers" currentType="varchar" notnull="true" currentNotnull="false"/>
        <addColumn tableName="payment.providers">
            <column name="exclude_in_same_tokenization" type="boolean" defaultValue="false" notnull="true"/>
            <column name="payment_mode" type="varchar(25)" notnull="true" checkConstraint="check ( payment_mode in ('card','skrill','wire_transfer','virtual_wallet_apple_pay','virtual_wallet_google_pay','non_monetary','in_app_purchase','crypto'))" checkConstraintName="ck_providers_payment_mode"/>
            <column name="integration_type" type="varchar(21)" notnull="true" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_providers_integration_type"/>
            <column name="countries" type="varchar[]"/>
            <column name="billing_descriptor" type="varchar"/>
            <column name="skip_first_order" type="boolean" defaultValue="false" notnull="true"/>
            <column name="rtp_enabled" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_providers_brand_id_code_type" tableName="payment.providers" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addUniqueConstraint constraintName="uq_providers_brand_id_code_type_integration_type" tableName="payment.providers" columnNames="brand_id,code,type,integration_type" oneToOne="false" nullableColumns=""/>
        <createTable name="payment.provider_redeem_limit_policies" pkName="pk_provider_redeem_limit_policies">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_provider_redeem_limit_policies_brand_id" foreignKeyIndex="ix_provider_redeem_limit_policies_brand_id"/>
            <column name="integration_type" type="varchar(21)" notnull="true" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_provider_redeem_limit_policies_integration_type"/>
            <column name="currency" type="varchar" notnull="true"/>
            <column name="max" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_provider_redeem_limit_policies_brand_id_integration_ty_1" columnNames="brand_id,integration_type,currency" oneToOne="false" nullableColumns="brand_id"/>
        </createTable>
        <createTable name="payment.purchase_track_event" pkName="pk_purchase_track_event">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_purchase_track_event_account_id" foreignKeyIndex="ix_purchase_track_event_account_id"/>
            <column name="code" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_purchase_track_event_account_id_code" columnNames="account_id,code" oneToOne="false" nullableColumns="code"/>
        </createTable>
        <createTable name="reward.random_reward_brand_mini_game_type" pkName="pk_random_reward_brand_mini_game_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_random_reward_brand_mini_game_type_brand_id" foreignKeyIndex="ix_random_reward_brand_mini_game_type_brand_id"/>
            <column name="mini_game_type_id" type="bigint" notnull="true" references="reward.random_reward_mini_game_type.id" foreignKeyName="fk_random_reward_brand_mini_game_type_mini_game_type_id" foreignKeyIndex="ix_random_reward_brand_mini_game_type_mini_game_type_id"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_brand_mini_game_type_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_brand_placement_type" pkName="pk_random_reward_brand_placement_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_random_reward_brand_placement_type_brand_id" foreignKeyIndex="ix_random_reward_brand_placement_type_brand_id"/>
            <column name="placement_type_id" type="bigint" notnull="true" references="reward.random_reward_placement_type.id" foreignKeyName="fk_random_reward_brand_placement_type_placement_type_id" foreignKeyIndex="ix_random_reward_brand_placement_type_placement_type_id"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_brand_placement_type_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_instance" withHistory="true" pkName="pk_random_reward_instance">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_instance_code"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_random_reward_instance_brand_id" foreignKeyIndex="ix_random_reward_instance_brand_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_random_reward_instance_account_id" foreignKeyIndex="ix_random_reward_instance_account_id"/>
            <column name="reward_template_id" type="bigint" notnull="true" references="reward.random_reward_template.id" foreignKeyName="fk_random_reward_instance_reward_template_id" foreignKeyIndex="ix_random_reward_instance_reward_template_id"/>
            <column name="status" type="varchar(9)" notnull="true" checkConstraint="check ( status in ('assigned','completed','expired','retracted'))" checkConstraintName="ck_random_reward_instance_status"/>
            <column name="expires_at" type="timestamp" notnull="true"/>
            <column name="snapshot" type="jsonb"/>
            <column name="request_id" type="varchar" notnull="true" unique="uq_random_reward_instance_request_id"/>
            <column name="source" type="varchar(13)" checkConstraint="check ( source in ('backoffice','bloomreach','payment_offer','offer_chain','quests'))" checkConstraintName="ck_random_reward_instance_source"/>
            <column name="source_reference" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_instance_offer_template_code" pkName="pk_random_reward_instance_offer_template_code">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="instance_id" type="bigint" notnull="true" references="reward.random_reward_instance.id" foreignKeyName="fk_random_reward_instance_offer_template_code_instance_id" foreignKeyIndex="ix_random_reward_instance_offer_template_code_instance_id"/>
            <column name="offer_template_code" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_mini_game_theme" pkName="pk_random_reward_mini_game_theme">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar(128)" notnull="true"/>
            <column name="feature_key" type="varchar(128)" notnull="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_mini_game_theme_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_mini_game_type" pkName="pk_random_reward_mini_game_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar(128)" notnull="true"/>
            <column name="feature_key" type="varchar(128)" notnull="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_mini_game_type_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_placement_type" pkName="pk_random_reward_placement_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar(128)" notnull="true"/>
            <column name="feature_key" type="varchar(128)" notnull="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_placement_type_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_prize_table" pkName="pk_random_reward_prize_table">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar(128)" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_random_reward_prize_table_brand_id" foreignKeyIndex="ix_random_reward_prize_table_brand_id"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_prize_table_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_prize_table_item" pkName="pk_random_reward_prize_table_item">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="prize_table_id" type="bigint" notnull="true" references="reward.random_reward_prize_table.id" foreignKeyName="fk_random_reward_prize_table_item_prize_table_id" foreignKeyIndex="ix_random_reward_prize_table_item_prize_table_id"/>
            <column name="name" type="varchar(128)" notnull="true"/>
            <column name="rank" type="integer" notnull="true"/>
            <column name="probability" type="decimal" notnull="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_prize_table_item_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_random_reward_prize_table_item_prize_table_id_rank" columnNames="prize_table_id,rank" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="reward.random_reward_prize_table_prize_item" pkName="pk_random_reward_prize_table_prize_item">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="prize_table_item_id" type="bigint" notnull="true" references="reward.random_reward_prize_table_item.id" foreignKeyName="fk_random_reward_prize_table_prize_item_prize_table_item_id" foreignKeyIndex="ix_random_reward_prize_table_prize_item_prize_table_item_id"/>
            <column name="prize_type_id" type="bigint" notnull="true" references="reward.random_reward_prize_type.id" foreignKeyName="fk_random_reward_prize_table_prize_item_prize_type_id" foreignKeyIndex="ix_random_reward_prize_table_prize_item_prize_type_id"/>
            <column name="data" type="jsonb"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_prize_table_prize_item_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_random_reward_prize_table_prize_item_prize_table_item__1" columnNames="prize_table_item_id,prize_type_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="reward.random_reward_prize_type" pkName="pk_random_reward_prize_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="name" type="varchar(128)" notnull="true"/>
            <column name="feature_key" type="varchar(128)" notnull="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_prize_type_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_template" pkName="pk_random_reward_template">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_random_reward_template_brand_id" foreignKeyIndex="ix_random_reward_template_brand_id"/>
            <column name="name" type="varchar(128)" notnull="true" unique="uq_random_reward_template_name"/>
            <column name="display_name" type="varchar(128)" notnull="true"/>
            <column name="display_name_short" type="varchar(128)" notnull="true"/>
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="test" type="boolean" defaultValue="false" notnull="true"/>
            <column name="available" type="boolean" defaultValue="false" notnull="true"/>
            <column name="prize_table_id" type="bigint" notnull="true" references="reward.random_reward_prize_table.id" foreignKeyName="fk_random_reward_template_prize_table_id" foreignKeyIndex="ix_random_reward_template_prize_table_id"/>
            <column name="mini_game_type_id" type="bigint" notnull="true" references="reward.random_reward_mini_game_type.id" foreignKeyName="fk_random_reward_template_mini_game_type_id" foreignKeyIndex="ix_random_reward_template_mini_game_type_id"/>
            <column name="mini_game_theme_id" type="bigint" references="reward.random_reward_mini_game_theme.id" foreignKeyName="fk_random_reward_template_mini_game_theme_id" foreignKeyIndex="ix_random_reward_template_mini_game_theme_id"/>
            <column name="icon_small_closed" type="varchar(2048)"/>
            <column name="icon_large_closed" type="varchar(2048)"/>
            <column name="icon_small_open" type="varchar(2048)"/>
            <column name="icon_large_open" type="varchar(2048)"/>
            <column name="shop_icon_small" type="varchar(2048)"/>
            <column name="shop_icon_large" type="varchar(2048)"/>
            <column name="time_limited" type="boolean" defaultValue="false" notnull="true"/>
            <column name="available_from" type="timestamp"/>
            <column name="available_to" type="timestamp"/>
            <column name="expires_after_days" type="integer" notnull="true"/>
            <column name="limit_per_day" type="bigint"/>
            <column name="issued_per_day" type="bigint"/>
            <column name="limit_total" type="bigint"/>
            <column name="issued_total" type="bigint"/>
            <column name="term_url" type="varchar(2048)"/>
            <column name="display_tagline" type="varchar(256)"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_template_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.random_reward_template_placement_type" pkName="pk_random_reward_template_placement_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="reward_template_id" type="bigint" notnull="true" references="reward.random_reward_template.id" foreignKeyName="fk_random_reward_template_placement_type_reward_template_id" foreignKeyIndex="ix_random_reward_template_placement_type_reward_template_id"/>
            <column name="placement_type_id" type="bigint" notnull="true" references="reward.random_reward_placement_type.id" foreignKeyName="fk_random_reward_template_placement_type_placement_type_id" foreignKeyIndex="ix_random_reward_template_placement_type_placement_type_id"/>
            <column name="code" type="uuid" notnull="true" unique="uq_random_reward_template_placement_type_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.refund_history" pkName="pk_refund_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="order_id" type="bigint"/>
            <column name="order_at" type="date"/>
            <column name="success" type="boolean" defaultValue="false" notnull="true"/>
            <column name="refund_error" type="varchar"/>
            <column name="refund_error_code" type="varchar"/>
            <column name="at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <foreignKey name="fk_refund_history_order" columnNames="order_id,order_at" refColumnNames="id,at" refTableName="payment.payment_orders"/>
        </createTable>
        <createTable name="fraud.report_log" pkName="pk_report_log">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="report_type" type="varchar(27)" notnull="true" checkConstraint="check ( report_type in ('worldpay_chargebacks','worldpay_reconciliation','worldpay_tc40','paynearme_tc40','fiserv_chargebacks','fiserv_tc40','emerchantpay_reconciliation','fiserv_arn','rapyd_tc40','checkout_tc40','rapyd_reconciliation'))" checkConstraintName="ck_report_log_report_type"/>
            <column name="report_name" type="varchar" notnull="true"/>
            <column name="report_identifier" type="varchar" notnull="true"/>
            <column name="processed" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_report_log_report_type_report_identifier" columnNames="report_type,report_identifier" oneToOne="false" nullableColumns=""/>
            <uniqueConstraint name="uq_report_log_report_type_report_name" columnNames="report_type,report_name" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.rescue_purchase_providers_settings" pkName="pk_rescue_purchase_providers_settings">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_rescue_purchase_providers_settings_brand_id" foreignKeyIndex="ix_rescue_purchase_providers_settings_brand_id"/>
            <column name="payment_method_type" type="varchar(24)" notnull="true" checkConstraint="check ( payment_method_type in ('SpreedlyGateway','SpreedlyGatewayApplePay','SpreedlyGatewayGooglePay','PayWithMyBank','Trustly','FiservCard','FiservGooglePay','FiservApplePay','NuveiGateway','Skrill','AppleInApp','AndroidInApp','Payper','Crypto','AeroPay'))" checkConstraintName="ck_rescue_purchase_providers_settings_payment_method_type"/>
            <column name="exclude_error_codes" type="varchar[]" notnull="true"/>
            <column name="exclude_internal_error_codes" type="varchar[]" notnull="true"/>
            <column name="countries" type="varchar[]" notnull="true"/>
            <column name="rescue_providers" type="varchar[]" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <alterColumn columnName="source_system" tableName="uam.reward_creditors" type="varchar(13)" currentType="varchar(10)" currentNotnull="true" checkConstraint="check ( source_system in ('ysi','b2','bloomreach','silver_social','rum','pmsg','fullstop','pca'))" checkConstraintName="ck_reward_creditors_source_system"/>
        <createTable name="payment.routing_error_config" withHistory="true" pkName="pk_routing_error_config">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="error_code" type="varchar" notnull="true"/>
            <column name="description" type="varchar"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_routing_error_config_brand_id" foreignKeyIndex="ix_routing_error_config_brand_id"/>
            <column name="error_type" type="varchar(15)" notnull="true" checkConstraint="check ( error_type in ('card_network','seon_rule','provider_custom','http_code'))" checkConstraintName="ck_routing_error_config_error_type"/>
            <column name="provider_type" type="varchar(19)" checkConstraint="check ( provider_type in ('spreedly','spreedly_apple_pay','spreedly_google_pay'))" checkConstraintName="ck_routing_error_config_provider_type"/>
            <column name="integration_type" type="varchar(21)" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_routing_error_config_integration_type"/>
            <column name="provider_id" type="integer" references="payment.providers.id" foreignKeyName="fk_routing_error_config_provider_id" foreignKeyIndex="ix_routing_error_config_provider_id"/>
            <column name="countries" type="varchar[]"/>
            <column name="fail_policy" type="varchar(16)" notnull="true" checkConstraint="check ( fail_policy in ('merchant_id','integration_type'))" checkConstraintName="ck_routing_error_config_fail_policy"/>
            <column name="retry_condition" type="varchar"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.routing_provider_ach_token" pkName="pk_routing_provider_ach_token">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="provider_id" type="integer" notnull="true" references="payment.providers.id" foreignKeyName="fk_routing_provider_ach_token_provider_id" foreignKeyIndex="ix_routing_provider_ach_token_provider_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_routing_provider_ach_token_account_id" foreignKeyIndex="ix_routing_provider_ach_token_account_id"/>
            <column name="token" type="varchar" notnull="true"/>
            <column name="remember" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.routing_rules" pkName="pk_routing_rules">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="rule_type" type="varchar(19)" notnull="true" checkConstraint="check ( rule_type in ('exclude','stick','stick_with_fallback'))" checkConstraintName="ck_routing_rules_rule_type"/>
            <column name="routing_type" type="varchar(19)" notnull="true" checkConstraint="check ( routing_type in ('spreedly','spreedly_apple_pay','spreedly_google_pay'))" checkConstraintName="ck_routing_rules_routing_type"/>
            <column name="priority" type="integer" notnull="true"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_routing_rules_brand_id" foreignKeyIndex="ix_routing_rules_brand_id"/>
            <column name="countries" type="varchar[]"/>
            <column name="currency" type="varchar"/>
            <column name="integration_types" type="varchar[]"/>
            <column name="rule_id" type="varchar"/>
            <column name="condition" type="varchar(4000)"/>
            <column name="description" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="payment.routing_rule_providers" pkName="pk_routing_rule_providers">
            <column name="rule_id" type="integer" notnull="true" primaryKey="true"/>
            <column name="provider_id" type="integer" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_routing_rule_providers_routing_rules" columnNames="rule_id" refColumnNames="id" refTableName="payment.routing_rules" indexName="ix_routing_rule_providers_routing_rules"/>
            <foreignKey name="fk_routing_rule_providers_providers" columnNames="provider_id" refColumnNames="id" refTableName="payment.providers" indexName="ix_routing_rule_providers_providers"/>
        </createTable>
        <createTable name="payment.sca_authentications" pkName="pk_sca_authentications">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="success" type="boolean" defaultValue="false" notnull="true"/>
            <column name="state" type="varchar(9)" notnull="true" checkConstraint="check ( state in ('created','failed','succeeded','pending','error'))" checkConstraintName="ck_sca_authentications_state"/>
            <column name="token" type="varchar"/>
            <column name="error" type="varchar"/>
            <column name="message" type="varchar"/>
            <column name="provider_type_spec" type="varchar(25)" notnull="true" checkConstraint="check ( provider_type_spec in ('spreedly_sca_emerchantpay','spreedly_sca_rapyd','spreedly_sca_fiserv','spreedly_sca_worldpay','spreedly_sca_test'))" checkConstraintName="ck_sca_authentications_provider_type_spec"/>
            <column name="exemption_type" type="varchar(50)" checkConstraint="check ( exemption_type in ('low_value_payment','transaction_risk_analysis'))" checkConstraintName="ck_sca_authentications_exemption_type"/>
            <column name="flow_performed" type="varchar"/>
            <column name="ecommerce_indicator" type="varchar"/>
            <column name="authentication_value" type="varchar"/>
            <column name="directory_server_transaction_id" type="varchar"/>
            <column name="acs_transaction_id" type="varchar"/>
            <column name="three_ds_requestor_challenge_ind" type="varchar"/>
            <column name="directory_response_status" type="varchar"/>
            <column name="authentication_response_status" type="varchar"/>
            <column name="enrolled" type="varchar"/>
            <column name="xid" type="varchar"/>
            <column name="response_code" type="varchar"/>
            <column name="used" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.social_media_rewards" pkName="pk_social_media_rewards">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true" unique="uq_social_media_rewards_code"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_social_media_rewards_brand_id" foreignKeyIndex="ix_social_media_rewards_brand_id"/>
            <column name="social_media" type="varchar" notnull="true"/>
            <column name="gc_amount" type="decimal(16,2)"/>
            <column name="sc_amount" type="decimal(16,2)"/>
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="claimed_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_social_media_rewards_claimed_by_id" foreignKeyIndex="ix_social_media_rewards_claimed_by_id"/>
            <column name="claimed_at" type="timestamp"/>
            <column name="expired_at" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="game_hub.supplier_bet_limits" pkName="pk_supplier_bet_limits">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="min_bet" type="decimal(16,2)"/>
            <column name="default_bet" type="decimal(16,2)"/>
            <column name="max_bet_limit" type="decimal(16,2)"/>
            <column name="max_win_limit" type="decimal(16,2)"/>
            <column name="supplier_id" type="integer" notnull="true" references="core.suppliers.id" foreignKeyName="fk_supplier_bet_limits_supplier_id" foreignKeyIndex="ix_supplier_bet_limits_supplier_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_supplier_bet_limits_supplier_id_currency" columnNames="supplier_id,currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <alterColumn columnName="game_mode" tableName="uam.temporary_tokens" checkConstraint="check ( game_mode in ('default','mini','multi'))" checkConstraintName="ck_temporary_tokens_game_mode"/>
        <addColumn tableName="uam.temporary_tokens">
            <column name="product_id" type="integer" references="core.products.id" foreignKeyName="fk_temporary_tokens_product_id" foreignKeyIndex="ix_temporary_tokens_product_id"/>
            <column name="platform" type="varchar(7)" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_temporary_tokens_platform"/>
        </addColumn>
        <createTable name="uam.themes" pkName="pk_themes">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_themes_brand_id" foreignKeyIndex="ix_themes_brand_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="name" type="varchar"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="rank" type="integer" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_themes_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.theme_products" pkName="pk_theme_products">
            <column name="theme_id" type="integer" notnull="true" primaryKey="true"/>
            <column name="product_id" type="integer" notnull="true" primaryKey="true"/>
            <foreignKey name="fk_theme_products_themes" columnNames="theme_id" refColumnNames="id" refTableName="uam.themes" indexName="ix_theme_products_themes"/>
            <foreignKey name="fk_theme_products_products" columnNames="product_id" refColumnNames="id" refTableName="core.products" indexName="ix_theme_products_products"/>
        </createTable>
        <createTable name="payment.transaction_limit" pkName="pk_transaction_limit">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="type" type="varchar(24)" notnull="true" checkConstraint="check ( type in ('single_redeem','single_deposit','total_deposit_before_kyc','total_deposit_daily'))" checkConstraintName="ck_transaction_limit_type"/>
            <column name="brand_id" type="integer" references="core.brands.id" foreignKeyName="fk_transaction_limit_brand_id" foreignKeyIndex="ix_transaction_limit_brand_id"/>
            <column name="countries" type="varchar[]"/>
            <column name="currency" type="varchar" notnull="true"/>
            <column name="min" type="decimal(16,2)"/>
            <column name="max" type="decimal(16,2)"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.vip_level_ggr_templates" pkName="pk_vip_level_ggr_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_vip_level_ggr_templates_brand_id" foreignKeyIndex="ix_vip_level_ggr_templates_brand_id"/>
            <column name="from_total" type="decimal(16,2)"/>
            <column name="to_total" type="decimal(16,2)"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_vip_level_ggr_templates_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.vip_level_points_changes" pkName="pk_vip_level_points_changes">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_vip_level_points_changes_account_id" foreignKeyIndex="ix_vip_level_points_changes_account_id"/>
            <column name="points" type="decimal(16,2)" notnull="true"/>
            <column name="transaction_id" type="varchar(36)" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_vip_level_points_changes_account_id_points_transaction_1" columnNames="account_id,points,transaction_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <addColumn tableName="payment.vip_level_purchase_templates">
            <column name="first2_days_total" type="decimal(16,2)"/>
            <column name="first3_days_total" type="decimal(16,2)"/>
            <column name="first14_days_total" type="decimal(16,2)"/>
            <column name="last30_days_total" type="decimal(16,2)"/>
            <column name="last60_days_total" type="decimal(16,2)"/>
        </addColumn>
        <createTable name="payment.volume_allocation_config" withHistory="true" pkName="pk_volume_allocation_config">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_volume_allocation_config_brand_id" foreignKeyIndex="ix_volume_allocation_config_brand_id"/>
            <column name="country" type="varchar" notnull="true"/>
            <column name="currency" type="varchar"/>
            <column name="integration_type" type="varchar(21)" checkConstraint="check ( integration_type in ('spreedly','skrill','skrill_ach','ach','sci','pay_with_my_bank','nuvei_mazooma_ach','fiserv','spreedly_rapyd','spreedly_fiserv','spreedly_emerchantpay','spreedly_worldpay','spreedly_checkout','spreedly_nuvei','spreedly_paynearme','apple_in_app','android_in_app','prizeout','masspay_ach','trustly','payper','standard_ach','airwallex_ach','aeropay_ach','checkbook_ach','nsc','crypto','orbital','paynearme_ach','aeropay'))" checkConstraintName="ck_volume_allocation_config_integration_type"/>
            <column name="provider" type="varchar(34)" checkConstraint="check ( provider in ('skrill','trustly','nuvei_mazooma_ach','fiserv','spreedly','spreedly_rapyd','spreedly_rapyd_2','spreedly_rapyd_3','spreedly_rapyd_4','spreedly_rapyd_5','spreedly_fiserv','spreedly_fiserv_2','spreedly_fiserv_3','spreedly_fiserv_4','spreedly_fiserv_5','spreedly_fiserv_6','spreedly_emerchantpay','spreedly_emerchantpay_2','spreedly_emerchantpay_3','spreedly_emerchantpay_4','spreedly_emerchantpay_5','spreedly_emerchantpay_6','fiserv_google_pay','fiserv_apple_pay','spreedly_apple_pay','spreedly_fiserv_apple_pay','spreedly_fiserv_apple_pay_2','spreedly_fiserv_apple_pay_3','spreedly_fiserv_apple_pay_4','spreedly_fiserv_apple_pay_5','spreedly_fiserv_apple_pay_6','spreedly_google_pay','spreedly_fiserv_google_pay','spreedly_fiserv_google_pay_2','spreedly_fiserv_google_pay_3','spreedly_fiserv_google_pay_4','spreedly_fiserv_google_pay_5','spreedly_fiserv_google_pay_6','spreedly_test_gateway','spreedly_test_gateway_2','spreedly_test_gateway_3','spreedly_worldpay','spreedly_checkout','spreedly_checkout_apple_pay','spreedly_checkout_google_pay','spreedly_worldpay_2','spreedly_worldpay_3','spreedly_worldpay_4','spreedly_worldpay_apple_pay','spreedly_worldpay_apple_pay_2','spreedly_worldpay_apple_pay_3','spreedly_worldpay_apple_pay_4','spreedly_worldpay_google_pay','spreedly_worldpay_google_pay_2','spreedly_worldpay_google_pay_3','spreedly_worldpay_google_pay_4','spreedly_emerchantpay_apple_pay','spreedly_emerchantpay_apple_pay_2','spreedly_emerchantpay_apple_pay_3','spreedly_emerchantpay_apple_pay_4','spreedly_emerchantpay_apple_pay_5','spreedly_emerchantpay_apple_pay_6','spreedly_emerchantpay_google_pay','spreedly_emerchantpay_google_pay_2','spreedly_emerchantpay_google_pay_3','spreedly_emerchantpay_google_pay_4','spreedly_emerchantpay_google_pay_5','spreedly_emerchantpay_google_pay_6','spreedly_nuvei','spreedly_nuvei_2','spreedly_paynearme','spreedly_paynearme_2','spreedly_paynearme_apple_pay','spreedly_paynearme_apple_pay_2','spreedly_paynearme_google_pay','spreedly_paynearme_google_pay_2','payper','apple_in_app','android_in_app','crypto','orbital','aeropay'))" checkConstraintName="ck_volume_allocation_config_provider"/>
            <column name="payment_mode" type="varchar(25)" notnull="true" checkConstraint="check ( payment_mode in ('card','skrill','wire_transfer','virtual_wallet_apple_pay','virtual_wallet_google_pay','non_monetary','in_app_purchase','crypto'))" checkConstraintName="ck_volume_allocation_config_payment_mode"/>
            <column name="allocation" type="integer" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_volume_allocation_config_brand_id_country_integration__1" columnNames="brand_id,country,integration_type,currency,payment_mode" oneToOne="false" nullableColumns="integration_type,currency"/>
            <uniqueConstraint name="uq_volume_allocation_config_brand_id_country_provider_cur_2" columnNames="brand_id,country,provider,currency,payment_mode" oneToOne="false" nullableColumns="provider,currency"/>
        </createTable>
        <alterColumn columnName="type" tableName="uam.wallet_sessions" checkConstraint="check ( type in ('payment','withdraw','lotto','bingo','game','mini_game','internal','clean_up','gold_mine_jackpot','my_stash_jackpot','jackpot','cashback','quizbeat','randomizer','multi_play_game','play_together','tournament','chargeback','unknown'))" checkConstraintName="ck_wallet_sessions_type"/>
        <addColumn tableName="uam.wallet_sessions">
            <column name="external_session_id" type="varchar(512)"/>
            <column name="bonus_balance" type="decimal(16,2)"/>
            <column name="platform" type="varchar(7)" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_wallet_sessions_platform"/>
        </addColumn>
        <createTable name="payment.weekly_personalized_offer_aggregation_history" pkName="pk_weekly_personalized_offer_aggregation_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_weekly_personalized_offer_aggregation_history_account_id" foreignKeyIndex="ix_weekly_personalized_offer_aggregation_history_account_id"/>
            <column name="start_at" type="date" notnull="true"/>
            <column name="end_at" type="date" notnull="true"/>
            <column name="total_weekly_wagered_gold" type="decimal(16,2)" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_weekly_personalized_offer_aggregation_history_account__1" columnNames="account_id,start_at,end_at" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.wheel_of_winners" pkName="pk_wheel_of_winners">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_wheel_of_winners_brand_id" foreignKeyIndex="ix_wheel_of_winners_brand_id"/>
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="cooldown_hours" type="integer" notnull="true"/>
            <column name="segment" type="varchar"/>
            <column name="segment_tags" type="varchar[]" notnull="true"/>
            <column name="exclude_segment" type="varchar"/>
            <column name="exclude_segment_tags" type="varchar[]" notnull="true"/>
            <column name="end_date" type="timestamp"/>
            <uniqueConstraint name="uq_wheel_of_winners_brand_id_code" columnNames="brand_id,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.wheel_of_winners_sections" pkName="pk_wheel_of_winners_sections">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="wheel_of_winners_id" type="bigint" notnull="true" references="uam.wheel_of_winners.id" foreignKeyName="fk_wheel_of_winners_sections_wheel_of_winners_id" foreignKeyIndex="ix_wheel_of_winners_sections_wheel_of_winners_id"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="label" type="varchar"/>
            <column name="gc_amount" type="decimal(16,2)"/>
            <column name="sc_amount" type="decimal(16,2)"/>
            <column name="chance" type="decimal(10,7)"/>
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="type_id" type="bigint" references="uam.wheel_of_winners_sections_type.id" foreignKeyName="fk_wheel_of_winners_sections_type_id" foreignKeyIndex="ix_wheel_of_winners_sections_type_id"/>
            <column name="free_spins_main_campaign_code" type="varchar"/>
            <column name="free_spins_fallback_campaign_code" type="varchar"/>
            <column name="prize_draw_entries" type="integer"/>
            <column name="purchase_offer_code" type="varchar"/>
            <column name="purchase_offer_count" type="integer"/>
            <column name="purchase_offer_discount" type="integer"/>
            <column name="data" type="jsonb"/>
        </createTable>
        <createTable name="uam.wheel_of_winners_sections_type" pkName="pk_wheel_of_winners_sections_type">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="label" type="varchar(128)" notnull="true"/>
            <column name="type" type="varchar(128)" notnull="true" checkConstraint="check ( type in ('gold_sweepstake_coins','free_spins','purchase_offer','prize_draw'))" checkConstraintName="ck_wheel_of_winners_sections_type_type" unique="uq_wheel_of_winners_sections_type_type"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <addHistoryTable baseTable="payment.withdraw_money_requests"/>
        <alterColumn columnName="status" tableName="payment.withdraw_money_requests" checkConstraint="check ( status in ('failed','new','pre_authorized','confirmed','declined','cancelled','locked'))" checkConstraintName="ck_withdraw_money_requests_status"/>
        <alterColumn columnName="uam_status" tableName="payment.withdraw_money_requests" checkConstraint="check ( uam_status in ('failed','new','pre_authorized','confirmed','declined','cancelled','locked'))" checkConstraintName="ck_withdraw_money_requests_uam_status"/>
        <addColumn tableName="payment.withdraw_money_requests" withHistory="true">
            <column name="provider_id" type="integer" references="payment.providers.id" foreignKeyName="fk_withdraw_money_requests_provider_id" foreignKeyIndex="ix_withdraw_money_requests_provider_id"/>
            <column name="local_currency" type="varchar(3)"/>
            <column name="base_amount" type="decimal(16,2)"/>
            <column name="locked_by_agent" type="varchar"/>
            <column name="remote_ip" type="varchar(45)"/>
            <column name="modified_by" type="varchar"/>
            <column name="risk_status_modified_by" type="varchar"/>
            <column name="modified_at_date" type="date"/>
            <column name="crypto_withdrawal_detail_id" type="bigint" references="payment.crypto_withdrawal_details.id" foreignKeyName="fk_withdraw_money_requests_crypto_withdrawal_detail_id" foreignKeyIndex="ix_withdraw_money_requests_crypto_withdrawal_detail_id"/>
            <column name="requested_at" type="timestamp"/>
            <column name="risk_status" type="varchar(12)" checkConstraint="check ( risk_status in ('approved','declined','payment_hold','hold','in_review','bi_approved','bi_declined'))" checkConstraintName="ck_withdraw_money_requests_risk_status"/>
            <column name="risk_status_approved_at" type="timestamp"/>
            <column name="comments" type="varchar"/>
            <column name="rtp_flag" type="boolean" defaultValue="false" notnull="true"/>
            <column name="processing_start_at" type="timestamp"/>
            <column name="automation_decline_reason" type="varchar"/>
            <column name="state" type="varchar"/>
            <column name="manual_intervention_needed" type="boolean" defaultValue="false" notnull="true"/>
            <column name="platform" type="varchar(7)" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_withdraw_money_requests_platform"/>
            <column name="app_name" type="varchar"/>
            <column name="app_version" type="varchar"/>
        </addColumn>
        <createTable name="payment.withdraw_money_request_meta_info" pkName="pk_withdraw_money_request_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="withdraw_id" type="bigint" notnull="true" uniqueOneToOne="uq_withdraw_money_request_meta_info_withdraw_id" references="payment.withdraw_money_requests.id" foreignKeyName="fk_withdraw_money_request_meta_info_withdraw_id"/>
            <column name="confirmed_at" type="timestamp"/>
            <column name="aggregated" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
        <createTable name="fraud.zendesk_export_info" pkName="pk_zendesk_export_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" uniqueOneToOne="uq_zendesk_export_info_account_id" references="fraud.accounts.id" foreignKeyName="fk_zendesk_export_info_account_id"/>
            <column name="total_purchase" type="decimal(16,2)"/>
            <column name="total_withdraw" type="decimal(16,2)"/>
            <column name="is_updated" type="boolean" defaultValue="false" notnull="true"/>
            <column name="email" type="varchar"/>
            <column name="last_updated_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterForeignKey name="fk_account_accepted_rules_history_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_accepted_rules_history_account_id" tableName="uam.account_accepted_rules_history"/>
        <alterForeignKey name="fk_account_fraud_info_account_id" columnNames="DROP FOREIGN KEY" tableName="uam.account_fraud_info"/>
        <alterForeignKey name="fk_account_jackpot_info_id" columnNames="DROP FOREIGN KEY" tableName="uam.account_jackpot_info"/>
        <alterForeignKey name="fk_card_bin_details_payment_method_id" columnNames="DROP FOREIGN KEY" tableName="payment.card_bin_details"/>
        <alterForeignKey name="fk_fraud_applied_rules_fraud_response_id" columnNames="DROP FOREIGN KEY" indexName="ix_fraud_applied_rules_fraud_response_id" tableName="uam.fraud_applied_rules"/>
        <alterForeignKey name="fk_jackpots_winner_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpots_winner_id" tableName="uam.jackpots"/>
        <alterForeignKey name="fk_jackpots_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpots_brand_id" tableName="uam.jackpots"/>
        <alterForeignKey name="fk_jackpots_template_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpots_template_id" tableName="uam.jackpots"/>
        <alterForeignKey name="fk_jackpot_account_contributions_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpot_account_contributions_account_id" tableName="uam.jackpot_account_contributions"/>
        <alterForeignKey name="fk_jackpot_account_contributions_jackpot_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpot_account_contributions_jackpot_id" tableName="uam.jackpot_account_contributions"/>
        <alterForeignKey name="fk_jackpot_contributions_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpot_contributions_account_id" tableName="uam.jackpot_contributions"/>
        <alterForeignKey name="fk_jackpot_contributions_jackpot_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpot_contributions_jackpot_id" tableName="uam.jackpot_contributions"/>
        <alterForeignKey name="fk_jackpots_personal_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpots_personal_account_id" tableName="uam.jackpots_personal"/>
        <alterForeignKey name="fk_jackpots_personal_multiplier_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpots_personal_multiplier_id" tableName="uam.jackpots_personal"/>
        <alterForeignKey name="fk_phone_number_request_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_phone_number_request_account_id" tableName="uam.phone_number_request"/>
        <alterForeignKey name="fk_routing_config_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_config_brand_id" tableName="payment.routing_config"/>
        <alterForeignKey name="fk_routing_config_group_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_config_group_id" tableName="payment.routing_config"/>
        <alterForeignKey name="fk_routing_error_brand_id" columnNames="DROP FOREIGN KEY" indexName="ix_routing_error_brand_id" tableName="payment.routing_error"/>
        <alterForeignKey name="fk_jackpot_templates_preferences_id" columnNames="DROP FOREIGN KEY" indexName="ix_jackpot_templates_preferences_id" tableName="uam.jackpot_templates"/>
        <alterForeignKey name="fk_idology_patriot_act_check_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_idology_patriot_act_check_account_id" tableName="fraud.idology_patriot_act"/>
        <alterForeignKey name="fk_postback_filters_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_account_free_spins_account_id" tableName="uam.postback_filters"/>
        <createIndex indexName="ix_accepted_payment_terms_account_id_code" tableName="payment.accepted_payment_terms" columns="account_id,code"/>
        <createIndex indexName="ix_accounts_identity" tableName="uam.accounts" columns="identity"/>
        <createIndex indexName="ix_accounts_created_at" tableName="uam.accounts" columns="created_at"/>
        <createIndex indexName="ix_account_auth_info_last_sign_in" tableName="uam.account_auth_info" columns="last_sign_in"/>
        <createIndex indexName="ix_account_auth_info_last_sign_in_ip" tableName="uam.account_auth_info" columns="last_sign_in_ip"/>
        <createIndex indexName="ix_account_auth_info_sign_up" tableName="uam.account_auth_info" columns="sign_up"/>
        <createIndex indexName="ix_account_auth_info_sign_up_ip" tableName="uam.account_auth_info" columns="sign_up_ip"/>
        <createIndex indexName="ix_account_card_aggregation_fingerprint_account" tableName="payment.account_card_aggregation" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_account_card_aggregation_fingerprint_account ON payment.account_card_aggregation (fingerprint, account_id) WHERE at is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_account_category_tags_category" tableName="uam.account_category_tags" columns="category"/>
        <createIndex indexName="ix_account_engagement_info_points_updated_at" tableName="uam.account_engagement_info" columns="points_updated_at"/>
        <createIndex indexName="ix_account_fraud_info_sign_up_fraud_score" tableName="fraud.account_fraud_info" columns="sign_up_fraud_score"/>
        <createIndex indexName="ix_account_free_spins_status" tableName="uam.account_free_spins" columns="status"/>
        <createIndex indexName="ix_account_gameplay_info_last_sc_gameplay" tableName="uam.account_gameplay_info" columns="last_sc_gameplay"/>
        <createIndex indexName="ix_account_gameplay_info_last_gc_gameplay" tableName="uam.account_gameplay_info" columns="last_gc_gameplay"/>
        <createIndex indexName="ix_account_gameplay_info_last_gameplay" tableName="uam.account_gameplay_info" columns="last_gameplay"/>
        <createIndex indexName="ix_account_missions_mission_id_account_id" tableName="uam.account_missions" columns="mission_id,account_id"/>
        <createIndex indexName="ix_account_offer_reward_log" tableName="payment.account_offer_reward_log" columns="" definition="CREATE INDEX ix_account_offer_reward_log ON payment.account_offer_reward_log (account_id, offer_id) WHERE status = 'created'" platforms="POSTGRES"/>
        <createIndex indexName="ix_account_offer_reward_log_status" tableName="payment.account_offer_reward_log" columns="status"/>
        <createIndex indexName="ix_account_otp_limit_limit_exceeded" tableName="fraud.account_otp_limit" columns="limit_exceeded"/>
        <createIndex indexName="ix_total_account_aggregation" tableName="payment.account_payment_aggregation" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_total_account_aggregation ON payment.account_payment_aggregation (account_id, integration_type) WHERE at is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_account_payment_settings_account_id" tableName="payment.account_payment_settings" columns="account_id"/>
        <createIndex indexName="ix_account_session_limits_end_at" tableName="uam.account_session_limits" columns="end_at"/>
        <createIndex indexName="ix_account_session_limits_account_id_end_at" tableName="uam.account_session_limits" columns="account_id,end_at"/>
        <createIndex indexName="ix_account_session_restrictions_account_id_end_at" tableName="uam.account_session_restrictions" columns="account_id,end_at"/>
        <createIndex indexName="ix_account_session_restrictions_end_at" tableName="uam.account_session_restrictions" columns="" definition="create index ix_account_session_restrictions_end_at on uam.account_session_restrictions (end_at) where end_at is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_account_tags_tag" tableName="uam.account_tags" columns="tag"/>
        <createIndex indexName="ix_account_wheel_of_winners_last_given_spin_at" tableName="uam.account_wheel_of_winners" columns="last_given_spin_at"/>
        <createIndex indexName="ix_account_wheel_of_winners_last_spin_at" tableName="uam.account_wheel_of_winners" columns="last_spin_at"/>
        <createIndex indexName="ix_withdraw_methods_account_id_type" tableName="payment.withdraw_methods" columns="account_id,type"/>
        <createIndex indexName="uq_account_saved_withdraw_method_type_code" tableName="payment.withdraw_methods" columns="" unique="true" definition="CREATE UNIQUE INDEX uq_account_saved_withdraw_method_type_code ON payment.withdraw_methods (account_id, remember, type, code) WHERE remember = true" platforms="POSTGRES"/>
        <createIndex indexName="ix_backoffice_events_brand_id_entity_type" tableName="uam.backoffice_events" columns="brand_id,entity_type"/>
        <createIndex indexName="ix_big_wins_spin_currency" tableName="uam.big_wins" columns="spin_currency"/>
        <createIndex indexName="ix_big_wins_created_at" tableName="uam.big_wins" columns="created_at"/>
        <createIndex indexName="ix_bonus_rewards_account_id_not_accepted" tableName="uam.bonus_rewards" columns="" definition="create index ix_bonus_rewards_account_id_not_accepted on uam.bonus_rewards (account_id) where accepted = false" platforms="POSTGRES"/>
        <createIndex indexName="ix_feature_settings_brand_enabled" tableName="uam.feature_settings_brand" columns="enabled"/>
        <createIndex indexName="ix_feature_settings_brand_country_code" tableName="uam.feature_settings_brand" columns="country_code"/>
        <createIndex indexName="ix_card_transaction_details_auth_code" tableName="payment.card_transaction_details" columns="auth_code"/>
        <createIndex indexName="ix_card_transaction_details_acquirer_reference_number" tableName="payment.card_transaction_details" columns="acquirer_reference_number"/>
        <createIndex indexName="ix_chargeback_chargeback_at" tableName="payment.chargeback" columns="chargeback_at"/>
        <createIndex indexName="ix_integration_type_currency" tableName="payment.chargeback_config" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_integration_type_currency ON payment.chargeback_config (currency, integration_type) WHERE brand_id is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_brand_integration_type_currency" tableName="payment.chargeback_config" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_brand_integration_type_currency ON payment.chargeback_config (brand_id, currency, integration_type) WHERE brand_id is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_chargeback_history_created_at_collection_status" tableName="payment.chargeback_history" columns="created_at,collection_status"/>
        <createIndex indexName="ix_chargeback_history_order_id_order_at" tableName="payment.chargeback_history" columns="order_id,order_at"/>
        <createIndex indexName="ix_completed_kyc_verifications_requests_processed_to_box" tableName="uam.completed_kyc_verifications_requests" columns="processed_to_box"/>
        <createIndex indexName="ix_countries_code" tableName="uam.countries" columns="code"/>
        <createIndex indexName="ix_countries_name" tableName="uam.countries" columns="name"/>
        <createIndex indexName="ix_daily_bonus_templates_enabled" tableName="uam.daily_bonus_templates" columns="enabled"/>
        <createIndex indexName="ix_daily_bonus_templates_country_code" tableName="uam.daily_bonus_templates" columns="country_code"/>
        <createIndex indexName="ix_daily_redeem_limit_state" tableName="payment.daily_redeem_limits" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_daily_redeem_limit_state ON payment.daily_redeem_limits (state) WHERE brand_id is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_daily_redeem_limit_brand_id_state" tableName="payment.daily_redeem_limits" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_daily_redeem_limit_brand_id_state ON payment.daily_redeem_limits (brand_id, state) WHERE brand_id is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_email_otp_request_account_id" tableName="fraud.email_otp_request" columns="account_id"/>
        <createIndex indexName="ix_external_reward_order_id_order_at" tableName="payment.external_reward" columns="order_id,order_at"/>
        <createIndex indexName="ix_fx_currency_rate_at" tableName="payment.fx_currency_rate" columns="at"/>
        <createIndex indexName="ix_fraud_response_response_id" tableName="fraud.fraud_response" columns="response_id"/>
        <createIndex indexName="ix_fraud_rule_category_details_category_id" tableName="fraud.fraud_rule_category_details" columns="category_id"/>
        <createIndex indexName="ix_free_spin_campaigns_provider_brand_id_currency" tableName="uam.free_spin_campaigns" columns="provider,brand_id,currency"/>
        <createIndex indexName="ix_free_spin_campaigns_status" tableName="uam.free_spin_campaigns" columns="status"/>
        <createIndex indexName="ix_game_allowance_by_registration_policies_enabled" tableName="uam.game_allowance_by_registration_policies" columns="enabled"/>
        <createIndex indexName="ix_game_allowance_policies_enabled" tableName="uam.game_allowance_policies" columns="enabled"/>
        <createIndex indexName="ix_feature_settings_game_lobby_enabled" tableName="uam.feature_settings_game_lobby" columns="enabled"/>
        <createIndex indexName="ix_feature_settings_game_lobby_start_date_time" tableName="uam.feature_settings_game_lobby" columns="start_date_time"/>
        <createIndex indexName="ix_giveaway_status" tableName="uam.giveaway" columns="status"/>
        <createIndex indexName="ix_giveaway_created_at" tableName="uam.giveaway" columns="created_at"/>
        <createIndex indexName="ix_feature_settings_gold_coin_generator_enabled" tableName="uam.feature_settings_gold_coin_generator" columns="enabled"/>
        <createIndex indexName="ix_feature_settings_gold_coin_generator_country_code" tableName="uam.feature_settings_gold_coin_generator" columns="country_code"/>
        <createIndex indexName="ix_homepage_features_enabled" tableName="uam.homepage_features" columns="enabled"/>
        <createIndex indexName="ix_homepage_features_country_code" tableName="uam.homepage_features" columns="country_code"/>
        <createIndex indexName="ix_suppliers_code" tableName="core.suppliers" columns="code"/>
        <createIndex indexName="ix_suppliers_search_code" tableName="core.suppliers" columns="search_code"/>
        <createIndex indexName="ix_suppliers_title" tableName="core.suppliers" columns="title"/>
        <createIndex indexName="ix_inbox_notifications_account_id_status" tableName="payment.inbox_notifications" columns="" definition="create index if not exists ix_inbox_notifications_account_id_status on payment.inbox_notifications (account_id, status) where status != 'unavailable'" platforms="POSTGRES"/>
        <createIndex indexName="ix_inbox_notifications_account_id_status_type" tableName="fraud.inbox_notifications" columns="account_id,status,type"/>
        <createIndex indexName="ix_inbox_notifications_token" tableName="fraud.inbox_notifications" columns="token"/>
        <createIndex indexName="ix_influencer_budget_spend_influencer_id_allocated_at" tableName="uam.influencer_budget_spend" columns="influencer_id,allocated_at"/>
        <createIndex indexName="ix_jackpot_account_free_contributions_account_id_expire_a_1" tableName="uam.jackpot_account_free_contributions" columns="account_id,expire_at,amount,used_amount"/>
        <createIndex indexName="ix_jackpot_opt_history_account_id_not_opted_out" tableName="uam.jackpot_opt_history" columns="" definition="create index ix_jackpot_opt_history_account_id_not_opted_out on uam.jackpot_opt_history (account_id) where opt_out_at is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_kyc_risk_spend_policy_currency" tableName="fraud.kyc_risk_spend_policy" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_currency ON fraud.kyc_risk_spend_policy (currency) WHERE fraud_brand_id is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_kyc_risk_spend_policy_fraud_brand_id_currency" tableName="fraud.kyc_risk_spend_policy" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_brand_id_currency ON fraud.kyc_risk_spend_policy (fraud_brand_id, currency) WHERE fraud_brand_id is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_kyc_verification_requests_id_number_hash" tableName="uam.kyc_verification_requests" columns="id_number_hash"/>
        <createIndex indexName="ix_loyalty_level_bonus_net_gaming_revenue" tableName="uam.loyalty_level_bonus" columns="net_gaming_revenue"/>
        <createIndex indexName="ix_missions_brand_id_priority" tableName="uam.missions" columns="brand_id,priority"/>
        <createIndex indexName="ix_missions_enabled_brand_priority_timerange" tableName="uam.missions" columns="" definition="create index ix_missions_enabled_brand_priority_timerange on uam.missions (brand_id, priority, start_at) where enabled = true and priority is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_mission_free_spin_rewards_mission_id" tableName="uam.mission_free_spin_rewards" columns="mission_id"/>
        <createIndex indexName="ix_mission_rewards_mission_id" tableName="uam.mission_rewards" columns="mission_id"/>
        <createIndex indexName="ix_mission_steps_mission_id_mission_step_action_id" tableName="uam.mission_steps" columns="mission_id,mission_step_action_id"/>
        <createIndex indexName="ix_mission_step_free_spin_rewards_mission_step_id" tableName="uam.mission_step_free_spin_rewards" columns="mission_step_id"/>
        <createIndex indexName="ix_mission_step_rewards_mission_step_id" tableName="uam.mission_step_rewards" columns="mission_step_id"/>
        <createIndex indexName="ix_otp_trigger_events_at" tableName="fraud.otp_trigger_events" columns="at"/>
        <createIndex indexName="ix_otp_trigger_rules_fraud_brand_id" tableName="fraud.otp_trigger_rules" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_otp_trigger_rules_fraud_brand_id ON fraud.otp_trigger_rules (fraud_brand_id) WHERE country is null" platforms="POSTGRES"/>
        <createIndex indexName="ix_otp_trigger_rules_fraud_brand_id_country" tableName="fraud.otp_trigger_rules" columns="" unique="true" definition="CREATE UNIQUE INDEX ix_otp_trigger_rules_fraud_brand_id_country ON fraud.otp_trigger_rules (fraud_brand_id, country) WHERE country is not null" platforms="POSTGRES"/>
        <createIndex indexName="ix_payment_aggregated_meta_info_provider_id_at" tableName="payment.payment_aggregated_meta_info" columns="provider_id,at"/>
        <createIndex indexName="ix_payment_method_meta_info_account_id_fingerprint" tableName="payment.payment_method_meta_info" columns="account_id,fingerprint"/>
        <createIndex indexName="ix_payment_orders_account_id_success_at" tableName="payment.payment_orders" columns="account_id,success,at"/>
        <createIndex indexName="ix_payment_orders_account_id_internal_status_at" tableName="payment.payment_orders" columns="account_id,internal_status,at"/>
        <createIndex indexName="ix_payment_orders_chargeback_at" tableName="payment.payment_orders" columns="chargeback_at"/>
        <createIndex indexName="ix_payment_orders_tc40_received_at" tableName="payment.payment_orders" columns="tc40_received_at"/>
        <dropIndex indexName="ix_phone_number_request_phone_number" tableName="uam.phone_number_request"/>
        <createIndex indexName="ix_phone_number_request_phone_number" tableName="fraud.phone_number_request" columns="phone_number"/>
        <createIndex indexName="idx_random_reward_instance_status_expires_at" tableName="reward.random_reward_instance" columns="status,expires_at"/>
        <createIndex indexName="ix_random_reward_instance_source_source_reference" tableName="reward.random_reward_instance" columns="source,source_reference"/>
        <createIndex indexName="ix_random_reward_instance_offer_template_code_offer_templ_1" tableName="reward.random_reward_instance_offer_template_code" columns="offer_template_code"/>
        <createIndex indexName="ix_refund_history_order_id_order_at" tableName="payment.refund_history" columns="order_id,order_at"/>
        <createIndex indexName="ix_refund_history_at" tableName="payment.refund_history" columns="at"/>
        <createIndex indexName="ix_rescue_purchase_providers_settings_brand_id_payment_me_1" tableName="payment.rescue_purchase_providers_settings" columns="brand_id,payment_method_type"/>
        <createIndex indexName="ix_routing_error_config_error_code" tableName="payment.routing_error_config" columns="error_code"/>
        <createIndex indexName="uq_account_saved_routing_ach_token" tableName="payment.routing_provider_ach_token" columns="" unique="true" definition="CREATE UNIQUE INDEX uq_account_saved_routing_ach_token ON payment.routing_provider_ach_token (account_id, provider_id, remember) WHERE remember = true" platforms="POSTGRES"/>
        <createIndex indexName="ix_sca_authentications_token" tableName="payment.sca_authentications" columns="token"/>
        <createIndex indexName="ix_themes_rank" tableName="uam.themes" columns="rank"/>
        <createIndex indexName="ix_withdraw_money_requests_transaction_id" tableName="payment.withdraw_money_requests" columns="transaction_id"/>
        <createIndex indexName="ix_withdraw_money_requests_modified_at_date" tableName="payment.withdraw_money_requests" columns="modified_at_date"/>
        <createIndex indexName="ix_withdraw_money_requests_modified_at" tableName="payment.withdraw_money_requests" columns="modified_at" definition="create index ix_withdraw_money_requests_modified_at on payment.withdraw_money_requests (date(timezone('UTC',modified_at)))" platforms="POSTGRES"/>
        <createIndex indexName="ix_withdraw_money_requests_modified_at_base" tableName="payment.withdraw_money_requests" columns="modified_at"/>
        <dropIndex indexName="ix_account_meta_info_last_sign_in" tableName="uam.account_meta_info"/>
        <dropIndex indexName="ix_account_meta_info_last_sign_in_ip" tableName="uam.account_meta_info"/>
        <dropIndex indexName="ix_account_meta_info_sign_up" tableName="uam.account_meta_info"/>
        <dropIndex indexName="ix_account_meta_info_sign_up_ip" tableName="uam.account_meta_info"/>
        <dropIndex indexName="ix_payment_orders_account_id_success" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_payment_orders_at" tableName="payment.payment_orders"/>
        <dropIndex indexName="ix_idology_check_at" tableName="fraud.idology_check"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="bot" tableName="uam.accounts"/>
        <dropColumn columnName="at" tableName="uam.accounts"/>
        <dropColumn columnName="username" tableName="uam.accounts"/>
        <dropColumn columnName="email_mask" tableName="uam.accounts"/>
        <dropColumn columnName="real_email_mask" tableName="uam.accounts"/>
        <dropColumn columnName="name" tableName="uam.accounts"/>
        <dropColumn columnName="phone_number_mask" tableName="uam.accounts"/>
        <dropColumn columnName="restrictions" tableName="uam.accounts"/>
        <dropColumn columnName="email_verified" tableName="uam.accounts"/>
        <dropColumn columnName="kyc" tableName="uam.accounts"/>
        <dropColumn columnName="status" tableName="uam.accounts"/>
        <dropColumn columnName="guest" tableName="uam.accounts"/>
        <dropColumn columnName="locked" tableName="uam.accounts"/>
        <dropColumn columnName="vip_level_purchase" tableName="uam.accounts"/>
        <dropColumn columnName="vip_level_override_purchase" tableName="uam.accounts"/>
        <dropColumn columnName="first_gc_casino_product_id" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_sc_casino_product_id" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_gc_lotto_product_id" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_sc_lotto_product_id" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_gc_bingo_product_id" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_sc_bingo_product_id" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="last_gc_casino_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="last_sc_casino_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="last_gc_lotto_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="last_sc_lotto_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="last_gc_bingo_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="last_sc_bingo_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_gc_casino_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_sc_casino_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_gc_lotto_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_sc_lotto_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_gc_bingo_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="first_sc_bingo_gameplay" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_game_round_count" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_game_round_count" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_game_round_count_1d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_game_round_count_1d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_game_round_count_2d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_game_round_count_2d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_game_round_count_7d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_game_round_count_7d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_wager_amount" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_wager_amount" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_win_amount" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_win_amount" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_wager_amount_1d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_wager_amount_1d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_win_amount_1d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_win_amount_1d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_wager_amount_2d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_wager_amount_2d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_win_amount_2d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_win_amount_2d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_wager_amount_7d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_wager_amount_7d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_gc_win_amount_7d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="total_sc_win_amount_7d" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="version" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="created_at" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="modified_at" tableName="uam.account_gameplay_info"/>
        <dropColumn columnName="last_sign_in_ip" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_country" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_city" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_state" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_user_agent" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_method" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_method" tableName="uam.account_meta_info"/>
        <dropColumn columnName="query" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_ip" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_country" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_city" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_state" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_user_agent" tableName="uam.account_meta_info"/>
        <dropColumn columnName="advertising_id" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_kyc" tableName="uam.account_meta_info"/>
        <dropColumn columnName="version" tableName="uam.account_meta_info"/>
        <dropColumn columnName="created_at" tableName="uam.account_meta_info"/>
        <dropColumn columnName="modified_at" tableName="uam.account_meta_info"/>
        <dropColumn columnName="modified_by_id" tableName="uam.account_notes"/>
        <dropColumn columnName="modified_by_id" tableName="core.affiliate_providers"/>
        <dropColumn columnName="title" tableName="core.brands"/>
        <dropColumn columnName="secret" tableName="core.brands"/>
        <dropColumn columnName="webhook_url" tableName="core.brands"/>
        <dropColumn columnName="home_page" tableName="core.brands"/>
        <dropColumn columnName="modified_by_id" tableName="uam.campaign_templates"/>
        <dropColumn columnName="first_name" tableName="uam.citizens"/>
        <dropColumn columnName="last_name" tableName="uam.citizens"/>
        <dropColumn columnName="city" tableName="uam.citizens"/>
        <dropColumn columnName="address" tableName="uam.citizens"/>
        <dropColumn columnName="address2" tableName="uam.citizens"/>
        <dropColumn columnName="postal" tableName="uam.citizens"/>
        <dropColumn columnName="version" tableName="uam.country_operation_policies"/>
        <dropColumn columnName="created_at" tableName="uam.country_operation_policies"/>
        <dropColumn columnName="modified_at" tableName="uam.country_operation_policies"/>
        <dropColumn columnName="modified_by_id" tableName="uam.daily_bonus_templates"/>
        <dropColumn columnName="modified_by_id" tableName="uam.email_templates"/>
        <dropColumn columnName="modified_by_id" tableName="core.xp_levels"/>
        <dropColumn columnName="doc_address" tableName="uam.kyc_verification_requests"/>
        <dropColumn columnName="modified_by_id" tableName="core.products"/>
        <dropColumn columnName="modified_by_id" tableName="core.providers"/>
        <dropColumn columnName="brand_id" tableName="uam.jackpot_preferences"/>
        <dropColumn columnName="simulation" tableName="payment.payment_orders"/>
        <dropColumn columnName="order_type" tableName="payment.payment_orders"/>
        <dropColumn columnName="modified_by_id" tableName="uam.push_templates"/>
        <dropColumn columnName="modified_by_id" tableName="uam.referrer_templates"/>
        <dropColumn columnName="modified_by_id" tableName="uam.user_agent_templates"/>
        <dropColumn columnName="modified_by_id" tableName="core.vip_levels"/>
        <dropColumn columnName="locked_by_id" tableName="payment.withdraw_money_requests"/>
        <dropColumn columnName="confirmed_by_id" tableName="payment.withdraw_money_requests"/>
        <dropColumn columnName="declined_by_id" tableName="payment.withdraw_money_requests"/>
        <dropTable name="uam.account_accepted_rules_history" sequenceCol="id"/>
        <dropTable name="uam.account_fraud_info" sequenceCol="id"/>
        <dropTable name="uam.account_jackpot_info" sequenceCol="id"/>
        <dropTable name="payment.card_bin_details" sequenceCol="payment_method_id"/>
        <dropTable name="uam.fraud_applied_rules" sequenceCol="id"/>
        <dropTable name="uam.fraud_response" sequenceCol="id"/>
        <dropTable name="uam.jackpots" sequenceCol="id"/>
        <dropTable name="uam.jackpot_account_contributions" sequenceCol="id"/>
        <dropTable name="uam.jackpot_contributions" sequenceCol="id"/>
        <dropTable name="uam.jackpot_multipliers" sequenceCol="id"/>
        <dropTable name="uam.jackpots_personal" sequenceCol="id"/>
        <dropTable name="uam.phone_number_request" sequenceCol="id"/>
        <dropTable name="payment.routing_config" sequenceCol="id"/>
        <dropTable name="payment.routing_error" sequenceCol="id"/>
        <dropTable name="fraud.idology_check" sequenceCol="account_id"/>
        <dropTable name="uam.jackpot_templates" sequenceCol="id"/>
        <dropTable name="fraud.idology_patriot_act" sequenceCol="id"/>
        <dropTable name="uam.postback_filters" sequenceCol="id"/>
    </changeSet>
</migration>