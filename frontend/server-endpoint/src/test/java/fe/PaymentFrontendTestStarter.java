package fe;

import org.springframework.cloud.ConfigurableCloudConnector;

import com.turbospaces.boot.Bootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.CloudOptions;
import com.turbospaces.common.PlatformUtil;

public class PaymentFrontendTestStarter {
    public static void main(String[] args) throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        cfg.loadLocalDevProperties();
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_PORT, 9992);
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_SECONDARY_PORT, 9993);
        cfg.setDefaultProperty(CloudOptions.CLOUD_APP_TERTIARY_PORT, PlatformUtil.findAvailableTcpPort());

        ConfigurableCloudConnector connector = new ConfigurableCloudConnector();
        Bootstrap bootstrap = PaymentFrontendStarter.run(cfg, connector, args);

        bootstrap.squashLogging(); // ~ reset logging level if necessary globally
    }
}
