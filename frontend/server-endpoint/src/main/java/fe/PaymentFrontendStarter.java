package fe;

import java.util.Collection;

import org.springframework.cloud.GcpCloudConnector;
import org.springframework.cloud.SmartCloudConnector;
import org.springframework.cloud.WildcardUPSFilter;
import org.springframework.util.ResourceUtils;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.google.cloud.spring.core.DefaultGcpProjectIdProvider;
import com.google.common.base.Joiner;
import com.turbospaces.api.CommonTopics;
import com.turbospaces.api.Topic;
import com.turbospaces.boot.SimpleBootstrap;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.PaymentApplicationConfig;
import com.turbospaces.healthcheck.KafkaHealthCheck;
import com.turbospaces.plugins.FlywaySpannerBootstrapInitializer;
import com.turbospaces.plugins.KafkaBootstrapInitializer;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import fe.di.FrontendAcceptorsDiModule;
import fe.di.PaymentFrontendServerDiModule;
import payment.PaymentTopics;

public class PaymentFrontendStarter {
    public static void main(String[] args) throws Throwable {
        run(new PaymentApplicationConfig(),
                new GcpCloudConnector(new WildcardUPSFilter(new PaymentFrontendUPSs().build(), new PaymentFrontendWildcardUPSs().build())), args);
    }
    public static SimpleBootstrap run(ApplicationConfig cfg, SmartCloudConnector connector, String[] args) throws Throwable {
        SimpleBootstrap bootstrap = bootstrap(cfg, connector);
        bootstrap.run(args); // ~ start
        return bootstrap;
    }
    public static SimpleBootstrap bootstrap(ApplicationConfig cfg, SmartCloudConnector connector) throws Throwable {
        PaymentFrontendServerProperties props = new PaymentFrontendServerProperties(cfg.factory());

        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "payment-frontend.properties"));
        cfg.setDefaultProperty(props.HTTP_REQUEST_MAX_SIZE.getKey(), props.KAFKA_RECORD_MAX_REQUEST_SIZE.get());
        cfg.setDefaultProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/spanner-migration");
        cfg.setDefaultProperty(props.REQUEST_REPLY_TOPIC.getKey(), PaymentFrontendTopics.RESP.name().toString());
        cfg.setDefaultProperty(props.APP_NOTIFY_TOPIC.getKey(), Joiner.on(',').join("notify", PaymentTopics.NOTIFY.name().toString()));

        DefaultGcpProjectIdProvider projectId = new DefaultGcpProjectIdProvider();
        SimpleBootstrap bootstrap = new SimpleBootstrap(props, connector, FrontendAcceptorsDiModule.class, PaymentFrontendServerDiModule.class);

        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
        Collection<Topic> topics = CommonTopics.asNotifies(props);

        //
        // ~ auto create topics (configuration part)
        //
        for (Topic topic : topics) {
            topic.configure(cfg);
        }

        //
        // ~ health checks
        //
        registerHealthChecks(bootstrap, topics);

        //
        // ~ Extensions
        //
        bootstrap.addBootstrapRegistryInitializer(new KafkaBootstrapInitializer(bootstrap.keyStore(), props, ksi, topics));
        if (props.FLYWAY_MIGRATION_RUN_ON_START.get()) {
            //
            // ~ not recommended for production, too unstable (needs to run manually in background)
            //
            bootstrap.addBootstrapRegistryInitializer(new FlywaySpannerBootstrapInitializer(props,
                    String.format("jdbc:cloudspanner:/projects/%s/instances/%s/databases/%s?autoConfigEmulator=%s",
                            projectId.getProjectId(),
                            props.SPANNER_INSTANCE_NAME.get(),
                            props.SPANNER_DATABASE_NAME.get(),
                            props.FLYWAY_MIGRATION_USE_AUTOCONFIG_EMULATOR.get())));
        }

        return bootstrap;
    }

    public static void registerHealthChecks(SimpleBootstrap bootstrap, Collection<Topic> topics) throws Exception {
        KafkaServiceInfo ksi = UPSs.findRequiredServiceInfoByName(bootstrap.cloud(), UPSs.KAFKA);
        HealthCheckRegistry healthChecks = bootstrap.healthCheckRegistry();
        healthChecks.register("kafka-check", new KafkaHealthCheck(ksi, bootstrap.keyStore(), topics));
    }
}
