<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true">
    <jmxConfigurator />
    <!-- -->
    <!-- import -->
    <!-- -->
    <include resource="logback-base-levels.xml" />
    <!-- -->
    <!-- appenders -->
    <!-- -->
    <appender name="SENTRY" class="com.turbospaces.logging.SentryAppender" />
    <appender name="CENTRAL" class="com.turbospaces.logging.ElasticSearchAppender">
        <template>payment-frontend-logging-template.json</template>
        <hourly>true</hourly>
        <designator>logging</designator>
        <properties>
            <property name="thread" value="%thread" scope="CONTEXT">
                <name>thread</name>
                <value>%thread</value>
            </property>
            <property name="level" value="%level" scope="CONTEXT">
                <name>level</name>
                <value>%level</value>
            </property>
            <property name="logger" value="%logger" scope="CONTEXT">
                <name>logger</name>
                <value>%logger</value>
            </property>
            <property name="message" value="%msg" scope="CONTEXT">
                <name>message</name>
                <value>%msg</value>
            </property>
            <property name="stacktrace" value="%ex" scope="CONTEXT">
                <name>stacktrace</name>
                <value>%ex</value>
            </property>
        </properties>
    </appender>
    <root>
        <level value="DEBUG" />
        <appender-ref ref="CENTRAL" />
        <appender-ref ref="SENTRY" />
    </root>
</configuration>