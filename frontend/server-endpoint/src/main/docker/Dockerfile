FROM bellsoft/liberica-runtime-container:jdk-${maven.compiler.source}-slim-glibc

ENV ENV_PORT=${PORT:-8089}
ENV ENV_SECONDARY_PORT=${SECONDARY_PORT:-8091}
ENV ENV_TERTIARY_PORT=${TERTIARY_PORT:-8093}
ENV ENV_INDEX=${INDEX:-0}
ENV ENV_JMX_PORT=${JMX_PORT:-9999}
ENV ENV_JAVA_OPTIONS="${jvm.gc.options}"
ENV ENV_JAVA_OPTIONS_COMMON="${jvm.common.options}"
ENV ENV_JMX_OPTIONS="${jvm.jmx.options}"
ENV ENV_HEAPDUMPPATH=${HEAPDUMPPATH:-/logs/dump.hprof}

ADD ${project.artifactId}-${project.version}-exec.jar /workspace/uber.jar

CMD ["/bin/sh", "-c", "java -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$ENV_HEAPDUMPPATH -Duser.timezone=UTC -Dorg.springframework.boot.logging.LoggingSystem=none -Dcloud.application.host=$HOSTNAME -Dcloud.application.port=$ENV_PORT -Dcloud.application.secondary-port=$ENV_SECONDARY_PORT -Dcloud.application.tertiary-port=$ENV_TERTIARY_PORT -Dcloud.application.instance_index=$ENV_INDEX ${jvm.unsafe.options} $ENV_JAVA_OPTIONS $ENV_JAVA_OPTIONS_COMMON -jar /workspace/uber.jar"]