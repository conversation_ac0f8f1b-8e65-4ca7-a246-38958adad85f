package fe.api.thrill.jackpot.notification;

import java.math.BigDecimal;

import gateway.api.ResponseOrNotification;
import lombok.ToString;

/**
 * A personal notification that you won a jackpot.
 */
@SuppressWarnings("java:S1104")
@ToString
public class JackpotWinNotification implements ResponseOrNotification {
    public String currency;
    public String winnerFirstName;
    public String winnerLastName;
    public String code;
    public BigDecimal amount;
    public BigDecimal spinAmount;
    public String spinCurrency;
    public String gameTitle;
    public long winAt;
    public boolean isCommunity;
}
