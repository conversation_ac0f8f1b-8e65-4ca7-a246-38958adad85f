package fe.api.thrill.jackpot.notification;

import java.util.ArrayList;
import java.util.List;

import fe.api.thrill.jackpot.JackpotPreferences;
import gateway.api.ResponseOrNotification;
import lombok.ToString;

@ToString
@SuppressWarnings("java:S1104")
public class JackpotUpdateNotification implements ResponseOrNotification {
    public String brandName;
    public List<JackpotPreferences> preferences = new ArrayList<>();
}
