package fe.api.thrill.jackpot;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;

public class JackpotWinnerInfo {

    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public String currency;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public String winnerFirstName;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public String winnerLastName;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED,
            allowableValues = {"hourly", "daily", "monthly"})
    public String code;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public BigDecimal amount;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public BigDecimal spinAmount;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public String spinCurrency;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public String gameTitle;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public long winAt;
}
