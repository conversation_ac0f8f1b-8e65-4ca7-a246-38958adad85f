package fe.api.thrill.jackpot;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.v3.oas.annotations.media.Schema;

public class JackpotPreferences {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED, ref = "Currency")
    public String currency;
    @Schema(requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public Boolean optional;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public Boolean optedIn;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public String minAmount;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public String maxAmount;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public List<JackpotInfo> jackpots = new ArrayList<>();
}
