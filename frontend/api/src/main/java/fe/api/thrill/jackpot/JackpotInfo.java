package fe.api.thrill.jackpot;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;

public class JackpotInfo {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED,
            allowableValues = {"hourly", "daily", "weekly", "monthly"})
    public String code;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public BigDecimal prevAmount;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public BigDecimal amount;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public Long updatedAt;
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    public Long lastWinAt;
    @Schema(requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public JackpotWinnerInfo lastWinner;
    @Schema(requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    public boolean isCommunity;
}
