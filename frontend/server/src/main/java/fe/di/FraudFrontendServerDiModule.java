package fe.di;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;

import com.turbospaces.spanner.SpannerDiModule;

@Configuration
@Import({
        SpannerDiModule.class,
        FrontendSpannerDiModule.class,
        CommonFrontendDiModule.class})
@EnableKafka
public class FraudFrontendServerDiModule {

}
