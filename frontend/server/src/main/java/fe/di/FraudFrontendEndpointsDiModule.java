package fe.di;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;

import api.v1.ApiFactory;
import fe.MessageDispatcher;
import fe.api.fraud.FraudApi;
import fe.api.fraud.FraudV2Api;
import fe.endpoints.DefaultFraudApiEndpoint;
import fe.endpoints.DefaultFraudDocumentationEndpoint;
import fe.endpoints.FraudDocumentationEndpoint;
import fe.endpoints.FraudV2ApiEndpoint;
import fe.handlers.fraud.FraudHandlersMarker;
import fe.services.CookieStorage;
import io.micrometer.core.instrument.MeterRegistry;

@Configuration
@ComponentScan(basePackageClasses = { FraudHandlersMarker.class })
public class FraudFrontendEndpointsDiModule {
    @Bean
    public FraudApi fraudApiEndpoint(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        return new DefaultFraudApiEndpoint(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }
    @Bean
    public FraudDocumentationEndpoint documentationFraudEndpoint(
            ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud,
            MeterRegistry meterRegistry) {
        return new DefaultFraudDocumentationEndpoint(props, apiFactory, cloud, meterRegistry);
    }
    @Bean
    public FraudV2Api fraudV2Api(
            ApplicationProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            MessageDispatcher dispatcher,
            CookieStorage cookieStorage,
            ApiFactory apiFactory) {
        return new FraudV2ApiEndpoint(props, cloud, meterRegistry, dispatcher, cookieStorage, apiFactory);
    }
}
