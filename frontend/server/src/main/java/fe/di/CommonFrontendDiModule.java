package fe.di;

import java.time.Duration;
import java.util.List;

import fe.services.DefaultKYCAssertionService;
import fe.services.KYCAssertionService;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.resteasy.AdminEndpoint;
import com.turbospaces.resteasy.DefaultAdminEndpoint;
import com.turbospaces.resteasy.ReadyIndicator;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.temporal.CustomDataConverter;
import com.turbospaces.temporal.TemporalDiModule;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import fe.FraudFrontendServerProperties;
import fe.FraudFrontendTopics;
import fe.services.DefaultFraudOtpAggregationService;
import fe.services.chat.DefaultLiveChatService;
import fe.services.chat.LiveChatService;
import fe.services.otp.DefaultOtpService;
import fe.services.otp.OtpAggregationService;
import fe.services.otp.OtpService;
import fraud.api.DefaultFraudServiceApi;
import fraud.api.FraudServiceApi;
import fraud.frontend.FraudFrontendPropertiesFragement;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import payment.api.DefaultPaymentServiceApi;
import payment.api.PaymentServiceApi;
import payment.api.temporal.PaymentTemporalClientFactoryBean;
import payment.api.temporal.PaymentWorkflowPostTemplate;
import uam.api.DefaultUamServiceApi;
import uam.api.UamServiceApi;

@Configuration
@Import({
        FrontendKafkaDiModule.class,
        FrontendDispatcherDiModule.class,
        FraudFrontendEndpointsDiModule.class,
        TemporalDiModule.class,

})
public class CommonFrontendDiModule {
    @Bean
    public ApiFactory apiFactory(ApplicationProperties props, CommonObjectMapper mapper) {
        return new DefaultApiFactory(props, mapper);
    }
    @Bean
    public AdminEndpoint adminApiEndpoint(
            ApplicationProperties props,
            CompositeMeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            List<ReadyIndicator> probes) {
        return new DefaultAdminEndpoint(props, meterRegistry, healthCheckRegistry, probes);
    }
    @Bean
    public UamServiceApi uamServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultUamServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, FraudFrontendTopics.RESP));
    }

    @Bean
    public PaymentWorkflowPostTemplate paymentWorkflowPostTemplate(
            FraudFrontendServerProperties props,
            ApiFactory apiFactory,
            PaymentTemporalClientFactoryBean clientFactory) throws Exception {
        return new PaymentWorkflowPostTemplate(props, apiFactory, props.REQUEST_REPLY_TIMEOUT.map(Duration::ofSeconds), clientFactory);
    }

    @Bean
    public PaymentTemporalClientFactoryBean paymentTemporalClientFactory(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry, CustomDataConverter customDataConverter) {
        return new PaymentTemporalClientFactoryBean(props, cloud, meterRegistry, customDataConverter);
    }

    @Bean
    public PaymentServiceApi paymentServiceApi(FraudFrontendServerProperties feProps, ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory, PaymentWorkflowPostTemplate paymentWorkflowPostTemplate) {
        return new DefaultPaymentServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, FraudFrontendTopics.RESP),

                feProps.PAYMENT_TEMPORAL_ENABLED,
                paymentWorkflowPostTemplate
                );
    }
    @Bean
    public FraudServiceApi fraudServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultFraudServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, FraudFrontendTopics.RESP));
    }
    @Bean
    public OtpAggregationService otpAggregationService(
            PaymentServiceApi paymentServiceApi,
            UamServiceApi uamServiceApi,
            FraudServiceApi fraudServiceApi) {
        return new DefaultFraudOtpAggregationService(paymentServiceApi, uamServiceApi, fraudServiceApi);
    }
    @Bean
    public OtpService otpService(
            FraudFrontendServerProperties props,
            MeterRegistry meterRegistry,
            OtpAggregationService otpAggregationService) {
        return new DefaultOtpService(props, meterRegistry, otpAggregationService);
    }
    @Bean
    public LiveChatService liveChatService(
            UamServiceApi uamServiceApi,
            PaymentServiceApi paymentServiceApi,
            FraudServiceApi fraudServiceApi,
            FraudFrontendPropertiesFragement fraudFrontendProperties) {
        return new DefaultLiveChatService(uamServiceApi, paymentServiceApi, fraudServiceApi, fraudFrontendProperties);
    }
    @Bean
    public KYCAssertionService kycAssertionService(FraudFrontendPropertiesFragement props) {
        return new DefaultKYCAssertionService(props);
    }
}
