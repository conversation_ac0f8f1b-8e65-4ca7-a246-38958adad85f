package fe.handlers.fraud.otp;

import java.util.Optional;

import org.springframework.stereotype.Service;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.CreateEmailOtpRequest;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;

@Service
public class CreateEmailOtpRequestHandler extends AbstractFrontendRequestHandler<CreateEmailOtpRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public CreateEmailOtpRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, CreateEmailOtpRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();

            fraud.api.v1.CreateEmailOtpRequest.Builder sreqb =
                    fraud.api.v1.CreateEmailOtpRequest.newBuilder()
                            .setIdentity(toIdentity(client, auth));

            var emailOtpRequest = fraudServiceApi.createEmailOtpRequest(sreqb.build(), toRoutingKey(auth));
            emailOtpRequest.addListener(new SafeSocketResponseConsumer(emailOtpRequest, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    setStatusAndMeta(respw, srespw, when);
                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
