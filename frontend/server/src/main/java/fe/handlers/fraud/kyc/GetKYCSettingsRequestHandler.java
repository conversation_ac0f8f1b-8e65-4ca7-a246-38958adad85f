package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetKYCSettingsRequest;
import fe.api.fraud.model.GetKYCSettingsResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.mappers.Mappers;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class GetKYCSettingsRequestHandler extends AbstractFrontendRequestHandler<GetKYCSettingsRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public GetKYCSettingsRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetKYCSettingsRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Throwable {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();

            var req = fraud.api.v1.GetKYCSettingsRequest.newBuilder().setIdentity(toIdentity(client, auth)).build();

            var wrapped = fraudServiceApi.getKYCSettings(req, toRoutingKey(auth));

            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    GetKYCSettingsResponseBody resp = (GetKYCSettingsResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (status.isOK()) {
                        fraud.api.v1.GetKYCSettingsResponse gresp = any.unpack(fraud.api.v1.GetKYCSettingsResponse.class);

                        if (gresp.getKycMinAge() > 0) {
                            resp.setKycMinAge(gresp.getKycMinAge());
                        }

                        if (!gresp.getSoftKYCStatesSettingsList().isEmpty()) {
                            resp.setSoftKYCStates(Mappers.toKYCStateSettings(gresp.getSoftKYCStatesSettingsList()));
                        }
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
