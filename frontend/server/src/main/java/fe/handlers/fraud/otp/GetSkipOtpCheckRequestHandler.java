package fe.handlers.fraud.otp;

import java.util.Objects;

import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.cfg.ApplicationProperties;

import common.JwtTags;
import fe.api.OtpCheck;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetSkipOtpCheckRequest;
import fe.api.fraud.model.GetSkipOtpCheckResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.services.otp.OtpService;
import gateway.api.GenericInboundWrapper;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import uam.api.v1.Identity;
@Service
public class GetSkipOtpCheckRequestHandler extends AbstractFrontendRequestHandler<GetSkipOtpCheckRequest> {
    private final OtpService otpService;
    @Inject
    public GetSkipOtpCheckRequestHandler(ApplicationProperties props, OtpService otpService) {
        super(props, GetSkipOtpCheckRequest.class);
        this.otpService = Objects.requireNonNull(otpService);
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Jwt jwt = client.jwt().orElse(null);
        Identity identity = resolveIdentity(client, jwt).build();
        AsciiString key;
        if (jwt != null && jwt.getClaims() != null) {
            key = AsciiString.cached(jwt.getClaims().get(JwtTags.JWT_ROUTING_KEY_CLAIM, String.class));
            GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
            otpService.checkOtp(identity, key).addCallback(new FutureCallback<>() {
                @Override
                public void onSuccess(OtpCheck result) {
                    GetSkipOtpCheckResponseBody reply = (GetSkipOtpCheckResponseBody) respw.getBody();
                    reply.skipOtp(result.skipOtp());
                    reply.mandatory(result.isMandatory());
                    if (result.showOtp()) {
                        reply.trigger(GetSkipOtpCheckResponseBody.TriggerEnum.fromString(result.getOtpTriggerRule().name().toLowerCase()));
                    }
                    client.writeAndFlush(respw, api.v1.CacheControl.getDefaultInstance());
                }
                @Override
                public void onFailure(Throwable t) {
                    client.setFaultWriteAndFlush((GenericInboundWrapper) reqw, t);
                }
            }, MoreExecutors.directExecutor());
        }
    }
}
