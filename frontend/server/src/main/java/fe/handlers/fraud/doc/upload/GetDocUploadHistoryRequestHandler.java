package fe.handlers.fraud.doc.upload;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetDocUploadHistoryRequest;
import fe.api.fraud.model.GetDocUploadHistoryResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import fraud.api.v1.DocUploadHistoryItem;
import fraud.api.v1.DocUploadStatus;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GetDocUploadHistoryRequestHandler extends AbstractFrontendRequestHandler<GetDocUploadHistoryRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public GetDocUploadHistoryRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetDocUploadHistoryRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Throwable {
        long when = reqw.timestamp().getTime();
        Optional<Jwt> opt = client.jwt();
        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth).build();
            var routingKey = toRoutingKey(auth);

            var req = fraud.api.v1.GetDocUploadHistoryRequest.newBuilder()
                    .setIdentity(identity)
                    .build();

            var wrapped = fraudServiceApi.getDocUploadHistory(req, routingKey);
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Throwable {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    var resp = (GetDocUploadHistoryResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (any.is(fraud.api.v1.GetDocUploadHistoryResponse.class)) {
                        var innerResp = any.unpack(fraud.api.v1.GetDocUploadHistoryResponse.class);

                        if (status.isOK()) {
                            List<fe.api.fraud.model.DocUploadHistoryItem> uploadHistoryItems = innerResp.getItemsList().stream().map(new Function<DocUploadHistoryItem, fe.api.fraud.model.DocUploadHistoryItem>() {
                                @Override
                                public fe.api.fraud.model.DocUploadHistoryItem apply(DocUploadHistoryItem docUploadHistoryItem) {
                                    return fe.api.fraud.model.DocUploadHistoryItem.builder()
                                            .uploadedAt(OffsetDateTime.ofInstant(Instant.ofEpochMilli(docUploadHistoryItem.getUploadDateTime()), ZoneOffset.UTC))
                                            .docType(mapDocType(docUploadHistoryItem))
                                            .docTypeTitle(docUploadHistoryItem.getDocTypeTitle())
                                            .customDocType(docUploadHistoryItem.getCustomDocType())
                                            .uploadStatus(mapStatus(docUploadHistoryItem))
                                            .build();
                                }
                            }).collect(Collectors.toList());

                            resp.setItems(uploadHistoryItems);
                        }
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });

        } else {
            replyWithErrorAuth(client, reqw);
        }
    }

    private static fe.api.fraud.model.DocUploadHistoryItem.DocTypeEnum mapDocType(DocUploadHistoryItem docUploadHistoryItem) {
        return fe.api.fraud.model.DocUploadHistoryItem.DocTypeEnum.fromString(docUploadHistoryItem.getDocType().name());
    }

    private static fe.api.fraud.model.DocUploadHistoryItem.UploadStatusEnum mapStatus(DocUploadHistoryItem docUploadHistoryItem) {
        if (docUploadHistoryItem.getStatus() == DocUploadStatus.DOC_IN_REVIEW) {
            return fe.api.fraud.model.DocUploadHistoryItem.UploadStatusEnum.PENDING_REVIEW;
        }  else {
            return fe.api.fraud.model.DocUploadHistoryItem.UploadStatusEnum.fromString(docUploadHistoryItem.getStatus().name());
        }
    }
}
