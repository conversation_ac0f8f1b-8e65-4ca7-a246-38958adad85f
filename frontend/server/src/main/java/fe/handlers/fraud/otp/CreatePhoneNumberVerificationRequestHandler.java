package fe.handlers.fraud.otp;

import static common.utils.ProtoUtils.getOptionalValue;

import java.util.Optional;

import api.v1.ApiFactory;
import api.v1.Reason;
import com.turbospaces.api.facade.ResponseStatusFacade;
import fraud.api.v1.OtpSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import api.v1.Code;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.CreatePhoneNumberVerificationRequest;
import fe.api.fraud.model.CreatePhoneNumberVerificationResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import uam.api.UamServiceApi;

@Service
public class CreatePhoneNumberVerificationRequestHandler extends AbstractFrontendRequestHandler<CreatePhoneNumberVerificationRequest> {
    private final FraudServiceApi fraudServiceApi;
    private final UamServiceApi uamServiceApi;

    @Inject
    public CreatePhoneNumberVerificationRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi, UamServiceApi uamServiceApi) {
        super(props, CreatePhoneNumberVerificationRequest.class);
        this.fraudServiceApi = fraudServiceApi;
        this.uamServiceApi = uamServiceApi;
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        CreatePhoneNumberVerificationRequest req = (CreatePhoneNumberVerificationRequest) reqw.body();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();

            uam.api.v1.GetAccountInfoRequest accountInfoRequest = uam.api.v1.GetAccountInfoRequest.newBuilder()
                    .setIdentity(toIdentity(client, auth))
                    .build();

            var accountInfoResponse = uamServiceApi.getAccountInfo(accountInfoRequest, toRoutingKey(auth)).thenVerifyOk();
            accountInfoResponse.addListener(new SafeSocketResponseConsumer(accountInfoResponse, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade accountInfoWrapper) throws Exception {
                    var accountInfo = accountInfoWrapper.body().unpack(uam.api.v1.GetAccountInfoResponse.class).getInfo();

                    if (accountInfo.getLocked()) {
                        ApiFactory apiFactory = client.apiFactory();

                        ResponseStatusFacade fault = apiFactory.status(Code.ERR_FROZEN.name(),
                                Reason.ACCOUNT_LOCKED.getNumber(), "Your account has been locked, please contact {link_support}", null);
                        replyWithErrorStatus(fault, client, reqw);
                        return;
                    }

                    fraud.api.v1.CreatePhoneNumberVerificationRequest.Builder sreqb = fraud.api.v1.CreatePhoneNumberVerificationRequest.newBuilder();
                    sreqb.setChannel(req.getChannel());
                    sreqb.setPhoneNumber(StringUtils.trim(req.getPhoneNumber()));
                    if (StringUtils.isNotEmpty(req.getSession())) {
                        sreqb.setSession(req.getSession());
                    }
                    sreqb.setIdentity(toIdentity(client, auth));
                    sreqb.setCountry(accountInfo.getPersonalInfo().getCountry());
                    sreqb.setIsAdmin(accountInfo.getAdmin());
                    if (req.getSource() != null) {
                        sreqb.setSource(OtpSource.valueOf(req.getSource().name().toUpperCase()));
                    }
                    var phoneNumberVerification = fraudServiceApi.createPhoneNumberVerification(sreqb.build(), toRoutingKey(auth));
                    phoneNumberVerification.addListener(new SafeSocketResponseConsumer(phoneNumberVerification, client, reqw, ack) {
                        @Override
                        public void accept(ResponseWrapperFacade srespw) throws Exception {
                            GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                            var resp = (CreatePhoneNumberVerificationResponseBody) respw.getBody();

                            Any any = srespw.body();
                            setStatusAndMeta(respw, srespw, when);
                            var status = srespw.status();

                            if (any.is(fraud.api.v1.CreatePhoneNumberVerificationResponse.class)) {
                                var innerResp = any.unpack(fraud.api.v1.CreatePhoneNumberVerificationResponse.class);
                                getOptionalValue(innerResp.getOtpLimitResetTimestamp()).ifPresent(resp::setOtpLimitResetTimestamp);
                                if (status.isOK()) {
                                    if (innerResp.getErrorCode() != Code.ERR_OK) {
                                        respw.getStatus().errorCode = innerResp.getErrorCode().toString().toLowerCase().intern();
                                        respw.getStatus().errorReasonCode = innerResp.getErrorReasonCode().getNumber();
                                        if (StringUtils.isNotEmpty(innerResp.getErrorMessage())) {
                                            respw.getStatus().errorText = innerResp.getErrorMessage();
                                        }
                                    }
                                }
                            }

                            client.writeAndFlush(respw, srespw.cacheControl());
                        }
                    });
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
