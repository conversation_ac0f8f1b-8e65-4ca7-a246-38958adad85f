package fe.handlers.fraud.kyc;

import java.time.Instant;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.Optional;

import api.v1.ApplicationException;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.rpc.ApiResponse;
import api.v1.EnhancedApplicationException;
import fe.api.fraud.model.SetKYCInfoRequestBody;
import fe.api.fraud.model.SetKYCInfoResponseBody;
import fe.services.KYCAssertionService;
import fe.services.KycValidationException;
import fraud.api.FraudServiceApi;
import fraud.frontend.FraudFrontendPropertiesFragement;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import uam.api.UamServiceApi;
import uam.api.v1.GetAccountInfoRequest;
import uam.api.v1.GetAccountInfoResponse;
import uam.api.v1.GetAccountMetaRequest;
import uam.api.v1.GetAccountMetaResponse;
import uam.api.v1.Identity;
import uam.api.v1.KYCPhase;

@Slf4j
@Service
public class SetKYCInfoRequestHandler extends AbstractFrontendRequestHandler<fe.api.fraud.SetKYCInfoRequest> {
    private final FraudFrontendPropertiesFragement fragement;
    private final UamServiceApi uamServiceApi;
    private final FraudServiceApi fraudServiceApi;
    private final KYCAssertionService kycAssertionService;

    @Inject
    public SetKYCInfoRequestHandler(
            ApplicationProperties props,
            FraudFrontendPropertiesFragement fragement,
            UamServiceApi uamServiceApi,
            FraudServiceApi fraudServiceApi,
            KYCAssertionService kycAssertionService) {
        super(props, fe.api.fraud.SetKYCInfoRequest.class);
        this.fragement = fragement;
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
        this.fraudServiceApi = fraudServiceApi;
        this.kycAssertionService = kycAssertionService;
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();
        var req = (SetKYCInfoRequestBody) reqw.body();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth);
            var routing = toRoutingKey(auth);

            GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
            var accountInfoFuture = getAccountInfo(identity, routing);
            var accountMetaFuture = getAccountMeta(identity, routing);

            Futures.allAsList(accountInfoFuture, accountMetaFuture).addListener(() -> {
                try {
                    var accountInfo = accountInfoFuture.get().unpackAndVerifyOk().getInfo();
                    var signUpMethod = accountMetaFuture.get().unpackAndVerifyOk().getInfo().getSignUpMethod();

                    kycAssertionService.assertEligibleForKyc(accountInfo, signUpMethod);
                    kycAssertionService.assertSofKycDataPresent(accountInfo);

                    sendReqToFraud(client, reqw, ack, when, auth);
                } catch (KycValidationException e ) {
                    log.warn("Failed Kyc validation", e);
                    toErrorResponse(client, e, respw, when);
                } catch (Throwable t) {
                    log.error("Failed to get account info", t);
                    toErrorResponse(client, t, respw, when);
                }
            }, MoreExecutors.directExecutor());
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }

    private void toErrorResponse(RestEasyClient client, Throwable t, GenericOutboundWrapper respw, long when) {
        respw.getHeaders().took = System.currentTimeMillis() - when;
        respw.getStatus().errorText = "Failed to get account info";
        if (t instanceof KycValidationException kve) {
            respw.getStatus().errorCode = kve.getCode().toString().toLowerCase().intern();
            respw.getStatus().errorReasonCode = kve.getReason().getNumber();
        }
        if (t instanceof ApplicationException) {
            respw.getStatus().errorCode = ((ApplicationException) t).getCode().toString().toLowerCase().intern();
        }
        if (t instanceof EnhancedApplicationException eae) {
            respw.getStatus().errorReasonCode = eae.getReason().getNumber();
        }
        client.writeAndFlush(respw, api.v1.CacheControl.getDefaultInstance());
    }

    private void sendReqToFraud(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack, long when, Jwt auth) {
        var req = (SetKYCInfoRequestBody) reqw.body();

        var fraudReq = fraud.api.v1.SetKYCInfoRequest.newBuilder();
        fraudReq.setIdentity(toIdentity(client, auth));
        if (req.getPhase() != null) {
            fraudReq.setPhase(fraud.api.v1.KYCPhase.valueOf(req.getPhase().toUpperCase()));
        }
        var wrapped = fraudServiceApi.setKYCInfo(fraudReq.build(), toRoutingKey(auth));

        wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws Exception {
                GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                var resp = (SetKYCInfoResponseBody) respw.getBody();

                Any any = srespw.body();
                setStatusAndMeta(respw, srespw, when);
                var status = srespw.status();

                if (status.isOK()) {
                    fraud.api.v1.SetKYCInfoResponse sresp = any.unpack(fraud.api.v1.SetKYCInfoResponse.class);

                    if (sresp.getNextKYC() > 0) {
                        Instant instant = Instant.ofEpochMilli(sresp.getNextKYC());
                        resp.setNextKYC(OffsetDateTime.ofInstant(instant, ZoneOffset.UTC));
                    }
                    if (StringUtils.isNotEmpty(sresp.getRedirectUrl())) {
                        resp.setRedirectUrl(sresp.getRedirectUrl());
                    }
                    if (StringUtils.isNotEmpty(sresp.getCode())) {
                        resp.setTransactionReference(sresp.getCode());
                    }
                    if (StringUtils.isNotEmpty(sresp.getScanReference())) {
                        resp.setScanReference(sresp.getScanReference());
                    }
                    if (StringUtils.isNotEmpty(sresp.getStatus())) {
                        resp.setStatus(SetKYCInfoResponseBody.StatusEnum.fromValue(sresp.getStatus().toLowerCase()));
                    }
                    if (StringUtils.isNotEmpty(sresp.getErrorReason())) {
                        resp.setErrorReason(sresp.getErrorReason());
                    }
                }

                client.writeAndFlush(respw, srespw.cacheControl());
            }
        });
    }

    private ApiResponse<GetAccountInfoResponse> getAccountInfo(Identity.Builder identity, AsciiString routing) {
        var req = GetAccountInfoRequest.newBuilder().setIdentity(identity).build();
        return uamServiceApi.getAccountInfo(req, routing);
    }

    private ApiResponse<GetAccountMetaResponse> getAccountMeta(Identity.Builder identity, AsciiString routing) {
        var req = GetAccountMetaRequest.newBuilder().setIdentity(identity).build();
        return uamServiceApi.getAccountMeta(req, routing);
    }
}
