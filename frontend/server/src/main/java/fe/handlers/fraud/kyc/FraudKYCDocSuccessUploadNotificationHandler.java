package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import fe.api.SocketServer;
import fe.api.fraud.model.KYCDocSuccessUploadNotification;
import fe.handlers.AbstractNotificationMessageHandler;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FraudKYCDocSuccessUploadNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.KYCDocSuccessUploadNotification> {
    @Inject
    public FraudKYCDocSuccessUploadNotificationHandler() {
        super(fraud.api.v1.KYCDocSuccessUploadNotification.class);
    }

    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.KYCDocSuccessUploadNotification kycdsun = body.unpack(fraud.api.v1.KYCDocSuccessUploadNotification.class);

        // ~ map
        KYCDocSuccessUploadNotification payload = new KYCDocSuccessUploadNotification();

        var account = kycdsun.getAccount();
        // ~ send out
        sendNotification(server, AsciiString.cached(account.getRoutingKey()), account.getAccountId(), payload);
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, KYCDocSuccessUploadNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send Kyc doc success upload notification", e);
        }
    }
}
