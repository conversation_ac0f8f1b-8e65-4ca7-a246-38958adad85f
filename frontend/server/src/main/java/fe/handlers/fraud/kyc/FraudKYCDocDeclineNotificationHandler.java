package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import fe.api.SocketServer;
import fe.handlers.AbstractNotificationMessageHandler;
import fe.api.fraud.model.KYCDocDeclineNotification;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static uam.model.LockReason.LOCKED_FAILED_KYC;

@Service
@Slf4j
public class FraudKYCDocDeclineNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.KYCDocDeclineNotification> {
    @Inject
    public FraudKYCDocDeclineNotificationHandler() {
        super(fraud.api.v1.KYCDocDeclineNotification.class);
    }
    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.KYCDocDeclineNotification kycddn = body.unpack(fraud.api.v1.KYCDocDeclineNotification.class);

        // ~ map
        KYCDocDeclineNotification payload = new KYCDocDeclineNotification();
        payload.setReason(LOCKED_FAILED_KYC.name());

        var account = kycddn.getAccount();
        // ~ send out
        sendNotification(server, AsciiString.cached(account.getRoutingKey()), account.getAccountId(), payload);
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, KYCDocDeclineNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send Kyc doc decline notification", e);
        }
    }
}
