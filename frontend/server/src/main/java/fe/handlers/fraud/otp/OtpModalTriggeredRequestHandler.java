package fe.handlers.fraud.otp;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import fe.RenameMeLaterFrontendServerProperties;
import fe.api.fraud.model.OtpModalTriggeredRequestBody;
import fraud.api.v1.OtpSource;
import fraud.api.v1.OtpTrigger;
import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.GeneratedMessageV3;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.ApiResponseEntity;

import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.OtpModalTriggeredRequest;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.mappers.Mappers;
import fe.services.otp.OtpService;
import fraud.api.FraudServiceApi;
import fraud.api.v1.OtpTriggeredRequest;
import gateway.api.GenericInboundWrapper;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class OtpModalTriggeredRequestHandler extends AbstractFrontendRequestHandler<OtpModalTriggeredRequest> {
    private final FraudServiceApi fraudServiceApi;
    private final OtpService otpService;

    @Inject
    public OtpModalTriggeredRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi, OtpService otpService) {
        super(props, OtpModalTriggeredRequest.class);
        this.fraudServiceApi = Objects.requireNonNull(fraudServiceApi);
        this.otpService = Objects.requireNonNull(otpService);
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            AsciiString routingKey = toRoutingKey(auth);
            var identity = toIdentity(client, auth).build();

            var req = (OtpModalTriggeredRequestBody) reqw.body();

            OtpTriggeredRequest.Builder fraudRequest = OtpTriggeredRequest.newBuilder();
            fraudRequest.setIdentity(identity);
            enhanceRequestWithOtpCheckInfo(fraudRequest, routingKey, req).addListener(() -> {
                var fraudFuture = fraudServiceApi.otpModalTriggered(fraudRequest.build(), routingKey).thenVerifyOk();

                FluentFuture.from(fraudFuture).addCallback(new FutureCallback<Object>() {
                    @SneakyThrows
                    @Override
                    public void onSuccess(Object result) {
                        GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                        ApiResponseEntity<? extends GeneratedMessageV3> fraudResult = fraudFuture.get();

                        setStatusAndMeta(respw, fraudResult, when);
                        client.writeAndFlush(respw, fraudResult.cacheControl());
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        log.error("Failed to process fraud and uam OtpModalTriggered requests", t);
                        client.setFaultWriteAndFlush((GenericInboundWrapper) reqw, t);
                    }
                }, MoreExecutors.directExecutor());
            }, MoreExecutors.directExecutor());
        }
    }

    private ListenableFuture<Void> enhanceRequestWithOtpCheckInfo(
            OtpTriggeredRequest.Builder innerRequest,
            AsciiString routingKey,
            OtpModalTriggeredRequestBody req) {
        if (Boolean.TRUE.equals(req.getSetUpNotifications())) {
            innerRequest.setOtpTrigger(OtpTrigger.SET_UP_NOTIFICATIONS);
            if (req.getSource() != null) {
                innerRequest.setSource(OtpSource.valueOf(req.getSource().name().toUpperCase()));
            }
            return Futures.immediateFuture(null);
        } else if (Boolean.TRUE.equals(req.getAccountClosure())) {
            innerRequest.setOtpTrigger(OtpTrigger.ACCOUNT_CLOSURE);
            if (req.getSource() != null) {
                innerRequest.setSource(OtpSource.valueOf(req.getSource().name().toUpperCase()));
            }
            return Futures.immediateFuture(null);
        } else {
            var timeout = ((RenameMeLaterFrontendServerProperties) props).REQUEST_REPLY_TIMEOUT.map(Duration::ofSeconds).get();

            return Futures.transform(otpService.checkOtpForOtpModalTriggered(innerRequest.getIdentity(), timeout, routingKey),
                    otpCheck -> {
                        if (otpCheck != null && otpCheck.showOtp()) {
                            innerRequest.setOtpTrigger(Mappers.otpTrigger(otpCheck.getOtpTriggerRule()));
                        }
                        return null;
                    }, MoreExecutors.directExecutor());
        }

    }
}
