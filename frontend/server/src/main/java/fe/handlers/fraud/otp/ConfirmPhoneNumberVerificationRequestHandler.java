package fe.handlers.fraud.otp;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.ConfirmPhoneNumberVerificationRequest;
import fe.api.fraud.model.ConfirmPhoneNumberVerificationResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;

@Service
public class ConfirmPhoneNumberVerificationRequestHandler extends AbstractFrontendRequestHandler<ConfirmPhoneNumberVerificationRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public ConfirmPhoneNumberVerificationRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, ConfirmPhoneNumberVerificationRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        ConfirmPhoneNumberVerificationRequest req = (ConfirmPhoneNumberVerificationRequest) reqw.body();
        long when = reqw.timestamp().getTime();

        fraud.api.v1.ConfirmPhoneNumberVerificationRequest.Builder sreqb = fraud.api.v1.ConfirmPhoneNumberVerificationRequest.newBuilder();
        sreqb.setOtp(StringUtils.trim(req.getOtp()));
        sreqb.setPhoneNumber(StringUtils.trim(req.getPhoneNumber()));
        if (StringUtils.isNotEmpty(req.getSession())) {
            sreqb.setSession(req.getSession());
        }

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            sreqb.setIdentity(toIdentity(client, auth));

            var wrapped = fraudServiceApi.confirmPhoneNumberVerification(sreqb.build(), toRoutingKey(auth));
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    ConfirmPhoneNumberVerificationResponseBody resp = (ConfirmPhoneNumberVerificationResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (status.isOK()) {
                        fraud.api.v1.ConfirmPhoneNumberVerificationResponse sresp = any.unpack(fraud.api.v1.ConfirmPhoneNumberVerificationResponse.class);
                        resp.setVerified(sresp.getVerified());
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
