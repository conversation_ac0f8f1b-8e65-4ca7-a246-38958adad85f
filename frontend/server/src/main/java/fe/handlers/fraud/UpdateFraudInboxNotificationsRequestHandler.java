package fe.handlers.fraud;

import api.v1.Code;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.UpdateFraudInboxNotificationRequest;
import fe.api.fraud.model.InboxNotification;
import fe.api.fraud.model.UpdateFraudInboxNotificationRequestBody;
import fe.api.fraud.model.UpdateFraudInboxNotificationResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import fraud.api.v1.InboxNotificationInfo;
import fraud.api.v1.InboxNotificationStatus;
import fraud.api.v1.UpdateFraudInboxNotificationsRequest;
import fraud.api.v1.UpdateFraudInboxNotificationsResponse;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class UpdateFraudInboxNotificationsRequestHandler extends AbstractFrontendRequestHandler<UpdateFraudInboxNotificationRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public UpdateFraudInboxNotificationsRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, UpdateFraudInboxNotificationRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        UpdateFraudInboxNotificationRequestBody req = (UpdateFraudInboxNotificationRequestBody) reqw.body();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            fraud.api.v1.UpdateFraudInboxNotificationsRequest.Builder sreqb = UpdateFraudInboxNotificationsRequest.newBuilder();
            sreqb.setToken(req.getNotificationToken());
            sreqb.setStatus(InboxNotificationStatus.valueOf(req.getStatus().name().toUpperCase()));
            sreqb.setIdentity(toIdentity(client, auth));

            AsciiString routingKey = toRoutingKey(auth);
            var wrapped = fraudServiceApi.updateInboxNotification(sreqb.build(), routingKey);
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    UpdateFraudInboxNotificationResponseBody resp = (UpdateFraudInboxNotificationResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();
                    if (status.errorCode() == Code.ERR_OK) {
                        UpdateFraudInboxNotificationsResponse sresp = any.unpack(UpdateFraudInboxNotificationsResponse.class);

                        InboxNotificationInfo notification = sresp.getNotification();
                        resp.setNotification(InboxNotification.builder()
                                .title(notification.getTitle())
                                .message(notification.getMessage())
                                .createdAt(notification.getCreatedAt())
                                .categoryCode(notification.getCategoryCode())
                                .status(InboxNotification.StatusEnum.valueOf(notification.getStatus().toUpperCase()))
                                .id(notification.getId())
                                .build());
                    }
                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
