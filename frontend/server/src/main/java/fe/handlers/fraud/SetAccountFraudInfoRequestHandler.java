package fe.handlers.fraud;

import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.model.SetAccountFraudInfoRequestBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import fraud.api.v1.SetAccountFraudInfoRequest;
import fraud.api.v1.UpdateSeonSession;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.netty.util.AsciiString;

@Service
public class SetAccountFraudInfoRequestHandler extends AbstractFrontendRequestHandler<fe.api.fraud.SetAccountFraudInfoRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Autowired
    public SetAccountFraudInfoRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, fe.api.fraud.SetAccountFraudInfoRequest.class);
        this.fraudServiceApi = Objects.requireNonNull(fraudServiceApi);
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        SetAccountFraudInfoRequestBody req = (SetAccountFraudInfoRequestBody) reqw.body();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            AsciiString routingKey = toRoutingKey(auth);

            var identity = toIdentity(client, auth);

            var sreq = SetAccountFraudInfoRequest.newBuilder();
            sreq.setIdentity(identity);
            sreq.setSeonSession(req.getSeonSession());
            sreq.setUpdateSeonSession(UpdateSeonSession.newBuilder().build());
            var wrapped = fraudServiceApi.setAccountFraudInfo(sreq.build(), routingKey);

            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    setStatusAndMeta(respw, srespw, when);
                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
