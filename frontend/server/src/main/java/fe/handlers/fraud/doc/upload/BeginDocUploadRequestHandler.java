package fe.handlers.fraud.doc.upload;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.BeginDocUploadRequest;
import fe.api.fraud.model.BeginDocUploadRequestBody;
import fe.api.fraud.model.BeginDocUploadResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.mappers.Mappers;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class BeginDocUploadRequestHandler extends AbstractFrontendRequestHandler<BeginDocUploadRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public BeginDocUploadRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, BeginDocUploadRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Throwable {
        long when = reqw.timestamp().getTime();
        var request = (BeginDocUploadRequestBody) reqw.body();
        var docType = request.getDocType();
        var customDocType = request.getCustomDocType();
        Optional<Jwt> opt = client.jwt();
        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth).build();
            var routingKey = toRoutingKey(auth);

            var fraudReq = fraud.api.v1.BeginDocUploadRequest.newBuilder()
                    .setIdentity(identity)
                    .setDocType(Mappers.toDocType(docType));
            if (customDocType != null) {
                fraudReq.setCustomDocType(customDocType);
            }

            var wrapped = fraudServiceApi.beginDocUpload(fraudReq.build(), routingKey);
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Throwable {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    var resp = (BeginDocUploadResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (any.is(fraud.api.v1.BeginDocUploadResponse.class)) {
                        var innerResp = any.unpack(fraud.api.v1.BeginDocUploadResponse.class);
                        if (status.isOK()) {
                            resp.setUploadUrl(innerResp.getUploadUrl());
                        }
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
