package fe.handlers.fraud.doc.upload;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import fe.api.SocketServer;
import fe.api.fraud.model.DocUploadCompletedNotification;
import fe.handlers.AbstractNotificationMessageHandler;
import fraud.api.v1.DocUploadStatus;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DocUploadCompletedNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.DocUploadCompletedNotification> {

    @Inject
    public DocUploadCompletedNotificationHandler() {
        super(fraud.api.v1.DocUploadCompletedNotification.class);
    }

    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        var notification = body.unpack(fraud.api.v1.DocUploadCompletedNotification.class);
        var fraudRouting = notification.getRouting();
        AsciiString routingKey = AsciiString.cached(fraudRouting.getHash());

        DocUploadCompletedNotification payload = new DocUploadCompletedNotification();
        payload.setStatus(resolveStatus(notification.getStatus()));

        // ~ send out
        try {
            server.broadcastByAccount(routingKey, fraudRouting.getId(), wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send doc upload completed notification", e);
        }
    }

    private DocUploadCompletedNotification.StatusEnum resolveStatus(DocUploadStatus internalStatus) {
        return switch (internalStatus) {
            case PENDING_REVIEW -> DocUploadCompletedNotification.StatusEnum.PENDING_REVIEW;
            case FAILED -> DocUploadCompletedNotification.StatusEnum.FAILED;
            case SESSION_EXPIRED -> DocUploadCompletedNotification.StatusEnum.SESSION_EXPIRED;
            default -> {
                log.error("Unexpected doc upload status");
                yield DocUploadCompletedNotification.StatusEnum.FAILED;
            }
        };
    }
}
