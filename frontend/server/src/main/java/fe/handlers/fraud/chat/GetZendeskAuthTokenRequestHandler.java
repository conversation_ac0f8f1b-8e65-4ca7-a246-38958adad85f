package fe.handlers.fraud.chat;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetZendeskAuthTokenRequest;
import fe.api.fraud.model.GetZendeskAuthTokenResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import fraud.api.v1.GetZendeskAccountAuthTokenResponse;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

@Service
public class GetZendeskAuthTokenRequestHandler extends AbstractFrontendRequestHandler<GetZendeskAuthTokenRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    protected GetZendeskAuthTokenRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetZendeskAuthTokenRequest.class);
        this.fraudServiceApi = Objects.requireNonNull(fraudServiceApi);
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Throwable {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            AsciiString routingKey = toRoutingKey(auth);
            var identity = toIdentity(client, auth).build();
            var sreq = fraud.api.v1.GetZendeskAccountAuthTokenRequest.newBuilder()
                    .setIdentity(identity)
                    .build();
            var wrapped = fraudServiceApi.getZendeskAccountAuthToken(sreq, routingKey);

            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    GetZendeskAuthTokenResponseBody resp = (GetZendeskAuthTokenResponseBody) respw.getBody();

                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (status.isOK()) {
                        Any any = srespw.body();
                        if (any.is(GetZendeskAccountAuthTokenResponse.class)) {
                            resp.setJwt(any.unpack(GetZendeskAccountAuthTokenResponse.class).getAuthToken());
                        }
                    }
                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
