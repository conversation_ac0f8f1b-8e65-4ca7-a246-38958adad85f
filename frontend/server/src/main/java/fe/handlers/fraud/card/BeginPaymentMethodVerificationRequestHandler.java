package fe.handlers.fraud.card;

import static common.utils.ProtoUtils.applyIfNotEmpty;
import static org.apache.commons.lang3.StringUtils.isBlank;

import java.util.Objects;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.model.BeginPaymentMethodVerificationRequestBody;
import fe.api.fraud.model.BeginPaymentMethodVerificationResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import fraud.api.v1.BeginCardVerificationRequest;
import fraud.api.v1.BeginCardVerificationResponse;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;

@Service
public class BeginPaymentMethodVerificationRequestHandler extends AbstractFrontendRequestHandler<fe.api.fraud.BeginPaymentMethodVerificationRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public BeginPaymentMethodVerificationRequestHandler(
            ApplicationProperties props,
            FraudServiceApi fraudServiceApi) {
        super(props, fe.api.fraud.BeginPaymentMethodVerificationRequest.class);
        this.fraudServiceApi = Objects.requireNonNull(fraudServiceApi);
        this.props = Objects.requireNonNull(props);
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        if (opt.isPresent()) {
            var req = (BeginPaymentMethodVerificationRequestBody) reqw.body();
            if (isBlank(req.getFingerprint())) {
                replyWithBadRequest(client, reqw);
                return;
            }
            Jwt auth = opt.get();
            var reqBuilder = BeginCardVerificationRequest.newBuilder()
                    .setIdentity(toIdentity(client, auth))
                    .setFingerprint(req.getFingerprint());
            var wrapped = fraudServiceApi.beginCardVerification(reqBuilder.build(), toRoutingKey(auth));
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    var respw = (GenericOutboundWrapper) reqw.toReply();
                    var resp = (BeginPaymentMethodVerificationResponseBody) respw.getBody();

                    setStatusAndMeta(respw, srespw, reqw.timestamp().getTime());
                    var status = srespw.status();

                    if (status.isOK()) {
                        Any any = srespw.body();
                        var fraudResp = any.unpack(BeginCardVerificationResponse.class);
                        applyIfNotEmpty(fraudResp.getRedirectUrl(), resp::setRedirectUrl);
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });

        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
