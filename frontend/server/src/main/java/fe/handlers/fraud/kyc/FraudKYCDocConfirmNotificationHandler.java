package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import fe.api.SocketServer;
import fe.handlers.AbstractNotificationMessageHandler;
import fe.api.fraud.model.KYCDocConfirmNotification;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FraudKYCDocConfirmNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.KYCDocConfirmNotification> {
    @Inject
    public FraudKYCDocConfirmNotificationHandler() {
        super(fraud.api.v1.KYCDocConfirmNotification.class);
    }
    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.KYCDocConfirmNotification kycdcn = body.unpack(fraud.api.v1.KYCDocConfirmNotification.class);

        // ~ map
        KYCDocConfirmNotification payload = new KYCDocConfirmNotification();

        var account = kycdcn.getAccount();
        // ~ send out
        sendNotification(server, AsciiString.cached(account.getRoutingKey()), account.getAccountId(), payload);
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, KYCDocConfirmNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send Kyc doc confirm notification", e);
        }
    }
}
