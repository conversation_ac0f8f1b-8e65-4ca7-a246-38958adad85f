package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetConfirmedKYCInfoRequest;
import fe.api.fraud.model.GetConfirmedKYCInfoResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.mappers.Mappers;
import fraud.api.FraudServiceApi;
import fraud.api.v1.GetConfirmedKYCInfoResponse;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Slf4j
@Service
public class GetConfirmedKYCInfoRequestHandler extends AbstractFrontendRequestHandler<GetConfirmedKYCInfoRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public GetConfirmedKYCInfoRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetConfirmedKYCInfoRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Throwable {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();

            var innerReq = fraud.api.v1.GetConfirmedKYCInfoRequest.newBuilder()
                    .setIdentity(toIdentity(client, auth))
                    .build();

            var wrapped = fraudServiceApi.getConfirmedKYCInfo(innerReq, toRoutingKey(auth));
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    var respw = (GenericOutboundWrapper) reqw.toReply();
                    var resp = (GetConfirmedKYCInfoResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (status.isOK()) {
                        var innerResp = any.unpack(GetConfirmedKYCInfoResponse.class);

                        resp.setKyc(innerResp.getKyc());
                        resp.setKycStatus(Mappers.toKycStatusEnum(innerResp.getKycStatus()));

                        if (innerResp.hasKycInfo()) {
                            resp.setKycInfo(Mappers.toKycInfo(innerResp.getKycInfo()));
                        }
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
