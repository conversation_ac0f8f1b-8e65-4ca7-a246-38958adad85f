package fe.handlers.fraud.doc.upload;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetDocUploadInfoRequest;
import fe.api.fraud.model.GetDocUploadInfoResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.mappers.Mappers;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GetDocUploadInfoRequestHandler extends AbstractFrontendRequestHandler<GetDocUploadInfoRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public GetDocUploadInfoRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetDocUploadInfoRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Throwable {
        long when = reqw.timestamp().getTime();
        Optional<Jwt> opt = client.jwt();
        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth).build();
            var routingKey = toRoutingKey(auth);

            var req = fraud.api.v1.GetDocUploadInfoRequest.newBuilder()
                    .setIdentity(identity)
                    .build();

            var wrapped = fraudServiceApi.getDocUploadInfo(req, routingKey);
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Throwable {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    var resp = (GetDocUploadInfoResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (any.is(fraud.api.v1.GetDocUploadInfoResponse.class)) {
                        var innerResp = any.unpack(fraud.api.v1.GetDocUploadInfoResponse.class);
                        if (status.isOK()) {
                            var supportedDocs = innerResp.getSupportedDocumentList().stream()
                                    .map(Mappers::toDocType)
                                    .collect(Collectors.toList());
                            resp.setTypes(supportedDocs);
                            resp.setUploadedDocsCount(innerResp.getUploadedDocsCount());
                            resp.setMaxUploadDocsLimit(innerResp.getMaxUploadDocsLimit());
                        }
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });

        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
