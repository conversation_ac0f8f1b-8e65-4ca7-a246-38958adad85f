package fe.handlers.fraud.inbox;

import java.util.Optional;

import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.FutureCallback;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import com.turbospaces.rpc.ApiResponseEntity;

import fe.api.SocketServer;
import fe.api.fraud.model.InboxNotification;
import fe.handlers.AbstractNotificationMessageHandler;
import fraud.api.FraudServiceApi;
import fraud.api.v1.GetFraudInboxNotificationsResponse;
import fraud.api.v1.InboxNotificationInfo;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import uam.api.v1.Identity;
import uam.api.v1.IdentityByAccountId;

@Service
@Slf4j
public class InboxNotificationsUpdatedNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.InboxUpdateNotification> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public InboxNotificationsUpdatedNotificationHandler(FraudServiceApi fraudServiceApi) {
        super(fraud.api.v1.InboxUpdateNotification.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.InboxUpdateNotification inup = body.unpack(fraud.api.v1.InboxUpdateNotification.class);
        log.debug("Received InboxUpdateNotification: {}", inup);

        // ~ map
        var identity = Identity.newBuilder()
                .setByAccountId(IdentityByAccountId.newBuilder().setAccountId(inup.getAccount().getAccountId()).build());
        var req = fraud.api.v1.GetFraudInboxNotificationsRequest.newBuilder()
                .setIdentity(identity)
                .build();
        var routingKey = AsciiString.cached(inup.getAccount().getRoutingKey());

        fraudServiceApi.getInboxNotifications(req, routingKey)
                .addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(ApiResponseEntity<GetFraudInboxNotificationsResponse> result) {
                        try {
                            GetFraudInboxNotificationsResponse getFraudInboxNotificationsResponse = result.unpackAndVerifyOk();
                            log.debug("getFraudInboxNotificationsResponse : {}", getFraudInboxNotificationsResponse);
                            Optional<InboxNotificationInfo> notificationInfo = getFraudInboxNotificationsResponse.getNotificationsList()
                                    .stream()
                                    .filter(inboxNotificationInfo -> inboxNotificationInfo.getToken().equals(inup.getToken()))
                                    .findFirst();

                            if (notificationInfo.isPresent()) {
                                InboxNotification payload = new InboxNotification();
                                payload.setCategoryCode(notificationInfo.get().getCategoryCode());
                                payload.setStatus(InboxNotification.StatusEnum.fromValue(notificationInfo.get().getStatus()));
                                payload.setCreatedAt(notificationInfo.get().getCreatedAt());
                                payload.setTitle(notificationInfo.get().getTitle());
                                payload.setMessage(notificationInfo.get().getMessage());

                                log.debug("Sending InboxUpdateNotification for account: {}, token: {}", inup.getAccount().getRoutingKey(), inup.getToken());
                                sendNotification(server, AsciiString.cached(inup.getAccount().getRoutingKey()), inup.getAccount().getAccountId(), payload);
                            }
                        } catch (Exception e) {
                            log.error("Error processing InboxUpdateNotification", e);
                        }
                    }

                    @Override
                    public void onFailure(Throwable t) {
                        log.error("Failed to get inbox notifications for account: {}, token: {}", inup.getAccount().getRoutingKey(), inup.getToken(), t);
                    }
                });
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, InboxNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send Kyc block notification", e);
        }
    }
}
