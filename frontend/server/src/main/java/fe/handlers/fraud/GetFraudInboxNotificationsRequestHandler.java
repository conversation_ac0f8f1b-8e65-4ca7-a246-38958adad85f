package fe.handlers.fraud;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;
import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetFraudInboxNotificationsRequest;
import fe.api.fraud.model.GetFraudInboxNotificationsResponseBody;
import fe.api.fraud.model.InboxNotification;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GetFraudInboxNotificationsRequestHandler extends AbstractFrontendRequestHandler<GetFraudInboxNotificationsRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public GetFraudInboxNotificationsRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetFraudInboxNotificationsRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth).build();
            var routingKey = toRoutingKey(auth);

            var req = fraud.api.v1.GetFraudInboxNotificationsRequest.newBuilder()
                    .setIdentity(identity)
                    .build();

            var wrapped = fraudServiceApi.getInboxNotifications(req, routingKey);

            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Throwable {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    var resp = (GetFraudInboxNotificationsResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (any.is(fraud.api.v1.GetFraudInboxNotificationsResponse.class) && status.isOK()) {
                        var innerResp = any.unpack(fraud.api.v1.GetFraudInboxNotificationsResponse.class);
                        List<InboxNotification> notifications = innerResp.getNotificationsList().stream()
                                .map(notification -> InboxNotification.builder()
                                        .title(notification.getTitle())
                                        .message(notification.getMessage())
                                        .createdAt(notification.getCreatedAt())
                                        .categoryCode(notification.getCategoryCode())
                                        .status(InboxNotification.StatusEnum.fromValue(notification.getStatus().toLowerCase()))
                                        .id(notification.getToken())
                                        .token(notification.getToken())
                                        .build())
                                .collect(Collectors.toList());
                        resp.notifications(notifications);
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
