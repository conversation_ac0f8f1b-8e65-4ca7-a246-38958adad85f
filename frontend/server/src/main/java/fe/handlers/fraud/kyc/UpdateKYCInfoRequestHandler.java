package fe.handlers.fraud.kyc;

import java.util.Objects;
import java.util.Optional;
import api.v1.ApplicationException;
import api.v1.EnhancedApplicationException;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.rpc.ApiResponse;
import fe.api.fraud.model.UpdateKYCInfoRequestBody;
import fe.services.KYCAssertionService;
import fe.services.KycValidationException;
import fraud.api.FraudServiceApi;
import fraud.frontend.FraudFrontendPropertiesFragement;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import uam.api.UamServiceApi;
import uam.api.v1.GetAccountInfoRequest;
import uam.api.v1.GetAccountInfoResponse;
import uam.api.v1.GetAccountMetaRequest;
import uam.api.v1.GetAccountMetaResponse;
import uam.api.v1.Identity;

@Slf4j
@Service
public class UpdateKYCInfoRequestHandler extends AbstractFrontendRequestHandler<fe.api.fraud.UpdateKYCInfoRequest> {
    private final FraudFrontendPropertiesFragement fragement;
    private final FraudServiceApi fraudServiceApi;
    private final UamServiceApi uamServiceApi;
    private final KYCAssertionService kycAssertionService;

    @Inject
    public UpdateKYCInfoRequestHandler(
            ApplicationProperties props,
            FraudFrontendPropertiesFragement fragement,
            FraudServiceApi fraudServiceApi,
            UamServiceApi uamServiceApi,
            KYCAssertionService kycAssertionService) {
        super(props, fe.api.fraud.UpdateKYCInfoRequest.class);
        this.fragement = fragement;
        this.fraudServiceApi = fraudServiceApi;
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
        this.kycAssertionService = kycAssertionService;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();
        var req = (UpdateKYCInfoRequestBody) reqw.body();
        var provider = req.getProvider();
        var transactionReference = req.getTransactionReference();

        if (StringUtils.isEmpty(provider) || StringUtils.isEmpty(transactionReference)) {
            replyWithBadRequest(client, reqw);
            return;
        }

        if (opt.isPresent()) {
            Jwt auth = opt.get();

            GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
            AsciiString routing = toRoutingKey(auth);
            Identity.Builder identity = toIdentity(client, auth);

            var accountInfoFuture = getAccountInfo(identity, routing);
            var accountMetaFuture = getAccountMeta(identity, routing);

            Futures.allAsList(accountInfoFuture, accountMetaFuture).addListener(() -> {
                try {
                    var accountInfo = accountInfoFuture.get().unpackAndVerifyOk().getInfo();
                    var accountMeta = accountMetaFuture.get().unpackAndVerifyOk().getInfo();

                    kycAssertionService.assertPlayable(accountInfo, accountMeta.getSignUpMethod());

                    fraud.api.v1.UpdateKYCInfoRequest.Builder fraudReq = fraud.api.v1.UpdateKYCInfoRequest.newBuilder();
                    fraudReq.setIdentity(identity);
                    fraudReq.setProvider(fraud.api.v1.KYCProvider.valueOf(provider.toUpperCase()));
                    fraudReq.setTransactionReference(transactionReference);

                    var wrapped = fraudServiceApi.updateKycInfo(fraudReq.build(), routing);

                    wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                        @Override
                        public void accept(ResponseWrapperFacade srespw) throws Exception {
                            GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                            setStatusAndMeta(respw, srespw, when);
                            client.writeAndFlush(respw, srespw.cacheControl());
                        }
                    });
                } catch (KycValidationException e) {
                    log.warn("Failed Kyc validation", e);
                    toErrorResponse(client, e, respw, when);
                } catch (Exception e) {
                    log.error("Failed to process UpdateKYCInfoRequest", e);
                    toErrorResponse(client, e, respw, when);
                }
            }, MoreExecutors.directExecutor());
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }

    private ApiResponse<GetAccountInfoResponse> getAccountInfo(Identity.Builder identity, AsciiString routing) {
        var req = GetAccountInfoRequest.newBuilder().setIdentity(identity).build();
        return uamServiceApi.getAccountInfo(req, routing);
    }

    private ApiResponse<GetAccountMetaResponse> getAccountMeta(Identity.Builder identity, AsciiString routing) {
        var req = GetAccountMetaRequest.newBuilder().setIdentity(identity).build();
        return uamServiceApi.getAccountMeta(req, routing);
    }

    private void toErrorResponse(RestEasyClient client, Throwable t, GenericOutboundWrapper respw, long when) {
        respw.getHeaders().took = System.currentTimeMillis() - when;
        respw.getStatus().errorText = "Failed to get account info";
        if (t instanceof KycValidationException kve) {
            respw.getStatus().errorCode = kve.getCode().toString().toLowerCase().intern();
            respw.getStatus().errorReasonCode = kve.getReason().getNumber();
        }
        client.writeAndFlush(respw, api.v1.CacheControl.getDefaultInstance());
    }
}
