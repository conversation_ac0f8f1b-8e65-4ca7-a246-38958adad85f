package fe.handlers.fraud.chat;

import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.cfg.ApplicationProperties;

import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetLiveChatSettingsRequest;
import fe.api.fraud.model.ChatDepartment;
import fe.api.fraud.model.GetLiveChatSettingsResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fe.services.chat.LiveChatService;
import fe.services.chat.LiveChatSettings;
import gateway.api.GenericInboundWrapper;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GetLiveChatSettingsRequestHandler extends AbstractFrontendRequestHandler<GetLiveChatSettingsRequest> {
    private final LiveChatService liveChatService;

    @Inject
    public GetLiveChatSettingsRequestHandler(ApplicationProperties props, LiveChatService liveChatService) {
        super(props, GetLiveChatSettingsRequest.class);
        this.liveChatService = Objects.requireNonNull(liveChatService);
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
        GetLiveChatSettingsResponseBody resp = (GetLiveChatSettingsResponseBody) respw.getBody();
        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth).build();
            var routingKey = toRoutingKey(auth);
            liveChatService.getLiveChatSettings(identity, routingKey).addCallback(new FutureCallback<>() {
                @Override
                public void onSuccess(LiveChatSettings liveChatSettings) {
                    resp.showLiveChat(liveChatSettings.showChat());
                    resp.vip(liveChatSettings.isVip());
                    liveChatSettings.getTag().ifPresent(resp::tag);
                    if (CollectionUtils.isNotEmpty(liveChatSettings.getTags())) {
                        resp.tags(liveChatSettings.getTags());
                    }
                    liveChatSettings.getTag().ifPresent(resp::addTagsItem);
                    liveChatSettings.getDepartment().ifPresent(d -> {
                        resp.departments(ChatDepartment.builder()
                                .enabled(liveChatSettings.getEnabledDepartments())
                                .select(d)
                                .build());
                    });
                    resp.setPurchaseFlowEnabled(liveChatSettings.isPurchaseFlowEnabled());
                    resp.setEmail(liveChatSettings.getEmail());
                    resp.setExternalId(liveChatSettings.getExternalId());
                    client.writeAndFlush(respw, api.v1.CacheControl.getDefaultInstance());
                }
                @Override
                public void onFailure(Throwable t) {
                    log.error("Unable to collect live chat info", t);
                    client.setFaultWriteAndFlush((GenericInboundWrapper) reqw, t);
                }
            }, MoreExecutors.directExecutor());
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
