package fe.handlers.fraud.kyc;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.Optional;

import fe.api.fraud.GetKYCInfoRequest;
import fe.api.fraud.model.GetKYCInfoResponseBody;
import fraud.api.FraudServiceApi;
import fraud.frontend.FraudFrontendPropertiesFragement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.google.protobuf.Timestamp;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import uam.api.UamServiceApi;

@Slf4j
@Service
public class GetKYCInfoRequestHandler extends AbstractFrontendRequestHandler<GetKYCInfoRequest> {
    private final FraudFrontendPropertiesFragement fragement;
    private final FraudServiceApi fraudServiceApi;
    private final UamServiceApi uamServiceApi;

    @Inject
    public GetKYCInfoRequestHandler(
            ApplicationProperties props,
            FraudFrontendPropertiesFragement fragement,
            FraudServiceApi fraudServiceApi,
            UamServiceApi uamServiceApi) {
        super(props, GetKYCInfoRequest.class);
        this.fragement = fragement;
        this.fraudServiceApi = fraudServiceApi;
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        GetKYCInfoRequest req = (GetKYCInfoRequest) reqw.body();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();

            sendReqToFraud(req, client, reqw, ack, when, auth);
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }

    private void sendReqToFraud(GetKYCInfoRequest req, RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack, long when, Jwt auth) {
        var greqb = fraud.api.v1.GetKYCInfoRequest.newBuilder();
        if (StringUtils.isNotEmpty(req.getProvider())) {
            greqb.setProvider(fraud.api.v1.KYCProvider.valueOf(req.getProvider().toUpperCase()));
        }
        if (StringUtils.isNotEmpty(req.getPhase())) {
            greqb.setPhase(fraud.api.v1.KYCPhase.valueOf(req.getPhase().toUpperCase()));
        }
        greqb.setIdentity(toIdentity(client, auth));

        var wrapped = fraudServiceApi.getKYCInfo(greqb.build(), toRoutingKey(auth));
        wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
            @Override
            public void accept(ResponseWrapperFacade srespw) throws Exception {
                GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                GetKYCInfoResponseBody resp = (GetKYCInfoResponseBody) respw.getBody();

                Any any = srespw.body();
                setStatusAndMeta(respw, srespw, when);
                var status = srespw.status();

                if (status.isOK()) {
                    fraud.api.v1.GetKYCInfoResponse gresp = any.unpack(fraud.api.v1.GetKYCInfoResponse.class);

                    if (StringUtils.isNotEmpty(gresp.getKyc())) {
                        resp.setKyc(gresp.getKyc());
                    }
                    if (StringUtils.isNotEmpty(gresp.getReason())) {
                        resp.setReason(gresp.getReason());
                    }
                    if (StringUtils.isNotEmpty(gresp.getStatus())) {
                        resp.setStatus(gresp.getStatus());
                    }
                    if (!Timestamp.getDefaultInstance().equals(gresp.getAllowNextUpload())) {
                        Instant instant = Instant.ofEpochSecond(gresp.getAllowNextUpload().getSeconds(), gresp.getAllowNextUpload().getNanos());
                        resp.setAllowNextUpload(OffsetDateTime.ofInstant(instant, ZoneOffset.UTC));
                    }
                }

                client.writeAndFlush(respw, srespw.cacheControl());
            }
        });
    }
}
