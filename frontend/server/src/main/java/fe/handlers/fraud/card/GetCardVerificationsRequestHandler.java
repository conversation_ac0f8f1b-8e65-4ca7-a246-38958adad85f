package fe.handlers.fraud.card;

import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetCardVerificationsRequest;
import fe.api.fraud.model.GetCardVerificationsResponseBody;
import fe.api.fraud.model.GetCardVerificationsResponseBodyCardsInner;
import fe.api.fraud.model.GetCardVerificationsResponseBodyCardsInner.StatusEnum;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class GetCardVerificationsRequestHandler extends AbstractFrontendRequestHandler<GetCardVerificationsRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public GetCardVerificationsRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetCardVerificationsRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }

    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        long when = reqw.timestamp().getTime();
        Optional<Jwt> opt = client.jwt();
        if (opt.isPresent()) {
            Jwt auth = opt.get();
            var identity = toIdentity(client, auth).build();
            var routingKey = toRoutingKey(auth);

            var req = fraud.api.v1.GetCardVerificationsRequest.newBuilder()
                    .setIdentity(identity)
                    .build();

            var wrapped = fraudServiceApi.getCardVerifications(req, routingKey);
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Throwable {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    var resp = (GetCardVerificationsResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (any.is(fraud.api.v1.GetCardVerificationsResponse.class)) {
                        var innerResp = any.unpack(fraud.api.v1.GetCardVerificationsResponse.class);
                        if (status.isOK()) {
                            resp.setVerificationRequired(innerResp.getVerificationRequired());
                            resp.setCards(innerResp.getCardVerificationList().stream()
                                    .map(card -> {
                                        var cardVerification = new GetCardVerificationsResponseBodyCardsInner()
                                                .fingerprint(card.getFingerprint())
                                                .cardBin(card.getCardBin())
                                                .lastFour(card.getLastFour());
                                        resolveStatus(card.getVerificationStatus()).ifPresent(cardVerification::status);
                                        return cardVerification;
                                    }).collect(Collectors.toList()));
                        }
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });

        } else {
            replyWithErrorAuth(client, reqw);
        }
    }

    private static Optional<StatusEnum> resolveStatus(String status) {
        try {
            return Optional.of(StatusEnum.fromString(status));
        } catch (IllegalArgumentException e) {
            log.error("Unknown status: " + status);
            return Optional.empty();
        }
    }
}
