package fe.handlers.fraud.otp;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import common.utils.ProtoUtils;
import fe.api.OtpCheck;
import fe.api.SocketServer;
import fe.handlers.AbstractNotificationMessageHandler;
import fe.services.otp.OtpService;
import fe.api.fraud.model.SkipOtpChangeNotification;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class FraudSkipOtpChangeNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.SkipOtpChangeNotification> {
    private final OtpService otpService;

    @Inject
    public FraudSkipOtpChangeNotificationHandler(OtpService otpService) {
        super(fraud.api.v1.SkipOtpChangeNotification.class);
        this.otpService = Objects.requireNonNull(otpService);
    }
    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.SkipOtpChangeNotification socn = body.unpack(fraud.api.v1.SkipOtpChangeNotification.class);

        if (!socn.hasAccount() && ProtoUtils.getOptionalValue(socn.getRoutingKey()).isEmpty()) {
            log.debug("Account is not present, skipping skipOtp check: {}", socn);
            return;
        }
        var identity = socn.getIdentity();
        var hash = socn.getRoutingKey();
        var routingKey = AsciiString.cached(hash);
        var accountId = socn.getAccountId();

        SkipOtpChangeNotification payload = new SkipOtpChangeNotification();
        otpService.checkOtp(identity, routingKey)
                .addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(OtpCheck otpCheck) {
                        payload.setSkipOtp(otpCheck.skipOtp());
                        payload.setMandatory(otpCheck.isMandatory());
                        if (otpCheck.showOtp()) {
                            payload.setTrigger(otpCheck.getOtpTriggerRule().name().toLowerCase());
                        }
                        sendNotification(server, routingKey, accountId, payload);
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        log.error("Unable to get skip otp", t);
                    }
                }, MoreExecutors.directExecutor());
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, SkipOtpChangeNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send skip otp notification", e);
        }
    }
}
