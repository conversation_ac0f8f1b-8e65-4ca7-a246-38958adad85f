package fe.handlers.fraud.otp;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.ConfirmEmailOtpRequest;
import fe.api.fraud.model.ConfirmEmailOtpResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;

@Service
public class ConfirmEmailOtpRequestHandler extends AbstractFrontendRequestHandler<ConfirmEmailOtpRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public ConfirmEmailOtpRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, ConfirmEmailOtpRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        ConfirmEmailOtpRequest req = (ConfirmEmailOtpRequest) reqw.body();
        long when = reqw.timestamp().getTime();

        fraud.api.v1.ConfirmEmailOtpRequest.Builder sreqb = fraud.api.v1.ConfirmEmailOtpRequest.newBuilder();
        sreqb.setOtp(StringUtils.trim(req.getOtp()));

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            sreqb.setIdentity(toIdentity(client, auth));

            var wrapped = fraudServiceApi.confirmEmailOtpRequest(sreqb.build(), toRoutingKey(auth));
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    ConfirmEmailOtpResponseBody resp = (ConfirmEmailOtpResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (status.isOK()) {
                        fraud.api.v1.ConfirmEmailOtpResponse sresp = any.unpack(fraud.api.v1.ConfirmEmailOtpResponse.class);
                        resp.setSuccess(sresp.getSuccess());
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
