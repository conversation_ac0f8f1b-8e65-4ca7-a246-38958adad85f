package fe.handlers.fraud.chat;

import java.util.Optional;

import org.springframework.stereotype.Service;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.SaveChatEventRequest;
import fe.api.fraud.model.SaveChatEventRequestBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import fraud.api.v1.ChatAction;
import fraud.api.v1.ChatFlow;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SaveChatEventRequestHandler extends AbstractFrontendRequestHandler<SaveChatEventRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public SaveChatEventRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, SaveChatEventRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        SaveChatEventRequestBody req = (SaveChatEventRequestBody) reqw.body();
        long when = reqw.timestamp().getTime();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            AsciiString routingKey = toRoutingKey(auth);
            var identity = toIdentity(client, auth).build();
            var sreq = fraud.api.v1.SaveChatEventRequest.newBuilder()
                    .setIdentity(identity)
                    .setAction(ChatAction.valueOf(req.getEvent().value().toUpperCase()))
                    .setFlow(ChatFlow.valueOf(req.getFlow().value().toUpperCase()))
                    .build();
            var wrapped = fraudServiceApi.saveChatEvent(sreq, routingKey);

            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    setStatusAndMeta(respw, srespw, when);
                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
