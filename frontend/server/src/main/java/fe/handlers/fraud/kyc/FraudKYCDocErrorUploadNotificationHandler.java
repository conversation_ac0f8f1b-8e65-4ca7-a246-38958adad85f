package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import fe.api.SocketServer;
import fe.api.fraud.model.KYCDocErrorUploadNotification;
import fe.handlers.AbstractNotificationMessageHandler;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FraudKYCDocErrorUploadNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.KYCDocErrorUploadNotification> {
    @Inject
    public FraudKYCDocErrorUploadNotificationHandler() {
        super(fraud.api.v1.KYCDocErrorUploadNotification.class);
    }

    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.KYCDocErrorUploadNotification kycdsun = body.unpack(fraud.api.v1.KYCDocErrorUploadNotification.class);

        // ~ map
        KYCDocErrorUploadNotification payload = new KYCDocErrorUploadNotification();

        var account = kycdsun.getAccount();
        // ~ send out
        sendNotification(server, AsciiString.cached(account.getRoutingKey()), account.getAccountId(), payload);
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, KYCDocErrorUploadNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send Kyc doc error upload notification", e);
        }
    }
}
