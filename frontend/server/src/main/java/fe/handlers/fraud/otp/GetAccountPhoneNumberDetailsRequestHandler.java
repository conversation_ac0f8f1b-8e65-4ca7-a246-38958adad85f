package fe.handlers.fraud.otp;

import java.util.Optional;

import org.springframework.stereotype.Service;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.cfg.ApplicationProperties;

import fe.SafeSocketResponseConsumer;
import fe.api.RequestAck;
import fe.api.RestEasyClient;
import fe.api.fraud.GetAccountPhoneNumberDetailsRequest;
import fe.api.fraud.model.GetAccountPhoneNumberDetailsResponseBody;
import fe.handlers.AbstractFrontendRequestHandler;
import fe.jwt.Jwt;
import fraud.api.FraudServiceApi;
import fraud.api.v1.GetAccountPhoneNumberDetailsResponse;
import gateway.api.GenericOutboundWrapper;
import gateway.api.RequestMessageWrapper;
import jakarta.inject.Inject;

@Service
public class GetAccountPhoneNumberDetailsRequestHandler extends AbstractFrontendRequestHandler<GetAccountPhoneNumberDetailsRequest> {
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public GetAccountPhoneNumberDetailsRequestHandler(ApplicationProperties props, FraudServiceApi fraudServiceApi) {
        super(props, GetAccountPhoneNumberDetailsRequest.class);
        this.fraudServiceApi = fraudServiceApi;
    }
    @Override
    public void accept(RestEasyClient client, RequestMessageWrapper reqw, RequestAck ack) throws Exception {
        Optional<Jwt> opt = client.jwt();
        long when = reqw.timestamp().getTime();

        fraud.api.v1.GetAccountPhoneNumberDetailsRequest.Builder sreqb = fraud.api.v1.GetAccountPhoneNumberDetailsRequest.newBuilder();

        if (opt.isPresent()) {
            Jwt auth = opt.get();
            sreqb.setIdentity(toIdentity(client, auth));

            var wrapped = fraudServiceApi.getAccountPhoneNumberDetails(sreqb.build(), toRoutingKey(auth));
            wrapped.addListener(new SafeSocketResponseConsumer(wrapped, client, reqw, ack) {
                @Override
                public void accept(ResponseWrapperFacade srespw) throws Exception {
                    GenericOutboundWrapper respw = (GenericOutboundWrapper) reqw.toReply();
                    GetAccountPhoneNumberDetailsResponseBody resp = (GetAccountPhoneNumberDetailsResponseBody) respw.getBody();

                    Any any = srespw.body();
                    setStatusAndMeta(respw, srespw, when);
                    var status = srespw.status();

                    if (status.isOK()) {
                        GetAccountPhoneNumberDetailsResponse spresp = any.unpack(GetAccountPhoneNumberDetailsResponse.class);
                        resp.setCountryCode(spresp.getCountryCode());
                    }

                    client.writeAndFlush(respw, srespw.cacheControl());
                }
            });
        } else {
            replyWithErrorAuth(client, reqw);
        }
    }
}
