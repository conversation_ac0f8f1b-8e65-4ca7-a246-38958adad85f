package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import fe.api.SocketServer;
import fe.handlers.AbstractNotificationMessageHandler;
import fe.api.fraud.model.KYCInReviewNotification;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class FraudKYCInReviewNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.KYCInReviewNotification> {
    @Inject
    public FraudKYCInReviewNotificationHandler() {
        super(fraud.api.v1.KYCInReviewNotification.class);
    }
    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.KYCInReviewNotification kycirn = body.unpack(fraud.api.v1.KYCInReviewNotification.class);

        // ~ map
        KYCInReviewNotification payload = new KYCInReviewNotification();

        var account = kycirn.getAccount();
        // ~ send out
        sendNotification(server, AsciiString.cached(account.getRoutingKey()), account.getAccountId(), payload);
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, KYCInReviewNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send Kyc in review notification", e);
        }
    }
}
