package fe.handlers.fraud.kyc;

import com.google.protobuf.Any;
import com.turbospaces.api.facade.NotificationWrapperFacade;
import fe.api.SocketServer;
import fe.handlers.AbstractNotificationMessageHandler;
import fe.api.fraud.model.KYCDocBlockNotification;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static uam.model.LockReason.LOCKED_FAILED_KYC;

@Service
@Slf4j
public class FraudKYCDocBlockNotificationHandler extends AbstractNotificationMessageHandler<fraud.api.v1.KYCDocBlockNotification> {
    @Inject
    public FraudKYCDocBlockNotificationHandler() {
        super(fraud.api.v1.KYCDocBlockNotification.class);
    }
    @Override
    public void accept(SocketServer server, NotificationWrapperFacade notfw) throws Exception {
        // ~ unpack
        Any body = notfw.body();
        fraud.api.v1.KYCDocBlockNotification kyccn = body.unpack(fraud.api.v1.KYCDocBlockNotification.class);

        // ~ map
        KYCDocBlockNotification payload = new KYCDocBlockNotification();
        payload.setReason(LOCKED_FAILED_KYC.name());

        // ~ send out
        var account = kyccn.getAccount();
        sendNotification(server, AsciiString.cached(account.getRoutingKey()), account.getAccountId(), payload);
    }

    private void sendNotification(SocketServer server, AsciiString routingKey, long accountId, KYCDocBlockNotification payload) {
        // ~ send out
        try {
            server.broadcastByAccount(routingKey, accountId, wrapOutbound(payload));
        } catch (Exception e) {
            log.error("Unable to send Kyc doc block notification", e);
        }
    }
}
