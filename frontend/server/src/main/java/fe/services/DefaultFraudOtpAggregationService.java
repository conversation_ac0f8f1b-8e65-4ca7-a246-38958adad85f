package fe.services;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Service;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;

import common.utils.ProtoUtils;
import fe.services.otp.OtpAggregationService;
import fe.services.otp.dto.AccountOtpInfo;
import fe.services.otp.dto.FraudOtpInfo;
import fe.services.otp.dto.PaymentOtpInfo;
import fe.services.otp.triggers.BonusAcceptanceTrigger;
import fe.services.otp.triggers.DailyBonusTrigger;
import fe.services.otp.triggers.OtpTrigger;
import fe.services.otp.triggers.SignedUpTrigger;
import fe.services.otp.triggers.UtmSourceTrigger;
import fraud.api.FraudServiceApi;
import fraud.api.v1.GetAccountFraudInfoRequest;
import fraud.api.v1.GetAccountOtpTriggerRulesRequest;
import fraud.api.v1.OtpTriggerRules;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import payment.api.PaymentServiceApi;
import payment.api.v1.GetPaymentMetaInfoRequest;
import uam.api.UamServiceApi;
import uam.api.v1.GetAccountOtpCheckInfoRequest;
import uam.api.v1.Identity;

@Slf4j
@Service
public class DefaultFraudOtpAggregationService implements OtpAggregationService {
    private final PaymentServiceApi paymentServiceApi;
    private final UamServiceApi uamServiceApi;
    private final FraudServiceApi fraudServiceApi;

    @Inject
    public DefaultFraudOtpAggregationService(PaymentServiceApi paymentServiceApi, UamServiceApi uamServiceApi, FraudServiceApi fraudServiceApi) {
        this.paymentServiceApi = Objects.requireNonNull(paymentServiceApi);
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
        this.fraudServiceApi = Objects.requireNonNull(fraudServiceApi);
    }

    @Override
    public ListenableFuture<List<OtpTrigger>> getOtpTriggers(Identity identity, AsciiString routingKey) {
        var req = GetAccountOtpTriggerRulesRequest.newBuilder().setIdentity(identity).build();
        return Futures.transform(
                fraudServiceApi.getAccountOtpTriggerRules(req, routingKey).thenVerifyOk(),
                resp -> {
                    try {
                        return buildTriggers(resp.unpack().getTriggerRules());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                MoreExecutors.directExecutor());
    }

    private List<OtpTrigger> buildTriggers(OtpTriggerRules rules) {
        var triggers = new ArrayList<OtpTrigger>();

        if (rules.getUtmSourceTriggerEnabled()) {
            var utmSourceTrigger = new UtmSourceTrigger();
            utmSourceTrigger.setUtmSources(rules.getUtmSourcesList());
            triggers.add(utmSourceTrigger);
        }

        if (rules.getSignUpFraudScoreTriggerEnabled()) {
            var signedUpTrigger = new SignedUpTrigger();
            signedUpTrigger.setSignUpFraudScoreThresholdMin(rules.getSignUpFraudScoreThresholdMin());
            signedUpTrigger.setSignUpFraudScoreThresholdMax(rules.getSignUpFraudScoreThresholdMax());
            signedUpTrigger.setMandatory(!rules.getSignUpFraudScoreTriggerOptional());
            triggers.add(signedUpTrigger);
        }

        if (rules.getBonusTriggerEnabled()) {
            var dailyBonusTrigger = new DailyBonusTrigger();
            dailyBonusTrigger.setDailyBonusAcceptedThreshold(rules.getDailyBonusAcceptedThreshold());
            triggers.add(dailyBonusTrigger);
        }

        if (rules.getBonusAcceptanceTriggerEnabled()) {
            var bonusAcceptanceTrigger = new BonusAcceptanceTrigger();
            bonusAcceptanceTrigger.setDailyBonusClaimThreshold(rules.getBonusAcceptanceTriggerThreshold());
            bonusAcceptanceTrigger.setWebOnly(rules.getBonusAcceptanceTriggerEnabledWebOnly());
            triggers.add(bonusAcceptanceTrigger);
        }

        return triggers;
    }

    @Override
    public ListenableFuture<AccountOtpInfo> getAccountInfo(Identity identity, AsciiString routingKey) {
        var req = GetAccountOtpCheckInfoRequest.newBuilder().setIdentity(identity).build();
        return Futures.transform(uamServiceApi.getAccountOtpCheckInfo(req, routingKey).thenVerifyOk(),
                resp -> {
                    try {
                        var body = resp.unpack();
                        var sweepstake = body.getSweepstake();
                        var hasPhoneNumber = body.getHasPhoneNumber();
                        var kycNotConfirmed = body.getKycNotConfirmed();
                        var acceptedDailyBonusesCount = body.getAcceptedDailyBonusesCount();
                        var utmSource = body.getUtmSource();
                        var isTestAccount = body.getIsTestAccount();
                        return new AccountOtpInfo(sweepstake, hasPhoneNumber, kycNotConfirmed, acceptedDailyBonusesCount, utmSource, isTestAccount);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }, MoreExecutors.directExecutor());
    }

    @Override
    public ListenableFuture<FraudOtpInfo> getAccountFraudInfo(Identity identity, AsciiString routingKey) {
        var req = GetAccountFraudInfoRequest.newBuilder().setIdentity(identity).build();
        return Futures.transform(
                fraudServiceApi.getAccountFraudInfo(req, routingKey).thenVerifyOk(),
                resp -> {
                    try {
                        var body = resp.unpack();
                        Integer signUpFraudScore = ProtoUtils.getNullableValue(body.getAccountFraudInfo().getSignUpFraudScore());
                        return new FraudOtpInfo(signUpFraudScore);
                    } catch (Throwable t) {
                        throw new RuntimeException(t);
                    }
                },
                MoreExecutors.directExecutor());
    }

    @Override
    public ListenableFuture<PaymentOtpInfo> getAccountPaymentInfo(Identity identity, AsciiString routingKey) {
        var req = GetPaymentMetaInfoRequest.newBuilder().setIdentity(identity).build();
        return Futures.transform(
                paymentServiceApi.getPaymentMetaInfo(req, routingKey).thenVerifyOk(),
                resp -> {
                    try {
                        var body = resp.unpack();
                        return new PaymentOtpInfo(body.getPaymentMetaInfo().getDepositCount());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                },
                MoreExecutors.directExecutor());
    }
}
