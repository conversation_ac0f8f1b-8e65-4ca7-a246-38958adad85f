package fe.services.chat;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class LiveChatConfiguration {
    private final boolean showLiveChatByIp;
    private final List<String> purchaseVipLevelsForShowChat;
    private final List<String> purchaseVipLevelsForShowVipChat;
    private final Map<String, String> purchaseVipLevelsChatDepartmentMap;
    private final Map<String, String> purchasesChatDepartmentMap;
    private final Map<String, String> purchaseVipLevelsTagMap;
    private final Map<String, String> purchasesTagMap;
    private final BigDecimal purchasesSumForSevenDaysThreshold;
    private final Map<String, String> xpLevelsChatDepartmentMap;
    private final Map<String, String> xpLevelsTagMap;
    private final List<String> ipAllowedCountries;
    private final boolean showChatByPurchaseVipLevelsOnly;
    private final boolean showChatByPurchasesForLastSevenDaysOnly;
    private final boolean showChatByAdmin;
    private final String adminDefaultDepartment;
    private final String adminDefaultTag;
}
