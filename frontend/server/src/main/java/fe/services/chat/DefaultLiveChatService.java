package fe.services.chat;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.google.common.base.Strings;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.common.util.concurrent.SettableFuture;

import common.utils.ProtoUtils;
import fraud.api.FraudServiceApi;
import fraud.api.v1.GetLiveChatSettingsRequest;
import fraud.api.v1.GetLiveChatSettingsResponse;
import fraud.frontend.FraudFrontendPropertiesFragement;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import payment.api.PaymentServiceApi;
import uam.api.UamServiceApi;
import uam.api.v1.AccountPurchaseVipLevel;
import uam.api.v1.AccountTagCategoryInfo;
import uam.api.v1.GetAccountInfoRequest;
import uam.api.v1.GetAggregatedPaymentInfoRequest;
import uam.api.v1.Identity;

@Slf4j
@Service
public class DefaultLiveChatService implements LiveChatService {
    private static final String CUSTOM_ADVANTAGE_PLAYER_TAG_CATEGORY = "custom";
    private final UamServiceApi uamServiceApi;
    private final PaymentServiceApi paymentServiceApi;
    private final FraudServiceApi fraudServiceApi;
    private final FraudFrontendPropertiesFragement fragement;

    @Inject
    public DefaultLiveChatService(UamServiceApi uamServiceApi, PaymentServiceApi paymentServiceApi, FraudServiceApi fraudServiceApi,
            FraudFrontendPropertiesFragement fragement) {
        this.uamServiceApi = Objects.requireNonNull(uamServiceApi);
        this.paymentServiceApi = Objects.requireNonNull(paymentServiceApi);
        this.fraudServiceApi = Objects.requireNonNull(fraudServiceApi);
        this.fragement = Objects.requireNonNull(fragement);
    }
    @Override
    public FluentFuture<LiveChatSettings> getLiveChatSettings(Identity identity, AsciiString routingKey) {
        var futures = new HashSet<ListenableFuture<?>>();
        LiveChatSettings liveChatSettings = new LiveChatSettings(LiveChatConfiguration.builder().build());

        var liveChatCustomTags = fragement.LIVE_CHAT_CUSTOM_TAGS.get();
        var accountInfoReq = GetAccountInfoRequest.newBuilder().setIdentity(identity).build();
        var accountInfoFuture = uamServiceApi.getAccountInfo(accountInfoReq, routingKey)
                .thenVerifyOkAndAccept(accountInfoResp -> {
                    var accountInfo = accountInfoResp.getInfo();
                    liveChatSettings.setAdmin(accountInfo.getAdmin());
                    liveChatSettings.setSweepstakeMode(accountInfo.getSweepstake());
                    liveChatSettings.setIpCountry(accountInfo.getLocation().getCountry());
                    accountPurchaseVipLevel(accountInfo).ifPresent(lvl -> liveChatSettings.setPurchaseVipLevel(lvl.name().toLowerCase()));
                    if (accountInfo.hasXpLevel()
                            && !Strings.isNullOrEmpty(accountInfo.getXpLevel().getName())
                            && accountInfo.getXpLevel().getBaseMonthlyLevel() != 0) {
                        liveChatSettings.setXpLevel(accountInfo.getXpLevel().getName());
                    }
                    accountInfo.getTagCategoriesList().stream()
                            .filter(accountTagCategoryInfo -> accountTagCategoryInfo.getCategory().equals(CUSTOM_ADVANTAGE_PLAYER_TAG_CATEGORY))
                            .findAny()
                            .map(AccountTagCategoryInfo::getTagsList)
                            .ifPresent(tags -> liveChatSettings.setTags(tags
                                    .stream()
                                    .filter(liveChatCustomTags::containsKey)
                                    .map(liveChatCustomTags::get)
                                    .collect(Collectors.toList())));
                    liveChatSettings.setEmail(accountInfo.getRouting().getEmail());
                    liveChatSettings.setExternalId(accountInfo.getRouting().getHash() + "/" + accountInfo.getRouting().getId());
                });
        futures.add(accountInfoFuture);

        var chatPaymentInfoReq = GetAggregatedPaymentInfoRequest.newBuilder()
                .setIdentity(identity)
                .setPeriodOfDays(7)
                .build();
        var liveChatPaymentInfoFuture = paymentServiceApi.getAggregatedPaymentInfo(chatPaymentInfoReq, routingKey)
                .thenVerifyOkAndAccept(resp -> {
                    liveChatSettings.setHasPurchases(resp.getDepositCountForAllHistory() > 0);
                    var totalPurchasesForPeriod = ProtoUtils.getOptionalValue(resp.getSumOfPurchasesForPeriod())
                            .map(BigDecimal::new)
                            .orElse(BigDecimal.ZERO);
                    liveChatSettings.setPurchasesSumForSevenDays(totalPurchasesForPeriod);
                });
        futures.add(liveChatPaymentInfoFuture);

        var getLiveChatSettingsReq = GetLiveChatSettingsRequest.newBuilder().setIdentity(identity).build();
        var liveChatSettingsFuture = fraudServiceApi.getLiveChatSettings(getLiveChatSettingsReq, routingKey)
                .thenVerifyOkAndAccept(resp -> {
                    liveChatSettings.setPurchaseFlowEnabled(resp.getPurchaseFlowEnabled());
                    liveChatSettings.setCfg(toLiveChatConfiguration(resp));
                });
        futures.add(liveChatSettingsFuture);

        var future = SettableFuture.<LiveChatSettings> create();
        FluentFuture.from(Futures.allAsList(futures))
                .addCallback(new FutureCallback<Object>() {
                    @Override
                    public void onSuccess(Object result) {
                        future.set(liveChatSettings);
                    }
                    @Override
                    public void onFailure(Throwable t) {
                        log.error("Unable to collect live chat info", t);
                        future.setException(t);
                    }
                }, MoreExecutors.directExecutor());
        return FluentFuture.from(future);
    }

    private static Optional<AccountPurchaseVipLevel> accountPurchaseVipLevel(uam.api.v1.AccountInfo info) {
        return applicableVipLevel(info.getPurchaseVipLevelOverride()).or(() -> applicableVipLevel(info.getPurchaseVipLevel()));
    }

    private static Optional<AccountPurchaseVipLevel> applicableVipLevel(AccountPurchaseVipLevel level) {
        return Optional.of(level).filter(l -> !l.equals(AccountPurchaseVipLevel.NONE_PURCHASE_VIP_LEVEL)
                && !l.equals(AccountPurchaseVipLevel.DO_NOT_CHANGE_PURCHASE_VIP_LEVEL));
    }

    private static LiveChatConfiguration toLiveChatConfiguration(GetLiveChatSettingsResponse resp) {
        Map<String, String> purchaseVipLevelDepartmentsMap = new HashMap<>();
        Map<String, String> purchaseVipLevelsTagsMap = new HashMap<>();
        Map<String, String> purchaseDepartmentsMap = new HashMap<>();
        Map<String, String> purchaseTagsMap = new HashMap<>();
        Map<String, String> xpLevelsDepartmentsMap = new HashMap<>();
        Map<String, String> xpLevelsTagsMap = new HashMap<>();
        var adminDefaultDepartmentWrapper = new Object() {
            String adminDefaultDepartment;
            String adminDefaultTags;
        };
        resp.getChatDepartmentsList().forEach(chatDepartment -> {
            switch (chatDepartment.getConditionCase()) {
                case VIPLEVEL -> {
                    purchaseVipLevelDepartmentsMap.put(chatDepartment.getVipLevel(), chatDepartment.getDepartment());
                    purchaseVipLevelsTagsMap.put(chatDepartment.getVipLevel(), chatDepartment.getTag());
                }
                case PURCHASES -> {
                    purchaseDepartmentsMap.put(chatDepartment.getPurchases(), chatDepartment.getDepartment());
                    purchaseTagsMap.put(chatDepartment.getPurchases(), chatDepartment.getTag());
                }
                case XPLEVEL -> {
                    xpLevelsDepartmentsMap.put(chatDepartment.getXpLevel(), chatDepartment.getDepartment());
                    xpLevelsTagsMap.put(chatDepartment.getXpLevel(), chatDepartment.getTag());
                }
                case ADMIN -> {
                    adminDefaultDepartmentWrapper.adminDefaultDepartment = chatDepartment.getDepartment();
                    adminDefaultDepartmentWrapper.adminDefaultTags = chatDepartment.getTag();
                }
            }
        });
        return LiveChatConfiguration.builder()
                .showLiveChatByIp(resp.getIpRulesEnabled())
                .purchaseVipLevelsForShowChat(resp.getPurchaseVipLevelsList())
                .purchaseVipLevelsForShowVipChat(resp.getPurchaseVipLevelsForVipChatList())
                .purchaseVipLevelsChatDepartmentMap(purchaseVipLevelDepartmentsMap)
                .purchasesChatDepartmentMap(purchaseDepartmentsMap)
                .purchaseVipLevelsTagMap(purchaseVipLevelsTagsMap)
                .purchasesTagMap(purchaseTagsMap)
                .purchasesSumForSevenDaysThreshold(new BigDecimal(resp.getPurchasesForPeriod()))
                .xpLevelsChatDepartmentMap(xpLevelsDepartmentsMap)
                .xpLevelsTagMap(xpLevelsTagsMap)
                .ipAllowedCountries(resp.getIpAllowedCountriesList())
                .showChatByPurchaseVipLevelsOnly(resp.getPurchaseVipLevelsRulesOnly())
                .showChatByPurchasesForLastSevenDaysOnly(resp.getPurchasesForPeriodRulesOnly())
                .showChatByAdmin(resp.getShowByAdmin())
                .adminDefaultDepartment(adminDefaultDepartmentWrapper.adminDefaultDepartment)
                .adminDefaultTag(adminDefaultDepartmentWrapper.adminDefaultTags)
                .build();
    }
}
