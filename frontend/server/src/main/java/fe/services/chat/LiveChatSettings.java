package fe.services.chat;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;
import lombok.Setter;

@Setter
public class LiveChatSettings {
    private LiveChatConfiguration cfg;

    private boolean isSweepstakeMode;
    private boolean hasPurchases;
    private String ipCountry;
    private BigDecimal purchasesSumForSevenDays;
    private String purchaseVipLevel;
    private String xpLevel;
    @Getter
    private boolean purchaseFlowEnabled;
    private boolean isAdmin;
    @Getter
    private List<String> tags;
    @Getter
    private String email;
    @Getter
    private String externalId;


    public LiveChatSettings(LiveChatConfiguration cfg) {
        this.cfg = cfg;
    }

    public boolean showChat() {
        if (cfg.isShowChatByAdmin() && isAdmin) {
            return true;
        }
        if (cfg.isShowLiveChatByIp()) {
            return isSweepstakeMode && hasPurchases && isIpCountryAllowed();
        }
        if (cfg.isShowChatByPurchaseVipLevelsOnly()) {
            return showChatByPurchaseVipLevel(purchaseVipLevel);
        }
        if (cfg.isShowChatByPurchasesForLastSevenDaysOnly()) {
            return showChatByPurchasesForLastSevenDays(purchasesSumForSevenDays);
        }
        return showChatByPurchaseVipLevel(purchaseVipLevel) || showChatByPurchasesForLastSevenDays(purchasesSumForSevenDays);
    }

    public boolean isVip() {
        return showChat() && showVipChatByPurchaseVipLevel(purchaseVipLevel);
    }

    public Optional<String> getDepartment() {
        if (showChat()) {
            if (cfg.isShowChatByAdmin() && isAdmin) {
                return Optional.ofNullable(cfg.getAdminDefaultDepartment());
            }
            if (StringUtils.isNotEmpty(xpLevel)) {
                return Optional.ofNullable(cfg.getXpLevelsChatDepartmentMap().get(xpLevel));
            }
            if (StringUtils.isNotEmpty(purchaseVipLevel) && !cfg.isShowChatByPurchasesForLastSevenDaysOnly()) {
                return Optional.ofNullable(cfg.getPurchaseVipLevelsChatDepartmentMap().get(purchaseVipLevel));
            }
            if (showChatByPurchasesForLastSevenDays(purchasesSumForSevenDays) && !cfg.isShowChatByPurchaseVipLevelsOnly()) {
                return Optional.ofNullable(cfg.getPurchasesChatDepartmentMap().get("7"));
            }
        }
        return Optional.empty();
    }

    public List<String> getEnabledDepartments() {
        if (StringUtils.isNotEmpty(xpLevel)) {
            return cfg.getXpLevelsChatDepartmentMap().values().stream().distinct().toList();
        }
        return Stream.concat(cfg.getPurchaseVipLevelsChatDepartmentMap().values().stream(), cfg.getPurchasesChatDepartmentMap().values().stream()).distinct().toList();
    }

    public Optional<String> getTag() {
        if (showChat()) {
            if (cfg.isShowChatByAdmin() && isAdmin) {
                return Optional.ofNullable(cfg.getAdminDefaultTag());
            }
            if (StringUtils.isNotEmpty(xpLevel)) {
                return Optional.ofNullable(cfg.getXpLevelsTagMap().get(xpLevel));
            }
            if (StringUtils.isNotEmpty(purchaseVipLevel) && !cfg.isShowChatByPurchasesForLastSevenDaysOnly()) {
                return Optional.ofNullable(cfg.getPurchaseVipLevelsTagMap().get(purchaseVipLevel));
            }
            if (showChatByPurchasesForLastSevenDays(purchasesSumForSevenDays) && !cfg.isShowChatByPurchaseVipLevelsOnly()) {
                return Optional.ofNullable(cfg.getPurchasesTagMap().get("7"));
            }
        }
        return Optional.empty();
    }

    private boolean showChatByPurchaseVipLevel(String level) {
        return Objects.nonNull(cfg.getPurchaseVipLevelsForShowChat()) && cfg.getPurchaseVipLevelsForShowChat().contains(level);
    }

    private boolean showVipChatByPurchaseVipLevel(String level) {
        return Objects.nonNull(cfg.getPurchaseVipLevelsForShowVipChat()) && cfg.getPurchaseVipLevelsForShowVipChat().contains(level);
    }

    private boolean showChatByPurchasesForLastSevenDays(BigDecimal purchases) {
        return purchases.compareTo(cfg.getPurchasesSumForSevenDaysThreshold()) >= 0;
    }

    private boolean isIpCountryAllowed() {
        return cfg.getIpAllowedCountries().contains(ipCountry);
    }
}
