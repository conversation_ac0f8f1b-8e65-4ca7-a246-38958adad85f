package fe.services;

import api.v1.ApplicationException;
import uam.api.v1.AccountInfo;

public interface KYCAssertionService {
    void assertPlayable(AccountInfo accountInfo, String signUpMethod) throws ApplicationException;
    void assertEligibleForKyc(AccountInfo accountInfo, String signUpMethod) throws ApplicationException;
    void assertSofKycDataPresent(AccountInfo accountInfo) throws ApplicationException;
}
