package fe.services;

import com.google.protobuf.Internal;

import lombok.Getter;

@Getter
public class KycValidationException extends RuntimeException {
    private final String errorMessage;
    private Internal.EnumLite code;
    private final Internal.EnumLite reason;

    public KycValidationException(String errorMessage, Internal.EnumLite code, Internal.EnumLite reason) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.code = code;
        this.reason = reason;
    }

    public KycValidationException(String errorMessage, Internal.EnumLite reason) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.reason = reason;
    }
}
