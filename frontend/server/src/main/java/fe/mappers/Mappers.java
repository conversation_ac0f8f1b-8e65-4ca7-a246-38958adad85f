package fe.mappers;

import fe.api.OtpTriggerRule;
import fe.api.fraud.model.GetConfirmedKYCInfoResponseBody;
import fe.api.fraud.model.KYCInfo;
import fe.api.fraud.model.KYCStateSettings;
import fraud.api.v1.DocType;
import fraud.api.v1.KYCStatus;
import fraud.api.v1.OtpTrigger;
import fe.api.fraud.model.BeginDocUploadRequestBody;
import fe.api.fraud.model.SupportedDocType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Slf4j
public class Mappers {
    public static OtpTrigger otpTrigger(OtpTriggerRule otpTriggerRule) {
        return switch (otpTriggerRule) {
            case SIGNED_UP_TRIGGER -> OtpTrigger.SIGNED_UP;
            case BONUS_TRIGGER -> OtpTrigger.BONUS;
            case BONUS_ACCEPTANCE_TRIGGER -> OtpTrigger.BONUS_ACCEPTANCE;
            case UTM_SOURCE_TRIGGER -> OtpTrigger.UTM_SOURCE;
            default -> throw new RuntimeException("Unknown OTP trigger rule " + otpTriggerRule.name());
        };
    }

    public static SupportedDocType toDocType(fraud.api.v1.SupportedDocType docType) {
        var type = SupportedDocType.TypeEnum.fromValue(docType.getType().toString());
        return new SupportedDocType().type(type).title(docType.getTitle());
    }

    public static DocType toDocType(BeginDocUploadRequestBody.DocTypeEnum type) {
        return switch (type) {
            case BeginDocUploadRequestBody.DocTypeEnum.BS -> DocType.BS;
            case BeginDocUploadRequestBody.DocTypeEnum.TR -> DocType.TR;
            case BeginDocUploadRequestBody.DocTypeEnum.PA -> DocType.PA;
            case BeginDocUploadRequestBody.DocTypeEnum.SA -> DocType.SA;
            case BeginDocUploadRequestBody.DocTypeEnum.PSL -> DocType.PSL;
            case BeginDocUploadRequestBody.DocTypeEnum.OTH -> DocType.OTH;
        };
    }

    public static KYCInfo toKycInfo(fraud.api.v1.KYCInfo kycInfo) {
        KYCInfo info = new KYCInfo();

        if (StringUtils.isNotEmpty(kycInfo.getFirstName())) {
            info.setFirstName(kycInfo.getFirstName());
        }
        if (StringUtils.isNotEmpty(kycInfo.getLastName())) {
            info.setLastName(kycInfo.getLastName());
        }
        if (StringUtils.isNotEmpty(kycInfo.getCountry())) {
            info.setCountry(kycInfo.getCountry());
        }
        if (StringUtils.isNotEmpty(kycInfo.getState())) {
            info.setState(kycInfo.getState());
        }
        if (StringUtils.isNotEmpty(kycInfo.getCity())) {
            info.setCity(kycInfo.getCity());
        }
        if (StringUtils.isNotEmpty(kycInfo.getZip())) {
            info.setZip(kycInfo.getZip());
        }
        if (StringUtils.isNotEmpty(kycInfo.getAddress())) {
            info.setAddress(kycInfo.getAddress());
        }
        return info;
    }

    public static GetConfirmedKYCInfoResponseBody.KycStatusEnum toKycStatusEnum(KYCStatus status) {
        return switch (status) {
            case KYCStatus.INITIAL -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.INITIAL;
            case KYCStatus.IN_REVIEW -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.IN_REVIEW;
            case KYCStatus.CONFIRMED -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.CONFIRMED;
            case KYCStatus.ID_CONFIRMED -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.ID_CONFIRMED;
            case KYCStatus.DOC_REVIEW -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.DOC_REVIEW;
            case KYCStatus.DOC_DECLINED -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.DOC_DECLINED;
            case KYCStatus.KYC_SESSION_EXPIRED -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.SESSION_EXPIRED;
            case KYCStatus.DECLINED -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.DECLINED;
            case KYCStatus.BLOCKED -> GetConfirmedKYCInfoResponseBody.KycStatusEnum.BLOCKED;
            case UNRECOGNIZED -> {
                log.error("Unknown KYC status: {}", status);
                yield GetConfirmedKYCInfoResponseBody.KycStatusEnum.INITIAL;
            }
        };
    }

    public static List<KYCStateSettings> toKYCStateSettings(List<fraud.api.v1.KYCStateSettings> settings) {
        return settings.stream().map(s -> {
            var stateSettings = new KYCStateSettings();
            stateSettings.setCountry(s.getCountry());
            stateSettings.setBlockedStates(s.getBlockedStatesList());
            stateSettings.setAllowedStates(s.getAllowedStatesList());
            return stateSettings;
        }).toList();
    }
}
