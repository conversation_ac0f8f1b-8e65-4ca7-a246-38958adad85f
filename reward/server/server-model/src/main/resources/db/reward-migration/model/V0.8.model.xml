<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="source" tableName="reward.account_reward_log" type="varchar(19)" currentType="varchar(13)" currentNotnull="false" checkConstraint="check ( source in ('quest','random_reward','internal','platform_tournament'))" checkConstraintName="ck_account_reward_log_source"/>
    </changeSet>
</migration>