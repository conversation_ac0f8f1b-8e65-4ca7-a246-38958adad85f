-- drop dependencies
alter table reward.bonus_deposit_details drop constraint if exists ck_bonus_deposit_details_platform;
-- apply alter tables
alter table reward.bonus_deposit_details alter column amount type decimal(16,2) using amount::decimal(16,2);
alter table reward.bonus_deposit_details alter column max_amount type decimal(16,2) using max_amount::decimal(16,2);
alter table reward.bonus_deposit_details alter column min_deposit_amount type decimal(16,2) using min_deposit_amount::decimal(16,2);
alter table reward.bonus_deposit_details alter column absolute_value type decimal(16,2) using absolute_value::decimal(16,2);
alter table reward.bonus_deposit_details add column if not exists bonus_code varchar not null;
alter table reward.bonus_deposit_details add column if not exists rules varchar;
-- apply post alter
alter table reward.bonus_deposit_details add constraint ck_bonus_deposit_details_platform check ( platform in ('web','android','ios','native')) not valid ;
