<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="amount" tableName="reward.bonus_deposit_details" type="decimal(16,2)" currentType="decimal" currentNotnull="false"/>
        <alterColumn columnName="max_amount" tableName="reward.bonus_deposit_details" type="decimal(16,2)" currentType="decimal" currentNotnull="false"/>
        <alterColumn columnName="min_deposit_amount" tableName="reward.bonus_deposit_details" type="decimal(16,2)" currentType="decimal" currentNotnull="true"/>
        <alterColumn columnName="platform" tableName="reward.bonus_deposit_details" checkConstraint="check ( platform in ('web','android','ios','native'))" checkConstraintName="ck_bonus_deposit_details_platform"/>
        <alterColumn columnName="absolute_value" tableName="reward.bonus_deposit_details" type="decimal(16,2)" currentType="decimal" currentNotnull="false"/>
        <addColumn tableName="reward.bonus_deposit_details">
            <column name="bonus_code" type="varchar" notnull="true"/>
            <column name="rules" type="varchar"/>
        </addColumn>
    </changeSet>
</migration>