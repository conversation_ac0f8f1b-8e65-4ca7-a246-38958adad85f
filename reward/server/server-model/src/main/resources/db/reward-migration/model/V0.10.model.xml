<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="reward.reward">
            <column name="xp_variant_reward_details_id" type="bigint" uniqueOneToOne="uq_reward_xp_variant_reward_details_id" references="reward.xp_variant_reward_details.id" foreignKeyName="fk_reward_xp_variant_reward_details_id"/>
        </addColumn>
        <createTable name="reward.xp_variant_reward_details" pkName="pk_xp_variant_reward_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="variant_code" type="varchar" notnull="true"/>
            <column name="xp_amount" type="decimal" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
    </changeSet>
</migration>