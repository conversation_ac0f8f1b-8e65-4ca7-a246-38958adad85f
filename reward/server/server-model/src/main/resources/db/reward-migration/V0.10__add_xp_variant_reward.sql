-- apply changes
create table reward.xp_variant_reward_details (
  id                            bigint generated by default as identity not null,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  xp_amount                     decimal(16,3) not null,
  variant_code                  varchar not null,
  constraint pk_xp_variant_reward_details primary key (id)
);

-- apply alter tables
alter table reward.reward add column if not exists xp_variant_reward_details_id bigint;
-- foreign keys and indices
alter table reward.reward add constraint fk_reward_xp_variant_reward_details_id foreign key (xp_variant_reward_details_id) references reward.xp_variant_reward_details (id) on delete restrict on update restrict not valid;

