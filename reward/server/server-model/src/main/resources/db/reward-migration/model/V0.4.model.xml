<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="reward.account_bonus_deposit_reward" pkName="pk_account_bonus_deposit_reward">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" references="reward.accounts.id" foreignKeyName="fk_account_bonus_deposit_reward_account_id" foreignKeyIndex="ix_account_bonus_deposit_reward_account_id"/>
            <column name="type" type="varchar(10)" checkConstraint="check ( type in ('assignable','global'))" checkConstraintName="ck_account_bonus_deposit_reward_type"/>
            <column name="status" type="varchar(9)" checkConstraint="check ( status in ('available','used'))" checkConstraintName="ck_account_bonus_deposit_reward_status"/>
            <column name="claimed" type="integer" notnull="true"/>
            <column name="capacity" type="integer"/>
            <column name="reward_id" type="bigint" references="reward.reward.id" foreignKeyName="fk_account_bonus_deposit_reward_reward_id" foreignKeyIndex="ix_account_bonus_deposit_reward_reward_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_bonus_deposit_reward_account_id_reward_id" columnNames="account_id,reward_id" oneToOne="false" nullableColumns="account_id,reward_id"/>
        </createTable>
        <alterColumn columnName="source" tableName="reward.account_reward_log" checkConstraint="check ( source in ('quest','random_reward','internal'))" checkConstraintName="ck_account_reward_log_source"/>
        <addColumn tableName="reward.account_reward_log">
            <column name="amount" type="decimal"/>
        </addColumn>
        <createTable name="reward.bonus_deposit_details" pkName="pk_bonus_deposit_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="internal_name" type="varchar"/>
            <column name="currency" type="varchar" notnull="true"/>
            <column name="countries" type="varchar[]"/>
            <column name="category" type="varchar(18)" notnull="true" checkConstraint="check ( category in ('cashout_restricted','release_restricted'))" checkConstraintName="ck_bonus_deposit_details_category"/>
            <column name="bonus_type" type="varchar(10)" notnull="true" checkConstraint="check ( bonus_type in ('assignable','global'))" checkConstraintName="ck_bonus_deposit_details_bonus_type"/>
            <column name="reward_type" type="varchar(10)" notnull="true" checkConstraint="check ( reward_type in ('fixed','percentage'))" checkConstraintName="ck_bonus_deposit_details_reward_type"/>
            <column name="amount" type="decimal"/>
            <column name="percent_value" type="decimal"/>
            <column name="max_amount" type="decimal"/>
            <column name="min_deposit_amount" type="decimal" notnull="true"/>
            <column name="opt_in" type="boolean" defaultValue="false" notnull="true"/>
            <column name="capacity" type="integer" notnull="true"/>
            <column name="product" type="varchar(6)" notnull="true" checkConstraint="check ( product in ('sports','casino','both'))" checkConstraintName="ck_bonus_deposit_details_product"/>
            <column name="forfeit_bonus_on_withdrawal" type="boolean" defaultValue="false" notnull="true"/>
            <column name="priority" type="integer" notnull="true"/>
            <column name="applied_from" type="timestamp"/>
            <column name="applied_to" type="timestamp"/>
            <column name="platform" type="varchar(7)" notnull="true" checkConstraint="check ( platform in ('web','android','ios'))" checkConstraintName="ck_bonus_deposit_details_platform"/>
            <column name="include_segments" type="varchar[]"/>
            <column name="include_segment_tags" type="varchar[]"/>
            <column name="exclude_segments" type="varchar[]"/>
            <column name="exclude_segment_tags" type="varchar[]"/>
            <column name="overall_min_odds" type="varchar" notnull="true"/>
            <column name="selection_level_odds" type="varchar" notnull="true"/>
            <column name="wagering_requirement_type" type="varchar(10)" notnull="true" checkConstraint="check ( wagering_requirement_type in ('absolute','multiplier'))" checkConstraintName="ck_bonus_deposit_details_wagering_requirement_type"/>
            <column name="absolute_value" type="decimal"/>
            <column name="multiplier_count" type="integer"/>
            <column name="multiplier_type" type="varchar(25)" checkConstraint="check ( multiplier_type in ('bonus_amount','deposit_plus_bonus_amount'))" checkConstraintName="ck_bonus_deposit_details_multiplier_type"/>
            <column name="claim_validity" type="integer"/>
            <column name="wagering_validity" type="integer"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="comments" type="varchar"/>
            <column name="extra_reward_code" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <addColumn tableName="reward.reward">
            <column name="bonus_deposit_details_id" type="bigint" uniqueOneToOne="uq_reward_bonus_deposit_details_id" references="reward.bonus_deposit_details.id" foreignKeyName="fk_reward_bonus_deposit_details_id"/>
        </addColumn>
        <createIndex indexName="ix_account_bonus_deposit_reward_account_id_type_status" tableName="reward.account_bonus_deposit_reward" columns="account_id,type,status"/>
    </changeSet>
</migration>