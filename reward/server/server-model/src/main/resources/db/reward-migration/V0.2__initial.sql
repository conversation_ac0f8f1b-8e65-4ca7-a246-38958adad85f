DO $$
BEGIN
-- Check if the reward schema exists
IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'reward' AND table_name = 'accounts') THEN
    -- Exit the script if the schema and tables already exist
    RAISE NOTICE 'Reward schema and tables already exists. Skipping creation.';
    RETURN;
END IF;

-- apply changes
create schema if not exists reward;

create table reward.account_reward_log (
  id                            bigint generated by default as identity not null,
  reward_id                     bigint,
  account_id                    bigint,
  attempt                       integer not null,
  at                            date not null,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  transaction_code              varchar not null,
  source                        varchar(13),
  source_reference              varchar,
  status                        varchar(14),
  constraint ck_account_reward_log_source check ( source in ('quest','random_reward')),
  constraint ck_account_reward_log_status check ( status in ('created','retrying','success','failed','not_applicable')),
  constraint uq_account_reward_log_transaction_code_at unique (transaction_code,at),
  constraint pk_account_reward_log primary key (id,at)
) partition by range (at);

create table reward.account_reward_log_default partition of reward.account_reward_log default;

create table reward.coin_reward_details (
  id                            bigint generated by default as identity not null,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  sc                            decimal(16,3),
  gc                            decimal(16,3),
  constraint pk_coin_reward_details primary key (id)
);

create table reward.freespin_reward_details (
  id                            bigint generated by default as identity not null,
  count                         integer not null,
  bet_level                     integer,
  fallback_id                   bigint,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  bet_value                     decimal(16,3) not null,
  bonus_code_prefix             varchar not null,
  bonus_code                    varchar,
  product_code                  varchar not null,
  constraint uq_freespin_reward_details_fallback_id unique (fallback_id),
  constraint pk_freespin_reward_details primary key (id)
);

create table reward.purchase_offer_reward_details (
  id                            bigint generated by default as identity not null,
  discount                      integer,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  offer_code                    varchar not null,
  constraint pk_purchase_offer_reward_details primary key (id)
);

create table reward.reward (
  id                            bigint generated by default as identity not null,
  code                          uuid not null,
  brand_id                      integer not null,
  rank                          integer not null,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  coin_reward_details_id        bigint,
  free_spin_reward_details_id   bigint,
  purchase_offer_reward_details_id bigint,
  type                          varchar(31) not null,
  name                          varchar,
  constraint uq_reward_code unique (code),
  constraint uq_reward_coin_reward_details_id unique (coin_reward_details_id),
  constraint uq_reward_free_spin_reward_details_id unique (free_spin_reward_details_id),
  constraint uq_reward_purchase_offer_reward_details_id unique (purchase_offer_reward_details_id),
  constraint pk_reward primary key (id)
);

create table reward.accounts (
  id                            bigint generated by default as identity not null,
  code                          uuid not null,
  remote_id                     bigint not null,
  brand_id                      integer not null,
  deleted                       boolean default false not null,
  version                       integer not null,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  hash                          varchar not null,
  constraint uq_accounts_code unique (code),
  constraint uq_accounts_brand_id_remote_id unique (brand_id,remote_id),
  constraint uq_accounts_remote_id unique (remote_id),
  constraint pk_accounts primary key (id)
);

create table reward.brands (
  id                            integer generated by default as identity not null,
  name                          varchar not null,
  mode                          varchar(22) not null,
  domain                        varchar,
  constraint ck_brands_mode check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web')),
  constraint uq_brands_name unique (name),
  constraint pk_brands primary key (id)
);

-- foreign keys and indices
create index ix_account_reward_log_reward_id on reward.account_reward_log (reward_id);
alter table reward.account_reward_log add constraint fk_account_reward_log_reward_id foreign key (reward_id) references reward.reward (id) on delete restrict on update restrict;

create index ix_account_reward_log_account_id on reward.account_reward_log (account_id);
alter table reward.account_reward_log add constraint fk_account_reward_log_account_id foreign key (account_id) references reward.accounts (id) on delete restrict on update restrict;

alter table reward.freespin_reward_details add constraint fk_freespin_reward_details_fallback_id foreign key (fallback_id) references reward.freespin_reward_details (id) on delete restrict on update restrict;

create index ix_reward_brand_id on reward.reward (brand_id);
alter table reward.reward add constraint fk_reward_brand_id foreign key (brand_id) references reward.brands (id) on delete restrict on update restrict;

alter table reward.reward add constraint fk_reward_coin_reward_details_id foreign key (coin_reward_details_id) references reward.coin_reward_details (id) on delete restrict on update restrict;

alter table reward.reward add constraint fk_reward_free_spin_reward_details_id foreign key (free_spin_reward_details_id) references reward.freespin_reward_details (id) on delete restrict on update restrict;

alter table reward.reward add constraint fk_reward_purchase_offer_reward_details_id foreign key (purchase_offer_reward_details_id) references reward.purchase_offer_reward_details (id) on delete restrict on update restrict;

create index ix_accounts_brand_id on reward.accounts (brand_id);
alter table reward.accounts add constraint fk_accounts_brand_id foreign key (brand_id) references reward.brands (id) on delete restrict on update restrict;

create index if not exists ix_account_reward_log_at on reward.account_reward_log (at);
create index if not exists ix_accounts_hash on reward.accounts (hash);
END
$$;
