<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="reward"/>
        <createTable name="reward.account_reward_log" partitionMode="MONTH" partitionColumn="at" pkName="pk_account_reward_log">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="reward_id" type="bigint" references="reward.reward.id" foreignKeyName="fk_account_reward_log_reward_id" foreignKeyIndex="ix_account_reward_log_reward_id"/>
            <column name="account_id" type="bigint" references="reward.accounts.id" foreignKeyName="fk_account_reward_log_account_id" foreignKeyIndex="ix_account_reward_log_account_id"/>
            <column name="transaction_code" type="varchar" notnull="true"/>
            <column name="source" type="varchar(13)" checkConstraint="check ( source in ('quest','random_reward'))" checkConstraintName="ck_account_reward_log_source"/>
            <column name="source_reference" type="varchar"/>
            <column name="status" type="varchar(14)" checkConstraint="check ( status in ('created','retrying','success','failed','not_applicable'))" checkConstraintName="ck_account_reward_log_status"/>
            <column name="attempt" type="integer" notnull="true"/>
            <column name="at" type="date" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_account_reward_log_transaction_code_at" columnNames="transaction_code,at" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="reward.coin_reward_details" pkName="pk_coin_reward_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="sc" type="decimal"/>
            <column name="gc" type="decimal"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.freespin_reward_details" pkName="pk_freespin_reward_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="bonus_code_prefix" type="varchar" notnull="true"/>
            <column name="bonus_code" type="varchar"/>
            <column name="count" type="integer" notnull="true"/>
            <column name="product_code" type="varchar" notnull="true"/>
            <column name="bet_value" type="decimal" notnull="true"/>
            <column name="bet_level" type="integer"/>
            <column name="fallback_id" type="bigint" uniqueOneToOne="uq_freespin_reward_details_fallback_id" references="reward.freespin_reward_details.id" foreignKeyName="fk_freespin_reward_details_fallback_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.purchase_offer_reward_details" pkName="pk_purchase_offer_reward_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="offer_code" type="varchar" notnull="true"/>
            <column name="discount" type="integer"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="reward.reward" pkName="pk_reward">
            <column name="type" type="varchar(31)" notnull="true"/>
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_reward_code"/>
            <column name="brand_id" type="integer" notnull="true" references="reward.brands.id" foreignKeyName="fk_reward_brand_id" foreignKeyIndex="ix_reward_brand_id"/>
            <column name="name" type="varchar"/>
            <column name="rank" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="coin_reward_details_id" type="bigint" uniqueOneToOne="uq_reward_coin_reward_details_id" references="reward.coin_reward_details.id" foreignKeyName="fk_reward_coin_reward_details_id"/>
            <column name="free_spin_reward_details_id" type="bigint" uniqueOneToOne="uq_reward_free_spin_reward_details_id" references="reward.freespin_reward_details.id" foreignKeyName="fk_reward_free_spin_reward_details_id"/>
            <column name="purchase_offer_reward_details_id" type="bigint" uniqueOneToOne="uq_reward_purchase_offer_reward_details_id" references="reward.purchase_offer_reward_details.id" foreignKeyName="fk_reward_purchase_offer_reward_details_id"/>
        </createTable>
        <createTable name="reward.accounts" pkName="pk_accounts">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="hash" type="varchar" notnull="true"/>
            <column name="code" type="uuid" notnull="true" unique="uq_accounts_code"/>
            <column name="remote_id" type="bigint" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="reward.brands.id" foreignKeyName="fk_accounts_brand_id" foreignKeyIndex="ix_accounts_brand_id"/>
            <column name="deleted" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_accounts_brand_id_remote_id" columnNames="brand_id,remote_id" oneToOne="false" nullableColumns=""/>
            <uniqueConstraint name="uq_accounts_remote_id" columnNames="remote_id" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="reward.brands" pkName="pk_brands">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true" unique="uq_brands_name"/>
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_brands_mode"/>
            <column name="domain" type="varchar"/>
        </createTable>
        <createIndex indexName="ix_account_reward_log_at" tableName="reward.account_reward_log" columns="at"/>
        <createIndex indexName="ix_accounts_hash" tableName="reward.accounts" columns="hash"/>
    </changeSet>
</migration>