-- drop dependencies
alter table reward.account_reward_log drop constraint if exists ck_account_reward_log_source;
-- apply changes
create table reward.account_bonus_deposit_reward (
  id                            bigint generated by default as identity not null,
  account_id                    bigint,
  claimed                       integer not null,
  capacity                      integer,
  reward_id                     bigint,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  type                          varchar(10),
  status                        varchar(9),
  constraint ck_account_bonus_deposit_reward_type check ( type in ('assignable','global')),
  constraint ck_account_bonus_deposit_reward_status check ( status in ('available','used')),
  constraint uq_account_bonus_deposit_reward_account_id_reward_id unique (account_id,reward_id),
  constraint pk_account_bonus_deposit_reward primary key (id)
);

create table reward.bonus_deposit_details (
  id                            bigint generated by default as identity not null,
  opt_in                        boolean default false not null,
  capacity                      integer not null,
  forfeit_bonus_on_withdrawal   boolean default false not null,
  priority                      integer not null,
  applied_from                  timestamptz,
  applied_to                    timestamptz,
  multiplier_count              integer,
  claim_validity                integer,
  wagering_validity             integer,
  inactive                      boolean default false not null,
  created_at                    timestamptz not null,
  modified_at                   timestamptz not null,
  amount                        decimal(16,3),
  percent_value                 decimal(16,3),
  max_amount                    decimal(16,3),
  min_deposit_amount            decimal(16,3) not null,
  absolute_value                decimal(16,3),
  internal_name                 varchar,
  currency                      varchar not null,
  countries                     varchar[],
  category                      varchar(18) not null,
  bonus_type                    varchar(10) not null,
  reward_type                   varchar(10) not null,
  product                       varchar(6) not null,
  platform                      varchar(7) not null,
  include_segments              varchar[],
  include_segment_tags          varchar[],
  exclude_segments              varchar[],
  exclude_segment_tags          varchar[],
  overall_min_odds              varchar not null,
  selection_level_odds          varchar not null,
  wagering_requirement_type     varchar(10) not null,
  multiplier_type               varchar(25),
  comments                      varchar,
  extra_reward_code             varchar,
  constraint ck_bonus_deposit_details_category check ( category in ('cashout_restricted','release_restricted')),
  constraint ck_bonus_deposit_details_bonus_type check ( bonus_type in ('assignable','global')),
  constraint ck_bonus_deposit_details_reward_type check ( reward_type in ('fixed','percentage')),
  constraint ck_bonus_deposit_details_product check ( product in ('sports','casino','both')),
  constraint ck_bonus_deposit_details_platform check ( platform in ('web','android','ios')),
  constraint ck_bonus_deposit_details_wagering_requirement_type check ( wagering_requirement_type in ('absolute','multiplier')),
  constraint ck_bonus_deposit_details_multiplier_type check ( multiplier_type in ('bonus_amount','deposit_plus_bonus_amount')),
  constraint pk_bonus_deposit_details primary key (id)
);

-- apply alter tables
alter table reward.account_reward_log add column if not exists amount decimal(16,3);
alter table reward.reward add column if not exists bonus_deposit_details_id bigint;
-- apply post alter
alter table reward.account_reward_log add constraint ck_account_reward_log_source check ( source in ('quest','random_reward','internal')) not valid;
-- foreign keys and indices
create index ix_account_bonus_deposit_reward_account_id on reward.account_bonus_deposit_reward (account_id);
alter table reward.account_bonus_deposit_reward add constraint fk_account_bonus_deposit_reward_account_id foreign key (account_id) references reward.accounts (id) on delete restrict on update restrict;

create index ix_account_bonus_deposit_reward_reward_id on reward.account_bonus_deposit_reward (reward_id);
alter table reward.account_bonus_deposit_reward add constraint fk_account_bonus_deposit_reward_reward_id foreign key (reward_id) references reward.reward (id) on delete restrict on update restrict;

alter table reward.reward add constraint fk_reward_bonus_deposit_details_id foreign key (bonus_deposit_details_id) references reward.bonus_deposit_details (id) on delete restrict on update restrict;

create index if not exists ix_account_bonus_deposit_reward_account_id_type_status on reward.account_bonus_deposit_reward (account_id,type,status);
