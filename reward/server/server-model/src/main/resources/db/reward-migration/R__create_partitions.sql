-- ${flyway:timestamp}
DO $$
DECLARE
    part_record RECORD;
    partition_expr TEXT;
    partition_bounds TEXT;
    is_partition BOOLEAN;
BEGIN
    FOR part_record IN
        SELECT
            relname AS table_name,
            pg_get_expr(relpartbound, c.oid) AS partition_expr
        FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE n.nspname = 'engagement'
          AND relname LIKE 'account_reward_log_%'
          AND relkind = 'r'
    LOOP
        partition_expr := part_record.partition_expr;

        -- Log the partition expression
        RAISE NOTICE 'Partition expression: %', partition_expr;

        -- Move the table to the reward schema
        EXECUTE format('ALTER TABLE engagement.%I SET SCHEMA reward',
                    part_record.table_name);

        -- Extract the FOR VALUES part of the partition expression
        partition_bounds := substring(partition_expr FROM 'FOR VALUES (.*)');

        -- Log the partition bounds
        RAISE NOTICE 'Partition bounds: %', partition_bounds;

        -- Skip if partition_bounds is empty
        IF partition_bounds IS NULL OR trim(partition_bounds) = '' THEN
            RAISE NOTICE 'Skipping table %', part_record.table_name;
            CONTINUE;
        END IF;

        -- Check if the table is already a partition
        SELECT EXISTS (
            SELECT 1
            FROM pg_inherits
            WHERE inhrelid = format('reward.%I', part_record.table_name)::regclass
              AND inhparent = 'reward.account_reward_log'::regclass
        ) INTO is_partition;

        IF is_partition THEN
            RAISE NOTICE 'Table % is already a partition, skipping.', part_record.table_name;
            CONTINUE;
        END IF;

        -- Reattach as partition
        EXECUTE format(
            'ALTER TABLE reward.account_reward_log ATTACH PARTITION reward.%I FOR VALUES %s',
            part_record.table_name, partition_bounds
        );

        -- Log the final EXECUTE statement
        RAISE NOTICE 'Executing: ALTER TABLE reward.account_reward_log ATTACH PARTITION reward.%I FOR VALUES %s',
            part_record.table_name, partition_bounds;
    END LOOP;
END $$;

select partition('${partitioning.default.reward.precision}', 'account_reward_log', ${partitioning.default.reward.scale}, 'reward');
