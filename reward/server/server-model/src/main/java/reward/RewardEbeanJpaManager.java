package reward;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanJpaManager;
import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import reward.repo.DefaultRewardAccountRepo;
import reward.repo.DefaultRewardBrandRepo;
import reward.repo.DefaultRewardRepo;
import reward.repo.RewardAccountRepo;
import reward.repo.RewardBrandRepo;
import reward.repo.RewardRepo;

import java.util.concurrent.ScheduledExecutorService;

public class RewardEbeanJpaManager extends EbeanJpaManager implements RewardJpaManager {
    private final RewardBrandRepo rewardBrandRepo;
    private final RewardAccountRepo rewardAccountRepo;
    private final RewardRepo rewardRepo;

    public RewardEbeanJpaManager(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            Tracer tracer,
            SpiEbeanServer database,
            ScheduledExecutorService timer) {
        super(props, meterRegistry, tracer, database, timer);
        this.rewardBrandRepo = new DefaultRewardBrandRepo(this);
        this.rewardAccountRepo = new DefaultRewardAccountRepo(this);
        this.rewardRepo = new DefaultRewardRepo(this);

    }

    @Override
    public RewardAccountRepo accountRepo() {
        return rewardAccountRepo;
    }

    @Override
    public RewardBrandRepo brandRepo() {
        return rewardBrandRepo;
    }

    @Override
    public RewardRepo rewardRepo() {
        return rewardRepo;
    }
}
