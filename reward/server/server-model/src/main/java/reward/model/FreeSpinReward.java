package reward.model;

import reward.model.FreeSpinRewardDetails;
import reward.model.Reward;
import reward.model.RewardTypeSpec;
import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToOne;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@DiscriminatorValue(RewardTypeSpec.FREE_SPIN)
public class FreeSpinReward extends Reward {
    @OneToOne(cascade = CascadeType.ALL)
    private FreeSpinRewardDetails freeSpinRewardDetails;
}
