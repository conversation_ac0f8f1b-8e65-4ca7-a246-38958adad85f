package reward.model;

import io.ebean.annotation.Cache;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorType;
import jakarta.persistence.Entity;
import jakarta.persistence.Inheritance;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "reward", schema = Schemas.REWARD)
@Inheritance
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING)
@Cache(naturalKey = "code")
public abstract class Reward extends BasicModel {
    @Column(nullable = false, unique = true, updatable = false)
    private UUID code;

    @ManyToOne(optional = false)
    private RewardBrand brand;

    @Column
    private String name;

    @Column(nullable = false)
    private Integer rank;

    @Column
    private String createdBy;

    @Column
    private String modifiedBy;

    @Version
    private int version;

    public void setUpdatedBy(String updatedBy) {
        if (this.getId() == null) {
            this.setCreatedBy(updatedBy);
        } else {
            this.setModifiedBy(updatedBy);
        }
    }
}
