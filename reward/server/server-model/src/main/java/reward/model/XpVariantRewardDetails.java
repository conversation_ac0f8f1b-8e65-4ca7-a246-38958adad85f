package reward.model;

import java.math.BigDecimal;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "xp_variant_reward_details", schema = Schemas.REWARD)
@NoArgsConstructor
@AllArgsConstructor
public class XpVariantRewardDetails extends BasicModel {
    @Column(nullable = false)
    private String variantCode;
    @Column(nullable = false)
    private BigDecimal xpAmount;
}
