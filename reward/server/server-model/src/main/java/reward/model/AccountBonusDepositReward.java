package reward.model;

import io.ebean.annotation.Index;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Getter
@Setter
@Table(name = "account_bonus_deposit_reward", schema = Schemas.REWARD)
@Index(columnNames = {"account_id", "type", "status"})
@UniqueConstraint(columnNames = {"account_id", "reward_id"})
public class AccountBonusDepositReward extends BasicModel {

    @ManyToOne
    @JoinColumn(name = "account_id")
    private RewardAccount account;

    @Column
    private BonusDepositTypeSpec type;

    @Column
    private BonusDepositAvailabilityStatusSpec status;

    @Column
    private int claimed;

    @Column
    private Integer capacity;

    @ManyToOne
    @JoinColumn(name = "reward_id")
    private BonusDepositReward reward;

    public AccountBonusDepositReward(BonusDepositReward bonusDepositReward,
                                     RewardAccount account) {
        this.account = account;
        this.type = bonusDepositReward.getBonusDepositDetails().getBonusType();
        this.status = BonusDepositAvailabilityStatusSpec.AVAILABLE;
        this.capacity = bonusDepositReward.getBonusDepositDetails().getCapacity();
        this.reward = bonusDepositReward;
    }

    public boolean isAvailable() {
        return BonusDepositAvailabilityStatusSpec.AVAILABLE.equals(status);
    }

    public void incrementClaimedRewardsCounter() {
        this.claimed += 1;
        if (this.claimed == this.capacity) {
            this.status = BonusDepositAvailabilityStatusSpec.USED;
        }
    }
}
