package reward.model;

import common.CoreConstraints;
import io.ebean.annotation.DbArray;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "bonus_deposit_details", schema = Schemas.REWARD)
@NoArgsConstructor
@AllArgsConstructor
public class BonusDepositRewardDetails extends BasicModel {

    @Column(nullable = false)
    private String bonusCode;

    @Column
    private String internalName;

    @Column(nullable = false)
    private String currency;

    @DbArray
    private List<String> countries = new ArrayList<>();

    @Column(nullable = false)
    private BonusCategorySpec category;

    @Column(nullable = false)
    private BonusDepositTypeSpec bonusType;

    @Column(nullable = false)
    private BonusDepositRewardTypeSpec rewardType;

    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal amount;

    @Column
    private BigDecimal percentValue;

    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal maxAmount;

    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE, nullable = false)
    private BigDecimal minDepositAmount;

    @Column(nullable = false)
    private boolean optIn;

    @Column(nullable = false)
    private int capacity;

    @Column(nullable = false)
    private BonusProductSpec product;

    @Column(nullable = false)
    private boolean forfeitBonusOnWithdrawal;

    @Column(nullable = false)
    private Integer priority;

    @Column
    private Date appliedFrom;

    @Column
    private Date appliedTo;

    @Column(nullable = false)
    private BonusDepositPlatformSpec platform;

    @DbArray
    private List<String> includeSegments = new ArrayList<>();

    @DbArray
    private List<String> includeSegmentTags = new ArrayList<>();

    @DbArray
    private List<String> excludeSegments = new ArrayList<>();

    @DbArray
    private List<String> excludeSegmentTags = new ArrayList<>();

    @Column(nullable = false)
    private String overallMinOdds;

    @Column(nullable = false)
    private String selectionLevelOdds;

    @Column(nullable = false)
    private WageringRequirements wageringRequirementType;

    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal absoluteValue;

    @Column
    private Integer multiplierCount;

    @Column
    private WageringMultiplierTypeSpec multiplierType;

    @Column
    private Integer claimValidity;

    @Column
    private Integer wageringValidity;

    @Column(nullable = false)
    private boolean inactive;

    @Column
    private String comments;

    @Column
    private String extraRewardCode;

    @DbArray
    private List<BonusDepositRuleSpec> rules = new ArrayList<>();

}
