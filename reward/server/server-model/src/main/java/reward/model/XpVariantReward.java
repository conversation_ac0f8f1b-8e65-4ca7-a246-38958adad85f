package reward.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToOne;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@DiscriminatorValue(RewardTypeSpec.XP_VARIANT)
public class XpVariantReward extends Reward {
    @OneToOne(cascade = CascadeType.ALL)
    private XpVariantRewardDetails xpVariantRewardDetails;
}
