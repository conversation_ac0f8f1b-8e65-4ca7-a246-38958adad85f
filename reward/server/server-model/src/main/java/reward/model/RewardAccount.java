package reward.model;

import java.time.Instant;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;

import api.v1.AccountRoutingFlatten;
import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import core.model.CoreAccount;
import reward.model.RewardBrand;
import io.ebean.annotation.Cache;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import io.netty.util.AsciiString;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "accounts", schema = Schemas.REWARD, uniqueConstraints = {@UniqueConstraint(columnNames = {"brand_id", "remote_id"})})
@NoArgsConstructor
@Cache(naturalKey = "remoteId")
public class RewardAccount extends CoreAccount {

    @Column(nullable = false, unique = true)
    private UUID code;

    @Index(unique = true)
    @Column(nullable = false)
    private Long remoteId;

    @ManyToOne(optional = false)
    private RewardBrand brand;

    @Column(nullable = false)
    private boolean deleted;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Instant createdAt;

    @WhenModified
    @Column(nullable = false)
    private Instant modifiedAt;

    public RewardAccount(RewardBrand brand) {
        setBrand(brand);
    }
    public void assertHash(String expected) throws ApplicationException {
        if (!StringUtils.equals(getHash(), expected)) {
            throw EnhancedApplicationException.of("Something went wrong, command is unroutable", Code.ERR_AUTH, Reason.COMMAND_NOT_ROUTABLE);
        }
    }
    public AccountRoutingFlatten flatten() {
        return AccountRoutingFlatten.standard(AsciiString.cached(getHash()), remoteId);
    }
}
