package reward.model;

import api.v1.ForcementModeSpec;
import core.model.CoreBrand;
import io.ebean.annotation.Cache;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "brands", schema = Schemas.REWARD)
@Cache(naturalKey = "name")
public class RewardBrand extends CoreBrand {

    @Column(nullable = false)
    private ForcementModeSpec mode;

    @Column
    private String domain;

    @Version
    private int version;

    public RewardBrand(String name) {
        setName(name);
        setMode(ForcementModeSpec.DEFAULT);
    }
}
