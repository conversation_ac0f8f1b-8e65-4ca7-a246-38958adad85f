package reward.model;

import io.ebean.annotation.DbPartition;
import io.ebean.annotation.Index;
import io.ebean.annotation.PartitionMode;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@Entity
@DbPartition(mode = PartitionMode.MONTH, property = "at")
@Table(name = "account_reward_log", schema = Schemas.REWARD)
@UniqueConstraint(columnNames = {"transaction_code", "at"})
public class AccountRewardLog {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @ManyToOne
    private Reward reward;

    @ManyToOne
    private RewardAccount account;

    @Column(nullable = false)
    private String transactionCode;

    @Column
    private AccountRewardSourceTypeSpec source;

    @Column
    private String sourceReference;

    @Column
    private BigDecimal amount;

    @Column
    private AccountRewardStatusSpec status;

    @Column(nullable = false)
    private int attempt;

    @Index
    @Column(nullable = false, updatable = false)
    private LocalDate at = LocalDate.now();

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public AccountRewardLog(RewardAccount account, Reward reward) {
        this.reward = reward;
        this.account = account;
        this.status = AccountRewardStatusSpec.CREATED;
        this.attempt = 0;
    }

    public boolean isSuccess() {
        return status == AccountRewardStatusSpec.SUCCESS;
    }

}
