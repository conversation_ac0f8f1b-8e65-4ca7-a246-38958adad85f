package reward.model;

import java.math.BigDecimal;

import io.vavr.CheckedConsumer;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "freespin_reward_details", schema = Schemas.REWARD)
@NoArgsConstructor
@AllArgsConstructor
public class FreeSpinRewardDetails extends BasicModel {
    // ~ prefix that will be used for bonus code generation
    @Column(nullable = false)
    private String bonusCodePrefix;

    @Column
    private String bonusCode;

    @Column(nullable = false)
    private Integer count;

    @Column(nullable = false)
    private String productCode;

    @Column(nullable = false)
    private BigDecimal betValue;

    @Column
    private Integer betLevel;

    @OneToOne(cascade = CascadeType.ALL)
    private FreeSpinRewardDetails fallback;

    public void visit(CheckedConsumer<FreeSpinRewardDetails> consumer) throws Throwable {
        consumer.accept(this);
        if (fallback != null) {
            fallback.visit(consumer);
        }
    }
}
