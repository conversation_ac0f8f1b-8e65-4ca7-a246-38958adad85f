package reward.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "purchase_offer_reward_details", schema = Schemas.REWARD)
public class PurchaseOfferRewardDetails extends BasicModel {
    @Column(nullable = false)
    private String offerCode;

    @Column
    private Integer discount;
}
