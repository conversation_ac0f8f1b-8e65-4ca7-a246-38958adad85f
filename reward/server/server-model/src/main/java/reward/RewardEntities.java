package reward;

import model.CoreEntities;
import reward.model.AccountBonusDepositReward;
import reward.model.AccountRewardLog;
import reward.model.BonusDepositReward;
import reward.model.BonusDepositRewardDetails;
import reward.model.CoinReward;
import reward.model.CoinRewardDetails;
import reward.model.FreeSpinReward;
import reward.model.FreeSpinRewardDetails;
import reward.model.PurchaseOfferReward;
import reward.model.PurchaseOfferRewardDetails;
import reward.model.Reward;
import reward.model.RewardAccount;
import reward.model.RewardBrand;
import reward.model.XpVariantReward;
import reward.model.XpVariantRewardDetails;

public class RewardEntities extends CoreEntities {

    public RewardEntities() {
        super();
        add(RewardBrand.class);
        add(RewardAccount.class);
        add(CoinRewardDetails.class);
        add(CoinReward.class);
        add(FreeSpinRewardDetails.class);
        add(FreeSpinReward.class);
        add(PurchaseOfferRewardDetails.class);
        add(PurchaseOfferReward.class);
        add(BonusDepositRewardDetails.class);
        add(BonusDepositReward.class);
        add(AccountBonusDepositReward.class);
        add(XpVariantReward.class);
        add(XpVariantRewardDetails.class);
        add(Reward.class);
        add(AccountRewardLog.class);
    }
}
