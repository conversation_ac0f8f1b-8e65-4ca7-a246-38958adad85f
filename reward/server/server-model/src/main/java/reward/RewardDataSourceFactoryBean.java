package reward;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.jdbc.HikariDataSourceFactoryBean;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.cloud.service.ServiceInfo;

import javax.sql.DataSource;

public class RewardDataSourceFactoryBean extends HikariDataSourceFactoryBean implements RewardDatasourceProvider<DataSource> {
    public RewardDataSourceFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, ServiceInfo si) {
        super(props, meterRegistry, si);
    }
}
