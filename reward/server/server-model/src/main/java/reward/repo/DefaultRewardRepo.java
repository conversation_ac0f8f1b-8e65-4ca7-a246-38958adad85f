package reward.repo;

import com.turbospaces.ebean.JpaManager;
import io.ebean.Transaction;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import reward.model.AccountBonusDepositReward;
import reward.model.AccountRewardLog;
import reward.model.BonusDepositReward;
import reward.model.BonusDepositTypeSpec;
import reward.model.Reward;
import reward.model.RewardAccount;
import reward.model.query.QAccountBonusDepositReward;
import reward.model.query.QAccountRewardLog;
import reward.model.query.QBonusDepositReward;
import reward.model.query.QReward;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

public class DefaultRewardRepo implements RewardRepo {
    private final JpaManager ebean;

    public DefaultRewardRepo(JpaManager ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public void save(Reward reward, Transaction tx) {
        ebean.save(reward, tx);
    }

    @Override
    public void save(AccountRewardLog reward, Transaction tx) {
        ebean.save(reward, tx);
    }

    @Override
    public Optional<AccountRewardLog> findAccountRewardLogByTransactionId(RewardAccount rewardAccount, String transactionCode, Transaction tx) {
        return new QAccountRewardLog(ebean)
                .usingTransaction(tx)
                .account.eq(rewardAccount)
                .transactionCode.eq(transactionCode)
                .findOneOrEmpty();
    }

    @Override
    public List<Reward> findByCodes(List<UUID> codes, Transaction tx) {
        return new QReward(ebean)
                .usingTransaction(tx)
                .code.in(codes)
                .findList();
    }

    @Override
    public List<BonusDepositReward> findGlobalBonusDepositRewards(FindBonusDepositRewardParams params, Transaction tx) {
        params.type = BonusDepositTypeSpec.GLOBAL;
        return getBonusDepositRewardQuery(params, tx).findList();
    }

    @Override
    public List<BonusDepositReward> findAssignableBonusDepositRewards(FindBonusDepositRewardParams params, Transaction tx) {
        params.type = BonusDepositTypeSpec.ASSIGNABLE;
        return getBonusDepositRewardQuery(params, tx).findList();
    }

    @Override
    public Optional<AccountBonusDepositReward> findAccountBonusDepositReward(BonusDepositReward reward, RewardAccount account, Transaction tx) {
        var params = FindBonusDepositRewardParams.builder();

        params.account(account);
        params.reward(reward);

        return getAccountBonusDepositRewardQuery(params.build(), tx)
                .findOneOrEmpty();
    }

    private QBonusDepositReward getBonusDepositRewardQuery(FindBonusDepositRewardParams params, Transaction tx) {
        var q = new QBonusDepositReward(ebean).usingTransaction(tx);

        q.bonusDepositDetails.fetch();

        if (params.brand != null) {
            q.brand.eq(params.brand);
        }
        if (params.inactive != null) {
            q.bonusDepositDetails.inactive.eq(params.inactive);
        }
        if (params.type != null) {
            q.bonusDepositDetails.bonusType.eq(params.type);
        }
        if (params.platform != null) {
            q.bonusDepositDetails.platform.eq(params.platform);
        }
        if (params.depositedAmount != null) {
            q.bonusDepositDetails.minDepositAmount.le(params.depositedAmount);
        }
        if (StringUtils.isNotBlank(params.country)) {
            q.bonusDepositDetails.countries.contains(params.country);
        }
        if (params.from != null) {
            q.bonusDepositDetails.appliedFrom.le(params.from);
        }
        if (params.to != null) {
            q.bonusDepositDetails.appliedTo.ge(params.to);
        }
        if (CollectionUtils.isNotEmpty(params.segments)) {
            var segmentsArray = params.segments.toArray(String[]::new);
            q.bonusDepositDetails.includeSegments.contains(segmentsArray);
            q.bonusDepositDetails.excludeSegments.notContains(segmentsArray);
        }
        if (CollectionUtils.isNotEmpty(params.segmentTags)) {
            var segmentTagsArray = params.segmentTags.toArray(String[]::new);
            q.bonusDepositDetails.includeSegmentTags.contains(segmentTagsArray);
            q.bonusDepositDetails.excludeSegmentTags.notContains(segmentTagsArray);
        }

        q.orderBy().bonusDepositDetails.priority.asc();

        return q;
    }

    private QAccountBonusDepositReward getAccountBonusDepositRewardQuery(FindBonusDepositRewardParams params, Transaction tx) {
        var q = new QAccountBonusDepositReward(ebean).usingTransaction(tx);

        q.reward.bonusDepositDetails.fetch();

        if (params.account != null) {
            q.account.eq(params.account);
        }
        if (params.reward != null) {
            q.reward.eq(params.reward);
        }
        if (params.brand != null) {
            q.reward.brand.eq(params.brand);
        }
        if (params.type != null) {
            q.type.eq(params.type);
        }
        if (params.status != null) {
            q.status.eq(params.status);
        }
        if (params.inactive != null) {
            q.reward.bonusDepositDetails.inactive.eq(params.inactive);
        }
        if (params.type != null) {
            q.reward.bonusDepositDetails.bonusType.eq(params.type);
        }
        if (params.platform != null) {
            q.reward.bonusDepositDetails.platform.eq(params.platform);
        }
        if (params.depositedAmount != null) {
            q.reward.bonusDepositDetails.minDepositAmount.le(params.depositedAmount);
        }
        if (StringUtils.isNotBlank(params.country)) {
            q.reward.bonusDepositDetails.countries.contains(params.country);
        }
        if (params.from != null) {
            q.reward.bonusDepositDetails.appliedFrom.le(params.from);
        }
        if (params.to != null) {
            q.reward.bonusDepositDetails.appliedTo.ge(params.to);
        }
        if (CollectionUtils.isNotEmpty(params.segments)) {
            var segmentsArray = params.segments.toArray(String[]::new);
            q.reward.bonusDepositDetails.includeSegments.contains(segmentsArray);
            q.reward.bonusDepositDetails.excludeSegments.notContains(segmentsArray);
        }
        if (CollectionUtils.isNotEmpty(params.segmentTags)) {
            var segmentTagsArray = params.segmentTags.toArray(String[]::new);
            q.reward.bonusDepositDetails.includeSegmentTags.contains(segmentTagsArray);
            q.reward.bonusDepositDetails.excludeSegmentTags.notContains(segmentTagsArray);
        }

        q.orderBy().reward.bonusDepositDetails.priority.asc();

        return q;
    }
}
