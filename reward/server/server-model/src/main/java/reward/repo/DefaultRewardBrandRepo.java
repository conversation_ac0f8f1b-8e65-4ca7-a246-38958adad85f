package reward.repo;

import static api.util.ErrorDetailUtils.detail;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.turbospaces.ebean.JpaManager;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import reward.model.RewardBrand;
import reward.model.query.QRewardBrand;
import io.ebean.Transaction;

public class DefaultRewardBrandRepo implements RewardBrandRepo {

    private final JpaManager ebean;

    public DefaultRewardBrandRepo(JpaManager ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public void configureBrand(String brandName, Transaction tx) {
        var created = brand(brandName, tx).isPresent();
        if (!created) {
            RewardBrand brand = new RewardBrand(brandName);
            ebean.save(brand, tx);
        }
    }

    @Override
    public RewardBrand requiredBrand(String name, Transaction tx) throws ApplicationException {
        return brand(name, tx).orElseThrow(
                EnhancedApplicationException.orElseThrow("unable to find brand by name: " + name, Code.ERR_NOT_FOUND, Reason.UNABLE_TO_FIND_BRAND_BY_NAME,
                        List.of(detail("name", name))));
    }

    @Override
    public Optional<RewardBrand> brand(String name, Transaction tx) {
        QRewardBrand q = new QRewardBrand(ebean).usingTransaction(tx);
        q.name.eq(name);
        return q.findOneOrEmpty();
    }
}
