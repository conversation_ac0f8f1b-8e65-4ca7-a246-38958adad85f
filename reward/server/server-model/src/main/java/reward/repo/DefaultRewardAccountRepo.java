package reward.repo;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.turbospaces.ebean.JpaManager;

import reward.model.RewardAccount;
import reward.model.RewardBrand;
import reward.model.query.QRewardAccount;
import io.ebean.Transaction;

public class DefaultRewardAccountRepo implements RewardAccountRepo {
    private final JpaManager ebean;

    public DefaultRewardAccountRepo(JpaManager ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public Optional<RewardAccount> account(Long id, Transaction tx) {
        QRewardAccount q = new QRewardAccount(ebean).usingTransaction(tx);
        q.setId(id);
        return q.findOneOrEmpty();
    }

    @Override
    public Map<Long, RewardAccount> filterExistedAccountsByBrand(RewardBrand brand, Transaction tx) {
        QRewardAccount query = new QRewardAccount(ebean).usingTransaction(tx);
        query.deleted.isFalse();
        query.brand.eq(brand);
        return query.id.asMapKey().findMap();
    }

    @Override
    public Optional<RewardAccount> accountByRemoteId(long id, Transaction tx) {
        QRewardAccount q = new QRewardAccount(ebean).usingTransaction(tx);
        q.remoteId.eq(id);
        q.brand.fetch();
        return q.findOneOrEmpty();
    }

}
