package reward.repo;

import java.util.Map;
import java.util.Optional;

import api.v1.ApplicationException;
import api.v1.Code;
import reward.model.RewardAccount;
import reward.model.RewardBrand;
import io.ebean.Transaction;

public interface RewardAccountRepo {

    Optional<RewardAccount> account(Long id, Transaction tx);

    default RewardAccount requiredAccount(long id, Transaction tx) throws ApplicationException {
        return account(id, tx).orElseThrow(ApplicationException.orElseThrow("unable to find account: " + id, Code.ERR_NOT_FOUND));
    }

    Map<Long, RewardAccount> filterExistedAccountsByBrand(RewardBrand brand, Transaction tx);

    Optional<RewardAccount> accountByRemoteId(long remoteId, Transaction tx);
}
