package reward.repo;

import api.v1.ApplicationException;
import api.v1.Code;
import io.ebean.Transaction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import reward.model.AccountBonusDepositReward;
import reward.model.AccountRewardLog;
import reward.model.BonusDepositAvailabilityStatusSpec;
import reward.model.BonusDepositPlatformSpec;
import reward.model.BonusDepositReward;
import reward.model.BonusDepositTypeSpec;
import reward.model.Reward;
import reward.model.RewardAccount;
import reward.model.RewardBrand;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface RewardRepo {
    void save(Reward reward, Transaction tx);

    List<Reward> findByCodes(List<UUID> codes, Transaction tx);

    default Reward requiredReward(UUID code, Transaction tx) throws ApplicationException {
        return findByCodes(List.of(code), tx).stream()
                .findFirst()
                .orElseThrow(() -> ApplicationException.of("Reward not found", Code.ERR_NOT_FOUND));
    }

    void save(AccountRewardLog reward, Transaction tx);

    Optional<AccountRewardLog> findAccountRewardLogByTransactionId(RewardAccount account, String transactionId, Transaction tx);

    // Bonus deposit rewards
    List<BonusDepositReward> findGlobalBonusDepositRewards(FindBonusDepositRewardParams params, Transaction tx);

    List<BonusDepositReward> findAssignableBonusDepositRewards(FindBonusDepositRewardParams params, Transaction tx);

    Optional<AccountBonusDepositReward> findAccountBonusDepositReward(BonusDepositReward reward, RewardAccount account, Transaction tx);

    @AllArgsConstructor
    @Builder
    final class FindBonusDepositRewardParams {
        public RewardBrand brand;
        public RewardAccount account;
        public BonusDepositTypeSpec type;
        public BonusDepositAvailabilityStatusSpec status;
        public Boolean inactive;
        public BonusDepositPlatformSpec platform;
        public BigDecimal depositedAmount;
        public String country;
        public Date from;
        public Date to;
        public BonusDepositReward reward;
        public final List<String> segments = new ArrayList<>();
        public final List<String> segmentTags = new ArrayList<>();

        public void addSegment(String segment) {
            this.segments.add(segment);
        }

        public void addSegmentTags(List<String> segmentTags) {
            this.segmentTags.addAll(segmentTags);
        }
    }
}
