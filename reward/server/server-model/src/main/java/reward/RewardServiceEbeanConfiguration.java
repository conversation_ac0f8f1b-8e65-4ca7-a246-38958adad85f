package reward;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import ebean.OpenPGPEncryptor;
import io.ebean.cache.ServerCachePlugin;
import io.ebean.config.EncryptKeyManager;
import io.ebean.config.dbplatform.DatabasePlatform;
import io.ebean.event.BeanPersistController;
import reward.model.Reward;
import reward.model.RewardAccount;
import reward.model.RewardBrand;
import org.springframework.beans.factory.InitializingBean;

import javax.sql.DataSource;
import java.util.List;

public class RewardServiceEbeanConfiguration extends EbeanDatabaseConfig implements InitializingBean {

    public RewardServiceEbeanConfiguration(DataSource ds,
                                           ApplicationProperties props,
                                           ServerCachePlugin cacheManager,
                                           EncryptKeyManager encryptKeyManager,
                                           List<BeanPersistController> controllers,
                                           DatabasePlatform databasePlatform) {
        this(ds, props, cacheManager, encryptKeyManager, databasePlatform, controllers);
    }

    public RewardServiceEbeanConfiguration(
            DataSource ds,
            ApplicationProperties props,
            ServerCachePlugin cacheManager,
            EncryptKeyManager encryptKeyManager,
            DatabasePlatform databasePlatform,
            List<BeanPersistController> controllers) {
        super(ds, props);
        setServerCachePlugin(cacheManager);
        setEncryptKeyManager(encryptKeyManager);
        setEncryptor(new OpenPGPEncryptor());
        setDatabasePlatform(databasePlatform);
        addAll(new RewardEntities());
        controllers.forEach(this::add);
    }

    @Override
    public void afterPropertiesSet() {
        setReplicated(RewardAccount.class);
        setReplicated(RewardBrand.class);
        setReplicated(Reward.class);
    }
}
