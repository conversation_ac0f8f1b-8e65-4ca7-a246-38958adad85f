package reward.config;

import java.util.List;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.flywaydb.core.api.output.MigrateResult;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.boot.test.EbeanSqlQueryCounter;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.ebean.FlywayUberRunner;
import com.turbospaces.ebean.MockCacheManager;

import ebean.DefaultEncryptKeyManager;
import io.ebean.config.EncryptKeyManager;
import io.ebean.platform.h2.H2Platform;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import lombok.extern.slf4j.Slf4j;
import model.Schemas;
import reward.RewardEbeanJpaManager;
import reward.RewardServiceEbeanConfiguration;

@Slf4j
@Configuration
public class RewardH2DatabaseDiModule {
    public static final String[] SCHEMAS = {Schemas.REWARD};

    @Bean
    public RewardH2DataSourceFactoryBean rewardDs() {
        return new RewardH2DataSourceFactoryBean(SCHEMAS);
    }

    @Bean
    public EncryptKeyManager encryptKeyManager(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry) {
        return new DefaultEncryptKeyManager(props, cloud, meterRegistry);
    }

    @Bean
    public CacheManager cacheManager(ApplicationProperties props, MeterRegistry meterRegistry) {
        return new MockCacheManager(props, meterRegistry);
    }

    @Bean
    public RewardServiceEbeanConfiguration ebeanConfig(ApplicationProperties props, CacheManager cacheManager, RewardH2DataSourceFactoryBean ds, EncryptKeyManager encryptKeyManager) throws Exception {
        var config = new RewardServiceEbeanConfiguration(ds.getObject(), props, cacheManager, encryptKeyManager, new H2Platform(), List.of());
        config.add(new EbeanSqlQueryCounter());
        return config;
    }

    @Bean
    public MockEbeanFactoryBean ebean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, RewardServiceEbeanConfiguration config) {
        return new MockEbeanFactoryBean(props, meterRegistry, tracer, config) {
            @Override
            protected RewardEbeanJpaManager createInstance() throws Exception {
                RewardEbeanJpaManager ebean = super.createInstance();
                MigrateResult migrateResult = FlywayUberRunner.run(ebean, SCHEMAS);
                log.info(ReflectionToStringBuilder.toString(migrateResult, ToStringStyle.SHORT_PREFIX_STYLE));
                return ebean;
            }
        };
    }

}
