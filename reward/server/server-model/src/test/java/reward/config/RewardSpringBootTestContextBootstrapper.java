package reward.config;

import com.turbospaces.boot.test.AbstractSpringBootTestContextBootstrapper;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EmbeddedH2FactoryBean;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestContext;

public class RewardSpringBootTestContextBootstrapper extends AbstractSpringBootTestContextBootstrapper<ApplicationProperties> {
    @Override
    protected ApplicationProperties createProps() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        withH2(false, EmbeddedH2FactoryBean.NAMESPACE_JUNIT);
        return new ApplicationProperties(cfg.factory());
    }

    @Override
    public TestContext buildTestContext() {
        var context = super.buildTestContext();
        context.markApplicationContextDirty(DirtiesContext.HierarchyMode.EXHAUSTIVE);
        return context;
    }
}
