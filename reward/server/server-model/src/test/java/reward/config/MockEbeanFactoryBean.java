package reward.config;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.AbstractEbeanFactoryBean;
import com.turbospaces.ebean.EbeanDatabaseConfig;
import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;
import reward.RewardEbeanJpaManager;

public class MockEbeanFactoryBean extends AbstractEbeanFactoryBean<RewardEbeanJpaManager> {
    public MockEbeanFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry, Tracer tracer, EbeanDatabaseConfig config) {
        super(props, meterRegistry, tracer, config);
    }

    @Override
    public Class<?> getObjectType() {
        return RewardEbeanJpaManager.class;
    }

    @Override
    protected RewardEbeanJpaManager createEbean(SpiEbeanServer db) {
        return new RewardEbeanJpaManager(props, meterRegistry, tracer, db, timer);
    }
}
