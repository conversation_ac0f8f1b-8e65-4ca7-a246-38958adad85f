package reward.repo;

import com.turbospaces.common.PlatformUtil;
import com.turbospaces.ebean.DataGeneratorUtil;
import reward.RewardEbeanJpaManager;
import reward.RewardJpaManager;
import reward.config.RewardH2DatabaseDiModule;
import reward.model.RewardAccount;
import reward.model.RewardBrand;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.BootstrapWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@BootstrapWith(reward.config.RewardSpringBootTestContextBootstrapper.class)
@ContextConfiguration(classes = {RewardH2DatabaseDiModule.class})
@TestExecutionListeners(mergeMode = TestExecutionListeners.MergeMode.MERGE_WITH_DEFAULTS, listeners = {AbstractRepoTest.class})
public class AbstractRepoTest implements TestExecutionListener {

    @Autowired
    protected RewardJpaManager ebean;

    @Override
    public void afterTestMethod(TestContext testContext) {
        DataGeneratorUtil.clearH2Db(testContext.getApplicationContext().getBean(RewardEbeanJpaManager.class), RewardH2DatabaseDiModule.SCHEMAS);
    }

    public RewardBrand createBrand(String name) {
        var brand = new RewardBrand(name);
        ebean.save(brand);
        return brand;
    }

    public RewardAccount createAccount(Long remoteId, RewardBrand brand) {
        var account = new RewardAccount(brand);
        account.setCode(PlatformUtil.randomUUID());
        account.setRemoteId(remoteId);
        account.setHash(remoteId + "/" + remoteId);
        ebean.save(account);
        return account;
    }

}
