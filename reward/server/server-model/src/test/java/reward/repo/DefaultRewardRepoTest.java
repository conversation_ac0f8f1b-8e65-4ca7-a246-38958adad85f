package reward.repo;

import java.math.BigDecimal;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.turbospaces.common.PlatformUtil;

import reward.CacheTestUtils;
import reward.model.RewardBrand;
import reward.model.CoinReward;
import reward.model.CoinRewardDetails;
import reward.model.FreeSpinReward;
import reward.model.FreeSpinRewardDetails;
import reward.model.Reward;

class DefaultRewardRepoTest extends AbstractRepoTest {

    @Test
    void testRewardCreatedAndFound() throws Throwable {
        // ~ creating a coin reward
        var coinReward = new CoinReward();
        coinReward.setName("coin");
        coinReward.setCode(PlatformUtil.randomUUID());
        coinReward.setRank(1);
        CoinRewardDetails coinRewardDetails = new CoinRewardDetails();
        coinRewardDetails.setSc(BigDecimal.ONE);
        coinReward.setCoinRewardDetails(coinRewardDetails);

        RewardBrand brand = new RewardBrand("brand");
        ebean.save(brand);
        try (var tx = ebean.newTransaction()) {
            coinReward.setBrand(brand);
            ebean.rewardRepo().save(coinReward, tx);
            tx.commit();
        }

        // ~ Cache assertions
        Assertions.assertEquals(0, CacheTestUtils.naturalCacheStats(ebean, RewardBrand.class, false).getSize());
        try (var tx = ebean.newReadOnlyTransaction()) {
            ebean.brandRepo().brand(brand.getName(), tx);
        }
        Assertions.assertEquals(1, CacheTestUtils.naturalCacheStats(ebean, RewardBrand.class, false).getSize());
        try (var tx = ebean.newReadOnlyTransaction()) {
            ebean.brandRepo().brand(brand.getName(), tx);
        }
        Assertions.assertEquals(1, CacheTestUtils.naturalCacheStats(ebean, RewardBrand.class, false).getHitCount());

        // ~ creating a free spin reward, two level cascading
        var freeSpin = new FreeSpinReward();
        freeSpin.setName("freeSpin");
        freeSpin.setBrand(brand);
        freeSpin.setCode(PlatformUtil.randomUUID());
        freeSpin.setRank(1);
        FreeSpinRewardDetails details = createFreeSpinRewardDetails();
        FreeSpinRewardDetails fallback1 = createFreeSpinRewardDetails();
        FreeSpinRewardDetails fallback2 = createFreeSpinRewardDetails();
        details.setFallback(fallback1);
        fallback1.setFallback(fallback2);
        freeSpin.setFreeSpinRewardDetails(details);
        try (var tx = ebean.newTransaction()) {
            ebean.rewardRepo().save(freeSpin, tx);
            tx.commit();
        }

        List<Reward> rewards;
        Assertions.assertEquals(0, CacheTestUtils.naturalCacheStats(ebean, Reward.class, false).getSize());
        try (var tx = ebean.newReadOnlyTransaction()) {
           rewards = ebean.rewardRepo().findByCodes(List.of(coinReward.getCode(), freeSpin.getCode()), tx);
        }
        Assertions.assertEquals(2, rewards.size());
        Assertions.assertEquals(2, CacheTestUtils.naturalCacheStats(ebean, Reward.class, false).getSize());
        try (var tx = ebean.newReadOnlyTransaction()) {
            rewards = ebean.rewardRepo().findByCodes(List.of(coinReward.getCode(), freeSpin.getCode()), tx);
        }
        Assertions.assertEquals(2, CacheTestUtils.naturalCacheStats(ebean, Reward.class, false).getHitCount());



        FreeSpinReward freeSpinRewardFound = (FreeSpinReward) rewards.stream().filter(r -> r instanceof FreeSpinReward).findFirst().get();
        Assertions.assertNotNull(freeSpinRewardFound.getFreeSpinRewardDetails().getFallback());
        Assertions.assertNotNull(freeSpinRewardFound.getFreeSpinRewardDetails().getFallback().getFallback());
        CoinReward coinRewardFound = (CoinReward) rewards.stream().filter(r -> r instanceof CoinReward).findFirst().get();
        Assertions.assertNotNull(coinRewardFound.getCoinRewardDetails().getId());
    }

    private static FreeSpinRewardDetails createFreeSpinRewardDetails() {
        FreeSpinRewardDetails details = new FreeSpinRewardDetails();
        details.setCount(42);
        details.setProductCode("game code");
        details.setBonusCodePrefix("prefix");
        details.setBonusCode("bonus code");
        details.setBetLevel(1);
        details.setBetValue(BigDecimal.TEN);
        return details;
    }
}