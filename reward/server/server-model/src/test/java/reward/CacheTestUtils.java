package reward;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import io.ebean.cache.ServerCacheStatistics;
import io.ebean.typequery.TQAssocBean;
import lombok.experimental.UtilityClass;

@UtilityClass
public class CacheTestUtils {
    public static ServerCacheStatistics beanCacheStats(RewardJpaManager ebean, Class<?> entityType, boolean reset) {
        var cacheStats = ebean.cacheManager().beanCache(entityType).statistics(reset);
        assertNotNull(cacheStats);
        return cacheStats;
    }

    public static ServerCacheStatistics naturalCacheStats(RewardJpaManager ebean, Class<?> entityType, boolean reset) {
        var cacheStats = ebean.cacheManager().naturalKeyCache(entityType).statistics(reset);
        assertNotNull(cacheStats);
        return cacheStats;
    }

    public static ServerCacheStatistics queryCacheStats(RewardJpaManager ebean, Class<?> entityType, boolean reset) {
        var cacheStats = ebean.cacheManager().queryCache(entityType).statistics(reset);
        assertNotNull(cacheStats);
        return cacheStats;
    }

    public static void cleanBeanCache(RewardJpaManager ebean, Class<?> entityType) {
        ebean.cacheManager().queryCache(entityType).clear();
        ebean.cacheManager().beanCache(entityType).statistics(true);
        assertEquals(0, ebean.cacheManager().beanCache(entityType).size());
    }

    public static void cleanQueryCache(RewardJpaManager ebean, Class<?> entityType) {
        ebean.cacheManager().queryCache(entityType).clear();
        ebean.cacheManager().queryCache(entityType).statistics(true);
        assertEquals(0, ebean.cacheManager().queryCache(entityType).size());
    }

    public static ServerCacheStatistics collectionCacheStats(RewardJpaManager ebean, Class<?> entityType,
                                                             TQAssocBean<?, ?, ?> assoc, boolean reset) {
        var cacheStats = ebean.cacheManager().collectionIdsCache(entityType, assoc.toString()).statistics(reset);
        assertNotNull(cacheStats);
        return cacheStats;
    }

}
