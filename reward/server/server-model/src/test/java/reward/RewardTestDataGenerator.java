package reward;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.test.context.TestContext;
import org.springframework.test.context.TestExecutionListener;

import com.turbospaces.ebean.DataGeneratorUtil;
import com.turbospaces.ebean.EbeanJpaManager;

import io.ebean.Transaction;
import model.Schemas;
import reward.model.RewardBrand;

public class RewardTestDataGenerator implements TestExecutionListener {
    public static final String TEST_BRAND = "bluedream";
    public static final String SC_CURRENCY = "SC";

    @Override
    public void beforeTestMethod(TestContext testContext) {
        EbeanJpaManager ebean = getEbean(testContext);
        try (Transaction tx = ebean.newTransaction()) {
            ebean.save(new RewardBrand(TEST_BRAND), tx);
            tx.commit();
        } catch (Throwable e) {
            ExceptionUtils.wrapAndThrow(e);
        }
    }

    @Override
    public void afterTestMethod(TestContext testContext) {
        DataGeneratorUtil.clearH2Db(getEbean(testContext), new String[]{Schemas.PAYMENT, Schemas.UAM, Schemas.CORE, Schemas.LOYALTY, Schemas.REWARD});
    }

    public static RewardBrand getBrand(RewardEbeanJpaManager ebean) {
        return ebean.find(RewardBrand.class).where().eq("name", TEST_BRAND).findOne();
    }

    private static EbeanJpaManager getEbean(TestContext testContext) {
        return testContext.getApplicationContext().getBean(EbeanJpaManager.class);
    }
}
