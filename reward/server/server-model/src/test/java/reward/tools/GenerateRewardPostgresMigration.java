package reward.tools;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.ebean.GeneratePostgresMigration;
import common.CoreConstraints;
import reward.RewardEntities;
import reward.RewardModelProperties;

public class GenerateRewardPostgresMigration implements CoreConstraints {
    public static void main(String... args) throws Throwable {
        ApplicationConfig cfg = ApplicationConfig.mock();
        RewardModelProperties props = new RewardModelProperties(cfg.factory());

        cfg.loadLocalDevProperties();
        cfg.setDefaultProperty(props.APP_DB_MIGRATION_PATH.getKey(), "db/reward-migration");
        cfg.setLocalProperty("service.postgres-owner.uri", "postgres://app_owner:app_owner@127.0.0.1:5432/engagement");

        GeneratePostgresMigration.generate(new RewardEntities(), props);
    }
}

