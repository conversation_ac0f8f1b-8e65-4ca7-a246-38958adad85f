package engagement.provider;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.UPSs;
import api.v1.ApiFactory;
import engagement.EngagementProto;
import gateway.AbstractApiEndpoint;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.inject.Inject;

import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import uam.CrmProto;

@Slf4j
@Hidden
public abstract class AbstractBloomreachApiEndpoint extends AbstractApiEndpoint {

    public static final String BLOOMREACH_PATH = "/bloomreach";
    public static final String OFFER_CHAIN_PATH = "/offerchain";
    public static final String BEARER_AUTH = "Bearer";
    public static final String API_TOKEN_PARAM = "apiToken";

    protected final DynamicCloud cloud;

    @Inject
    protected AbstractBloomreachApiEndpoint(ApplicationProperties props,
            ApiFactory apiFactory,
            DynamicCloud cloud) {
        super(props, apiFactory, cloud);
        this.cloud = Objects.requireNonNull(cloud);
    }

    protected boolean isForbidden(HttpHeaders headers, String brand, AsyncResponse async) {
        boolean isValidAuth = false;
        try {
            auth(headers.getHeaderString(HttpHeaders.AUTHORIZATION), brand);
            isValidAuth = true;
        } catch (WebApplicationException err) {
            log.warn("Authorization error: {}", err.getMessage(), err);
            forbiddenResponse(async, err);
        } catch (Exception e) {
            log.error("Error during authorization: {}", e.getMessage(), e);
            forbiddenResponse(async, e);
        }
        return !isValidAuth;
    }

    protected void auth(String authHeader, String brand) throws Exception {
        Optional<RawServiceInfo> optEngagementApi = UPSs.findScopedServiceInfoByName(brand, cloud, EngagementProto.UPS_ENGAGEMENT_MANAGEMENT);
        Optional<PlainServiceInfo> optBloomreachApi = UPSs.findScopedServiceInfoByName(brand, cloud, CrmProto.UPS_BLOOMREACH);
        if (optEngagementApi.isPresent()) {
            String token = optEngagementApi.get().read();
            checkAuthToken(authHeader, token);
        } else if (optBloomreachApi.isPresent()) {
            // remove after migration to new secret
            PlainServiceInfo si = optBloomreachApi.get();
            String secretToken = UPSs.getRequiredQueryParam(API_TOKEN_PARAM, si);
            checkAuthToken(authHeader, secretToken);
        } else {
            throw new RuntimeException("No values present for both secrets: %s, %s with scope: %s, please one of them to SecretManager"
                            .formatted(EngagementProto.UPS_ENGAGEMENT_MANAGEMENT, CrmProto.UPS_BLOOMREACH, brand));
        }
    }

    private static void forbiddenResponse(AsyncResponse async, Exception e) {
        async.resume(Response.status(Response.Status.FORBIDDEN).entity(e.getMessage()).build());
    }

    private static void checkAuthToken(String authHeader, String token) {
        boolean isValidAuth = StringUtils.isNotEmpty(authHeader) && authHeader.startsWith(BEARER_AUTH);
        var auth = isValidAuth ? authHeader.split(StringUtils.SPACE)[1] : StringUtils.EMPTY;
        AbstractApiEndpoint.malformed(auth, token);
    }

    protected static boolean validUUID(String uuid) {
        boolean rez = false;
        if (isNotBlank(uuid)) {
            String[] keyHash = uuid.split("/");
            rez = keyHash.length == 2 && StringUtils.isAlphanumeric(keyHash[0]) && StringUtils.isNumeric(keyHash[1])
                    && isNotBlank(keyHash[0]);
        }
        return rez;
    }
}
