package engagement.provider.model;

import com.google.common.collect.Sets;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Setter;

import java.util.Set;

@Setter
public abstract class BloomreachRequest {

    @NotNull
    @Schema(description = "List of account ids in format: {hash/id}", requiredMode = Schema.RequiredMode.REQUIRED)
    public Set<String> uuids = Sets.newHashSet();

    @NotBlank
    @Schema(description = "Brand name", requiredMode = Schema.RequiredMode.REQUIRED)
    public String brand;

    @NotBlank
    @Schema(description = "Id of request - UUID", requiredMode = Schema.RequiredMode.REQUIRED)
    public String requestId;
}
