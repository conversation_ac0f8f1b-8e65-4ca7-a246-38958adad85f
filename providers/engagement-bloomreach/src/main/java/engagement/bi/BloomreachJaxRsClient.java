package engagement.bi;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpStatus;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.cloud.service.UriBasedServiceInfo;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.UnexpectedJaxrsException;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.resteasy.SimpleScopedJaxRsClient;

import engagement.bi.data.BatchCommands;
import engagement.bi.data.CommandData;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BloomreachJaxRsClient extends SimpleScopedJaxRsClient<BloomreachApi> {
    @Inject
    public BloomreachJaxRsClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpClient httpClient,
            CommonObjectMapper mapper) {
        super(props, meterRegistry, rateLimiterRegistry, httpClient, mapper, BloomreachApi.class);
    }

    public void sendBloomreachCommands(BatchCommands request, String token, String auth, UriBasedServiceInfo si) {
        var api = proxy(si);

        try (var response = api.sendBatch(token, auth, request)) {
            if (HttpStatus.SC_OK != response.getStatus()) {
                throw new UnexpectedJaxrsException(response);
            }
        } catch (Exception err) {
            log.error("Failed to process bloomreach request", err);
            ExceptionUtils.wrapAndThrow(err);
        }
    }

    public void sendBloomreachEvent(CommandData request, String token, String auth, UriBasedServiceInfo si) {
        var api = proxy(si);

        try (var response = api.sendEvent(token, auth, request)) {
            if (HttpStatus.SC_OK != response.getStatus()) {
                throw new UnexpectedJaxrsException(response);
            }
        } catch (Exception err) {
            log.error("Failed to process bloomreach request", err);
            ExceptionUtils.wrapAndThrow(err);
        }
    }
}
