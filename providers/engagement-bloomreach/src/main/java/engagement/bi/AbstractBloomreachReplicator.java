package engagement.bi;

import static org.apache.commons.codec.binary.Base64.encodeBase64String;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.springframework.cloud.DynamicCloud;

import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import engagement.bi.data.CommandData;
import engagement.bi.data.CommandProperties;
import engagement.bi.data.CustomerId;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AbstractBloomreachReplicator {

    protected final DynamicCloud cloud;
    protected final BloomreachJaxRsClient client;

    public AbstractBloomreachReplicator(
            DynamicCloud cloud,
            BloomreachJaxRsClient client) {
        this.cloud = Objects.requireNonNull(cloud);
        this.client = Objects.requireNonNull(client);
    }

    protected String getAuth(PlainServiceInfo si) {
        return "Basic " + encodeBase64String((si.getUserName() + ":" + si.getPassword()).getBytes(StandardCharsets.UTF_8));
    }

    protected boolean useAltUUID(PlainServiceInfo si) {
        return UPSs.getQueryParam(BloomreachApi.ALT_UUID_PARAM, si).map(Boolean::valueOf).orElse(false);
    }

    public void send(PlainServiceInfo si, String projectToken, String auth, CommandData event) {
        log.debug("Sending Bloomreach event: {}", event.properties);
        client.sendBloomreachEvent(event, projectToken, auth, si);
    }

    public CommandData toCommandData(CommandProperties props, String eventType, String timestamp, boolean altUUID) {
        var req = new CommandData();
        req.properties = props;
        req.customer_ids = toCustomerId(props.uuid, altUUID);
        req.event_type = eventType;
        req.timestamp = timestamp;
        return req;
    }

    public CustomerId toCustomerId(String uuid, boolean altUUID) {
        var customerId = new CustomerId();
        if (altUUID) {
            customerId.UUID = uuid;
        } else {
            customerId.uuid = uuid;
        }
        return customerId;
    }

    public static long convertMillisToSeconds(long timestampInMillis) {
        return TimeUnit.MILLISECONDS.toSeconds(timestampInMillis);
    }
}
