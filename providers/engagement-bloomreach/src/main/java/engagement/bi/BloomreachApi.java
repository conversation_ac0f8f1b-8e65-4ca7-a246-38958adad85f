package engagement.bi;

import org.apache.http.HttpHeaders;

import engagement.bi.data.BatchCommands;
import engagement.bi.data.CommandData;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

public interface BloomreachApi {
    String PROJECT_TOKEN_PARAM = "projectToken";
    String ALT_UUID_PARAM = "altUUID"; // alternative uuid field (required because of wrongly created blooomreach project)

    String PATH_BATCH = "/track/v2/projects/{" + PROJECT_TOKEN_PARAM + "}/batch";
    String PATH_EVENT = "/track/v2/projects/{" + PROJECT_TOKEN_PARAM + "}/customers/events";

    @POST
    @Path(PATH_BATCH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Response sendBatch(
            @PathParam(PROJECT_TOKEN_PARAM) String token,
            @HeaderParam(HttpHeaders.AUTHORIZATION) String auth,
            BatchCommands payload);

    @POST
    @Path(PATH_EVENT)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    Response sendEvent(
            @PathParam(PROJECT_TOKEN_PARAM) String token,
            @HeaderParam(HttpHeaders.AUTHORIZATION) String auth,
            CommandData payload);
}
