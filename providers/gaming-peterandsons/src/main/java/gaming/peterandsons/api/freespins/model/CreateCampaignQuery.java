package gaming.peterandsons.api.freespins.model;

import static gaming.peterandsons.api.PeterAndSonsFreeSpinsApi.FREE_SPIN_TYPE;

import java.math.BigDecimal;
import java.util.List;

import lombok.Builder;

public class CreateCampaignQuery extends BaseCampaignQuery {

    @Builder
    public CreateCampaignQuery(String bonusCode,
            String requestId,
            Long start,
            Long end,
            String wallet,
            String operator,
            String brand,
            String provider,
            String gameId,
            List<String> nativeIds,
            Integer betCount,
            BigDecimal amount,
            String currency) {
        super(null, bonusCode, requestId, start, end, wallet, operator, brand, provider, gameId, nativeIds,
                FreeSpinsConfig.builder()
                        .betCount(betCount)
                        .amount(amount)
                        .currency(currency)
                        .bonusCode(bonusCode)
                        .build(),
                CampaignMutationType.CREATE);
    }

    @Override
    protected String makeQueryString() {
        var queryString = "mutation { "
                + "addCampaign( "
                + "data: { "
                + "type: \"%s\" "
                + "name: \"%s\" "
                + "start: %d "
                + "end: %d "
                + "wallets: [\"%s\"] "
                + "operators: [\"%s\"] "
                + "brands: [\"%s\"] "
                + "providers: [\"%s\"] "
                + "games: [\"%s\"] "
                + "nativeIds: [\"%s\"] "
                + "config: { "
                + "bets: %d "
                + "amount: %s "
                + "currency: \"%s\" "
                + "bonusCode: \"%s\" "
                + "}"
                + "}"
                + ")"
                + "}";

        return queryString.formatted(FREE_SPIN_TYPE, getName(), getStart(), getEnd(),
                getWallet(), getOperator(), getBrand(), getProvider(), getGameId(),
                String.join("\",\"", getNativeIds()),
                getConfig().getBetCount(), getConfig().getAmount().toString(),
                getConfig().getCurrency(), getBonusCode());
    }
}
