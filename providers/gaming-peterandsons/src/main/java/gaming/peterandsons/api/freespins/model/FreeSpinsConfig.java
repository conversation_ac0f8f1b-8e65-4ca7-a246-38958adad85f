package gaming.peterandsons.api.freespins.model;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Builder;

@Builder
public final class FreeSpinsConfig {

    private final Integer betCount;
    private final BigDecimal amount;
    private final String currency;
    private final String bonusCode;

    @JsonCreator
    private FreeSpinsConfig(@JsonProperty("bets") Integer betCount,
            @JsonProperty("amount") BigDecimal amount,
            @JsonProperty("currency") String currency,
            @JsonProperty("bonusCode") String bonusCode) {
        this.betCount = betCount;
        this.amount = amount;
        this.currency = currency;
        this.bonusCode = bonusCode;
    }

    @JsonProperty("bets")
    public Integer getBetCount() {
        return betCount;
    }

    @JsonProperty("amount")
    public BigDecimal getAmount() {
        return amount;
    }

    @JsonProperty("currency")
    public String getCurrency() {
        return currency;
    }

    @JsonProperty("bonusCode")
    public String getBonusCode() {
        return bonusCode;
    }
}
