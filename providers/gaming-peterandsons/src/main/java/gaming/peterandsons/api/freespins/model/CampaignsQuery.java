package gaming.peterandsons.api.freespins.model;

import static gaming.peterandsons.api.PeterAndSonsFreeSpinsApi.FREE_SPIN_TYPE;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class CampaignsQuery implements GraphQLQuery {

    private static final Integer DEFAULT_LIMIT = 1000;
    private static final String DEFAULT_RETURNED_TYPES = "{ items { campaignId type name }}}"; // add necessary items
    private static final String ONLY_CAMPAIGN_ID_RETURNED_TYPE = "{ items { campaignId }}}";
    private static final String NATIVE_IDS_RETURNED_TYPE = "{ items { campaignId nativeIds}}}";

    private final Integer limit;
    private final Integer offset;
    private final Sort sort;
    private final List<Filter> filter;
    private final Map<String, Object> options;
    private final CampaignsQueryType type;
    private final String queryString;

    public CampaignsQuery() {
        this(
                DEFAULT_LIMIT,
                List.of(
                        freeSpinsFilter()),
                CampaignsQueryType.DEFAULT_LIMIT_1000);
    }

    public CampaignsQuery(String bonusCode, String requestId, String playerId, String currency) {
        this(
                null,
                List.of(
                        freeSpinsFilter(),
                        filterByName(GraphQLQuery.createCampaignName(bonusCode, requestId)),
                        filterByPlayerId(playerId),
                        filterByCurrency(currency)),
                CampaignsQueryType.DEFAULT_BY_NAME);
    }

    public CampaignsQuery(String bonusCode) {
        this(
                DEFAULT_LIMIT,
                List.of(
                        freeSpinsFilter(),
                        filterByBonusCode(bonusCode)),
                CampaignsQueryType.ONLY_CAMPAIGN_ID_BY_BONUS_CODE);
    }

    public CampaignsQuery(String bonusCode, String campaignId) {
        this(
                DEFAULT_LIMIT,
                List.of(
                        freeSpinsFilter(),
                        filterByBonusCode(bonusCode),
                        filterByCampaignId(campaignId)),
                CampaignsQueryType.PLAYER_IDS_BY_CAMPAIGN_ID);
    }

    public CampaignsQuery(Integer limit, List<Filter> filters, CampaignsQueryType type) {
        this(limit, null, null, filters, null, type);
    }

    public CampaignsQuery(Integer limit, Integer offset, Sort sort, List<Filter> filter, Map<String, Object> options, CampaignsQueryType type) {
        this.limit = limit;
        this.offset = offset;
        this.sort = sort;
        this.filter = filter;
        this.options = options;
        this.type = type;
        this.queryString = makeQueryString();
    }

    @Override
    public String getQueryString() {
        return queryString;
    }

    private static Filter filterByName(String name) {
        return new Filter(FilterType.EQUAL, "name", name);
    }

    private static Filter filterByCampaignId(String campaignId) {
        return new Filter(FilterType.EQUAL, "campaignId", campaignId);
    }

    private static Filter filterByPlayerId(String playerId) {
        return new Filter(FilterType.CONTAIN, "nativeIds", List.of(playerId).toString());
    }

    private static Filter filterByPlayerIdList(List<String> playerIdList) {
        return new Filter(FilterType.CONTAIN, "nativeIds", Set.of(playerIdList).toString());
    }

    private static Filter filterByCurrency(String currency) {
        return new Filter(FilterType.EQUAL, "config", "currency", currency);
    }

    private static Filter filterByBonusCode(String bonusCode) {
        return new Filter(FilterType.EQUAL, "config", "bonusCode", bonusCode);
    }

    private static Filter freeSpinsFilter() {
        return new Filter(FilterType.EQUAL, "type", FREE_SPIN_TYPE);
    }

    private String makeQueryString() {
        var returnedTypes = switch (type) {
            case DEFAULT_LIMIT_1000, DEFAULT_BY_NAME -> DEFAULT_RETURNED_TYPES;
            case ONLY_CAMPAIGN_ID_BY_BONUS_CODE -> ONLY_CAMPAIGN_ID_RETURNED_TYPE;
            case PLAYER_IDS_BY_CAMPAIGN_ID -> NATIVE_IDS_RETURNED_TYPE;
        };
        return makeBaseQueryString().concat(returnedTypes);
    }

    private String makeBaseQueryString() {
        return "query { campaigns (limit: "
                .concat(limit.toString())
                .concat(", filter: [")
                .concat(filter.stream().map(Filter::toString).collect(Collectors.joining(", ")))
                .concat("]) ");
    }
}
