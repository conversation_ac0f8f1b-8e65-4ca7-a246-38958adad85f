package gaming.peterandsons.api;

import java.util.List;

import com.turbospaces.ups.PlainServiceInfo;

import aggregator.model.FreeSpinCampaign;
import common.model.OperatorSpec;
import common.model.freespins.CreateFreeSpinsBaseRequest;
import gaming.peterandsons.api.freespins.model.CampaignsResponse;
import gaming.peterandsons.api.freespins.model.CreateCampaignResponse;
import gaming.peterandsons.api.freespins.model.DeleteCampaignResponse;
import gaming.peterandsons.api.freespins.model.EditCampaignResponse;
import gaming.peterandsons.api.freespins.model.LoginResponse;

public interface PeterAndSonsFreeSpinsApi {

    String FREE_SPIN_TYPE = "freeBets";

    CampaignsResponse campaigns(String bonusCode, PlainServiceInfo psi, String authToken) throws Exception;

    CampaignsResponse campaigns(String bonusCode, String campaignId, PlainServiceInfo psi, String authToken) throws Exception;

    CreateCampaignResponse createNewCampaign(CreateFreeSpinsBaseRequest request,
            FreeSpinCampaign freeSpinCampaign,
            List<String> playerIdList,
            OperatorSpec operator,
            PlainServiceInfo psi,
            String authToken) throws Exception;

    EditCampaignResponse editCampaignPlayerIdList(String campaignId,
            List<String> playerIdList,
            PlainServiceInfo psi,
            String authToken) throws Exception;

    EditCampaignResponse disableCampaign(String campaignId, PlainServiceInfo psi, String authToken) throws Exception;

    DeleteCampaignResponse deleteCampaign(String campaignId, PlainServiceInfo psi, String authToken) throws Exception;

    LoginResponse getAuthToken(PlainServiceInfo psi) throws Exception;
}
