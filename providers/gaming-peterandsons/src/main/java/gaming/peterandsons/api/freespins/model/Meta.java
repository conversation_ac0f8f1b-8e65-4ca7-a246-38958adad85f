package gaming.peterandsons.api.freespins.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode
public final class Meta {

    private final Integer total;
    private final Integer limit;
    private final Integer offset;
    private final Boolean hasPrev;
    private final Boolean hasNext;

    @JsonCreator
    private Meta(@JsonProperty("total") Integer total,
            @JsonProperty("limit") Integer limit,
            @JsonProperty("offset") Integer offset,
            @JsonProperty("hasPrev") Boolean hasPrev,
            @JsonProperty("hasNext") Boolean hasNext) {
        this.total = total;
        this.limit = limit;
        this.offset = offset;
        this.hasPrev = hasPrev;
        this.hasNext = hasNext;
    }
}
