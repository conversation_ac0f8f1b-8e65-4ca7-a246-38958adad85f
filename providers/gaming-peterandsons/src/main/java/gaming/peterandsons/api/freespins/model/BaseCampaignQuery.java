package gaming.peterandsons.api.freespins.model;

import java.util.List;

import lombok.Getter;

@Getter
abstract class BaseCampaignQuery implements GraphQLQuery {

    protected Boolean enabled;
    private final String campaignId;
    private String bonusCode;
    private String requestId;
    private String name; // bonusCode-requestId
    private Long start;
    private Long end;
    private String wallet;
    private String operator;
    private String brand;
    private String provider;
    private String gameId;

    private final List<String> nativeIds; // our playerIds
    private FreeSpinsConfig config;
    private final CampaignMutationType mutationType;
    private final String queryString;

    protected BaseCampaignQuery(String campaignId,
            String bonusCode,
            String requestId,
            Long start,
            Long end,
            String wallet,
            String operator,
            String brand,
            String provider,
            String gameId,
            List<String> nativeIds,
            FreeSpinsConfig config,
            CampaignMutationType mutationType) {
        this.campaignId = campaignId;
        this.bonusCode = bonusCode;
        this.requestId = requestId;
        this.name = GraphQLQuery.createCampaignName(bonusCode, requestId);
        this.start = start;
        this.end = end;
        this.wallet = wallet;
        this.operator = operator;
        this.brand = brand;
        this.provider = provider;
        this.gameId = gameId;
        this.nativeIds = nativeIds;
        this.config = config;
        this.mutationType = mutationType;
        this.queryString = makeQueryString();
    }

    protected BaseCampaignQuery(String campaignId,
            String bonusCode,
            String requestId,
            Long start,
            Long end,
            String wallet,
            String operator,
            String brand,
            String provider,
            String gameId,
            List<String> nativeIds,
            FreeSpinsConfig config,
            CampaignMutationType mutationType,
            Boolean enabled) {
        this.campaignId = campaignId;
        this.bonusCode = bonusCode;
        this.requestId = requestId;
        this.name = GraphQLQuery.createCampaignName(bonusCode, requestId);
        this.start = start;
        this.end = end;
        this.wallet = wallet;
        this.operator = operator;
        this.brand = brand;
        this.provider = provider;
        this.gameId = gameId;
        this.nativeIds = nativeIds;
        this.config = config;
        this.mutationType = mutationType;
        this.queryString = makeQueryString();
        this.enabled = enabled;
    }

    protected BaseCampaignQuery(String campaignId,
            CampaignMutationType mutationType) {
        this(campaignId, null, mutationType);
    }

    protected BaseCampaignQuery(String campaignId,
            List<String> nativeIds,
            CampaignMutationType mutationType) {
        this.campaignId = campaignId;
        this.nativeIds = nativeIds;
        this.mutationType = mutationType;
        this.queryString = makeQueryString();
    }

    protected abstract String makeQueryString();
}
