package gaming.peterandsons.api;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum Parameters {
    TOKEN("key"),
    GAME("game"),
    LOCALE("language"),
    CURRENCY("currency"),
    OPERATOR("operator"),
    WALLET("wallet"),
    PROVIDER("provider"),
    CASH_URL("depositUrl"),
    HOME_URL("lobbyUrl");

    private final String code;

    public String code() {
        return this.code;
    }
}
