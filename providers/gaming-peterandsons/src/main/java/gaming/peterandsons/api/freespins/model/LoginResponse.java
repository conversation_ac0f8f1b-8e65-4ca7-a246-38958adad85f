package gaming.peterandsons.api.freespins.model;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonProperty;

public record LoginResponse(
        @JsonProperty("data") LoginData data,
        @JsonProperty("errors") List<QueryError> errors
) implements FreeSpinCampaignResponse {

    public boolean isSuccess() {
        return data != null && data.login() != null
                && StringUtils.isNotEmpty(data.login().token()) && CollectionUtils.isEmpty(errors);
    }
}
