package gaming.peterandsons.api.freespins.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;

@Getter
public final class CampaignsResponseData implements ResponseData {

    private final Campaigns campaigns;

    @JsonCreator
    private CampaignsResponseData(@JsonProperty("campaigns") Campaigns campaigns) {
        this.campaigns = campaigns;
    }

    @Getter
    public static final class Campaigns {
        private final Meta meta;
        private final List<Campaign> items;

        @JsonCreator
        private Campaigns(@JsonProperty("meta") Meta meta, @JsonProperty("items") List<Campaign> items) {
            this.meta = meta;
            this.items = items;
        }
    }
}
