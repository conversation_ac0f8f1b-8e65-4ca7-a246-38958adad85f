package gaming.peterandsons.api.freespins.model;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.fasterxml.jackson.annotation.JsonProperty;

public record CampaignsResponse(
        @JsonProperty("data") CampaignsResponseData data,
        @JsonProperty("errors") List<QueryError> errors
) implements FreeSpinCampaignResponse {

    public boolean isSuccess() {
        return data != null && data.getCampaigns() != null
                && CollectionUtils.isNotEmpty(data.getCampaigns().getItems())
                && CollectionUtils.isEmpty(errors);
    }
}
