package gaming.peterandsons.api;

import java.net.URI;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Iterables;
import com.google.common.net.HttpHeaders;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.model.FreeSpinCampaign;
import common.AbstractGamingProviderApi;
import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CreateFreeSpinsBaseRequest;
import common.model.launch.GameLaunchInfo;
import common.model.launch.GameLaunchResponse;
import common.utils.BaseMapper;
import gaming.peterandsons.api.freespins.exception.QueryParamNotFound;
import gaming.peterandsons.api.freespins.model.CampaignMutationType;
import gaming.peterandsons.api.freespins.model.CampaignsQuery;
import gaming.peterandsons.api.freespins.model.CampaignsResponse;
import gaming.peterandsons.api.freespins.model.CreateCampaignResponse;
import gaming.peterandsons.api.freespins.model.DeleteCampaignQuery;
import gaming.peterandsons.api.freespins.model.DeleteCampaignResponse;
import gaming.peterandsons.api.freespins.model.EditCampaignQuery;
import gaming.peterandsons.api.freespins.model.EditCampaignResponse;
import gaming.peterandsons.api.freespins.model.GraphQLQuery;
import gaming.peterandsons.api.freespins.model.LoginQuery;
import gaming.peterandsons.api.freespins.model.LoginResponse;
import gaming.peterandsons.api.freespins.model.Query;
import gaming.peterandsons.utils.Mappers;
import io.netty.handler.codec.http.QueryStringDecoder;
import jakarta.inject.Inject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;

@Service
public class DefaultPeterAndSonsApi extends AbstractGamingProviderApi<PeterAndSonsProductSpec>
        implements PeterAndSonsApi, PeterAndSonsLaunchApi, PeterAndSonsFreeSpinsApi {

    private static final String GRAPHQL_API_PATH = "/graphql";
    private static final String BACKOFFICE_LOGIN_KEY = "backoffice-login";
    private static final String BACKOFFICE_PASSWORD_KEY = "backoffice-password";

    @Inject
    public DefaultPeterAndSonsApi(AggregatorServerProperties props,
            DynamicCloud cloud,
            OkHttpClient httpClient,
            CommonObjectMapper mapper) {
        super(props, cloud, httpClient, mapper);
    }

    @Override
    public Collection<PeterAndSonsProductSpec> getProducts(String operator, PlainServiceInfo si) {
        return new LinkedList<>();
    }

    @Override
    public GameLaunchResponse launch(GameLaunchInfo gameLaunchInfo, String operator) throws Exception {
        var psi = getPlainServiceInfo(OperatorSpec.byCode(operator));
        LinkedHashMap<String, String> params = getLaunchParams(gameLaunchInfo, operator, psi);

        var uri = makeURI(gameLaunchInfo.isDemo() ? DEMO_LAUNCH_PATH : LAUNCH_PATH, psi, params);

        return Mappers.toGameLaunchResponse(uri.toString());
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.PETERANDSONS;
    }

    @Override
    public CampaignsResponse campaigns(String bonusCode, PlainServiceInfo psi, String authToken) throws Exception {
        return campaigns(psi, new CampaignsQuery(bonusCode), authToken);
    }

    @Override
    public CampaignsResponse campaigns(String bonusCode, String campaignId, PlainServiceInfo psi, String authToken) throws Exception {
        return campaigns(psi, new CampaignsQuery(bonusCode, campaignId), authToken);
    }

    @Override
    public CreateCampaignResponse createNewCampaign(CreateFreeSpinsBaseRequest request,
            FreeSpinCampaign freeSpinCampaign,
            List<String> playerIdList,
            OperatorSpec operator,
            PlainServiceInfo psi,
            String authToken) throws Exception {
        Supplier<? extends GraphQLQuery> graphQLQuerySupplier = () -> Mappers.toCreateCampaignQuery(request, freeSpinCampaign, playerIdList, operator,
                psi.getUserName(), PETERANDSONS);
        var response = makeGraphQLRequestAndSend(psi, graphQLQuerySupplier, authToken);

        return mapper.readValue(response, CreateCampaignResponse.class);
    }

    @Override
    public EditCampaignResponse editCampaignPlayerIdList(String campaignId,
            List<String> playerIdList,
            PlainServiceInfo psi,
            String authToken) throws Exception {
        Supplier<? extends GraphQLQuery> graphQLQuerySupplier = () -> new EditCampaignQuery(campaignId, playerIdList, CampaignMutationType.EDIT_PLAYER_ID_LIST);
        var response = makeGraphQLRequestAndSend(psi, graphQLQuerySupplier, authToken);
        return mapper.readValue(response, EditCampaignResponse.class);
    }

    @Override
    public EditCampaignResponse disableCampaign(String campaignId, PlainServiceInfo psi, String authToken) throws Exception {
        Supplier<? extends GraphQLQuery> graphQLQuerySupplier = () -> new EditCampaignQuery(campaignId, CampaignMutationType.DISABLE_CAMPAIGN);
        var response = makeGraphQLRequestAndSend(psi, graphQLQuerySupplier, authToken);
        return mapper.readValue(response, EditCampaignResponse.class);
    }

    @Override
    public DeleteCampaignResponse deleteCampaign(String campaignId, PlainServiceInfo psi, String authToken) throws Exception {
        Supplier<? extends GraphQLQuery> graphQLQuerySupplier = () -> new DeleteCampaignQuery(campaignId);
        var response = makeGraphQLRequestAndSend(psi, graphQLQuerySupplier, authToken);
        return mapper.readValue(response, DeleteCampaignResponse.class);
    }

    @Override
    public LoginResponse getAuthToken(PlainServiceInfo psi) throws Exception {
        var uri = makeURI(GRAPHQL_API_PATH, psi);
        var loginQuery = new LoginQuery(getLogin(psi), getPassword(psi));
        var body = makeGraphQlRequestBodyString(loginQuery.getQueryString());
        var tokenRequest = postRequestBuilder(uri, body).build();
        var tokenResponse = sendRequest(tokenRequest);
        return mapper.readValue(tokenResponse, LoginResponse.class);
    }

    private String makeGraphQLRequestAndSend(PlainServiceInfo psi,
            Supplier<? extends GraphQLQuery> graphQLQuerySupplier,
            String authToken) throws Exception {
        var uri = makeURI(GRAPHQL_API_PATH, psi);
        var graphQLQuery = graphQLQuerySupplier.get();
        var body = makeGraphQlRequestBodyString(graphQLQuery.getQueryString());
        var request = postHttpRequest(uri, body, authToken);
        return sendRequest(request);
    }

    private LinkedHashMap<String, String> getLaunchParams(GameLaunchInfo gameLaunchInfo,
            String operator,
            PlainServiceInfo psi) {
        var params = new LinkedHashMap<String, String>();

        params.put(Parameters.TOKEN.code(), gameLaunchInfo.getToken());
        params.put(Parameters.GAME.code(), BaseMapper.removePrefixFromGameId(gameLaunchInfo.getGameId(), provider()).toLowerCase());
        params.put(Parameters.LOCALE.code(), BaseMapper.toProviderLocale(gameLaunchInfo.getLocale(), provider()));
        params.put(Parameters.CURRENCY.code(), Mappers.toProviderCurrency(gameLaunchInfo.getCurrency()));
        params.put(Parameters.OPERATOR.code(), operator);
        params.put(Parameters.WALLET.code(), psi.getUserName());
        params.put(Parameters.PROVIDER.code(), PETERANDSONS);

        params.put(Parameters.HOME_URL.code(), gameLaunchInfo.getHomeUrl());
        params.put(Parameters.CASH_URL.code(), gameLaunchInfo.getCashUrl());

        params.values().removeIf(StringUtils::isEmpty);

        return params;
    }

    private Request postHttpRequest(URI uri, String body, String token) {
        return postRequestBuilder(uri, body)
                .addHeader(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .build();
    }

    private Request.Builder postRequestBuilder(URI uri, String body) {
        return new Request.Builder()
                .post(RequestBody.create(body, getMediaType(ContentType.APPLICATION_JSON.toString())))
                .url(uri.toString())
                .addHeader(org.apache.http.HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.toString());
    }

    private String getLogin(PlainServiceInfo psi) {
        return getQueryParam(psi, BACKOFFICE_LOGIN_KEY);
    }

    private String getPassword(PlainServiceInfo psi) {
        return getQueryParam(psi, BACKOFFICE_PASSWORD_KEY);
    }

    private String getQueryParam(PlainServiceInfo psi, String key) {
        QueryStringDecoder decoder = new QueryStringDecoder(psi.getUri());
        var queryParam = decoder.parameters().get(key);
        if (queryParam == null) {
            throw new QueryParamNotFound(key);
        }
        return Iterables.getOnlyElement(queryParam);
    }

    private String makeGraphQlRequestBodyString(String queryOrMutationString) throws JsonProcessingException {
        return mapper.writeValueAsString(new Query(queryOrMutationString));
    }

    private CampaignsResponse campaigns(PlainServiceInfo psi, CampaignsQuery query, String authToken) throws Exception {
        Supplier<? extends GraphQLQuery> graphQLQuerySupplier = () -> query;
        var response = makeGraphQLRequestAndSend(psi, graphQLQuerySupplier, authToken);
        return mapper.readValue(response, CampaignsResponse.class);
    }
}
