package gaming.peterandsons.api.freespins.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

@Getter
@EqualsAndHashCode
public final class Campaign {

    String campaignId;
    String type;
    String name;
    Float createdAt;
    Float start;
    Float end;
    Boolean enabled;
    Integer optIns;
    Integer optOuts;
    List<String> wallets;
    List<String> operators;
    List<String> brands;
    List<String> providers;
    List<String> games;
    List<String> playerIds;
    List<String> nativeIds;
    FreeSpinsConfig config;

    @Builder
    @JsonCreator
    private Campaign(@JsonProperty("campaignId") String campaignId,
            @JsonProperty("type") String type,
            @JsonProperty("name") String name,
            @JsonProperty("createdAt") Float createdAt,
            @JsonProperty("start") Float start,
            @JsonProperty("end") Float end,
            @JsonProperty("enabled") Boolean enabled,
            @JsonProperty("optIns") Integer optIns,
            @JsonProperty("optOuts") Integer optOuts,
            @JsonProperty("wallets") List<String> wallets,
            @JsonProperty("operators") List<String> operators,
            @JsonProperty("brands") List<String> brands,
            @JsonProperty("providers") List<String> providers,
            @JsonProperty("games") List<String> games,
            @JsonProperty("playerIds") List<String> playerIds,
            @JsonProperty("nativeIds") List<String> nativeIds,
            @JsonProperty("config") FreeSpinsConfig config) {
        this.campaignId = campaignId;
        this.type = type;
        this.name = name;
        this.createdAt = createdAt;
        this.start = start;
        this.end = end;
        this.enabled = enabled;
        this.optIns = optIns;
        this.optOuts = optOuts;
        this.wallets = wallets;
        this.operators = operators;
        this.brands = brands;
        this.providers = providers;
        this.games = games;
        this.playerIds = playerIds;
        this.nativeIds = nativeIds;
        this.config = config;
    }
}
