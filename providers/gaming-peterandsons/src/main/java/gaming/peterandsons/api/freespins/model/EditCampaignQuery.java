package gaming.peterandsons.api.freespins.model;

import java.util.List;

import lombok.Builder;
import lombok.Getter;

@Getter
public class EditCampaignQuery extends BaseCampaignQuery {

    @Builder
    public EditCampaignQuery(String campaignId,
            Boolean enabled,
            String bonusCode,
            String requestId,
            Long start,
            Long end,
            String wallet,
            String operator,
            String brand,
            String provider,
            String gameId,
            List<String> nativeIds,
            FreeSpinsConfig config,
            CampaignMutationType editType) {
        super(campaignId, bonusCode, requestId, start, end, wallet, operator, brand, provider,
                gameId, nativeIds, config, editType, enabled);
    }

    public EditCampaignQuery(String campaignId,
            List<String> nativeIds,
            CampaignMutationType editType) {
        super(campaignId, nativeIds, editType);
    }

    public EditCampaignQuery(String campaignId,
            CampaignMutationType editType) {
        super(campaignId, editType);
    }

    @Override
    protected String makeQueryString() {
        return switch (getMutationType()) {
            case EDIT_PLAYER_ID_LIST -> makeQueryString(getCampaignId(), enabled, getNativeIds());
            case DISABLE_CAMPAIGN -> makeQueryString(getCampaignId(), false, List.of());
            default -> throw new IllegalStateException("Unexpected operation type: " + getMutationType());
        };
    }

    private String makeQueryString(String campaignId, Boolean enabled, List<String> nativeIds) {
        var builder = new StringBuilder("mutation { editCampaign( campaignId: \"%s\" data: {".formatted(campaignId));

        if (enabled != null) {
            builder.append(" enabled: ").append(enabled);
        }
        if (nativeIds != null) {
            builder.append(" nativeIds: [\"")
                    .append(String.join("\",\"", nativeIds))
                    .append("\"]");
        }

        builder.append("})}");
        return builder.toString();
    }
}
