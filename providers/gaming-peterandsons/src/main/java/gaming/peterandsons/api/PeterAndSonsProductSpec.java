package gaming.peterandsons.api;

import static common.utils.BaseMapper.addPrefixToGameId;

import common.model.GameType;
import common.model.ProductSpec;
import common.model.ProviderSpec;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class PeterAndSonsProductSpec implements ProductSpec {
    private final String gameId;

    @Override
    public String code() {
        return addPrefixToGameId(gameId, provider()).toLowerCase();
    }

    @Override
    public String title() {
        return gameId;
    }

    @Override
    public String type() {
        return GameType.SLOTS.code();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.PETERANDSONS;
    }

    @Override
    public String supplier() {
        return provider().code();
    }
}
