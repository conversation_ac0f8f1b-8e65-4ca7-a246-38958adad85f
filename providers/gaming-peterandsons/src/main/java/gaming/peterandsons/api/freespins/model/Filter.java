package gaming.peterandsons.api.freespins.model;

public record Filter(
        FilterType type,
        String field,
        String jsonField,
        String value
) {

    public Filter(FilterType type, String field, String value) {
        this(type, field, null, value);
    }

    @Override
    public String toString() {
        return "{"
                + "type: " + type.name()
                + ", field: " + getString(field)
                + ", jsonField: " + getString(jsonField)
                + ", value: " + getString(value)
                + "}";
    }

    private String getString(Object value) {
        if (value == null) {
            return null;
        }
        return "\"" + value + "\"";
    }
}
