package gaming.peterandsons.api.freespins;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.http.HttpStatus;
import org.springframework.cloud.DynamicCloud;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.FluentFuture;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.freespins.patrianna.UamFreeSpinsClient;
import aggregator.model.FreeSpinCampaign;
import common.AbstractFreeSpinsApi;
import common.AggregatorServerProperties;
import common.exception.FreeSpinsExceededTimeException;
import common.exception.FreeSpinsException;
import common.model.BaseResponse;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinResult;
import common.model.freespins.CreateFreeSpinsBaseRequest;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinIdempotencyStatus;
import common.model.freespins.FreeSpinsInfo;
import common.model.freespins.FreeSpinsStatus;
import common.utils.FreeSpinsUtils;
import common.utils.ServiceInfoUtils;
import gaming.peterandsons.api.PeterAndSonsFreeSpinsApi;
import gaming.peterandsons.api.freespins.exception.InternalFreeSpinsProcessingException;
import gaming.peterandsons.api.freespins.model.Campaign;
import gaming.peterandsons.api.freespins.model.CampaignsResponse;
import gaming.peterandsons.api.freespins.model.DeleteCampaignResponse;
import gaming.peterandsons.api.freespins.model.EditCampaignResponse;
import gaming.peterandsons.api.freespins.model.FreeSpinCampaignResponse;
import gaming.peterandsons.api.freespins.model.LoginResponse;
import gaming.peterandsons.api.freespins.model.QueryError;
import gaming.peterandsons.api.freespins.model.ResponseData;
import gaming.peterandsons.utils.Mappers;
import io.micrometer.core.instrument.MeterRegistry;
import io.vavr.CheckedFunction0;
import reactor.core.publisher.Flux;

public class DefaultFreeSpinsPeterAndSonsService extends AbstractFreeSpinsApi {

    private static final String INTERNAL_PROCESSING_ERROR_KEY = "internal-processing-error";

    private final PeterAndSonsFreeSpinsApi peterAndSonsApi;

    public DefaultFreeSpinsPeterAndSonsService(AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            PeterAndSonsFreeSpinsApi peterAndSonsApi,
            UamFreeSpinsClient uamFreeSpinsClient,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager) {
        super(props, cloud, meterRegistry, uamFreeSpinsClient, spannerTemplate, cacheManager);
        this.peterAndSonsApi = Objects.requireNonNull(peterAndSonsApi);
    }

    @Override
    public CreateFreeSpinsResponse createFreeSpins(OperatorSpec operator, CreateFreeSpinsRequest request) throws Exception {
        var psi = ServiceInfoUtils.getPlainServiceInfo(operator, provider().code(), cloud);

        var createFreeSpinsResponseOpt = getCreateFreeSpinsResponseByRequestId(request.getBonusCode(), operator, provider(), request.getRequestId());
        if (createFreeSpinsResponseOpt.isPresent()) {
            var response = createFreeSpinsResponseOpt.get();
            validateBonusCodeFromRequestId(request, response.getBonusCode());
            return response;
        }

        var response = new CreateFreeSpinsResponse();
        response.setBonusCode(request.getBonusCode());

        var loginResponse = getAuthToken(psi);
        checkLoginResponseData(loginResponse, response);
        if (response.getCode() == HttpStatus.SC_UNAUTHORIZED) {
            return response;
        }

        var authToken = loginResponse.data().login().token();

        var createFreeSpinResult = createFreeSpins(operator, request, request.getPlayerIdList(), psi, authToken);
        var freeSpinsInfo = createFreeSpinResult.freeSpinsInfo();
        List<FreeSpinsInfo> freeSpinsResponseList = Mappers.toFreeSpinsInfoListWithPlayers(
                request.getPlayerIdList(), freeSpinsInfo);

        if (freeSpinsInfo.isApplied()) {
            var accountFreeSpinsList = prepareAccountFreeSpinsList(operator, provider(), request, freeSpinsResponseList, FreeSpinsStatus.CREATED);
            saveFreeSpinsWithIdempotencyKey(operator, provider(), request.getRequestId(), accountFreeSpinsList, FreeSpinIdempotencyStatus.FINISHED);
        } else {
            response.setMessage(freeSpinsInfo.getMessage());
            response.setCode(HttpStatus.SC_BAD_REQUEST);
        }

        response.setFreeSpinsList(freeSpinsResponseList);
        response.setCurrency(createFreeSpinResult.currency());
        return response;
    }

    @Override
    public CancelPlayerFreeSpinResponse cancelPlayerFreeSpin(OperatorSpec operator,
            CancelPlayerFreeSpinRequest request) throws Exception {
        var psi = ServiceInfoUtils.getPlainServiceInfo(operator, provider().code(), cloud);

        var freeSpinForCancel = request.getFreeSpin();
        var freeSpinId = freeSpinForCancel.getFreeSpinsId();
        var response = new CancelPlayerFreeSpinResponse(request.getCampaign(), freeSpinForCancel);

        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());
        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);
            return response;
        }

        var loginResponse = getAuthToken(psi);
        checkLoginResponseData(loginResponse, response);
        if (response.getCode() == HttpStatus.SC_UNAUTHORIZED) {
            return response;
        }

        var authToken = loginResponse.data().login().token();

        var campaigns = getCampaigns(request.getCampaign(), freeSpinId, psi, authToken);
        checkCampaignsData(campaigns, response);

        if (response.getCode() == HttpStatus.SC_NOT_FOUND) {
            return response;
        }

        var campaign = campaigns.data().getCampaigns().getItems().getFirst();
        List<String> allCampaignPlayerIds = campaign.getNativeIds() == null ? List.of() : campaign.getNativeIds();

        var freeSpinsInfoListForSaving = allCampaignPlayerIds.stream()
                .filter(playerId -> !playerId.equals(freeSpinForCancel.getPlayerId()))
                .toList();
        var editCampaignResponse = editCampaignPlayerIdList(psi, freeSpinId, freeSpinsInfoListForSaving, authToken);

        if (editCampaignResponse == null || editCampaignResponse.data() == null
                || editCampaignResponse.data().isApplied() == null) {
            response.getFreeSpin().setApplied(false);
            response.setMessage(getNotAppliedCampaignErrorMessage(editCampaignResponse));
        } else {
            response.getFreeSpin().setApplied(editCampaignResponse.data().isApplied());
        }

        return response;
    }

    @Override
    public CancelFreeSpinsResponse cancelFreeSpinsBatching(OperatorSpec operator, CancelFreeSpinsRequest request) throws Exception {
        var bonusCode = request.getCampaign();
        return cancelFreeSpinsByBonusCode(operator, bonusCode);
    }

    @Override
    protected FreeSpinsInfo removePlayerFromBonus(PlainServiceInfo psi, String currency, FreeSpinsInfo freeSpin) {
        throw new UnsupportedOperationException();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.PETERANDSONS;
    }

    private CreateFreeSpinResult createFreeSpins(OperatorSpec operator,
            CreateFreeSpinsBaseRequest request,
            List<String> playerIdList,
            PlainServiceInfo psi,
            String authToken) throws FreeSpinsException {
        FreeSpinCampaign freeSpinCampaign;
        var campaignOpt = readFreeSpinsCampaign(operator, provider(), request.getBonusCode());

        if (campaignOpt.isPresent()) {
            freeSpinCampaign = campaignOpt.get();
        } else {
            insertFreeSpinCampaign(operator, provider(), request, FreeSpinCampaignStatus.CREATED);
            freeSpinCampaign = FreeSpinsUtils.toFreeSpinCampaign(request);
        }

        var freeSpinsInfoBuilder = FreeSpinsInfo.builder();
        try {
            var campaign = peterAndSonsApi.createNewCampaign(request, freeSpinCampaign, playerIdList, operator, psi, authToken);
            if (campaign == null || !campaign.isSuccess()) {
                freeSpinsInfoBuilder
                        .isApplied(false)
                        .message(getNotAppliedCampaignErrorMessage(campaign));
            } else {
                var campaignId = campaign.data().campaignId();
                freeSpinsInfoBuilder
                        .freeSpinsId(campaignId)
                        .isApplied(StringUtils.isNotEmpty(campaignId));
            }
        } catch (Exception e) {
            freeSpinsInfoBuilder
                    .isApplied(false)
                    .message(e.getMessage());
        }
        return CreateFreeSpinResult.builder()
                .freeSpinsInfo(freeSpinsInfoBuilder.build())
                .currency(freeSpinCampaign.getCurrency())
                .build();
    }

    private CancelFreeSpinsResponse cancelFreeSpinsByBonusCode(OperatorSpec operator,
            String bonusCode) throws Exception {
        var psi = ServiceInfoUtils.getPlainServiceInfo(operator, provider().code(), cloud);

        List<FreeSpinsInfo> canceledFreeSpinsList = Collections.synchronizedList(Lists.newArrayList());
        CancelFreeSpinsResponse response = new CancelFreeSpinsResponse(bonusCode, canceledFreeSpinsList);

        var freeSpinCampaignOpt = readFreeSpinCampaign(bonusCode, operator, provider());
        if (freeSpinCampaignOpt.isEmpty()) {
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);
            return response;
        }

        var loginResponse = getAuthToken(psi);
        checkLoginResponseData(loginResponse, response);
        if (response.getCode() == HttpStatus.SC_UNAUTHORIZED) {
            return response;
        }

        var authToken = loginResponse.data().login().token();

        var campaigns = getCampaigns(bonusCode, psi, authToken);
        checkCampaignsData(campaigns, response);
        if (response.getCode() == HttpStatus.SC_NOT_FOUND) {
            return response;
        }

        var freeSpinsInfoListForCancel = campaigns.data().getCampaigns().getItems().stream()
                .map(Campaign::getCampaignId)
                .map(freeSpinsId -> FreeSpinsInfo.builder()
                        .freeSpinsId(freeSpinsId)
                        .build())
                .toList();

        try {
            cancelFreeSpinsBufferProcessing(freeSpinsInfoListForCancel, canceledFreeSpinsList, psi, authToken);
        } catch (FreeSpinsExceededTimeException e) {
            logger.error(e.getMessage(), e);
            response.setCode(HttpStatus.SC_REQUEST_TOO_LONG);
            response.setMessage(e.getMessage());
        }

        return response;
    }

    private void cancelFreeSpinsBufferProcessing(List<FreeSpinsInfo> freeSpinsInfoListForCancel,
            List<FreeSpinsInfo> canceledFreeSpinsList,
            PlainServiceInfo psi,
            String authToken) {
        int processingBuffer = props.FREE_SPINS_ENDPOINTS_PROCESSING_BUFFER.get();

        Flux.fromIterable(freeSpinsInfoListForCancel).buffer(processingBuffer).subscribe(freeSpins -> {
            CountDownLatch latch = new CountDownLatch(freeSpins.size());
            StopWatch stopWatch = StopWatch.createStarted();
            cancelFreeSpinsBuffered(psi, freeSpins, canceledFreeSpinsList, latch, authToken);

            try {
                boolean completely = latch.await(props.REQUEST_REPLY_FREE_SPINS_BUFFER_TIMEOUT.get(), TimeUnit.SECONDS);
                if (completely) {
                    stopWatch.stop();
                    logger.debug(SUCCESSFUL_CANCELLED_FREE_SPINS_BUFFER_MSG, stopWatch);
                } else {
                    var msg = String.format(EXCEEDED_TIME_CANCEL,
                            freeSpinsInfoListForCancel.size(), canceledFreeSpinsList.size());
                    logger.error(msg);
                    throw new FreeSpinsExceededTimeException(msg);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new FreeSpinsExceededTimeException(String.format(EXCEEDED_TIME_CANCEL,
                        freeSpinsInfoListForCancel.size(), canceledFreeSpinsList.size()));
            }
        });
    }

    private void cancelFreeSpinsBuffered(PlainServiceInfo psi,
            List<FreeSpinsInfo> bufferedFreeSpins,
            List<FreeSpinsInfo> canceledFreeSpinsList,
            CountDownLatch latch,
            String authToken) {
        bufferedFreeSpins.forEach(
                freeSpinsInfo -> FluentFuture.from(executor.submit((CheckedFunction0<FreeSpinsInfo>) () -> deleteFreeSpins(freeSpinsInfo, psi, authToken)))
                        .addCallback(new FutureCallback<>() {
                            @Override
                            public void onSuccess(FreeSpinsInfo canceledFreeSpins) {
                                canceledFreeSpinsList.add(canceledFreeSpins);
                                latch.countDown();
                            }

                            @Override
                            public void onFailure(Throwable t) {
                                logger.error(t.getMessage(), t);
                                var freeSpinInfo = FreeSpinsInfo.builder()
                                        .freeSpinsId(freeSpinsInfo.getFreeSpinsId())
                                        .isApplied(false)
                                        .message(t.getMessage())
                                        .build();
                                canceledFreeSpinsList.add(freeSpinInfo);
                                latch.countDown();
                            }
                        }, MoreExecutors.directExecutor()));
    }

    private FreeSpinsInfo deleteFreeSpins(FreeSpinsInfo freeSpinsInfo, PlainServiceInfo psi, String authToken) {
        var freeSpinsId = freeSpinsInfo.getFreeSpinsId();
        var deleteCampaignResponse = deleteFreeSpinsCampaign(freeSpinsId, psi, authToken);

        var isApplied = deleteCampaignResponse.data() != null
                && deleteCampaignResponse.data().isApplied() != null
                && deleteCampaignResponse.data().isApplied();

        return FreeSpinsInfo.builder()
                .playerId(freeSpinsInfo.getPlayerId())
                .freeSpinsId(freeSpinsInfo.getFreeSpinsId())
                .isApplied(isApplied)
                .message(isApplied ? freeSpinsInfo.getMessage() : getNotAppliedCampaignErrorMessage(deleteCampaignResponse))
                .build();
    }

    private DeleteCampaignResponse deleteFreeSpinsCampaign(String freeSpinsId, PlainServiceInfo psi, String authToken) {
        try {
            return peterAndSonsApi.deleteCampaign(freeSpinsId, psi, authToken);
        } catch (Exception e) {
            return getErrorResponse(DeleteCampaignResponse::new, e);
        }
    }

    private EditCampaignResponse editCampaignPlayerIdList(PlainServiceInfo psi,
            String freeSpinsId,
            List<String> playerIdList,
            String authToken) {
        try {
            return peterAndSonsApi.editCampaignPlayerIdList(freeSpinsId, playerIdList, psi, authToken);
        } catch (Exception e) {
            return getErrorResponse(EditCampaignResponse::new, e);
        }
    }

    private CampaignsResponse getCampaigns(String bonusCode, PlainServiceInfo psi, String authToken) {
        try {
            return peterAndSonsApi.campaigns(bonusCode, psi, authToken);
        } catch (Exception e) {
            return getErrorResponse(CampaignsResponse::new, e);
        }
    }

    private CampaignsResponse getCampaigns(String bonusCode, String freeSpinsId, PlainServiceInfo psi, String authToken) {
        try {
            return peterAndSonsApi.campaigns(bonusCode, freeSpinsId, psi, authToken);
        } catch (Exception e) {
            return getErrorResponse(CampaignsResponse::new, e);
        }
    }

    private LoginResponse getAuthToken(PlainServiceInfo psi) throws Exception {
        try {
            return peterAndSonsApi.getAuthToken(psi);
        } catch (Exception e) {
            if (e instanceof InternalFreeSpinsProcessingException) {
                return getErrorResponse(LoginResponse::new, List.of(INTERNAL_PROCESSING_ERROR_KEY), e);
            }
            return getErrorResponse(LoginResponse::new, e);
        }
    }

    private <R extends FreeSpinCampaignResponse, T extends ResponseData> R getErrorResponse(
            BiFunction<T, List<QueryError>, R> errorResponseProducer,
            Exception e) {
        return getErrorResponse(errorResponseProducer, List.of(), e);
    }

    private <R extends FreeSpinCampaignResponse, T extends ResponseData> R getErrorResponse(
            BiFunction<T, List<QueryError>, R> errorResponseProducer,
            List<String> path,
            Exception e) {
        var errorMessage = e.getMessage();
        logger.warn(errorMessage, e);
        return errorResponseProducer.apply(null, List.of(new QueryError(errorMessage, path)));
    }

    private void checkLoginResponseData(LoginResponse loginResponse, BaseResponse response) {
        if (loginResponse == null || !loginResponse.isSuccess()) {
            response.setError(String.valueOf(HttpStatus.SC_UNAUTHORIZED));
            response.setMsg(getNotAppliedCampaignErrorMessage(loginResponse));
        }
    }

    private void checkCampaignsData(CampaignsResponse campaigns, BaseResponse response) {
        if (campaigns == null || !campaigns.isSuccess()) {
            response.setError(String.valueOf(HttpStatus.SC_NOT_FOUND));
            response.setMsg(campaigns == null || CollectionUtils.isEmpty(campaigns.errors())
                    ? BONUS_CODE_NOT_FOUND_MSG
                    : "Bad request to provider: " + String.join(
                            ", ",
                            campaigns.errors().stream().map(QueryError::message).toList()));
        }
    }

    private String getNotAppliedCampaignErrorMessage(FreeSpinCampaignResponse campaign) {
        var message = "";
        if (campaign != null && CollectionUtils.isNotEmpty(campaign.errors())) {
            var isInternalError = campaign.errors().stream()
                    .anyMatch(queryError -> CollectionUtils.isNotEmpty(queryError.path())
                            && queryError.path().contains(INTERNAL_PROCESSING_ERROR_KEY));
            message = isInternalError ? message : "Error of free spins operation on provider side. ";
            message = message.concat(String.join(
                    ". ",
                    campaign.errors().stream().map(QueryError::message).toList()));
            if (!message.endsWith(".")) {
                message = message.concat(".");
            }
        }
        return message;
    }
}
