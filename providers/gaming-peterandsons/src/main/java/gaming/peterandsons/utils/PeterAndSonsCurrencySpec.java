package gaming.peterandsons.utils;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum PeterAndSonsCurrencySpec {
    PATRIANNASC("SC"),
    PATRIANNAGC("GC");

    private final String code;
    private final String shortCode;

    PeterAndSonsCurrencySpec(String shortCode) {
        this.shortCode = shortCode;
        this.code = name().toLowerCase().intern();
    }

    public String code() {
        return code;
    }

    public String shortCode() {
        return shortCode;
    }

    @JsonCreator
    public static PeterAndSonsCurrencySpec fromString(String name) {
        for (PeterAndSonsCurrencySpec currency : PeterAndSonsCurrencySpec.values()) {
            if (currency.code.equalsIgnoreCase(name) || currency.shortCode.equalsIgnoreCase(name)) {
                return currency;
            }
        }
        throw new IllegalArgumentException("unknown peterandsons currency: " + name);
    }
}
