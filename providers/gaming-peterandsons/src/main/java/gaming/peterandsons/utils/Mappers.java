package gaming.peterandsons.utils;

import static common.utils.BaseMapper.addPrefixToGameId;
import static common.utils.BaseMapper.removePrefixFromGameId;
import static gaming.peterandsons.api.PeterAndSonsFreeSpinsApi.FREE_SPIN_TYPE;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.turbospaces.common.PlatformUtil;

import aggregator.model.FreeSpinCampaign;
import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CreateFreeSpinsBaseRequest;
import common.model.freespins.FreeSpinsInfo;
import common.model.launch.GameLaunchResponse;
import common.utils.FreeSpinsUtils;
import common.utils.WalletSessionUtils;
import gaming.peterandsons.api.freespins.model.CreateCampaignQuery;
import gaming.peterandsons.b2b.model.AuthenticateResponse;
import gaming.peterandsons.b2b.model.Campaign;
import gaming.peterandsons.b2b.model.CancelRequest;
import gaming.peterandsons.b2b.model.Error;
import gaming.peterandsons.b2b.model.ErrorResponse;
import gaming.peterandsons.b2b.model.TransactionRequest;

public interface Mappers {
    String WITHDRAW = "withdraw";
    String DEPOSIT = "deposit";

    static String fromProviderTransactionType(String type) {
        return switch (type) {
            case WITHDRAW -> WalletTransactionInfo.TYPE_DEBIT;
            case DEPOSIT -> WalletTransactionInfo.TYPE_CREDIT;
            default -> type;
        };
    }

    static String toProviderCurrency(String currency) {
        return PeterAndSonsCurrencySpec.fromString(currency).code();
    }

    static ErrorResponse toErrorResponse(Error.Code code, String message) {
        return new ErrorResponse(Error.builder()
                .code(code.name())
                .message(message)
                .build());
    }

    static AuthenticateResponse toAuthenticateResponse(
            String token,
            String playerId,
            BigDecimal balance,
            String currency,
            String operator) {
        return AuthenticateResponse.builder()
                .auth(encode(token))
                .playerId(playerId)
                .balance(balance)
                .currency(toProviderCurrency(currency))
                .operator(operator)
                .build();
    }

    static WalletSessionRequestInfo toWalletSessionRequestInfo(
            TransactionRequest request,
            String currency,
            IdentityInfo identity,
            ProviderSpec provider) {

        return WalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(request.getRoundId())
                .complete(request.isFinished())
                .product(addPrefixToGameId(request.getGame(), ProviderSpec.PETERANDSONS).toLowerCase())
                .source(provider.code())
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .transactions(List.of(toTransactionInfo(request.getType().toLowerCase(), request, currency)))
                .build();
    }

    static CancelWalletSessionRequestInfo toCancelWalletSessionRequestInfo(
            CancelRequest request,
            String currency,
            IdentityInfo identity,
            ProviderSpec provider) {

        return CancelWalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(request.getRoundId())
                .source(provider.code())
                .transactions(List.of(toTransactionInfo(WalletTransactionInfo.TYPE_REFUND, request, currency)))
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .build();
    }

    static WalletTransactionInfo toTransactionInfo(String type, TransactionRequest request, String currency) {
        return WalletSessionUtils
                .toTransactionInfo(fromProviderTransactionType(type), currency, request.getTransactionId(), request.getAmount());
    }

    static WalletTransactionInfo toTransactionInfo(String type, CancelRequest request, String currency) {
        return WalletSessionUtils
                .toTransactionInfo(fromProviderTransactionType(type), currency, request.getTransactionId(), BigDecimal.ZERO);
    }

    static String encode(String value) {
        return Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8));
    }

    static String decode(String value) {
        return new String(Base64.getDecoder().decode(value.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
    }

    static String toPermanentToken(String headerString) {
        if (StringUtils.isEmpty(headerString)) {
            return "";
        }
        return decode(headerString.replace("Bearer", "").trim());
    }

    static GameLaunchResponse toGameLaunchResponse(String uri) {
        GameLaunchResponse response = new GameLaunchResponse();
        response.setGameUrl(uri);
        return response;
    }

    static CreateCampaignQuery toCreateCampaignQuery(CreateFreeSpinsBaseRequest request,
            FreeSpinCampaign freeSpinCampaign,
            List<String> playerIdList,
            OperatorSpec operator,
            String wallet,
            String provider) {
        return CreateCampaignQuery.builder()
                .bonusCode(freeSpinCampaign.getBonusCode())
                .requestId(request.getRequestId())
                .start(toEpochMilli(freeSpinCampaign.getStartDate()))
                .end(toEpochMilli(freeSpinCampaign.getExpirationDate()))
                .wallet(wallet)
                .operator(operator.code())
                .brand(operator.code())
                .provider(provider)
                .gameId(removePrefixFromGameId(FreeSpinsUtils.toFreeSpinsCampaignGameCode(freeSpinCampaign),
                        ProviderSpec.PETERANDSONS).toLowerCase())
                .nativeIds(new ArrayList<>(playerIdList))
                .betCount(freeSpinCampaign.getSpins())
                .amount(new BigDecimal(freeSpinCampaign.getBetAmount()))
                .currency(toProviderCurrency(freeSpinCampaign.getCurrency()))
                .build();
    }

    static List<FreeSpinsInfo> toFreeSpinsInfoListWithPlayers(List<String> playerIdList, FreeSpinsInfo freeSpinsInfo) {
        return playerIdList.stream()
                .map(playerId -> toFreeSpinsInfo(freeSpinsInfo, playerId))
                .toList();
    }

    static FreeSpinsInfo toFreeSpinsInfo(FreeSpinsInfo freeSpinsInfo, String playerId) {
        return FreeSpinsInfo.builder()
                .playerId(playerId)
                .freeSpinsId(freeSpinsInfo.getFreeSpinsId())
                .isApplied(freeSpinsInfo.isApplied())
                .message(freeSpinsInfo.getMessage())
                .build();
    }

    static Long toEpochMilli(LocalDateTime localDateTime) {
        return localDateTime == null ? null : localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    static boolean isNotFreeSpin(Campaign campaign) {
        return !FREE_SPIN_TYPE.equals(campaign.campaignType());
    }
}
