package gaming.peterandsons.b2b;

import static common.utils.BaseMapper.addPrefixToGameId;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.http.UnexpectedHttpStatusException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import common.AbstractApiEndpoint;
import common.AggregatorServerProperties;
import common.exception.GameProcessingException;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.limiter.ErrorRateLimiter;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.HiddenModeUtils;
import common.utils.SignatureUtils;
import gaming.peterandsons.PeterAndSonsGamingService;
import gaming.peterandsons.b2b.model.AuthenticateRequest;
import gaming.peterandsons.b2b.model.BalanceRequest;
import gaming.peterandsons.b2b.model.BalanceResponse;
import gaming.peterandsons.b2b.model.Campaign;
import gaming.peterandsons.b2b.model.CancelRequest;
import gaming.peterandsons.b2b.model.Error;
import gaming.peterandsons.b2b.model.ErrorResponse;
import gaming.peterandsons.b2b.model.TransactionRequest;
import gaming.peterandsons.utils.Mappers;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;

@Component
public class DefaultPeterAndSonsEndpoint extends AbstractApiEndpoint implements PeterAndSonsEndpoint {

    private final PeterAndSonsGamingService gamingService;
    private final ErrorRateLimiter errorRateLimiter;

    public DefaultPeterAndSonsEndpoint(AggregatorServerProperties props,
            DynamicCloud cloud,
            PeterAndSonsGamingService gamingService,
            ErrorRateLimiter errorRateLimiter) {
        super(props, cloud);
        this.gamingService = Objects.requireNonNull(gamingService);
        this.errorRateLimiter = Objects.requireNonNull(errorRateLimiter);
    }

    @Override
    public void auth(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            AuthenticateRequest request,
            InputStream io) throws Exception {
        if (isSignatureInvalid(headers, io, operator)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        var tempTokenOptional = TempTokenFlatten.underscore().readExternalOpt(request.getTemporaryToken());
        if (tempTokenOptional.isEmpty()) {
            logger.warn("Invalid temporary token: {}", request.getTemporaryToken());
            sendError(async, Error.Code.PLAYER_UNAUTHORIZED, Error.INVALID_TEMPORARY_TOKEN);
            return;
        }

        var flatten = tempTokenOptional.get();
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var gameCode = addPrefixToGameId(request.getGame(), provider()).toLowerCase();
        var currency = flatten.getCurrency();
        var playerId = getExternalPlayerId(
                flatten.getRoutingKey(),
                flatten.getAccountId(),
                flatten.getCurrency());

        var auth = Mappers.toPermanentToken(headers.getHeaderString(HttpHeaders.AUTHORIZATION));
        if (StringUtils.isNotEmpty(auth)) {
            var identity = byPermanentToken(context, headers, auth, flatten.getAccountId()).build();
            sendAuthResponse(async, auth, playerId, identity, operator, currency, flatten.getRoutingKey());
            return;
        }

        var identity = byAccountId(context, headers, flatten.getAccountId()).build();

        var requestInfo = GetPermanentTokenRequestInfo.builder()
                .identity(identity)
                .token(request.getTemporaryToken())
                .build();

        var future = gamingService.authenticate(
                requestInfo,
                gameCode,
                operator,
                AsciiString.cached(flatten.getRoutingKey()));

        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(GetPermanentTokenResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> {
                        var response = Mappers.toAuthenticateResponse(
                                result.getToken(),
                                playerId,
                                balance.getBalance(),
                                balance.getCurrency(),
                                operator.code());
                        async.resume(okResponse(response));
                    });
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    @Override
    public void balance(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            BalanceRequest request,
            InputStream io) throws Exception {
        if (isSignatureInvalid(headers, io, operator)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(request.getPlayerId());
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var auth = Mappers.toPermanentToken(headers.getHeaderString(HttpHeaders.AUTHORIZATION));
        var identity = byPermanentToken(context, headers, auth, flatten.getAccountId()).build();
        var currency = flatten.getCurrency();

        var requestInfo = new AccountBalanceRequestInfo(identity);

        var future = gamingService.getBalance(requestInfo, operator, AsciiString.cached(flatten.getRoutingKey()));

        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AccountBalanceResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> async.resume(okResponse(new BalanceResponse(balance.getBalance()))));
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    @Override
    public void transaction(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            TransactionRequest request,
            InputStream io) throws Exception {
        if (isSignatureInvalid(headers, io, operator)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(request.getPlayerId());
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var auth = Mappers.toPermanentToken(headers.getHeaderString(HttpHeaders.AUTHORIZATION));
        var identity = byPermanentToken(context, headers, auth, flatten.getAccountId()).build();
        var currency = flatten.getCurrency();

        var campaign = new Campaign(request);
        var requestInfo = Mappers.toWalletSessionRequestInfo(request, currency, identity, determineProviderSpec());

        var future = gamingService.submit(
                requestInfo,
                campaign,
                operator,
                AsciiString.cached(flatten.getRoutingKey()));
        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AccountBalanceResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> async.resume(okResponse(new BalanceResponse(balance.getBalance()))));
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    @Override
    public void cancel(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            CancelRequest request,
            InputStream io) throws Exception {
        if (isSignatureInvalid(headers, io, operator)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(request.getPlayerId());
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var auth = Mappers.toPermanentToken(headers.getHeaderString(HttpHeaders.AUTHORIZATION));
        var identity = byPermanentToken(context, headers, auth, flatten.getAccountId()).build();
        var currency = flatten.getCurrency();

        var requestInfo = Mappers.toCancelWalletSessionRequestInfo(request, currency, identity, determineProviderSpec());

        var future = gamingService.refund(auth, requestInfo, operator, AsciiString.cached(flatten.getRoutingKey()));
        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AccountBalanceResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> async.resume(okResponse(new BalanceResponse(balance.getBalance()))));
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    private ProviderSpec determineProviderSpec() {
        return HiddenModeUtils.determineProviderByHiddenMode(props.PROVIDERS_HIDDEN_MODE.get(provider().code()), provider());
    }

    private ProviderSpec provider() {
        return ProviderSpec.PETERANDSONS;
    }

    private void verifySignature(HttpHeaders headers, InputStream io, OperatorSpec operator) throws Exception {
        String si = operator.irgsUps(provider().code());
        PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
        io.reset();
        String authKey = psi.getPassword();
        String data = IOUtils.toString(io, StandardCharsets.UTF_8);
        String signatureTheir = headers.getHeaderString(X_SERVER_AUTHORIZATION);
        malformed(signatureTheir, SignatureUtils.signHMacSHA256(data, authKey));
    }

    private boolean isSignatureInvalid(HttpHeaders headers, InputStream io, OperatorSpec operator) {
        try {
            verifySignature(headers, io, operator);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return true;
        }
        return false;
    }

    private String getExternalPlayerId(String routingKey, String accountId, String currency) {
        return AccountRoutingCurrencyFlatten
                .underscore(routingKey, accountId, currency)
                .writeExternal();
    }

    private static Response okResponse(Object entity) {
        MDC.remove(MdcTags.MDC_ROUTING_KEY);
        return Response.ok(entity).build();
    }

    private static Response errorResponse(int status, ErrorResponse error) {
        return Response.status(status).entity(error).build();
    }

    private void sendError(AsyncResponse async, Error.Code code, String message) {
        var error = Mappers.toErrorResponse(code, message);
        MDC.remove(MdcTags.MDC_ROUTING_KEY);
        async.resume(errorResponse(code.getStatus(), error));
    }

    private Error.Code logAndMapToErrorCode(Throwable origin) {
        var codeToReturn = Error.Code.UNKNOWN;

        Throwable cause = getRootCause(origin);

        if (cause instanceof UnexpectedHttpStatusException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));

            codeToReturn = switch (ex.getCode()) {
                case HttpStatus.SC_PAYMENT_REQUIRED -> Error.Code.INSUFFICIENT_FUNDS;
                case HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN, HttpStatus.SC_NOT_FOUND, HttpStatus.SC_LOCKED -> Error.Code.PLAYER_UNAUTHORIZED;
                default -> Error.Code.UNKNOWN;
            };
        }

        // ~ log to sentry if necessary
        if (codeToReturn != Error.Code.UNKNOWN || cause instanceof GameProcessingException) {
            logWarn(cause);
        } else {
            logError(cause, errorRateLimiter.check(cause));
        }

        return codeToReturn;
    }

    private void sendAuthResponse(AsyncResponse async,
            String auth,
            String playerId,
            IdentityInfo identity,
            OperatorSpec operator,
            String currency,
            String routingKey) {
        var requestInfo = new AccountBalanceRequestInfo(identity);

        var future = gamingService.getBalance(
                requestInfo,
                operator,
                AsciiString.cached(routingKey));

        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AccountBalanceResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> {
                        var response = Mappers.toAuthenticateResponse(
                                auth,
                                playerId,
                                balance.getBalance(),
                                balance.getCurrency(),
                                operator.code());
                        async.resume(okResponse(response));
                    });
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }
}
