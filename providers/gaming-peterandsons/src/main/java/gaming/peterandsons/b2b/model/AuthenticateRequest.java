package gaming.peterandsons.b2b.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class AuthenticateRequest {
    private String operator;

    @NotEmpty
    @JsonProperty("key")
    private String temporaryToken;

    private String wallet;

    private String provider;

    @NotEmpty
    private String game;
}
