package gaming.peterandsons.b2b.model;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class TransactionRequest {

    @NotEmpty
    @JsonProperty("nativeId")
    private String playerId;

    @JsonProperty("playerId")
    private String adapterPlayerId;

    @NotEmpty
    private String transactionId;

    @JsonProperty("category")
    private String transactionCategory;

    @NotEmpty
    private String type;

    @NotNull
    private BigDecimal amount;

    @NotEmpty
    private String game;

    @NotEmpty
    private String roundId;

    @JsonProperty("roundFinished")
    private boolean finished;

    private String provider;
    private BigDecimal jackpotAmount;
    private String name;

    private String campaignType;
    private String campaignId;
}
