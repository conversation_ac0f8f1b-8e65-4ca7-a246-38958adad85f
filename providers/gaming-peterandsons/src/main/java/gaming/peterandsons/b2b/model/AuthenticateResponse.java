package gaming.peterandsons.b2b.model;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthenticateResponse {

    @JsonProperty("nativeId")
    private String playerId;

    @JsonProperty("token")
    private String auth;
    private BigDecimal balance;
    private String currency;

    @JsonProperty("brand")
    private String operator;

    private String country;
    private String nickname;
    private String gender;
    private String jurisdiction;
}
