package gaming.avatar;

import com.google.common.util.concurrent.FluentFuture;

import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import common.GamingService;
import common.model.OperatorSpec;
import io.netty.util.AsciiString;

public interface AvatarGamingService extends GamingService {

    FluentFuture<GetPermanentTokenResponseInfo> authenticate(
            GetPermanentTokenRequestInfo request,
            String gameId,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<AccountBalanceResponseInfo> getBalance(
            AccountBalanceRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<AccountBalanceResponseInfo> submit(
            WalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<AccountBalanceResponseInfo> refund(
            String sessionId,
            CancelWalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey);
}
