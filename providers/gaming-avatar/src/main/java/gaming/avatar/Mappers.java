package gaming.avatar;

import static common.model.ProviderSpec.AVATAR;
import static common.utils.BaseMapper.addPrefixToGameId;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;

import com.turbospaces.common.PlatformUtil;

import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.model.ProviderSpec;
import common.model.launch.GameLaunchResponse;
import common.utils.WalletSessionUtils;
import gaming.avatar.b2b.model.AuthenticateResponse;
import gaming.avatar.b2b.model.CancelRequest;
import gaming.avatar.b2b.model.Error;
import gaming.avatar.b2b.model.ErrorResponse;
import gaming.avatar.b2b.model.TransactionRequest;

public interface Mappers {
    String WITHDRAW = "withdraw";
    String DEPOSIT = "deposit";

    static String fromProviderTransactionType(String type) {
        return switch (type) {
            case WITHDRAW -> WalletTransactionInfo.TYPE_DEBIT;
            case DEPOSIT -> WalletTransactionInfo.TYPE_CREDIT;
            default -> type;
        };
    }

    static String toProviderCurrency(String currency) {
        return currency.toLowerCase();
    }

    static String toLaunchProviderCurrency(String currency, String prefix) {
        return prefix + currency.toLowerCase();
    }

    static ErrorResponse toErrorResponse(Error.Code code, String message) {
        return new ErrorResponse(Error.builder()
                .code(code.name())
                .message(message)
                .build());
    }

    static AuthenticateResponse toAuthenticateResponse(
            String token,
            String playerId,
            BigDecimal balance,
            String currency,
            String brand) {
        return AuthenticateResponse.builder()
                .auth(encode(token))
                .playerId(playerId)
                .balance(balance)
                .currency(toProviderCurrency(currency))
                .brand(brand)
                .build();
    }

    static WalletSessionRequestInfo toWalletSessionRequestInfo(
            TransactionRequest request,
            String currency,
            IdentityInfo identity,
            ProviderSpec provider) {

        return WalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(request.getRoundId())
                .complete(request.isFinished())
                .product(addPrefixToGameId(request.getGame(), AVATAR).toLowerCase())
                .source(provider.code())
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .transactions(List.of(toTransactionInfo(request.getType().toLowerCase(), request, currency)))
                .build();
    }

    static CancelWalletSessionRequestInfo toCancelWalletSessionRequestInfo(
            CancelRequest request,
            String currency,
            IdentityInfo identity,
            ProviderSpec provider) {

        return CancelWalletSessionRequestInfo.builder()
                .identity(identity)
                .sessionId(request.getRoundId())
                .source(provider.code())
                .transactions(List.of(toTransactionInfo(WalletTransactionInfo.TYPE_REFUND, request, currency)))
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .build();
    }

    static WalletTransactionInfo toTransactionInfo(String type, TransactionRequest request, String currency) {
        return WalletSessionUtils
                .toTransactionInfo(fromProviderTransactionType(type), currency, request.getTransactionId(), request.getAmount());
    }

    static WalletTransactionInfo toTransactionInfo(String type, CancelRequest request, String currency) {
        return WalletSessionUtils
                .toTransactionInfo(fromProviderTransactionType(type), currency, request.getTransactionId(), BigDecimal.ZERO);
    }

    static String encode(String value) {
        return Base64.getEncoder().encodeToString(value.getBytes(StandardCharsets.UTF_8));
    }

    static String decode(String value) {
        return new String(Base64.getDecoder().decode(value.getBytes(StandardCharsets.UTF_8)), StandardCharsets.UTF_8);
    }

    static String toPermanentToken(String headerString) {
        return decode(headerString.replace("Bearer ", ""));
    }

    static GameLaunchResponse toGameLaunchResponse(String uri) {
        GameLaunchResponse response = new GameLaunchResponse();
        response.setGameUrl(uri);
        return response;
    }
}
