package gaming.avatar;

import static common.model.ProviderSpec.AVATAR;

import java.math.BigDecimal;
import java.util.List;

import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.google.cloud.spring.data.spanner.core.SpannerReadOptions;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.util.concurrent.FluentFuture;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.UnexpectedJaxrsException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.model.GameSession;
import aggregator.model.WalletSessionWithTransactions;
import aggregator.model.WalletTransaction;
import aggregator.repo.DefaultSpannerTransactionalCallback;
import aggregator.wallet.patrianna.UamSeamlessWalletClient;
import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.AbstractGamingService;
import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.WalletSessionUtils;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedFunction0;
import io.vavr.Tuple1;
import io.vavr.Tuple3;
import jakarta.inject.Inject;
import jakarta.ws.rs.WebApplicationException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DefaultAvatarGamingService extends AbstractGamingService implements AvatarGamingService {
    @Inject
    public DefaultAvatarGamingService(
            AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamSeamlessWalletClient client,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager) {
        super(props, cloud, meterRegistry, spannerTemplate, cacheManager, client, AVATAR);
    }

    @Override
    public FluentFuture<GetPermanentTokenResponseInfo> authenticate(
            GetPermanentTokenRequestInfo request,
            String gameId,
            OperatorSpec operator,
            AsciiString routingKey) {
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        var si = operator.igpUps();

        return FluentFuture.from(executor.submit((CheckedFunction0<GetPermanentTokenResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            var accountId = request.getIdentity().accountId();
            var response = client.authenticate(psi, request, traceId, routingKey);

            spannerTemplate.performReadWriteTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager, callback -> {
                var brandRepo = callback.brandRepo();
                var account = brandRepo.getOrCreateAccount(operator.code(), accountId);
                var product = brandRepo.getOrCreateProduct(operator.code(), gameId);
                var session = new GameSession(provider().code(), account, product, response.getToken(), request.getToken(), response.getToken());
                brandRepo.getOrCreateGameSession(session);

                return new Object();
            }));

            return response;
        }));
    }

    @Override
    public FluentFuture<AccountBalanceResponseInfo> getBalance(
            AccountBalanceRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<AccountBalanceResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            return client.balance(psi, request, traceId, routingKey);
        }));
    }

    @Override
    public FluentFuture<AccountBalanceResponseInfo> submit(
            WalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<AccountBalanceResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            String accountId = request.getIdentity().accountId();
            String currency = request.currency();

            var tuple = spannerTemplate.performReadOnlyTransaction(
                    new DefaultSpannerTransactionalCallback<>(cacheManager,
                            callback -> {
                                var brandRepo = callback.brandRepo();
                                var walletSessionRepo = callback.walletSessionRepo();
                                var account = brandRepo.requiredAccount(operator.code(), accountId);
                                var opt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), provider().code(), request.getSessionId(),
                                        currency);
                                return new Tuple1<>(opt);
                            }),
                    new SpannerReadOptions());

            var optionalWalletSession = tuple._1();

            if (optionalWalletSession.isPresent()) {
                var walletSession = optionalWalletSession.get();
                if (isRefundTransactionExistFor(request.getTransactions(), walletSession)) {
                    throw new WebApplicationException("Transaction is already refunded");
                }
                if (isAnotherCreditTransaction(request.getTransactions(), walletSession)) {
                    throw new WebApplicationException("Round is already finished");
                }
            }

            var walletResponse = client.submit(psi, request, traceId, routingKey);
            upsertUamWalletSession(spannerTemplate, cacheManager, request, operator, provider());

            return new AccountBalanceResponseInfo(walletResponse);
        }));
    }

    @Override
    public FluentFuture<AccountBalanceResponseInfo> refund(
            String sessionId,
            CancelWalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<AccountBalanceResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            String accountId = request.getIdentity().accountId();
            String currency = request.currency();

            var tuple = spannerTemplate.performReadOnlyTransaction(
                    new DefaultSpannerTransactionalCallback<>(cacheManager,
                            callback -> {
                                var brandRepo = callback.brandRepo();
                                var walletSessionRepo = callback.walletSessionRepo();
                                var account = brandRepo.requiredAccount(operator.code(), accountId);
                                var opt = walletSessionRepo.walletSessionWithTransactions(account.getHash(), provider().code(), request.getSessionId(),
                                        currency);
                                var gameSession = brandRepo.requiredGameSession(account.getHash(), sessionId, provider().code());
                                var productHash = gameSession.getProductHash();
                                return new Tuple3<>(account, opt, productHash);
                            }),
                    new SpannerReadOptions());

            var account = tuple._1();
            var optionalWalletSession = tuple._2();
            var productHash = tuple._3();

            var walletSession = optionalWalletSession.orElse(
                    createWalletSession(account.getHash(), currency, request.getSessionId(), productHash));

            request.getTransactions().forEach(requestTransaction -> walletSession.getTransactions().stream()
                    .filter(transaction -> transaction.getReference().equals(requestTransaction.getReference()))
                    .findFirst()
                    .ifPresent(parentTransaction -> requestTransaction.setAmount(new BigDecimal(parentTransaction.getAmount()))));

            if (WalletSessionUtils.isRefundOriginTypeEnabled(props, operator)) {
                WalletSessionUtils.addOriginTypeToRefundTransactions(request.getTransactions(), walletSession, WalletTransactionInfo.TYPE_DEBIT);
            }

            try {
                var walletResponse = client.refund(psi, request, traceId, routingKey);
                if (!isRefundTransactionExistFor(request.getTransactions(), walletSession)) {
                    upsertRefundedUamWalletSession(spannerTemplate, request, account, walletSession, provider());
                }

                return new AccountBalanceResponseInfo(walletResponse);
            } catch (UnexpectedJaxrsException err) {
                log.warn(err.getMessage(), err);
                return processException(err, psi, request.getIdentity(), traceId, routingKey);
            }
        }));
    }

    private boolean isRefundTransactionExistFor(List<WalletTransactionInfo> transactions,
            WalletSessionWithTransactions walletSession) {
        return transactions.stream().anyMatch(txToCheck -> walletSession.getTransactions().stream()
                .filter(tx -> tx.getReference().equals(txToCheck.getReference()))
                .anyMatch(WalletTransaction::isRefund));
    }

    private boolean isAnotherCreditTransaction(List<WalletTransactionInfo> transactions,
            WalletSessionWithTransactions walletSession) {
        return transactions.stream().anyMatch(txToCheck -> walletSession.getTransactions().stream()
                .filter(tx -> !tx.getReference().equals(txToCheck.getReference()))
                .anyMatch(WalletTransaction::isCredit));
    }

    private ProviderSpec provider() {
        return ProviderSpec.AVATAR;
    }
}
