package gaming.avatar.api;

import java.net.URL;
import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedHashMap;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import com.google.common.collect.Iterables;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import common.AbstractGamingProviderApi;
import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.launch.GameLaunchInfo;
import common.model.launch.GameLaunchResponse;
import common.utils.BaseMapper;
import gaming.avatar.Mappers;
import io.netty.handler.codec.http.QueryStringDecoder;
import jakarta.inject.Inject;
import okhttp3.OkHttpClient;

@Service
public class DefaultAvatar<PERSON><PERSON> extends AbstractGamingProviderApi<AvatarProductSpec> implements Avatar<PERSON>pi, AvatarLaunchApi {
    @Inject
    public DefaultAvatarApi(AggregatorServerProperties props, DynamicCloud cloud, OkHttpClient httpClient, CommonObjectMapper mapper) {
        super(props, cloud, httpClient, mapper);
    }

    @Override
    public Collection<AvatarProductSpec> getProducts(String operator, PlainServiceInfo si) throws Exception {
        URL url = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "avatar-products.json");
        AvatarProduct[] products = mapper.readValue(
                url,
                AvatarProduct[].class);
        return toGames(products);
    }

    private Collection<AvatarProductSpec> toGames(AvatarProduct[] products) {
        return Arrays.stream(products).map(AvatarProductSpec::new).toList();
    }

    @Override
    public GameLaunchResponse launch(GameLaunchInfo gameLaunchInfo, String operator) throws Exception {
        var psi = getPlainServiceInfo(OperatorSpec.fromString(operator), gameLaunchInfo.getCurrency());

        LinkedHashMap<String, String> params = getLaunchParams(gameLaunchInfo, psi, operator);
        var uri = makeURI(gameLaunchInfo.isDemo() ? DEMO_LAUNCH_PATH : LAUNCH_PATH, psi, params);

        return Mappers.toGameLaunchResponse(uri.toString());
    }

    private LinkedHashMap<String, String> getLaunchParams(GameLaunchInfo gameLaunchInfo, PlainServiceInfo psi, String operator) {
        var params = new LinkedHashMap<String, String>();

        params.put(Parameters.TOKEN.code(), gameLaunchInfo.getToken());
        params.put(Parameters.GAME.code(), BaseMapper.removePrefixFromGameId(gameLaunchInfo.getGameId(), provider()).toLowerCase());
        params.put(Parameters.LOCALE.code(), BaseMapper.toProviderLocale(gameLaunchInfo.getLocale(), provider()));
        params.put(Parameters.CURRENCY.code(), Mappers.toLaunchProviderCurrency(gameLaunchInfo.getCurrency(), CURRENCY_PREFIX));
        params.put(Parameters.PARTNER.code(), operator);
        params.put(Parameters.WALLET.code(), getWallet(psi));
        params.put(Parameters.PROVIDER.code(), AVATAR_UX);

        params.put(Parameters.HOME_URL.code(), gameLaunchInfo.getHomeUrl());
        params.put(Parameters.CASH_URL.code(), gameLaunchInfo.getCashUrl());
        params.put(Parameters.HIDE_HISTORY.code(), HIDE_HISTORY);

        params.values().removeIf(StringUtils::isEmpty);

        return params;
    }

    private static String getWallet(PlainServiceInfo si) {
        QueryStringDecoder decoder = new QueryStringDecoder(si.getUri());
        return Iterables.getOnlyElement(decoder.parameters().get(Parameters.WALLET.code()));
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.AVATAR;
    }
}
