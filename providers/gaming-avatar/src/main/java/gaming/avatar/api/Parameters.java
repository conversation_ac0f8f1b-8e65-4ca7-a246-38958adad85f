package gaming.avatar.api;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum Parameters {
    TOKEN("key"),
    GAME("game"),
    LOCALE("language"),
    CURRENCY("currency"),
    PARTNER("operator"),
    WALLET("wallet"),
    PROVIDER("provider"),
    CASH_URL("depositUrl"),
    HOME_URL("lobbyUrl"),
    HIDE_HISTORY("hideHistory");

    private final String code;

    public String code() {
        return this.code;
    }
}
