package gaming.avatar.api;

import static common.utils.BaseMapper.addPrefixToGameId;

import java.util.Objects;

import common.model.GameType;
import common.model.ProductSpec;
import common.model.ProviderSpec;

public class AvatarProductSpec implements ProductSpec {
    private final AvatarProduct payload;

    public AvatarProductSpec(AvatarProduct payload) {
        this.payload = Objects.requireNonNull(payload);
    }

    @Override
    public String code() {
        return addPrefixToGameId(payload.getGameId(), ProviderSpec.AVATAR).toLowerCase();
    }

    @Override
    public String title() {
        return payload.getGameName();
    }

    @Override
    public String type() {
        return GameType.SLOTS.code();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.AVATAR;
    }

    @Override
    public String supplier() {
        return provider().code() + "-ux";
    }
}
