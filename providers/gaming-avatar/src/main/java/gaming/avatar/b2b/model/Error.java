package gaming.avatar.b2b.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Error {
    @JsonIgnore
    public static final String INVALID_SIGNATURE = "Invalid request signature";
    @JsonIgnore
    public static final String INVALID_TEMPORARY_TOKEN = "Invalid temporary token";
    private String message;
    private String code;

    @Getter
    @RequiredArgsConstructor
    public enum Code {
        PLAYER_UNAUTHORIZED(401),
        SERVER_UNAUTHORIZED(403),
        INSUFFICIENT_FUNDS(402),
        LOSS_LIMIT(400),
        UNKNOWN(500);

        private final int status;
    }
}
