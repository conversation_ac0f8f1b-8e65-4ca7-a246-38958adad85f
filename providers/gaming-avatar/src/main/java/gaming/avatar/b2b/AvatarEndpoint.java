package gaming.avatar.b2b;

import java.io.InputStream;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.http.HttpProto;

import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import gaming.avatar.b2b.model.AuthenticateRequest;
import gaming.avatar.b2b.model.BalanceRequest;
import gaming.avatar.b2b.model.CancelRequest;
import gaming.avatar.b2b.model.TransactionRequest;
import io.netty.channel.ChannelHandlerContext;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;

@Path(HttpProto.V1 + "/avatar/{operator}/")
@ApiEndpoint(ipWhitelistKey = AggregatorServerProperties.IP_WHITELIST_AVATAR_KEY)
public interface AvatarEndpoint {
    String X_SERVER_AUTHORIZATION = "X-Server-Authorization";
    String AUTH_PATH = "authenticate";
    String BALANCE_PATH = "balance";
    String TRANSACTION_PATH = "transaction";
    String CANCEL_PATH = "cancel";

    @POST
    @Path(AUTH_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void auth(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext context,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull AuthenticateRequest request,
            InputStream io) throws Exception;

    @POST
    @Path(BALANCE_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void balance(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext context,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull BalanceRequest request,
            InputStream io) throws Exception;

    @POST
    @PUT
    @Path(TRANSACTION_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void transaction(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext context,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull TransactionRequest request,
            InputStream io) throws Exception;

    @POST
    @DELETE
    @Path(CANCEL_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void cancel(@Suspended AsyncResponse async,
            @Context ChannelHandlerContext context,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull CancelRequest request,
            InputStream io) throws Exception;
}
