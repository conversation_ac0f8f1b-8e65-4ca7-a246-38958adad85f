package gaming.avatar.b2b;

import static common.utils.BaseMapper.addPrefixToGameId;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

import org.apache.commons.io.IOUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.http.UnexpectedHttpStatusException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import common.AbstractApiEndpoint;
import common.AggregatorServerProperties;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.TempTokenFlatten;
import common.limiter.ErrorRateLimiter;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.HiddenModeUtils;
import common.utils.SignatureUtils;
import gaming.avatar.AvatarGamingService;
import gaming.avatar.Mappers;
import gaming.avatar.b2b.model.AuthenticateRequest;
import gaming.avatar.b2b.model.BalanceRequest;
import gaming.avatar.b2b.model.BalanceResponse;
import gaming.avatar.b2b.model.CancelRequest;
import gaming.avatar.b2b.model.Error;
import gaming.avatar.b2b.model.ErrorResponse;
import gaming.avatar.b2b.model.TransactionRequest;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;

@Component
public class DefaultAvatarEndpoint extends AbstractApiEndpoint implements AvatarEndpoint {
    private final AvatarGamingService gamingService;
    private final ErrorRateLimiter errorRateLimiter;

    public DefaultAvatarEndpoint(AggregatorServerProperties props,
            DynamicCloud cloud,
            AvatarGamingService gamingService,
            ErrorRateLimiter errorRateLimiter) {
        super(props, cloud);
        this.gamingService = Objects.requireNonNull(gamingService);
        this.errorRateLimiter = Objects.requireNonNull(errorRateLimiter);
    }

    @Override
    public void auth(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            AuthenticateRequest request,
            InputStream io) throws Exception {
        var tempTokenOptional = TempTokenFlatten.underscore().readExternalOpt(request.getTemporaryToken());
        if (tempTokenOptional.isEmpty()) {
            logger.warn("Invalid temporary token: {}", request.getTemporaryToken());
            sendError(async, Error.Code.PLAYER_UNAUTHORIZED, Error.INVALID_TEMPORARY_TOKEN);
            return;
        }
        var flatten = tempTokenOptional.get();
        var currency = flatten.getCurrency();

        if (isSignatureInvalid(headers, io, operator, currency)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var gameCode = addPrefixToGameId(request.getGame(), ProviderSpec.AVATAR).toLowerCase();
        var brand = getPlainServiceInfo(operator, currency).getUserName();
        var identity = byAccountId(context, headers, flatten.getAccountId()).build();

        var requestInfo = GetPermanentTokenRequestInfo.builder()
                .identity(identity)
                .token(request.getTemporaryToken())
                .build();

        var future = gamingService.authenticate(
                requestInfo,
                gameCode,
                operator,
                AsciiString.cached(flatten.getRoutingKey()));

        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(GetPermanentTokenResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> {
                        var playerId = getExternalPlayerId(
                                flatten.getRoutingKey(),
                                flatten.getAccountId(),
                                flatten.getCurrency());

                        var response = Mappers.toAuthenticateResponse(
                                result.getToken(),
                                playerId,
                                balance.getBalance(),
                                balance.getCurrency(),
                                brand);
                        async.resume(okResponse(response));
                    });
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    @Override
    public void balance(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            BalanceRequest request,
            InputStream io) throws Exception {
        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(request.getPlayerId());
        var currency = flatten.getCurrency();
        if (isSignatureInvalid(headers, io, operator, currency)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var auth = Mappers.toPermanentToken(headers.getHeaderString(HttpHeaders.AUTHORIZATION));
        var identity = byPermanentToken(context, headers, auth, flatten.getAccountId()).build();

        var requestInfo = new AccountBalanceRequestInfo(identity);

        var future = gamingService.getBalance(requestInfo, operator, AsciiString.cached(flatten.getRoutingKey()));

        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AccountBalanceResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> async.resume(okResponse(new BalanceResponse(balance.getBalance()))));
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    @Override
    public void transaction(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            TransactionRequest request,
            InputStream io) throws Exception {
        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(request.getPlayerId());
        var currency = flatten.getCurrency();
        if (isSignatureInvalid(headers, io, operator, currency)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var auth = Mappers.toPermanentToken(headers.getHeaderString(HttpHeaders.AUTHORIZATION));
        var identity = byPermanentToken(context, headers, auth, flatten.getAccountId()).build();

        var requestInfo = Mappers.toWalletSessionRequestInfo(request, currency, identity, determineProviderSpec());

        var future = gamingService.submit(
                requestInfo,
                operator,
                AsciiString.cached(flatten.getRoutingKey()));
        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AccountBalanceResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> async.resume(okResponse(new BalanceResponse(balance.getBalance()))));
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    @Override
    public void cancel(AsyncResponse async,
            ChannelHandlerContext context,
            HttpHeaders headers,
            OperatorSpec operator,
            CancelRequest request,
            InputStream io) throws Exception {
        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(request.getPlayerId());
        var currency = flatten.getCurrency();
        if (isSignatureInvalid(headers, io, operator, currency)) {
            sendError(async, Error.Code.SERVER_UNAUTHORIZED, Error.INVALID_SIGNATURE);
            return;
        }

        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var auth = Mappers.toPermanentToken(headers.getHeaderString(HttpHeaders.AUTHORIZATION));
        var identity = byPermanentToken(context, headers, auth, flatten.getAccountId()).build();

        var requestInfo = Mappers.toCancelWalletSessionRequestInfo(request, currency, identity, determineProviderSpec());

        var future = gamingService.refund(auth, requestInfo, operator, AsciiString.cached(flatten.getRoutingKey()));
        future.addCallback(new FutureCallback<>() {
            @Override
            public void onSuccess(AccountBalanceResponseInfo result) {
                try {
                    result.getAmount(currency).ifPresent(balance -> async.resume(okResponse(new BalanceResponse(balance.getBalance()))));
                } catch (Throwable t) {
                    onFailure(t);
                }
            }

            @Override
            public void onFailure(Throwable t) {
                sendError(async, logAndMapToErrorCode(t), t.getMessage());
            }
        }, MoreExecutors.directExecutor());
    }

    private ProviderSpec determineProviderSpec() {
        return HiddenModeUtils.determineProviderByHiddenMode(props.PROVIDERS_HIDDEN_MODE.get(provider().code()), provider());
    }

    private ProviderSpec provider() {
        return ProviderSpec.AVATAR;
    }

    private void verifySignature(HttpHeaders headers, InputStream io, PlainServiceInfo psi) throws Exception {
        io.reset();
        String authKey = psi.getPassword();
        String data = IOUtils.toString(io, StandardCharsets.UTF_8);
        String signatureTheir = headers.getHeaderString(X_SERVER_AUTHORIZATION);
        malformed(signatureTheir, SignatureUtils.signHMacSHA256(data, authKey));
    }

    private PlainServiceInfo getPlainServiceInfo(OperatorSpec operator, String currency) {
        return UPSs.findRequiredServiceInfoByName(cloud, operator.irgsUps(provider().code(), currency));
    }

    private boolean isSignatureInvalid(HttpHeaders headers, InputStream io, OperatorSpec operator, String currency) {
        try {
            var psi = getPlainServiceInfo(operator, currency);
            verifySignature(headers, io, psi);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return true;
        }
        return false;
    }

    private String getExternalPlayerId(String routingKey, String accountId, String currency) {
        return AccountRoutingCurrencyFlatten
                .underscore(routingKey, accountId, currency)
                .writeExternal();
    }

    private static Response okResponse(Object entity) {
        MDC.remove(MdcTags.MDC_ROUTING_KEY);
        return Response.ok(entity).build();
    }

    private static Response errorResponse(int status, ErrorResponse error) {
        return Response.status(status).entity(error).build();
    }

    private void sendError(AsyncResponse async, Error.Code code, String message) {
        var error = Mappers.toErrorResponse(code, message);
        MDC.remove(MdcTags.MDC_ROUTING_KEY);
        async.resume(errorResponse(code.getStatus(), error));
    }

    private Error.Code logAndMapToErrorCode(Throwable origin) {
        var codeToReturn = Error.Code.UNKNOWN;

        Throwable cause = getRootCause(origin);

        if (cause instanceof UnexpectedHttpStatusException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));

            codeToReturn = switch (ex.getCode()) {
                case HttpStatus.SC_PAYMENT_REQUIRED -> Error.Code.INSUFFICIENT_FUNDS;
                case HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN, HttpStatus.SC_NOT_FOUND, HttpStatus.SC_LOCKED -> Error.Code.PLAYER_UNAUTHORIZED;
                default -> Error.Code.UNKNOWN;
            };
        }

        // ~ log to sentry if necessary
        if (codeToReturn == Error.Code.UNKNOWN) {
            logError(cause, errorRateLimiter.check(cause));
        } else {
            logWarn(cause);
        }

        return codeToReturn;
    }
}
