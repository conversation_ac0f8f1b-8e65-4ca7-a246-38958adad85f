package gaming.avatar.b2b.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class CancelRequest {

    @NotEmpty
    @JsonProperty("nativeId")
    private String playerId;

    @JsonProperty("playerId")
    private String adapterPlayerId;

    @NotEmpty
    private String transactionId;

    @NotEmpty
    private String roundId;
}
