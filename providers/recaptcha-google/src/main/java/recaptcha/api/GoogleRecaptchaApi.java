package recaptcha.api;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import recaptcha.data.GoogleRecaptchaResponse;

public interface GoogleRecaptchaApi {

    @POST
    @Path("/recaptcha/api/siteverify")
    @Consumes(MediaType.APPLICATION_JSON)
    GoogleRecaptchaResponse verifyGoogleRecaptcha(@QueryParam("secret") String secret, @QueryParam("response") String response);
}
