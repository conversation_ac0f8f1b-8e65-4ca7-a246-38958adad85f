package recaptcha.sink;

import org.apache.http.impl.client.CloseableHttpClient;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.resteasy.SimpleScopedJaxRsClient;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import recaptcha.api.GoogleRecaptchaApi;

public class GoogleRecaptchaJaxRsClient extends SimpleScopedJaxRsClient<GoogleRecaptchaApi> {
    @Inject
    public GoogleRecaptchaJaxRsClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpClient httpClient,
            CommonObjectMapper mapper) {
        super(props, meterRegistry, rateLimiterRegistry, httpClient, mapper, GoogleRecaptchaApi.class);
    }
}
