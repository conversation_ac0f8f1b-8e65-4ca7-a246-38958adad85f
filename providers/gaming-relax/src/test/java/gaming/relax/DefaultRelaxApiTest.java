package gaming.relax;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.List;

import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.json.CommonObjectMapper;

import common.AggregatorServerProperties;
import gaming.relax.api.DefaultRelaxApi;
import gaming.relax.api.model.RelaxProductSpec;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okio.Timeout;

class DefaultRelaxApiTest {

    @org.junit.jupiter.api.Test
    void toGames() throws Exception {
        DefaultRelaxApi relaxApi = getDefaultRelaxApi();

        List<RelaxProductSpec> rp = relaxApi.toGames(json);
        assertNotNull(rp);
        assertEquals(1, rp.size());
        assertEquals("bestslot", rp.getFirst().code());

    }

    private static DefaultRelaxApi getDefaultRelaxApi() {
        AggregatorServerProperties props = new AggregatorServerProperties(ApplicationConfig.mock().factory());
        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());

        return new DefaultRelaxApi(
                props, cloud, new OkHttpClient() {
                    @Override
                    public Call newCall(Request request) {
                        // Custom implementation of newCall if needed
                        return new Call() {
                            @Override
                            public Request request() {
                                return new Request.Builder().build();
                            }

                            @Override
                            public Response execute() {
                                return new Response.Builder().build();
                            }

                            @Override
                            public void enqueue(Callback callback) {

                            }

                            @Override
                            public void cancel() {

                            }

                            @Override
                            public boolean isExecuted() {
                                return false;
                            }

                            @Override
                            public boolean isCanceled() {
                                return false;
                            }

                            @Override
                            public Timeout timeout() {
                                return new Timeout();
                            }

                            @Override
                            public Call clone() {
                                return this;
                            }
                        };
                    }
                },
                new CommonObjectMapper());
    }

    private final String json = """
            {
             "status": "ok",
             "cdnbaseurl": "https://d3nsdzdtjbr5ml.cloudfront.net",
             "games": [
              {
               "gameid": "bestslot",
               "name": "Best Slot",
               "studio": "Relax Gaming",
               "channels": [
                "web",
                "mobile"
               ],
               "resolutions": {
                "web": [
                 [
                  1024,
                  768
                 ]
                ]
               },
               "freespins": {
                "channels": [
                 "web",
                 "mobile"
                ],
                "paylines": [
                 1
                ]
               },
               "legalbetsizes": [
                40,
                80,
                120,
                200,
                400,
                800,
                1200,
                2000,
                4000,
                8000,
                12000
               ]
              }
             ],
             "currency": "SEK",
             "currencymultiplier": 10
            }
            """;
}
