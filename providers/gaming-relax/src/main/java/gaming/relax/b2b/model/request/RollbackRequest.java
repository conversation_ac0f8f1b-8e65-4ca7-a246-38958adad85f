package gaming.relax.b2b.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Builder
public class RollbackRequest {
    @NotEmpty
    @JsonProperty("customerid")
    private String customerId;

    @NotEmpty
    @JsonProperty("gamesessionid")
    private String gameSessionId;

    @JsonProperty("txid")
    private long txId;

    @JsonProperty("originaltxid")
    private long originalTxId;
}
