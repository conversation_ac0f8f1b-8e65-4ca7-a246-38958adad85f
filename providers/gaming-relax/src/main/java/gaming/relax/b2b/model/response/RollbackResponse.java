package gaming.relax.b2b.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Getter
@Builder
public class RollbackResponse {
    private long balance;
    @JsonProperty("txid")
    private long txId;
    @NotEmpty
    @JsonProperty("remotetxid")
    private String remoteTxId;
}
