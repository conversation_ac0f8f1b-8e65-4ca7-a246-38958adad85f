package gaming.relax.b2b.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@Getter
@EqualsAndHashCode
public class BalanceChangeResponse {
    @NotEmpty
    @JsonProperty("remotetxid")
    private String remoteTxId;

    @JsonProperty("txid")
    private long txId;

    private long balance;
}
