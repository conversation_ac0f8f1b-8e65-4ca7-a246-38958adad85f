package gaming.relax.b2b.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import io.ebean.annotation.DbEnumValue;

public enum TransactionTypeSpec {
    DEPOSIT,
    WITHDRAW,
    DEBIT,
    CREDIT,
    FEE,
    R<PERSON><PERSON>RD,
    R<PERSON><PERSON>RD_REDEEMABLE,
    REFUND,
    BONUS,
    JACKPOT,
    CLEAN_UP;

    private String dbValue;

    private TransactionTypeSpec() {
        this.dbValue = this.name().toLowerCase().intern();
    }

    @JsonValue
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

    @JsonCreator
    public static TransactionTypeSpec fromString(String name) {
        String lname = name.toLowerCase();
        for (TransactionTypeSpec type : TransactionTypeSpec.values()) {
            if (type.dbValue.equalsIgnoreCase(lname)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown transaction type");
    }
}
