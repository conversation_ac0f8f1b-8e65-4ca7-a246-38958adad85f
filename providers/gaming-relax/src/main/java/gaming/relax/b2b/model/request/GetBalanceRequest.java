package gaming.relax.b2b.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetBalanceRequest {
    @JsonProperty("customerid")
    private String customerId;

    @JsonProperty("cashiertoken")
    private String cashierToken;

    @NotEmpty
    private String currency;
}
