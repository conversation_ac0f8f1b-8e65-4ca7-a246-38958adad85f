package gaming.relax.b2b.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Getter
@ToString
public class GetBalanceResponse {
    @JsonProperty("customercurrency")
    private String customerCurrency;

    private long balance;
}
