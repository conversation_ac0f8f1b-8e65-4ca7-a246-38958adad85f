package gaming.relax.b2b.model.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

public abstract class RelaxErrorResponse {

    public static Error from(ErrorType type, String message) {
        return new Error(type.getHttpCode(), type.getErrorCode(), message, type.getClientError());
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class Error {
        @JsonIgnore
        private int httpCode;

        @JsonProperty("errorcode")
        private String errorCode;

        @JsonProperty("errormessage")
        private String errorMessage;

        @JsonProperty("clienterror")
        private ClientError clientError;
    }

    @Getter
    @AllArgsConstructor
    public enum ErrorType {
        INVALID_TOKEN(401, "INVALID_TOKEN", null),
        BLOCKED_FROM_PRODUCT(403, "BLOCKED_FROM_PRODUCT", null),
        CUSTOM_ERROR(403, "CUSTOM_ERROR", new ClientError(null, "Insufficient coins to play.", "OK")),
        TRANSACTION_DECLINED(403, "TRANSACTION_DECLINED", null),
        FREE_SPINS_CAMPAIGN_NOT_FOUND(403, "CUSTOM_ERROR", new ClientError(null, "The requested free spins campaign was not found.", "OK")),
        FREE_SPINS_ERROR(403, "CUSTOM_ERROR", new ClientError(null, "Error processing free spins request", "OK")),
        UNHANDLED(500, "UNHANDLED", null);

        private final int httpCode;
        private final String errorCode;
        private final ClientError clientError;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @ToString
    private static class ClientError {
        private String title;
        private String message;
        private String buttontext;
    }
}
