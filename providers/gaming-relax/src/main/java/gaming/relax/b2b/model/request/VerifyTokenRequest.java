package gaming.relax.b2b.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VerifyTokenRequest {
    private String channel;
    @JsonProperty("clientid")
    private String clientId;
    @NotEmpty
    private String token;
}
