package gaming.relax.b2b.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import gaming.relax.b2b.model.TransactionTypeSpec;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@Getter
public class BalanceChangeRequest {
    @NotEmpty
    @JsonProperty("gameref")
    private String gameRef;

    @NotEmpty
    @JsonProperty("gamesessionid")
    private String gameSessionId;

    @JsonProperty("txid")
    private long txId;

    @JsonProperty("txtype")
    private String txType;

    private long amount;

    @JsonProperty("cashiertoken")
    private String cashierToken;

    private boolean ended;

    @JsonProperty("customerid")
    private String customerId;

    @NotEmpty
    private String currency;

    @JsonProperty("promocode")
    private String promoCode;

    @Getter
    public enum BalanceChangeType {
        DEBIT(TransactionTypeSpec.DEBIT),
        CREDIT(TransactionTypeSpec.CREDIT);

        BalanceChangeType(TransactionTypeSpec typeSpec) {
            this.typeSpec = typeSpec;
        }

        private final TransactionTypeSpec typeSpec;

    }
}
