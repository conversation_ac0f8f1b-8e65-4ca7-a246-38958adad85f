package gaming.relax.b2b;

import java.math.BigDecimal;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.http.HttpProto;

import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import gaming.relax.b2b.model.request.BalanceChangeRequest;
import gaming.relax.b2b.model.request.GetBalanceRequest;
import gaming.relax.b2b.model.request.RollbackRequest;
import gaming.relax.b2b.model.request.VerifyTokenRequest;
import io.netty.channel.ChannelHandlerContext;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.UriInfo;

@Path(HttpProto.V1 + "/{operator}/relax")
@ApiEndpoint(ipWhitelistKey = AggregatorServerProperties.IP_WHITELIST_RELAX_KEY)
public interface RelaxEndpoint {
    String VERIFY_TOKEN = "/verifyToken";
    String GET_BALANCE = "/getBalance";
    String WITHDRAW = "/withdraw";
    String DEPOSIT = "/deposit";
    String ROLLBACK = "/rollback";

    int MONEY_SCALE = 2;

    static long toRelaxBalance(BigDecimal b) {
        return b.movePointRight(MONEY_SCALE).longValue();
    }

    static BigDecimal fromRelaxBalance(long b) {
        return BigDecimal.valueOf(b).movePointLeft(MONEY_SCALE);
    }

    @POST
    @Path(VERIFY_TOKEN)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void verifyToken(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull VerifyTokenRequest req) throws Exception;

    @POST
    @Path(GET_BALANCE)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void getBalance(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull GetBalanceRequest req) throws Exception;

    @POST
    @Path(WITHDRAW)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void withdrawBalance(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull BalanceChangeRequest req) throws Exception;

    @POST
    @Path(DEPOSIT)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void depositBalance(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull BalanceChangeRequest req) throws Exception;

    @POST
    @Path(ROLLBACK)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void rollbackBalance(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull RollbackRequest req) throws Exception;
}
