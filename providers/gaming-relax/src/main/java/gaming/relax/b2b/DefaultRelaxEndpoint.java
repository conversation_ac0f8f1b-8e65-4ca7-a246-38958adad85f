package gaming.relax.b2b;

import static gaming.relax.b2b.model.response.RelaxErrorResponse.ErrorType.BLOCKED_FROM_PRODUCT;
import static gaming.relax.b2b.model.response.RelaxErrorResponse.ErrorType.CUSTOM_ERROR;
import static gaming.relax.b2b.model.response.RelaxErrorResponse.ErrorType.FREE_SPINS_CAMPAIGN_NOT_FOUND;
import static gaming.relax.b2b.model.response.RelaxErrorResponse.ErrorType.FREE_SPINS_ERROR;
import static gaming.relax.b2b.model.response.RelaxErrorResponse.ErrorType.INVALID_TOKEN;
import static gaming.relax.b2b.model.response.RelaxErrorResponse.ErrorType.TRANSACTION_DECLINED;
import static gaming.relax.b2b.model.response.RelaxErrorResponse.ErrorType.UNHANDLED;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import com.google.common.collect.Iterables;
import com.google.common.hash.Hashing;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.UnexpectedHttpStatusException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.FreeSpinCampaignInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.AbstractApiEndpoint;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.flatten.PermanentTokenFlatten;
import common.limiter.ErrorRateLimiter;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.HiddenModeUtils;
import common.utils.WalletSessionUtils;
import gaming.relax.RelaxGamingService;
import gaming.relax.b2b.exception.RelaxException;
import gaming.relax.b2b.model.FreeSpinsTransactionTypes;
import gaming.relax.b2b.model.TransactionTypeSpec;
import gaming.relax.b2b.model.request.BalanceChangeRequest;
import gaming.relax.b2b.model.request.GetBalanceRequest;
import gaming.relax.b2b.model.request.RollbackRequest;
import gaming.relax.b2b.model.request.VerifyTokenRequest;
import gaming.relax.b2b.model.response.BalanceChangeResponse;
import gaming.relax.b2b.model.response.GetBalanceResponse;
import gaming.relax.b2b.model.response.RelaxErrorResponse;
import gaming.relax.b2b.model.response.RollbackResponse;
import gaming.relax.b2b.model.response.VerifyTokenResponse;
import gaming.relax.utility.Mappers;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.http.QueryStringDecoder;
import io.netty.util.AsciiString;
import jakarta.persistence.EntityNotFoundException;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;

@Component
public class DefaultRelaxEndpoint extends AbstractApiEndpoint implements RelaxEndpoint {

    public static final String VERIFY_TOKEN_COUNTRY_CODE = "US";

    private static final String UNABLE_TO_FIND_ACCOUNT = "unable to find account";
    private static final String UNABLE_TO_FIND_GAME_SESSION = "unable to find game session";
    private static final String HAS_BEEN_EXPIRED_OR_HAS_BEEN_USED = "has been expired or has been used";
    private static final String INSUFFICIENT_COINS = "Insufficient coins";
    private static final String UNABLE_TO_FIND_PRODUCT_BY_CODE = "unable to find product by code";
    private static final String UNABLE_TO_FIND_AT_LEAST_ONE_TRANSACTION = "unable to find at least one transaction";
    private static final String UNABLE_TO_FIND_FS_CAMPAIGN = "unable to find free spin campaign";
    private static final String INVALID_TX_TYPE = "Invalid transaction type";

    private final RelaxGamingService gamingService;
    private final ErrorRateLimiter errorRateLimiter;

    public DefaultRelaxEndpoint(AggregatorServerProperties props,
            DynamicCloud cloud,
            RelaxGamingService gamingService,
            ErrorRateLimiter errorRateLimiter) {
        super(props, cloud);
        this.gamingService = Objects.requireNonNull(gamingService);
        this.errorRateLimiter = Objects.requireNonNull(errorRateLimiter);
    }

    @Override
    public void verifyToken(AsyncResponse async, UriInfo uriInfo, ChannelHandlerContext ctx, HttpHeaders headers,
            OperatorSpec operator, VerifyTokenRequest req) {

        logger.debug("authenticate: {}", req);
        checkAuthHeader(this::getHookSecret, operator, headers);

        var tokenInfo = getTokenInfoByTempToken(ctx, headers, req.getToken());
        if (tokenInfo.isInvalid()) {
            sendInvalidTokenResponse(async, req.getToken());
            return;
        }

        var routingKey = tokenInfo.getRoutingKey();
        var accountId = tokenInfo.getAccountId();
        var currency = tokenInfo.getCurrency();

        MDC.put(MdcTags.MDC_ROUTING_KEY, routingKey.toString());

        var permanentTokenRequestInfoBuilder = GetPermanentTokenRequestInfo.builder()
                .identity(byAccountId(ctx, headers, accountId).build())
                .token(req.getToken());

        gamingService.authenticate(
                permanentTokenRequestInfoBuilder.build(),
                operator,
                routingKey).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(GetPermanentTokenResponseInfo permanentTokenResponseInfo) {
                        try {
                            permanentTokenResponseInfo.getBalances().stream()
                                    .filter(balance -> balance.getCurrency().equals(currency))
                                    .findFirst()
                                    .ifPresentOrElse(accountBalanceInfo -> {
                                        var userId = AccountRoutingCurrencyFlatten.standard(routingKey.toString(),
                                                accountId, currency);

                                        var response = VerifyTokenResponse.builder()
                                                .customerId(userId.writeExternal())
                                                .balance(RelaxEndpoint.toRelaxBalance(accountBalanceInfo.getBalance()))
                                                .cashierToken(permanentTokenResponseInfo.getToken())
                                                .countryCode(VERIFY_TOKEN_COUNTRY_CODE)
                                                .customerCurrency(currency + ".")
                                                .build();
                                        MDC.remove(MdcTags.MDC_ROUTING_KEY);

                                        async.resume(Response.status(Response.Status.OK).entity(response).build());
                                    }, () -> responseCurrencyNotFound(currency, async));
                        } catch (Exception e) {
                            onFailure(e);
                        }
                    }

                    @Override
                    public void onFailure(Throwable throwable) {
                        RelaxErrorResponse.ErrorType errorType = logAndMapToErrorCode(throwable);
                        MDC.remove(MdcTags.MDC_ROUTING_KEY);
                        async.resume(Response
                                .status(Response.Status.fromStatusCode(errorType.getHttpCode()))
                                .entity(RelaxErrorResponse.from(errorType, throwable.getMessage()))
                                .build());
                    }
                }, MoreExecutors.directExecutor());
    }

    @Override
    public void getBalance(AsyncResponse async, UriInfo uriInfo, ChannelHandlerContext ctx, HttpHeaders headers,
            OperatorSpec operator, GetBalanceRequest req) throws Exception {
        logger.debug("balance: {}", req);
        checkAuthHeader(this::getHookSecret, operator, headers);

        var tokenInfo = getTokenInfo(req.getCashierToken(), req.getCustomerId(), ctx, headers);
        MDC.put(MdcTags.MDC_ROUTING_KEY, String.valueOf(tokenInfo.routingKey));
        var identity = tokenInfo.identity().build();

        var accountBalanceRequestInfo = new AccountBalanceRequestInfo(identity);

        gamingService.getBalance(accountBalanceRequestInfo, operator, tokenInfo.routingKey())
                .addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(AccountBalanceResponseInfo accountBalanceResponseInfo) {
                        try {
                            accountBalanceResponseInfo.getBalances().stream()
                                    .filter(balance -> balance.getCurrency().equals(tokenInfo.currency()))
                                    .findFirst()
                                    .ifPresentOrElse(accountBalanceInfo -> {
                                        GetBalanceResponse.GetBalanceResponseBuilder resp = GetBalanceResponse.builder();
                                        resp.balance(RelaxEndpoint.toRelaxBalance(accountBalanceInfo.getBalance()));
                                        resp.customerCurrency(tokenInfo.currency() + ".");
                                        MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                        async.resume(Response.status(Response.Status.OK).entity(resp.build()).build());
                                    }, () -> responseCurrencyNotFound(tokenInfo.currency(), async));

                        } catch (Exception e) {
                            onFailure(e);
                        }
                    }

                    @Override
                    public void onFailure(Throwable throwable) {
                        RelaxErrorResponse.ErrorType errorType = logAndMapToErrorCode(throwable);
                        MDC.remove(MdcTags.MDC_ROUTING_KEY);
                        async.resume(Response
                                .status(Response.Status.fromStatusCode(errorType.getHttpCode()))
                                .entity(RelaxErrorResponse.from(errorType, throwable.getMessage()))
                                .build());
                    }
                }, MoreExecutors.directExecutor());
    }

    @Override
    public void withdrawBalance(AsyncResponse async, UriInfo uri, ChannelHandlerContext ctx, HttpHeaders headers,
            OperatorSpec operator, BalanceChangeRequest req) {
        if (logger.isDebugEnabled()) {
            logger.debug("withdraw: {}", req);
        }
        changeBalance(async, ctx, headers, operator, req, BalanceChangeRequest.BalanceChangeType.DEBIT);
    }

    @Override
    public void depositBalance(AsyncResponse async, UriInfo uri, ChannelHandlerContext ctx, HttpHeaders headers,
            OperatorSpec operator, BalanceChangeRequest req) {
        if (logger.isDebugEnabled()) {
            logger.debug("deposit: {}", req);
        }
        changeBalance(async, ctx, headers, operator, req, BalanceChangeRequest.BalanceChangeType.CREDIT);
    }

    @Override
    public void rollbackBalance(AsyncResponse async, UriInfo uriInfo, ChannelHandlerContext ctx, HttpHeaders headers,
            OperatorSpec operator, RollbackRequest req) {
        if (logger.isDebugEnabled()) {
            logger.debug("rollback: {}", req);
        }
        checkAuthHeader(this::getHookSecret, operator, headers);
        var roundId = getShortenRoundIdIfNeeded(req.getGameSessionId());
        var transactionId = Hashing.murmur3_128().newHasher()
                .putString(req.getGameSessionId(), StandardCharsets.UTF_8)
                .putString(String.valueOf(req.getTxId()), StandardCharsets.UTF_8)
                .hash();

        var tokenInfo = getCustomerTokenInfo(req.getCustomerId(), ctx, headers);
        MDC.put(MdcTags.MDC_ROUTING_KEY, String.valueOf(tokenInfo.routingKey));

        var cancelWalletSessionRequestInfoBuilder = CancelWalletSessionRequestInfo.builder()
                .identity(tokenInfo.identity().build())
                .sessionId(roundId)
                .source(determineProviderSpec().code())
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()));

        var walletTransactionInfoBuilder = WalletTransactionInfo.builder()
                .currency(tokenInfo.currency())
                .reference(String.valueOf(req.getOriginalTxId()))
                .type(TransactionTypeSpec.REFUND.getValue())
                .build();

        cancelWalletSessionRequestInfoBuilder.transactions(List.of(walletTransactionInfoBuilder));

        gamingService.refund(cancelWalletSessionRequestInfoBuilder.build(), ProviderSpec.RELAX, operator,
                req.getOriginalTxId(), tokenInfo.routingKey()).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(AccountBalanceResponseInfo accountBalanceResponseInfo) {
                        try {
                            MDC.remove(MdcTags.MDC_ROUTING_KEY);
                            accountBalanceResponseInfo.getBalances().stream()
                                    .filter(balance -> balance.getCurrency().equals(tokenInfo.currency()))
                                    .findFirst()
                                    .ifPresentOrElse(accountBalanceInfo -> async.resume(Response.status(Response.Status.OK).entity(RollbackResponse.builder()
                                            .txId(req.getTxId())
                                            .remoteTxId(transactionId.toString())
                                            .balance(RelaxEndpoint.toRelaxBalance(accountBalanceInfo.getBalance()))
                                            .build()).build()), () -> responseCurrencyNotFound(tokenInfo.currency(), async));
                        } catch (Exception e) {
                            onFailure(e);
                        }
                    }

                    @Override
                    public void onFailure(Throwable throwable) {
                        RelaxErrorResponse.ErrorType errorType = logAndMapToErrorCode(throwable);
                        MDC.remove(MdcTags.MDC_ROUTING_KEY);
                        async.resume(Response
                                .status(Response.Status.fromStatusCode(errorType.getHttpCode()))
                                .entity(RelaxErrorResponse.from(errorType, throwable.getMessage()))
                                .build());
                    }
                }, MoreExecutors.directExecutor());
    }

    private void changeBalance(AsyncResponse async,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            BalanceChangeRequest req,
            BalanceChangeRequest.BalanceChangeType balanceChangeType) {

        checkAuthHeader(this::getHookSecret, operator, headers);
        var roundId = getShortenRoundIdIfNeeded(req.getGameSessionId());
        var transactionId = Hashing.murmur3_128().newHasher()
                .putString(req.getGameSessionId(), StandardCharsets.UTF_8)
                .putString(String.valueOf(req.getTxId()), StandardCharsets.UTF_8)
                .hash();

        var tokenInfo = getTokenInfo(req.getCashierToken(), req.getCustomerId(), ctx, headers);
        MDC.put(MdcTags.MDC_ROUTING_KEY, String.valueOf(tokenInfo.routingKey));

        var walletSessionRequestInfoBuilder = WalletSessionRequestInfo.builder()
                .identity(tokenInfo.identity().build())
                .sessionId(roundId)
                .product(getGameIdWithPrefixIfPulsz(req.getGameRef(), operator))
                .source(determineProviderSpec().code())
                .at(new AtInfo(PlatformUtil.toLocalUTCDate()))
                .complete(req.isEnded());

        var walletTransactionInfoBuilder = WalletTransactionInfo.builder()
                .currency(tokenInfo.currency())
                .reference(String.valueOf(req.getTxId()))
                .amount(RelaxEndpoint.fromRelaxBalance(req.getAmount()))
                .type(balanceChangeType.getTypeSpec().getValue());

        if (!StringUtils.isBlank(req.getPromoCode())) {
            FreeSpinsTransactionTypes txType = FreeSpinsTransactionTypes.fromCode(req.getTxType());

            if (Objects.isNull(txType)) {
                String error = "Invalid transaction type: %s for %s".formatted(req.getTxType(), req.getPromoCode());

                var e = logAndMapToErrorCode(new RelaxException(HttpStatus.SC_BAD_REQUEST, error));

                async.resume(Response
                        .status(Response.Status.fromStatusCode(e.getHttpCode()))
                        .entity(RelaxErrorResponse.from(e, error))
                        .build());

                return;
            }

            if (balanceChangeType == BalanceChangeRequest.BalanceChangeType.CREDIT
                    && List.of(FreeSpinsTransactionTypes.FS_DEPOSIT, FreeSpinsTransactionTypes.FS_PAYOUT).contains(txType)) {

                walletSessionRequestInfoBuilder.freeSpinCampaign(
                        FreeSpinCampaignInfo.builder()
                                .campaign(req.getPromoCode())
                                .build());
            }
        }

        walletSessionRequestInfoBuilder
                .transactions(List.of(walletTransactionInfoBuilder.build()));

        gamingService.submit(walletSessionRequestInfoBuilder.build(), operator, tokenInfo.routingKey())
                .addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(WalletSessionResponseInfo walletSessionResponseInfo) {
                        try {
                            var balanceChangeResponseBuilder = BalanceChangeResponse.builder();
                            walletSessionResponseInfo.getBalances().stream()
                                    .filter(balance -> balance.getCurrency().equals(tokenInfo.currency()))
                                    .findFirst()
                                    .ifPresentOrElse(accountBalanceInfo -> {
                                        balanceChangeResponseBuilder
                                                .remoteTxId(transactionId.toString())
                                                .balance(RelaxEndpoint.toRelaxBalance(accountBalanceInfo.getBalance()))
                                                .txId(req.getTxId());
                                        MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                        async.resume(Response.status(Response.Status.OK)
                                                .entity(balanceChangeResponseBuilder.build())
                                                .build());
                                    }, () -> responseCurrencyNotFound(tokenInfo.currency(), async));
                        } catch (Exception e) {
                            onFailure(e);
                        }
                    }

                    @Override
                    public void onFailure(Throwable throwable) {
                        RelaxErrorResponse.ErrorType errorType = logAndMapToErrorCode(throwable);
                        MDC.remove(MdcTags.MDC_ROUTING_KEY);
                        async.resume(Response
                                .status(Response.Status.fromStatusCode(errorType.getHttpCode()))
                                .entity(RelaxErrorResponse.from(errorType, throwable.getMessage()))
                                .build());
                    }
                }, MoreExecutors.directExecutor());
    }

    private ProviderSpec determineProviderSpec() {
        return HiddenModeUtils.determineProviderByHiddenMode(props, ProviderSpec.RELAX);
    }

    // temporary solution while relax works directly and through the aggregator on pulsz
    private String getGameIdWithPrefixIfPulsz(String gameId, OperatorSpec operator) {
        if ((OperatorSpec.PULSZ.equals(operator) || OperatorSpec.PULSZ_BINGO.equals(operator))
                && props.GAME_LIST_PREFIX_PULSZ_RELAX.get()) {
            return String.format("%s%s", Mappers.SPECIFIC_GAME_PREFIX, gameId);
        }
        return gameId;
    }

    private void responseCurrencyNotFound(String cur, AsyncResponse async) {
        var message = String.format("Currency %s not found", cur);

        logger.warn(message);
        MDC.remove(MdcTags.MDC_ROUTING_KEY);
        async.resume(Response
                .status(Response.Status.fromStatusCode(UNHANDLED.getHttpCode()))
                .entity(RelaxErrorResponse.from(UNHANDLED, message))
                .build());
    }

    private void sendInvalidTokenResponse(AsyncResponse async, String token) {
        var message = "Invalid token: " + token;
        logger.warn(message);
        async.resume(Response
                .status(INVALID_TOKEN.getHttpCode())
                .entity(RelaxErrorResponse.from(INVALID_TOKEN, message))
                .build());
    }

    private TokenInfo getTokenInfo(String cashierToken, String customerId, ChannelHandlerContext ctx,
            HttpHeaders headers) {
        return StringUtils.isNotEmpty(cashierToken) ? getCashierTokenInfo(cashierToken, ctx, headers)
                : getCustomerTokenInfo(customerId, ctx, headers);
    }

    private TokenInfo getCustomerTokenInfo(String token, ChannelHandlerContext ctx, HttpHeaders headers) {
        AccountRoutingCurrencyFlatten flatten = AccountRoutingCurrencyFlatten.standard().readExternal(token);
        var identity = byAccountId(ctx, headers, flatten.getAccountId());
        return new TokenInfo(identity, AsciiString.of(flatten.getRoutingKey()), flatten.getAccountId(),
                flatten.getCurrency());
    }

    private TokenInfo getCashierTokenInfo(String token, ChannelHandlerContext ctx, HttpHeaders headers) {
        var flattenOpt = PermanentTokenFlatten.standard().readExternalOpt(token);
        if (flattenOpt.isPresent()) {
            PermanentTokenFlatten flatten = flattenOpt.get();
            var identity = byPermanentToken(ctx, headers, token, flatten.getAccountId());
            return new TokenInfo(identity, flatten.getRoutingKey(), flatten.getAccountId(), flatten.getCurrency());
        }
        var accountFlattenOpt = AccountRoutingCurrencyFlatten.standard().readExternalOpt(token);
        if (accountFlattenOpt.isPresent()) {
            var flatten = accountFlattenOpt.get();
            var identity = byPermanentToken(ctx, headers, token, flatten.getAccountId());
            return new TokenInfo(identity, AsciiString.of(flatten.getRoutingKey()), flatten.getAccountId(), flatten.getCurrency());
        }
        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(token);
        var identity = byAccountId(ctx, headers, flatten.getAccountId());
        return new TokenInfo(identity, AsciiString.of(flatten.getRoutingKey()), flatten.getAccountId(), flatten.getCurrency());
    }

    private String getHookSecret(PlainServiceInfo info) {
        QueryStringDecoder decoder = new QueryStringDecoder(info.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        return Iterables.getOnlyElement(opts.get("hookSecretKey"));
    }

    private void checkAuthHeader(Function<PlainServiceInfo, String> expected, OperatorSpec operator,
            HttpHeaders headers) {

        String si = String.format("%s-%s-%s", AggregatorWildcardUPSs.IRGS_PREFIX, operator.code(),
                ProviderSpec.RELAX.code());
        PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
        String their = Iterables.getOnlyElement(headers.getRequestHeader(HttpHeaders.AUTHORIZATION));
        String our = expected.apply(psi);

        malformed(their, our);
    }

    private String getShortenRoundIdIfNeeded(String roundId) {
        if (roundId.length() > props.MAX_ROUND_ID_LENGTH.get()) {
            return WalletSessionUtils.shortenWalletSession(roundId);
        }
        return roundId;
    }

    private record TokenInfo(IdentityInfo.IdentityInfoBuilder identity, AsciiString routingKey, String accountId,
            String currency) {}

    private RelaxErrorResponse.ErrorType logAndMapToErrorCode(Throwable origin) {
        var codeToReturn = UNHANDLED;
        var cause = getRootCause(origin);

        if (cause instanceof UnexpectedHttpStatusException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));

            codeToReturn = switch (ex.getCode()) {
                case HttpStatus.SC_UNAUTHORIZED -> INVALID_TOKEN;
                case HttpStatus.SC_PAYMENT_REQUIRED -> CUSTOM_ERROR;
                case HttpStatus.SC_LOCKED -> BLOCKED_FROM_PRODUCT;
                case HttpStatus.SC_FORBIDDEN -> TRANSACTION_DECLINED;
                case HttpStatus.SC_NOT_FOUND -> checkExceptionMessage(ex.getEntity(), codeToReturn);
                default -> UNHANDLED;
            };
        } else if (origin instanceof EntityNotFoundException || origin instanceof RelaxException) {
            codeToReturn = checkExceptionMessage(origin.getMessage(), codeToReturn);
        }

        // ~ log to sentry if necessary
        if (codeToReturn == UNHANDLED) {
            logError(cause, errorRateLimiter.check(cause));
        } else {
            logWarn(cause);
        }

        return codeToReturn;
    }

    private static RelaxErrorResponse.ErrorType checkExceptionMessage(String message, RelaxErrorResponse.ErrorType error) {
        return switch (message) {
            case String s when s.contains(UNABLE_TO_FIND_ACCOUNT) -> CUSTOM_ERROR;
            case String s when s.contains(UNABLE_TO_FIND_GAME_SESSION)
                    || s.contains(HAS_BEEN_EXPIRED_OR_HAS_BEEN_USED) -> INVALID_TOKEN;
            case String s when s.contains(INSUFFICIENT_COINS) -> CUSTOM_ERROR;
            case String s when s.contains(UNABLE_TO_FIND_PRODUCT_BY_CODE) -> CUSTOM_ERROR;
            case String s when s.contains(UNABLE_TO_FIND_AT_LEAST_ONE_TRANSACTION) -> CUSTOM_ERROR;
            case String s when s.contains(UNABLE_TO_FIND_FS_CAMPAIGN) -> FREE_SPINS_CAMPAIGN_NOT_FOUND;
            case String s when s.contains(INVALID_TX_TYPE) -> FREE_SPINS_ERROR;
            default -> error;
        };
    }
}
