package gaming.relax.api.model;

import java.util.Objects;

import common.model.GameType;
import common.model.ProductSpec;
import common.model.ProviderSpec;
import lombok.Getter;
import lombok.experimental.Accessors;

@Accessors(fluent = true)
@Getter
public class RelaxProductSpec implements ProductSpec {
    private final RelaxProduct payload;

    public RelaxProductSpec(RelaxProduct payload) {
        this.payload = Objects.requireNonNull(payload);
    }

    @Override
    public String code() {
        return payload.getGameId();
    }

    @Override
    public String title() {
        return payload.getName();
    }

    @Override
    public String type() {
        return GameType.SLOTS.code();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.RELAX;
    }

    @Override
    public String supplier() {
        return payload.getStudio();
    }

    @Override
    public String toString() {
        return payload.toString();
    }
}
