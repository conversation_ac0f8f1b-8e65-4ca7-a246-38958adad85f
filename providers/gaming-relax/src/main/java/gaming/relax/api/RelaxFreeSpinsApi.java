package gaming.relax.api;

import com.turbospaces.ups.PlainServiceInfo;

import common.model.OperatorSpec;
import gaming.relax.api.freespins.model.request.CancelFreeSpinsRelaxRequest;
import gaming.relax.api.freespins.model.request.CreateFreeSpinsRelaxBatchRequest;
import gaming.relax.api.freespins.model.request.CreateFreeSpinRelaxRequest;
import gaming.relax.api.freespins.model.response.CancelFreeSpinsRelaxResponse;
import gaming.relax.api.freespins.model.response.CreateFreeSpinsRelaxResponse;

public interface RelaxFreeSpinsApi {
    CreateFreeSpinsRelaxResponse createFreeSpin(CreateFreeSpinRelaxRequest request, OperatorSpec operator) throws Exception;
    CreateFreeSpinsRelaxResponse createFreeSpinsBatch(CreateFreeSpinsRelaxBatchRequest request, OperatorSpec operator) throws Exception;
    CancelFreeSpinsRelaxResponse cancelFreeSpins(CancelFreeSpinsRelaxRequest request, PlainServiceInfo serviceInfo) throws Exception;
}
