package gaming.relax.api.freespins;

import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.http.HttpStatus;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.ServiceInfo;
import org.springframework.stereotype.Service;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.freespins.patrianna.UamFreeSpinsClient;
import aggregator.model.FreeSpinCampaign;
import common.AbstractFreeSpinsApi;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.FreeSpinsService;
import common.exception.FreeSpinsExceededTimeException;
import common.exception.FreeSpinsException;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.FreeSpinIdempotencyStatus;
import common.model.freespins.FreeSpinsInfo;
import common.model.freespins.FreeSpinsStatus;
import gaming.relax.api.RelaxFreeSpinsApi;
import gaming.relax.api.freespins.model.request.CancelFreeSpinsRelaxRequest;
import gaming.relax.api.freespins.model.request.CreateFreeSpinRelaxRequest;
import gaming.relax.api.freespins.model.request.CreateFreeSpinsRelaxBatchRequest;
import gaming.relax.api.freespins.model.response.CancelFreeSpinsRelaxResponse;
import gaming.relax.api.freespins.model.response.CreateFreeSpinsRelaxResponse;
import gaming.relax.utility.Mappers;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;

@Service
public class DefaultFreeSpinsRelaxService extends AbstractFreeSpinsApi implements FreeSpinsService {
    private final RelaxFreeSpinsApi freeSpinsApi;

    @Inject
    public DefaultFreeSpinsRelaxService(AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamFreeSpinsClient uamFreeSpinsClient,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager,
            RelaxFreeSpinsApi freeSpinsApi) {
        super(props, cloud, meterRegistry, uamFreeSpinsClient, spannerTemplate, cacheManager);
        this.freeSpinsApi = Objects.requireNonNull(freeSpinsApi);
    }

    @Override
    public CreateFreeSpinsResponse createFreeSpins(OperatorSpec operator, CreateFreeSpinsRequest request) throws Exception {
        var createFreeSpinsResponseOpt = getCreateFreeSpinsResponseByRequestId(request.getBonusCode(), operator, provider(), request.getRequestId());

        if (createFreeSpinsResponseOpt.isPresent()) {
            var response = createFreeSpinsResponseOpt.get();
            validateBonusCodeFromRequestId(request, response.getBonusCode());

            return new CreateFreeSpinsResponse(response.getBonusCode(), response.getFreeSpinsList(), response.getCurrency());
        }

        var campaignOpt = readFreeSpinsCampaign(operator, provider(), request.getBonusCode());

        if (campaignOpt.isEmpty()) {
            throw new FreeSpinsException(String.format("FS Campaign not found by bonus code: %s", request.getBonusCode()));
        }

        var freeSpinsCampaign = campaignOpt.get();
        List<FreeSpinsInfo> freeSpinsInfoList = createFreeSpins(freeSpinsCampaign, request.getPlayerIdList(), request.getRequestId());

        if (freeSpinsInfoList.isEmpty()) {
            return CreateFreeSpinsResponse.buildError(400, "Empty player list ");
        }

        var accountFreeSpinsList = prepareAccountFreeSpinsList(operator, provider(), request, freeSpinsInfoList, FreeSpinsStatus.CREATED);
        saveFreeSpinsWithIdempotencyKey(operator, provider(), request.getRequestId(), accountFreeSpinsList, FreeSpinIdempotencyStatus.FINISHED);

        return new CreateFreeSpinsResponse(request.getBonusCode(), freeSpinsInfoList, freeSpinsCampaign.getCurrency());
    }

    @Override
    public CancelPlayerFreeSpinResponse cancelPlayerFreeSpin(OperatorSpec operator, CancelPlayerFreeSpinRequest request)
            throws Exception {
        PlainServiceInfo serviceInfo = getPlainServiceInfo(operator.code());

        CancelPlayerFreeSpinResponse response = new CancelPlayerFreeSpinResponse();
        response.setBonusCode(request.getCampaign());

        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());

        if (freeSpinCampaignOpt.isEmpty()) {
            response.setError(String.valueOf(HttpStatus.SC_NOT_FOUND));
            response.setMsg(BONUS_CODE_NOT_FOUND_MSG);

            return response;
        }

        var freeSpinInfo = removePlayerFromBonus(serviceInfo, request.getCurrency(), request.getFreeSpin());
        response.setFreeSpin(freeSpinInfo);

        return response;
    }

    @Override
    public CancelFreeSpinsResponse cancelFreeSpinsBatching(OperatorSpec operator, CancelFreeSpinsRequest request)
            throws Exception {

        PlainServiceInfo serviceInfo = getPlainServiceInfo(operator.code());

        List<FreeSpinsInfo> canceledFreeSpinsList = Collections.synchronizedList(Lists.newArrayList());
        CancelFreeSpinsResponse response = new CancelFreeSpinsResponse(request.getCampaign(), canceledFreeSpinsList);
        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());

        if (freeSpinCampaignOpt.isEmpty()) {
            return CancelFreeSpinsResponse.buildError((HttpStatus.SC_NOT_FOUND), BONUS_CODE_NOT_FOUND_MSG);
        }

        if (request.getFreeSpinsList() != null && !request.getFreeSpinsList().isEmpty()) {
            if (request.getFreeSpinsList().size() == 1) {
                var freeSpinInfo = removePlayerFromBonus(serviceInfo, request.getCurrency(), request.getFreeSpinsList().getFirst());

                response.setFreeSpinsList(List.of(freeSpinInfo));
            } else {
                try {
                    cancelFreeSpinsBufferProcessing(request, canceledFreeSpinsList, serviceInfo);
                } catch (FreeSpinsExceededTimeException e) {
                    logger.error(e.getMessage(), e);

                    response = CancelFreeSpinsResponse.buildError((HttpStatus.SC_REQUEST_TOO_LONG), e.getMessage());
                }
            }
        } else {
            logger.error("Free spins batching cancel list is invalid {}", request.getFreeSpinsList());
            response = CancelFreeSpinsResponse.buildError((HttpStatus.SC_BAD_REQUEST), "Free spins batching cancel list is invalid");
        }

        return response;
    }

    @Override
    public FreeSpinsInfo removePlayerFromBonus(PlainServiceInfo serviceInfo, String currency, FreeSpinsInfo freeSpin) throws Exception {
        var response = cancelFreeSpins(freeSpin, serviceInfo);

        if (response.isSuccess()) {
            return FreeSpinsInfo.builder()
                    .playerId(freeSpin.getPlayerId())
                    .freeSpinsId(response.getFreeSpinId())
                    .isApplied(true)
                    .build();
        }

        return FreeSpinsInfo.builder()
                .playerId(freeSpin.getPlayerId())
                .isApplied(false)
                .message(response.getMessage())
                .build();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.RELAX;
    }

    private List<FreeSpinsInfo> createFreeSpins(FreeSpinCampaign freeSpinCampaign, List<String> players, String requestId) throws Exception {
        CreateFreeSpinsRelaxResponse response;

        if (players.size() > 1) {
            CreateFreeSpinsRelaxBatchRequest request = CreateFreeSpinsRelaxBatchRequest.builder()
                    .spins(freeSpinCampaign.getSpins())
                    .betAmount(Mappers.toProviderBetAmount(freeSpinCampaign.getBetAmount()))
                    .expirationTimeInSeconds(freeSpinCampaign.getExpirationDate().toEpochSecond(ZoneOffset.UTC))
                    .gameId(freeSpinCampaign.getGames())
                    .currency(Mappers.toProviderCurrency(freeSpinCampaign.getCurrency()))
                    .playerCurrency(Mappers.toProviderCurrency(freeSpinCampaign.getCurrency()))
                    .promoCode(freeSpinCampaign.getBonusCode())
                    .remoteUsernames(players.stream().map(Mappers::toProviderRemoteUsername).toList())
                    .transactionId(requestId)
                    .build();

            response = freeSpinsApi.createFreeSpinsBatch(request, OperatorSpec.byCode(freeSpinCampaign.getOperator()));
        } else if (players.size() == 1) {
            CreateFreeSpinRelaxRequest request = CreateFreeSpinRelaxRequest.builder()
                    .spins(freeSpinCampaign.getSpins())
                    .betAmount(Mappers.toProviderBetAmount(freeSpinCampaign.getBetAmount()))
                    .expirationTimeInSeconds(freeSpinCampaign.getExpirationDate().toEpochSecond(ZoneOffset.UTC))
                    .gameId(freeSpinCampaign.getGames())
                    .currency(Mappers.toProviderCurrency(freeSpinCampaign.getCurrency()))
                    .playerCurrency(Mappers.toProviderCurrency(freeSpinCampaign.getCurrency()))
                    .promoCode(freeSpinCampaign.getBonusCode())
                    .remoteUsername(Mappers.toProviderRemoteUsername(players.getFirst()))
                    .transactionId(requestId)
                    .build();

            response = freeSpinsApi.createFreeSpin(request, OperatorSpec.byCode(freeSpinCampaign.getOperator()));
        } else {
            return Collections.emptyList();
        }

        if (response.isSuccess()) {
            return response.getFreeSpinIds()
                    .stream()
                    .map(f -> FreeSpinsInfo.builder()
                            .freeSpinsId(f.getLast())
                            .isApplied(true)
                            .playerId(Mappers.fromProviderRemoteUsername(f.getFirst()))
                            .build())
                    .collect(Collectors.toList());
        } else {
            return players.stream()
                    .map(playerId -> FreeSpinsInfo.builder()
                            .isApplied(false)
                            .playerId(playerId)
                            .message(response.getMessage())
                            .build())
                    .collect(Collectors.toList());
        }
    }

    private CancelFreeSpinsRelaxResponse cancelFreeSpins(FreeSpinsInfo freeSpinsInfo, PlainServiceInfo serviceInfo) throws Exception {
        CancelFreeSpinsRelaxRequest request = CancelFreeSpinsRelaxRequest.builder()
                .freeSpinId(freeSpinsInfo.getFreeSpinsId())
                .build();

        return freeSpinsApi.cancelFreeSpins(request, serviceInfo);
    }

    private PlainServiceInfo getPlainServiceInfo(String operator) {
        String name = String.format("%s-%s-%s", AggregatorWildcardUPSs.IRGS_PREFIX, operator, provider().code());
        Optional<ServiceInfo> optionalServiceInfo = UPSs.findServiceInfoByName(cloud, name);

        return (PlainServiceInfo) optionalServiceInfo.orElseThrow();
    }
}
