package gaming.relax.api.freespins.model.response;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateFreeSpinsRelaxResponse {
    @JsonProperty("txid")
    private String transactionId;
    @JsonProperty("freespinids")
    private List<List<String>> freeSpinIds;
    private String status;
    @JsonProperty("errorcode")
    private String error;
    @JsonIgnore
    private String message;

    @JsonIgnore
    public boolean isSuccess() {
        return !StringUtils.isBlank(transactionId)
                && "OK".equalsIgnoreCase(status)
                && !CollectionUtils.isEmpty(freeSpinIds)
                && StringUtils.isBlank(error)
                && StringUtils.isBlank(message);
    }
}
