package gaming.relax.api;

import java.io.IOException;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.entity.ContentType;
import org.jboss.resteasy.util.BasicAuthHelper;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Iterables;
import com.google.common.net.HttpHeaders;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import common.AbstractGamingProviderApi;
import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.launch.GameLaunchInfo;
import common.model.launch.GameLaunchResponse;
import common.utils.BaseMapper;
import gaming.relax.api.freespins.model.request.CancelFreeSpinsRelaxRequest;
import gaming.relax.api.freespins.model.request.CreateFreeSpinRelaxRequest;
import gaming.relax.api.freespins.model.request.CreateFreeSpinsRelaxBatchRequest;
import gaming.relax.api.freespins.model.response.CancelFreeSpinsRelaxResponse;
import gaming.relax.api.freespins.model.response.CreateFreeSpinsRelaxResponse;
import gaming.relax.api.model.Parameters;
import gaming.relax.api.model.RelaxProduct;
import gaming.relax.api.model.RelaxProductSpec;
import gaming.relax.utility.Mappers;
import io.netty.handler.codec.http.QueryStringDecoder;
import jakarta.inject.Inject;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Service
public class DefaultRelaxApi extends AbstractGamingProviderApi<RelaxProductSpec> implements RelaxApi, RelaxLaunchApi, RelaxFreeSpinsApi {
    private static final String GET_GAMES = "/casino/games/getgames";
    private static final String CREATE_FREE_SPINS = "/casino/freespins/add";
    private static final String CANCEL_FREE_SPINS = "/casino/freespins/cancel";

    @Inject
    public DefaultRelaxApi(AggregatorServerProperties props, DynamicCloud cloud, OkHttpClient httpClient, CommonObjectMapper mapper) {
        super(props, cloud, httpClient, mapper);
    }

    public List<RelaxProductSpec> toGames(String payload) throws Exception {
        ImmutableList.Builder<RelaxProductSpec> games = ImmutableList.builder();
        ObjectNode root = (ObjectNode) mapper.readTree(payload);
        ArrayNode children = (ArrayNode) root.get("games");
        Iterator<JsonNode> it = children.elements();

        while (it.hasNext()) {
            RelaxProduct item = getRelaxProduct(it);
            games.add(new RelaxProductSpec(item));
        }

        return games.build();
    }

    @Override
    public Collection<RelaxProductSpec> getProducts(String operator, PlainServiceInfo si) throws Exception {
        Request get = getGamesHttpRequest(si);

        String text = sendRequest(get);

        return toGames(text);
    }

    @Override
    public GameLaunchResponse launch(GameLaunchInfo gameLaunchInfo, String operator) throws Exception {
        var currency = gameLaunchInfo.getCurrency();
        var operatorSpec = OperatorSpec.fromString(operator);
        var psiByCurrency = getPlainServiceInfo(operatorSpec);
        var launchSi = getLaunchPlainServiceInfo(operatorSpec);

        var params = gameLaunchInfo.isDemo()
                ? getDemoLaunchParams(gameLaunchInfo, currency)
                : getLaunchParams(gameLaunchInfo, psiByCurrency, currency);
        var uri = makeURI(LAUNCH_PATH, launchSi, params);

        return Mappers.toGameLaunchResponse(uri.toString());
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.RELAX;
    }

    @Override
    public CreateFreeSpinsRelaxResponse createFreeSpin(CreateFreeSpinRelaxRequest createFreeSpinsRelaxRequest, OperatorSpec operator) throws Exception {
        var serviceInfo = getPlainServiceInfo(operator);
        Request request = buildRequest(serviceInfo, mapper.writeValueAsString(createFreeSpinsRelaxRequest), CREATE_FREE_SPINS);

        String response = sendRequest(request);

        return mapper.readValue(response, CreateFreeSpinsRelaxResponse.class);
    }

    @Override
    public CreateFreeSpinsRelaxResponse createFreeSpinsBatch(CreateFreeSpinsRelaxBatchRequest createFreeSpinsRelaxRequest, OperatorSpec operator)
            throws Exception {
        var serviceInfo = getPlainServiceInfo(operator);
        Request request = buildRequest(serviceInfo, mapper.writeValueAsString(createFreeSpinsRelaxRequest), CREATE_FREE_SPINS);

        String response = sendRequest(request);

        return mapper.readValue(response, CreateFreeSpinsRelaxResponse.class);
    }

    @Override
    public CancelFreeSpinsRelaxResponse cancelFreeSpins(CancelFreeSpinsRelaxRequest cancelFreeSpinsRelaxRequest, PlainServiceInfo serviceInfo)
            throws Exception {
        Request request = buildRequest(serviceInfo, mapper.writeValueAsString(cancelFreeSpinsRelaxRequest), CANCEL_FREE_SPINS);

        String response = sendRequest(request);

        return mapper.readValue(response, CancelFreeSpinsRelaxResponse.class);
    }

    @Override
    public boolean isValidResponse(Response response) {
        return response.isSuccessful()
                || (response.code() >= HttpStatus.SC_BAD_REQUEST
                        && response.code() < HttpStatus.SC_INTERNAL_SERVER_ERROR);
    }

    private LinkedHashMap<String, String> getLaunchParams(GameLaunchInfo gameLaunchInfo, PlainServiceInfo psi, String currency) {
        var params = getGeneralLaunchParams(gameLaunchInfo, currency);
        params.put(Parameters.MODE.code(), REAL);
        params.put(Parameters.PARTNER.code(), psi.getUserName());
        params.put(Parameters.PARTNER_ID.code(), getPartnerId(psi));

        params.values().removeIf(StringUtils::isEmpty);

        return params;
    }

    private LinkedHashMap<String, String> getDemoLaunchParams(GameLaunchInfo gameLaunchInfo, String currency) {
        var params = getGeneralLaunchParams(gameLaunchInfo, currency);
        params.put(Parameters.MODE.code(), FUN);

        params.values().removeIf(StringUtils::isEmpty);

        return params;
    }

    private LinkedHashMap<String, String> getGeneralLaunchParams(GameLaunchInfo gameLaunchInfo, String currency) {
        var params = new LinkedHashMap<String, String>();

        params.put(Parameters.TOKEN.code(), gameLaunchInfo.getToken());
        params.put(Parameters.GAME.code(), gameLaunchInfo.getGameId());
        params.put(Parameters.CURRENCY.code(), Mappers.toProviderCurrency(currency));
        params.put(Parameters.LOCALE.code(), BaseMapper.toProviderLocale(gameLaunchInfo.getLocale(), provider()));
        params.put(Parameters.PLATFORM.code(), Mappers.toProviderPlatform(gameLaunchInfo.getType()));
        params.put(Parameters.FULL_SCREEN.code(), BooleanUtils.FALSE);
        params.put(Parameters.JURISDICTION.code(), "IM");

        return params;
    }

    private String getPartnerId(PlainServiceInfo info) {
        QueryStringDecoder decoder = new QueryStringDecoder(info.getUri());
        Map<String, List<String>> opts = decoder.parameters();

        return Iterables.getOnlyElement(opts.get("partnerId"));
    }

    private RelaxProduct getRelaxProduct(Iterator<JsonNode> it) throws IOException {
        JsonNode node = it.next();
        logger.debug(node.toString());

        return mapper.readValue(node.traverse(), RelaxProduct.class);
    }

    private Request getGamesHttpRequest(PlainServiceInfo serviceInfo) throws Exception {
        return new Request.Builder()
                .get()
                .url(makeURI(serviceInfo.getPath() + GET_GAMES, serviceInfo).toString())
                .addHeader(HttpHeaders.ACCEPT, "application/json")
                .addHeader(HttpHeaders.CONTENT_TYPE, "application/json")
                .addHeader(HttpHeaders.AUTHORIZATION, BasicAuthHelper.createHeader(serviceInfo.getUserName(), serviceInfo.getPassword()))
                .build();
    }

    private Request buildRequest(PlainServiceInfo serviceInfo, String requestBody, String path) throws Exception {
        return new Request.Builder()
                .url(makeURI(serviceInfo.getPath() + path, serviceInfo).toString())
                .post(RequestBody.create(requestBody,
                        MediaType.parse(ContentType.APPLICATION_JSON.getMimeType())))
                .addHeader(HttpHeaders.ACCEPT, "application/json")
                .addHeader(HttpHeaders.CONTENT_TYPE, "application/json")
                .addHeader(HttpHeaders.AUTHORIZATION, BasicAuthHelper.createHeader(serviceInfo.getUserName(), serviceInfo.getPassword()))
                .build();
    }
}
