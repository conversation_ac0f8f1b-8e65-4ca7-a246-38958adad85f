package gaming.relax.api.model;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum Parameters {
    TOKEN("ticket"),
    GAME("gameid"),
    CURRENCY("currency"),
    LOCALE("lang"),
    PARTNER("partner"),
    PARTNER_ID("partnerid"),
    PLATFORM("channel"),
    FULL_SCREEN("fullscreen"),
    MODE("moneymode"),
    JURISDICTION("jurisdiction");

    private final String code;

    public String code() {
        return this.code;
    }
}
