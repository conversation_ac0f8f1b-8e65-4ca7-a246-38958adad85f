package gaming.relax.api.freespins.model.response;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CancelFreeSpinsRelaxResponse {
    @JsonProperty("expire")
    private String expirationDate;
    @JsonProperty("freespinid")
    private String freeSpinId;
    private String status;
    @JsonProperty("errorcode")
    private String error;
    @JsonIgnore
    private String message;

    @JsonIgnore
    public boolean isSuccess() {
        return !StringUtils.isBlank(expirationDate)
                && !StringUtils.isBlank(freeSpinId)
                && StringUtils.isBlank(error)
                && StringUtils.isBlank(message);
    }
}
