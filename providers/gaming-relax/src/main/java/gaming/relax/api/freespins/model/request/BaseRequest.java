package gaming.relax.api.freespins.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@AllArgsConstructor
@SuperBuilder
@NoArgsConstructor
@Data
public class BaseRequest {
    @JsonProperty("txid")
    private String transactionId;
    @JsonProperty("gameid")
    private String gameId;
    @JsonProperty("amount")
    private int spins;
    @JsonProperty("freespinvalue")
    private String betAmount;
    @JsonProperty("expire")
    private Long expirationTimeInSeconds;
    @JsonProperty("promocode")
    private String promoCode;
    @JsonProperty("paylines")
    private final int payLines = 1;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("playercurrency")
    private String playerCurrency;
}
