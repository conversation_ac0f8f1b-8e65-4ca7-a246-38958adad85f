package gaming.relax.api.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelaxProduct {

    @JsonProperty("gameid")
    private String gameId;

    private String name;

    private String studio;

    private List<String> channels;
}
