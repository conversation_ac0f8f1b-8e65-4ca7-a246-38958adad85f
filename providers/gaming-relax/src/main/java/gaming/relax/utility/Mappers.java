package gaming.relax.utility;

import java.math.BigDecimal;

import common.GameLaunchService;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.model.launch.GameLaunchResponse;
import gaming.relax.api.RelaxLaunchApi;

public interface Mappers {

    String PROVIDER_SC = "SC.";
    String PROVIDER_GC = "GC.";
    String GC = "GC";
    String SC = "SC";
    String SPECIFIC_GAME_PREFIX = "rlx_";
    BigDecimal MULTIPLIER = BigDecimal.valueOf(100);

    static String toProviderBetAmount(String betAmount) {
        return String.valueOf(new BigDecimal(betAmount).multiply(MULTIPLIER).intValue());
    }

    static String toProviderCurrency(String currency) {
        return switch (currency) {
            case SC -> PROVIDER_SC;
            case GC -> PROVIDER_GC;
            default -> currency;
        };
    }

    static GameLaunchResponse toGameLaunchResponse(String uri) {
        GameLaunchResponse response = new GameLaunchResponse();
        response.setGameUrl(uri);
        return response;
    }

    static String toProviderPlatform(String platform) {
        return switch (platform) {
            case GameLaunchService.DESKTOP -> RelaxLaunchApi.DESKTOP;
            case GameLaunchService.MOBILE -> RelaxLaunchApi.MOBILE;
            default -> platform;
        };
    }

    static String toProviderRemoteUsername(String playerId) {
        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(playerId);

        return AccountRoutingCurrencyFlatten.standard(flatten.getRoutingKey(),
                flatten.getAccountId(),
                flatten.getCurrency())
                .writeExternal();
    }

    static String fromProviderRemoteUsername(String playerId) {
        var flatten = AccountRoutingCurrencyFlatten.standard().readExternal(playerId);

        return AccountRoutingCurrencyFlatten.underscore(flatten.getRoutingKey(),
                flatten.getAccountId(),
                flatten.getCurrency())
                .writeExternal();
    }
}
