package gaming.relax;

import com.google.common.util.concurrent.FluentFuture;

import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import common.GamingService;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import io.netty.util.AsciiString;

public interface RelaxGamingService extends GamingService {

    FluentFuture<GetPermanentTokenResponseInfo> authenticate(
            GetPermanentTokenRequestInfo req,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<AccountBalanceResponseInfo> getBalance(
            AccountBalanceRequestInfo req,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<WalletSessionResponseInfo> submit(
            WalletSessionRequestInfo req,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<AccountBalanceResponseInfo> refund(
            CancelWalletSessionRequestInfo req,
            ProviderSpec provider,
            OperatorSpec operator,
            long originalTxId,
            AsciiString routingKey);
}
