package gaming.relax;

import static common.model.ProviderSpec.RELAX;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.google.cloud.spring.data.spanner.core.SpannerReadOptions;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.util.concurrent.FluentFuture;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.UnexpectedJaxrsException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.model.GameSession;
import aggregator.model.WalletSessionWithTransactions;
import aggregator.repo.DefaultSpannerTransactionalCallback;
import aggregator.wallet.patrianna.UamSeamlessWalletClient;
import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionResponseInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenRequestInfo;
import aggregator.wallet.patrianna.types.GetPermanentTokenResponseInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import common.AbstractGamingService;
import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.WalletSessionUtils;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedFunction0;
import io.vavr.Tuple2;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DefaultRelaxGamingService extends AbstractGamingService implements RelaxGamingService {
    private static final String DEFAULT_GAME = "no_game";

    @Inject
    public DefaultRelaxGamingService(
            AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamSeamlessWalletClient client,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager) {
        super(props, cloud, meterRegistry, spannerTemplate, cacheManager, client, RELAX);
    }

    @Override
    public FluentFuture<GetPermanentTokenResponseInfo> authenticate(
            GetPermanentTokenRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<GetPermanentTokenResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            String accountId = request.getIdentity().accountId();
            var tuple = spannerTemplate.performReadWriteTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager,
                    callback -> {
                        var brandRepo = callback.brandRepo();
                        var account = brandRepo.getOrCreateAccount(operator.code(), accountId);
                        var gameSessionOpt = brandRepo.gameSession(account.getHash(), request.getToken(), ProviderSpec.RELAX.code());
                        return new Tuple2<>(gameSessionOpt, account);
                    }));
            var gameSessionOpt = tuple._1();
            var account = tuple._2();
            if (gameSessionOpt.isPresent()) {
                var balanceRequest = new AccountBalanceRequestInfo(request.getIdentity());
                var balanceResponse = client.balance(psi, balanceRequest, traceId, routingKey);

                GetPermanentTokenResponseInfo response = new GetPermanentTokenResponseInfo();
                response.setToken(gameSessionOpt.get().getPermanentToken());
                response.setBalances(balanceResponse.getBalances());
                response.setGoldCurrency(balanceResponse.getGoldCurrency());
                response.setSweepstakeCurrency(balanceResponse.getSweepstakeCurrency());
                response.setFiatCurrency(balanceResponse.getFiatCurrency());
                return response;
            }

            GetPermanentTokenResponseInfo response = client.authenticate(psi, request, traceId, routingKey);
            spannerTemplate.performReadWriteTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager,
                    callback -> {
                        var brandRepo = callback.brandRepo();
                        var product = brandRepo.getOrCreateProduct(operator.code(), DEFAULT_GAME);
                        var session = new GameSession(
                                ProviderSpec.RELAX.code(),
                                account,
                                product,
                                request.getToken(),
                                request.getToken(),
                                response.getToken());
                        brandRepo.insert(session);
                        return new Object();
                    }));
            return response;
        }));
    }

    @Override
    public FluentFuture<AccountBalanceResponseInfo> getBalance(
            AccountBalanceRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<AccountBalanceResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            return client.balance(psi, request, traceId, routingKey);
        }));
    }

    @Override
    public FluentFuture<WalletSessionResponseInfo> submit(
            WalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<WalletSessionResponseInfo>) () -> {
            if (request.getFreeSpinCampaign() != null) {
                checkFreeSpinsCampaignExists(request.getFreeSpinCampaign().getCampaign(),
                        operator.code(),
                        provider.code());
            }

            WalletSessionResponseInfo resp = client.submit(UPSs.findRequiredServiceInfoByName(cloud, si),
                    request,
                    traceId,
                    routingKey);

            upsertUamWalletSession(spannerTemplate, cacheManager, request, operator, ProviderSpec.RELAX);
            return resp;
        }));
    }

    @Override
    public FluentFuture<AccountBalanceResponseInfo> refund(
            CancelWalletSessionRequestInfo request,
            ProviderSpec provider,
            OperatorSpec operator,
            long originalTxId,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<AccountBalanceResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            String accountId = request.getIdentity().accountId();
            String currency = request.currency();

            var tuple = spannerTemplate.performReadOnlyTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager,
                    callback -> {
                        var brandRepo = callback.brandRepo();
                        var walletSessionRepo = callback.walletSessionRepo();
                        var account = brandRepo.requiredAccount(operator.code(), accountId);
                        var opt = walletSessionRepo.walletSessionWithTransactions(account.getHash(),
                                ProviderSpec.RELAX.code(), request.getSessionId(), currency);
                        return new Tuple2<>(account, opt);
                    }), new SpannerReadOptions());

            var account = tuple._1();
            Optional<WalletSessionWithTransactions> optws = tuple._2();

            if (optws.isEmpty()) {
                log.warn("There is No session found for account {}, provider {} & currency {}",
                        accountId, ProviderSpec.RELAX.code(), currency);

                return getBalance(psi, request.getIdentity(), traceId, routingKey);
            }

            try {
                optws.ifPresent(ws -> {
                    ws.getTransactions().stream().filter(walletTransaction -> walletTransaction.getReference()
                            .equals(String.valueOf(originalTxId))).findFirst()
                            .ifPresent(walletTransaction -> request.getTransactions()
                                    .stream()
                                    .findFirst()
                                    .ifPresent(walletTransactionInfo -> walletTransactionInfo
                                            .setAmount(new BigDecimal(walletTransaction.getAmount()))));
                    request.setAt(new AtInfo(ws.getCreatedAt()));
                });
                var txToCheck = request.getTransactions().getFirst();
                if (Objects.isNull(txToCheck.getAmount())) {
                    return getBalance(psi, request.getIdentity(), traceId, routingKey);
                }

                if (WalletSessionUtils.isRefundOriginTypeEnabled(props, operator)) {
                    WalletSessionUtils.addOriginTypeToRefundTransactions(request.getTransactions(), optws.get());
                }

                CancelWalletSessionResponseInfo resp = client.refund(psi, request, traceId, routingKey);
                upsertRefundedUamWalletSession(spannerTemplate, request, account, optws.get(), ProviderSpec.RELAX);

                return new AccountBalanceResponseInfo(resp);
            } catch (UnexpectedJaxrsException err) {
                log.error(err.getMessage(), err);
                return processException(err, psi, request.getIdentity(), traceId, routingKey);
            }
        }));
    }

    @Override
    public UamSeamlessWalletClient getClient() {
        return this.client;
    }

    private void checkFreeSpinsCampaignExists(String campaign, String operator, String provider) {
        spannerTemplate.performReadOnlyTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager,
                callback -> {
                    var brandRepo = callback.brandRepo();
                    brandRepo.requiredFreeSpinCampaign(campaign, operator, provider);

                    return new Object();
                }), new SpannerReadOptions());
    }
}
