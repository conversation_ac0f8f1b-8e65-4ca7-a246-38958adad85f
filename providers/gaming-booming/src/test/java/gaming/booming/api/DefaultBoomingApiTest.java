package gaming.booming.api;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.notNull;
import static org.mockito.Mockito.when;

import java.nio.charset.StandardCharsets;

import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.ConfigurableCloudConnector;
import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import common.AggregatorServerProperties;
import common.model.launch.GameLaunchInfo;
import gaming.booming.Mappers;
import gaming.booming.api.launch.BoomingGameLaunchResponse;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import okhttp3.ResponseBody;

@ExtendWith(MockitoExtension.class)
class DefaultBoomingApiTest {

    private static final String PULSZ_URI = "******************************************************************?secret=ZYkbtoQuq59ZldkJu4VtjPrc6v7MlYfrkyoKO8jBuvIGIn3FRp79LGybcUf4rF27";
    private static final String TYPE_MOBILE = "mobile";
    private static final String TYPE_DESKTOP = "desktop";
    private static final String PULSZ = "pulsz";
    private static final PlainServiceInfo si = new PlainServiceInfo("irgs-pulsz-gc-booming", PULSZ_URI);
    private static final CommonObjectMapper mapper = new CommonObjectMapper();
    private static final OkHttpClient mockHttpClient = Mockito.mock(OkHttpClient.class);
    private CommonObjectMapper mockMapper;
    private DefaultBoomingApi boomingApi;

    @BeforeAll
    static void setUpBeforeAll() throws Exception {
        ResponseBody responseBody = Mockito.mock(ResponseBody.class);
        Response mockResponse = Mockito.mock(Response.class);
        Call mockCall = Mockito.mock(Call.class);

        when(mockHttpClient.newCall(notNull())).thenReturn(mockCall);
        when(mockCall.execute()).thenReturn(mockResponse);
        when(mockResponse.body()).thenReturn(responseBody);
        when(mockResponse.code()).thenReturn(200);
        when(mockResponse.isSuccessful()).thenReturn(true);
    }

    @BeforeEach
    public void setUp() throws Throwable {
        mockMapper = Mockito.mock(CommonObjectMapper.class);

        DynamicCloud cloud = new DynamicCloud(new ConfigurableCloudConnector());
        cloud.addUps(si);
        ApplicationConfig cfg = ApplicationConfig.mock();
        AggregatorServerProperties props = new AggregatorServerProperties(cfg.factory());

        boomingApi = new DefaultBoomingApi(props, cloud, mockHttpClient, mockMapper);
        boomingApi.afterPropertiesSet();
    }

    @Test
    void launch_mobileType() throws Exception {
        // given
        var gameLaunch = genGameLaunch(TYPE_MOBILE);
        var responseString = IOUtils.resourceToString("/json/launch_mobile.json", StandardCharsets.UTF_8);
        var response = mapper.readValue(responseString, BoomingGameLaunchResponse.class);
        var boomingRequest = Mappers.toBoomingGameLaunchRequest(gameLaunch, "");
        var expectedURL = response.getGameUrl();

        when(mockMapper.writeValueAsString(any())).thenReturn(String.valueOf(boomingRequest));
        when(mockMapper.readValue((String) any(), (Class<BoomingGameLaunchResponse>) any())).thenReturn(response);
        // when
        var actual = boomingApi.launch(gameLaunch, PULSZ);

        // then
        assertEquals(expectedURL, actual.getGameUrl());
    }

    @Test
    void launch_desktopType() throws Exception {
        // given
        var gameLaunch = genGameLaunch(TYPE_DESKTOP);
        var responseString = IOUtils.resourceToString("/json/launch_desktop.json", StandardCharsets.UTF_8);
        var response = mapper.readValue(responseString, BoomingGameLaunchResponse.class);
        var boomingRequest = Mappers.toBoomingGameLaunchRequest(gameLaunch, "");
        var expectedURL = response.getGameUrl();

        when(mockMapper.writeValueAsString(any())).thenReturn(String.valueOf(boomingRequest));
        when(mockMapper.readValue((String) any(), (Class<BoomingGameLaunchResponse>) any())).thenReturn(response);

        // when
        var actual = boomingApi.launch(gameLaunch, "pulsz");

        // then
        assertEquals(expectedURL, actual.getGameUrl());
    }

    @Test
    void provider() {
        // given
        var expected = "booming";

        // when
        var actual = boomingApi.provider().code();

        // then
        assertEquals(expected, actual);
    }

    private GameLaunchInfo genGameLaunch(String type) {
        return GameLaunchInfo.builder()
                .gameId("5f9ad932f7447a001df72349")
                .locale("en")
                .balance("99.16")
                .currency("GC")
                .playerId("ffb1cd70_13752173307_GC")
                .token("ffb1cd70_13752173307_GC_1111111")
                .homeUrl("https://www.someHomeUrl.info")
                .cashUrl("https://www.someHomeUrl.info/store")
                .type(type)
                .build();
    }
}
