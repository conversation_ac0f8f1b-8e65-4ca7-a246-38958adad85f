package gaming.booming;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import common.model.OperatorSpec;
import gaming.booming.api.DefaultNonceService;

class DefaultNonceServiceTest {

    @Test
    void upAndGetNonce() {
        DefaultNonceService defaultNonceService = new DefaultNonceService();
        long first = defaultNonceService.getValidNonce(OperatorSpec.BLUEDREAM.code());
        long second = defaultNonceService.getValidNonce(OperatorSpec.BLUEDREAM.code());
        Assertions.assertEquals(first, second - 1);
    }
}
