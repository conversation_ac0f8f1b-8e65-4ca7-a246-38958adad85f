package gaming.booming.utils;

import java.util.List;
import java.util.Map;

import org.apache.commons.codec.digest.DigestUtils;

import com.google.common.collect.Iterables;
import com.turbospaces.ups.PlainServiceInfo;

import common.utils.SignatureUtils;
import io.netty.handler.codec.http.QueryStringDecoder;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SignatureUtilsBooming {
    public static String generateSignature(String key, String nonce, String body, String path) throws Exception {
        String msgToSign = String.format("%s%s%s", path, nonce, DigestUtils.sha256Hex(body));
        return SignatureUtils.signHMacSHA512(msgToSign, key);
    }

    public static String getSecretFromPsi(PlainServiceInfo info) {
        QueryStringDecoder decoder = new QueryStringDecoder(info.getUri());
        Map<String, List<String>> opts = decoder.parameters();
        return Iterables.getOnlyElement(opts.get("secret"));
    }
}
