package gaming.booming.b2b.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class CallbackRequest {
    @NotEmpty
    @JsonProperty("session_id")
    private String sessionId;
    private int round;
    @NotEmpty
    @JsonProperty("transaction_id")
    private String transactionId;
    @NotEmpty
    @JsonProperty("player_id")
    private String playerId;
    private String type;
    @NotEmpty
    private String debit;
    @NotEmpty
    private String credit;
    @JsonProperty("game_cycle")
    private GameCycle gameCycle;
    @JsonProperty("operator_launch_data")
    private String operatorLaunchData;
    private Campaign campaign;
}
