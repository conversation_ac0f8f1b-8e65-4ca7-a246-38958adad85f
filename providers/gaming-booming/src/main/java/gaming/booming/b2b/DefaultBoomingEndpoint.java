package gaming.booming.b2b;

import static common.utils.BaseMapper.addPrefixToGameId;

import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Objects;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Component;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.UnexpectedHttpStatusException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.FreeSpinCampaignInfo;
import aggregator.wallet.patrianna.types.IdentityByAccountIdInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.AbstractApiEndpoint;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.limiter.ErrorRateLimiter;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.HiddenModeUtils;
import gaming.booming.BoomingCurrencySpec;
import gaming.booming.BoomingGamingService;
import gaming.booming.Mappers;
import gaming.booming.b2b.model.BaseResponse;
import gaming.booming.b2b.model.Button;
import gaming.booming.b2b.model.CallbackRequest;
import gaming.booming.b2b.model.CallbackResponse;
import gaming.booming.utils.SignatureUtilsBooming;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AsciiString;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;

@Component
public class DefaultBoomingEndpoint extends AbstractApiEndpoint implements BoomingEndpoint {

    private final BoomingGamingService boomingGamingService;
    private final ErrorRateLimiter errorRateLimiter;

    @Inject
    public DefaultBoomingEndpoint(AggregatorServerProperties props,
            DynamicCloud cloud,
            BoomingGamingService boomingGamingService,
            ErrorRateLimiter errorRateLimiter) {
        super(props, cloud);
        this.boomingGamingService = Objects.requireNonNull(boomingGamingService);
        this.errorRateLimiter = Objects.requireNonNull(errorRateLimiter);
    }

    @Override
    public void callback(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            CallbackRequest req,
            InputStream io) throws Exception {
        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(req.getPlayerId());
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        try {
            verifySignature(headers, io, operator, Mappers.toHttpsCallbackUri(uriInfo.getRequestUri()), flatten.getCurrency());
        } catch (Exception ignored) {
            logger.error(ignored.getMessage(), ignored);
            sendError(async, HttpStatus.SC_INTERNAL_SERVER_ERROR, BaseResponse.INVALID_REQUEST_SIGNATURE);
            return;
        }

        var identityByAccountIdInfo = new IdentityByAccountIdInfo(flatten.getAccountId(), remoteIp(ctx, headers));

        if (StringUtils.isEmpty(req.getOperatorLaunchData())) {
            sendError(async, HttpStatus.SC_INTERNAL_SERVER_ERROR, String.format("Invalid request arguments, game_id= %s", req.getOperatorLaunchData()));
            return;
        }

        var txs = new ArrayList<WalletTransactionInfo>();
        WalletSessionRequestInfo sreqb = new WalletSessionRequestInfo();
        var debitTx = new WalletTransactionInfo();
        if (hasBonus(req)) {
            req.setDebit("0");
            sreqb.setFreeSpinCampaign(
                    new FreeSpinCampaignInfo(req.getCampaign().getBonusCode(), req.getCampaign().getFreeSpinsId()));
        }
        debitTx.setAmount(new BigDecimal(req.getDebit()));
        debitTx.setReference(Mappers.toDebitReference(req.getTransactionId()));
        debitTx.setType(WalletTransactionInfo.TYPE_DEBIT);
        debitTx.setCurrency(flatten.getCurrency());

        var creditTx = new WalletTransactionInfo();
        creditTx.setAmount(new BigDecimal(req.getCredit()));
        creditTx.setReference(Mappers.toCreditReference(req.getTransactionId()));
        creditTx.setType(WalletTransactionInfo.TYPE_CREDIT);
        creditTx.setCurrency(flatten.getCurrency());
        txs.add(debitTx);
        txs.add(creditTx);

        sreqb.setSessionId(generateWalletSession(req.getSessionId(), String.valueOf(req.getRound())));
        sreqb.setSource(determineProviderSpec().code());
        sreqb.setTransactions(txs);
        sreqb.setProduct(addPrefixToGameId(req.getOperatorLaunchData(), ProviderSpec.BOOMING));
        sreqb.setAt(new AtInfo(PlatformUtil.toLocalUTCDate()));
        sreqb.setIdentity(new IdentityInfo(identityByAccountIdInfo));
        sreqb.setComplete(true);

        boomingGamingService.submit(
                sreqb,
                operator,
                AsciiString.cached(flatten.getRoutingKey())).addCallback(
                        new FutureCallback<>() {
                            @Override
                            public void onSuccess(WalletSessionResponseInfo result) {
                                try {
                                    for (var balance : result.getBalances()) {
                                        if (balance.getCurrency().equals(flatten.getCurrency())) {
                                            var resp = new CallbackResponse();
                                            resp.setBalance(balance.getBalance().toPlainString());
                                            MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                            async.resume(Response.status(Response.Status.OK).entity(resp).build());
                                        }
                                    }
                                } catch (Throwable t) {
                                    onFailure(t);
                                }
                            }

                            @Override
                            public void onFailure(Throwable throwable) {
                                sendError(async, logAndMapToErrorCode(throwable), throwable.getMessage());
                            }
                        }, MoreExecutors.directExecutor());
    }

    @Override
    public void rollback(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            CallbackRequest req,
            InputStream io) throws Exception {
        var flatten = AccountRoutingCurrencyFlatten.underscore().readExternal(req.getPlayerId());
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        try {
            verifySignature(headers, io, operator, Mappers.toHttpsCallbackUri(uriInfo.getRequestUri()), flatten.getCurrency());
        } catch (Exception ignored) {
            logger.error(ignored.getMessage(), ignored);
            sendError(async, HttpStatus.SC_INTERNAL_SERVER_ERROR, BaseResponse.INVALID_REQUEST_SIGNATURE);
            return;
        }
        if (StringUtils.isEmpty(req.getOperatorLaunchData())) {
            sendError(async, HttpStatus.SC_INTERNAL_SERVER_ERROR, String.format("Invalid request arguments, game_id= %s", req.getOperatorLaunchData()));
            return;
        }

        var identityByAccountIdInfo = new IdentityByAccountIdInfo(flatten.getAccountId(), remoteIp(ctx, headers));

        var txs = new ArrayList<WalletTransactionInfo>();
        var refundDebitTx = new WalletTransactionInfo();
        if (hasBonus(req)) {
            req.setDebit("0");
        }
        refundDebitTx.setAmount(new BigDecimal(req.getDebit()));
        refundDebitTx.setReference(Mappers.toDebitReference(req.getTransactionId()));
        refundDebitTx.setType(WalletTransactionInfo.TYPE_REFUND);
        refundDebitTx.setCurrency(flatten.getCurrency());

        var refundCreditTx = new WalletTransactionInfo();
        refundCreditTx.setAmount(new BigDecimal(req.getCredit()));
        refundCreditTx.setReference(Mappers.toCreditReference(req.getTransactionId()));
        refundCreditTx.setType(WalletTransactionInfo.TYPE_REFUND);
        refundCreditTx.setCurrency(flatten.getCurrency());
        txs.add(refundDebitTx);
        txs.add(refundCreditTx);

        var sreqCancel = new CancelWalletSessionRequestInfo();
        sreqCancel.setSessionId(generateWalletSession(req.getSessionId(), String.valueOf(req.getRound())));
        sreqCancel.setTransactions(txs);
        sreqCancel.setIdentity(new IdentityInfo(identityByAccountIdInfo));
        sreqCancel.setAt(new AtInfo(PlatformUtil.toLocalUTCDate()));
        sreqCancel.setSource(determineProviderSpec().code());

        boomingGamingService.refund(
                sreqCancel,
                operator,
                AsciiString.cached(flatten.getRoutingKey())).addCallback(
                        new FutureCallback<>() {
                            @Override
                            public void onSuccess(AccountBalanceResponseInfo result) {
                                try {
                                    for (var balance : result.getBalances()) {
                                        if (balance.getCurrency().equals(flatten.getCurrency())) {
                                            var resp = new CallbackResponse();
                                            resp.setBalance(balance.getBalance().toPlainString());
                                            MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                            async.resume(Response.status(Response.Status.OK).entity(resp).build());
                                        }
                                    }
                                } catch (Throwable t) {
                                    onFailure(t);
                                }
                            }

                            @Override
                            public void onFailure(Throwable throwable) {
                                sendError(async, logAndMapToErrorCode(throwable), throwable.getMessage());
                            }
                        }, MoreExecutors.directExecutor());
    }

    private ProviderSpec determineProviderSpec() {
        return HiddenModeUtils.determineProviderByHiddenMode(props, ProviderSpec.BOOMING);
    }

    private void sendError(AsyncResponse async, int code, String message) {

        BaseResponse errorResponse = new BaseResponse();
        if (HttpStatus.SC_OK == code) {
            errorResponse.setError(BaseResponse.LOW_BALANCE);
        } else {
            errorResponse.setError(BaseResponse.CUSTOM);
            errorResponse.setMessage(message);

            var closeButton = new Button();
            closeButton.setTitle("OK");
            closeButton.setAction("close_dialog");

            var exitButton = new Button();
            exitButton.setTitle("Exit");
            exitButton.setAction("exit");

            var buttons = new ArrayList<Button>();
            buttons.add(closeButton);
            buttons.add(exitButton);

            errorResponse.setButtons(buttons);
        }
        MDC.remove(MdcTags.MDC_ROUTING_KEY);

        async.resume(Response.status(HttpStatus.SC_OK).entity(errorResponse).build());
    }

    private int logAndMapToErrorCode(Throwable origin) {
        int codeToReturn = HttpStatus.SC_NOT_FOUND;

        Throwable cause = getRootCause(origin);

        // ~ log to sentry if necessary
        if (cause instanceof UnexpectedHttpStatusException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));

            codeToReturn = ex.getCode() == HttpStatus.SC_PAYMENT_REQUIRED
                    ? HttpStatus.SC_OK
                    : HttpStatus.SC_INTERNAL_SERVER_ERROR;

            logWarn(cause);
        } else {
            logError(cause, errorRateLimiter.check(cause));
        }

        return codeToReturn;
    }

    @VisibleForTesting
    void verifySignature(HttpHeaders headers, InputStream io, OperatorSpec operator, String url, String currency) throws Exception {
        var si = AggregatorWildcardUPSs.<PlainServiceInfo> findCurrencyScopedIrgsServiceInfo(
                BoomingCurrencySpec.fromString(currency).shortCode().toLowerCase(), cloud, operator.code(), ProviderSpec.BOOMING.code());
        io.reset();
        String apiSecret = SignatureUtilsBooming.getSecretFromPsi(si);
        String body = IOUtils.toString(io, StandardCharsets.UTF_8);
        String nonce = headers.getHeaderString(NONCE);
        String signatureTheir = headers.getHeaderString(SIGNATURE);
        malformed(signatureTheir, SignatureUtilsBooming.generateSignature(apiSecret, nonce, body, url));
    }

    private static String generateWalletSession(String sessionId, String roundNum) {
        return String.format("%s%s", sessionId, roundNum);
    }

    private boolean hasBonus(CallbackRequest req) {
        return req.getCampaign() != null && StringUtils.isNotBlank(req.getCampaign().getFreeSpinsId());
    }
}
