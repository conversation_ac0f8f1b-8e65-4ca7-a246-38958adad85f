package gaming.booming.b2b;

import java.io.InputStream;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.http.HttpProto;

import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import gaming.booming.b2b.model.CallbackRequest;
import io.netty.channel.ChannelHandlerContext;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.UriInfo;

@Path(HttpProto.V1 + "/{operator}/booming")
@ApiEndpoint(ipWhitelistKey = AggregatorServerProperties.IP_WHITELIST_BOOMING_KEY)
public interface BoomingEndpoint {
    String NONCE = "Bg-Nonce";
    String SIGNATURE = "Bg-Signature";
    String CALLBACK_PATH = "/callback";
    String ROLLBACK_PATH = "/rollback-callback";

    @POST
    @Path(CALLBACK_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void callback(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull CallbackRequest request,
            InputStream io) throws Exception;

    @POST
    @Path(ROLLBACK_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void rollback(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull CallbackRequest request,
            InputStream io) throws Exception;
}
