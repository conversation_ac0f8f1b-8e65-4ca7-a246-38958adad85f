package gaming.booming.b2b.model;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BaseResponse {
    public static final String LOW_BALANCE = "low_balance";
    public static final String CUSTOM = "custom";
    public static final String INVALID_REQUEST_SIGNATURE = "Invalid request signature";
    private String error;
    private String message;
    private List<Button> buttons;
}
