package gaming.booming.api.freespins.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateFreeSpinsBoomingRequest {
    private String name;
    @JsonProperty("campaign_operator_data")
    private String campaignOperatorData;
    @JsonProperty("start_date")
    private String startDate;
    @JsonProperty("end_date")
    private String endDate;
    @JsonProperty("spins_expire_date")
    private String spinsExpireDate;
    @JsonProperty("open_to_all")
    private boolean openToAll;
    @JsonProperty("total_spins")
    private int totalSpins;
    @JsonProperty("player_list")
    private String playerList;
    @JsonProperty("game_bet_options")
    private JsonNode gameBetOptions;
}
