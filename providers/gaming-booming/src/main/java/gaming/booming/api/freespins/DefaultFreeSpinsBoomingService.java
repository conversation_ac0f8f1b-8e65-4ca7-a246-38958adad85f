package gaming.booming.api.freespins;

import static common.utils.BaseMapper.removePrefixFromGameId;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.http.HttpStatus;
import org.springframework.cloud.DynamicCloud;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.collect.Lists;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.okhttp.UnexpectedHttpClientException;
import com.turbospaces.ups.PlainServiceInfo;

import aggregator.freespins.patrianna.UamFreeSpinsClient;
import aggregator.model.FreeSpinCampaign;
import common.AbstractFreeSpinsApi;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.exception.FreeSpinsExceededTimeException;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.model.freespins.CancelFreeSpinsRequest;
import common.model.freespins.CancelFreeSpinsResponse;
import common.model.freespins.CancelPlayerFreeSpinRequest;
import common.model.freespins.CancelPlayerFreeSpinResponse;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.CreateFreeSpinsResponse;
import common.model.freespins.FreeSpinCampaignStatus;
import common.model.freespins.FreeSpinIdempotencyStatus;
import common.model.freespins.FreeSpinsInfo;
import common.model.freespins.FreeSpinsStatus;
import common.utils.FreeSpinsUtils;
import gaming.booming.BoomingCurrencySpec;
import gaming.booming.Mappers;
import gaming.booming.api.freespins.model.BetOptions;
import gaming.booming.api.freespins.model.CancelFreeSpinsBoomingRequest;
import gaming.booming.api.freespins.model.CreateFreeSpinsBoomingResponse;
import io.micrometer.core.instrument.MeterRegistry;

public class DefaultFreeSpinsBoomingService extends AbstractFreeSpinsApi {
    private final BoomingFreeSpinsApi boomingApi;
    private final CommonObjectMapper mapper;

    public DefaultFreeSpinsBoomingService(
            AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            BoomingFreeSpinsApi boomingFreeSpinsApi,
            UamFreeSpinsClient uamFreeSpinsClient,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager,
            CommonObjectMapper mapper) {
        super(props, cloud, meterRegistry, uamFreeSpinsClient, spannerTemplate, cacheManager);
        this.boomingApi = boomingFreeSpinsApi;
        this.mapper = mapper;
    }

    @Override
    public CreateFreeSpinsResponse createFreeSpins(OperatorSpec operator, CreateFreeSpinsRequest request) throws Exception {
        var createFreeSpinsResponseOpt = getCreateFreeSpinsResponseByRequestId(request.getBonusCode(), operator, provider(), request.getRequestId());
        if (createFreeSpinsResponseOpt.isPresent()) {
            var response = createFreeSpinsResponseOpt.get();
            validateBonusCodeFromRequestId(request, response.getBonusCode());
            return response;
        }

        FreeSpinCampaign freeSpinCampaign;
        var campaignOpt = readFreeSpinsCampaign(operator, provider(), request.getBonusCode());

        if (campaignOpt.isPresent()) {
            freeSpinCampaign = campaignOpt.get();
        } else {
            insertFreeSpinCampaign(operator, provider(), request, FreeSpinCampaignStatus.CREATED);
            freeSpinCampaign = FreeSpinsUtils.toFreeSpinCampaign(request);
        }

        PlainServiceInfo psi = AggregatorWildcardUPSs.findCurrencyScopedIrgsServiceInfo(freeSpinCampaign.getCurrency().toLowerCase(),
                cloud,
                operator.code(),
                provider().code());

        var requestProvider = Mappers.toCreateFreeSpinsRequest(freeSpinCampaign, buildGameBetOptions(freeSpinCampaign), request.getPlayerIdList());

        CreateFreeSpinsBoomingResponse response;
        try {
            response = boomingApi.createFreeSpins(psi, requestProvider);
        } catch (UnexpectedHttpClientException e) {
            return CreateFreeSpinsResponse.buildError(e.getCode(), e.getMessage());
        }
        var freeSpinsResponseList = Mappers.toFreeSpinsInfoList(request, response.getCampaignId());

        var accountFreeSpinsList = prepareAccountFreeSpinsList(operator, provider(), request, freeSpinsResponseList, FreeSpinsStatus.CREATED);
        saveFreeSpinsWithIdempotencyKey(operator, provider(), request.getRequestId(), accountFreeSpinsList, FreeSpinIdempotencyStatus.FINISHED);

        return new CreateFreeSpinsResponse(request.getBonusCode(), freeSpinsResponseList, freeSpinCampaign.getCurrency());
    }

    @Override
    public CancelPlayerFreeSpinResponse cancelPlayerFreeSpin(OperatorSpec operator, CancelPlayerFreeSpinRequest request) throws Exception {
        var req = CancelFreeSpinsRequest.builder()
                .campaign(request.getCampaign())
                .currency(request.getCurrency())
                .cancelBonusCode(request.isCancelBonusCode())
                .requestId(request.getRequestId())
                .freeSpinsList(List.of(request.getFreeSpin()))
                .build();
        var response = cancelFreeSpinsBatching(operator, req);
        return new CancelPlayerFreeSpinResponse(response.getBonusCode(), response.getFreeSpinsList().getFirst());
    }

    @Override
    public CancelFreeSpinsResponse cancelFreeSpinsBatching(OperatorSpec operator, CancelFreeSpinsRequest request) throws Exception {
        PlainServiceInfo psi = AggregatorWildcardUPSs.findCurrencyScopedIrgsServiceInfo(request.getCurrency().toLowerCase(),
                cloud,
                operator.code(),
                provider().code());
        List<FreeSpinsInfo> canceledFreeSpinsList = Collections.synchronizedList(Lists.newArrayList());

        var freeSpinCampaignOpt = readFreeSpinCampaign(request.getCampaign(), operator, provider());
        CancelFreeSpinsResponse response = new CancelFreeSpinsResponse();
        response.setBonusCode(request.getCampaign());
        if (freeSpinCampaignOpt.isEmpty()) {
            response.setFreeSpinsList(canceledFreeSpinsList);
            response.setCode(HttpStatus.SC_NOT_FOUND);
            response.setMessage(BONUS_CODE_NOT_FOUND_MSG);
            return response;
        }
        var uniqueFreeSpinsIdReq = buildCancelRequestWithUniqueFreeSpinsId(request);
        if (request.getFreeSpinsList().size() == 1) {
            var freeSpinInfo = removePlayerFromBonus(psi, request.getCurrency(), request.getFreeSpinsList().getFirst());
            if (!freeSpinInfo.isApplied()) {
                response.setFreeSpinsList(List.of(freeSpinInfo));
                return response;
            }
            canceledFreeSpinsList.add(freeSpinInfo);
        } else {
            try {
                cancelFreeSpinsBufferProcessing(uniqueFreeSpinsIdReq, canceledFreeSpinsList, psi);
            } catch (FreeSpinsExceededTimeException e) {
                logger.error(e.getMessage(), e);
                response.setCode(HttpStatus.SC_REQUEST_TOO_LONG);
                response.setMessage(e.getMessage());
            }
        }
        var allFreeSpins = getAllFreeSpinsByBonusCode(operator, uniqueFreeSpinsIdReq);

        var resultList = buildCancelResultList(request, canceledFreeSpinsList, allFreeSpins);
        response.setFreeSpinsList(resultList);
        return response;
    }

    @Override
    protected FreeSpinsInfo removePlayerFromBonus(PlainServiceInfo psi, String currency, FreeSpinsInfo freeSpin) {
        var freeSpinsInfoBuilder = FreeSpinsInfo.builder();

        try {
            boomingApi.cancelFreeSpins(psi,
                    freeSpin.getFreeSpinsId(), new CancelFreeSpinsBoomingRequest());
            freeSpinsInfoBuilder
                    .playerId(freeSpin.getPlayerId())
                    .freeSpinsId(freeSpin.getFreeSpinsId())
                    .isApplied(true);
        } catch (Exception e) {
            freeSpinsInfoBuilder
                    .playerId(freeSpin.getPlayerId())
                    .freeSpinsId(freeSpin.getFreeSpinsId())
                    .message(e.getMessage())
                    .isApplied(false);
        }
        return freeSpinsInfoBuilder.build();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.BOOMING;
    }

    private JsonNode buildGameBetOptions(FreeSpinCampaign freeSpinCampaign) {
        ObjectNode gameBetOption = mapper.createObjectNode();
        String currency = BoomingCurrencySpec.fromString(freeSpinCampaign.getCurrency()).code().toUpperCase();
        ArrayNode betOptionsList = mapper.valueToTree(List.of(new BetOptions(currency, freeSpinCampaign.getBetAmount())));
        gameBetOption.set(removePrefixFromGameId(FreeSpinsUtils.toFreeSpinsCampaignGameCode(freeSpinCampaign), ProviderSpec.BOOMING), betOptionsList);
        return gameBetOption;
    }

    static CancelFreeSpinsRequest buildCancelRequestWithUniqueFreeSpinsId(CancelFreeSpinsRequest request) {
        List<FreeSpinsInfo> uniqueFreeSpinsId = request.getFreeSpinsList().stream()
                .collect(Collectors.toMap(FreeSpinsInfo::getFreeSpinsId, freeSpinsInfo -> freeSpinsInfo, (x, y) -> x))
                .values().stream()
                .toList();
        return CancelFreeSpinsRequest.builder()
                .campaign(request.getCampaign())
                .currency(request.getCurrency())
                .cancelBonusCode(request.isCancelBonusCode())
                .requestId(request.getRequestId())
                .freeSpinsList(uniqueFreeSpinsId)
                .build();
    }

    private List<FreeSpinsInfo> buildCancelResultList(CancelFreeSpinsRequest request, List<FreeSpinsInfo> canceledFreeSpins,
            List<FreeSpinsInfo> allFreeSpins) {
        List<String> freeSpinsIdApplied = canceledFreeSpins.stream()
                .filter(FreeSpinsInfo::isApplied)
                .map(FreeSpinsInfo::getFreeSpinsId)
                .toList();
        Map<String, String> playerIdFreeSpinsId = request.getFreeSpinsList().stream()
                .collect(Collectors.toMap(FreeSpinsInfo::getPlayerId, FreeSpinsInfo::getFreeSpinsId, (x, y) -> x));

        List<FreeSpinsInfo> freeSpinResultList = new ArrayList<>();
        request.getFreeSpinsList().forEach(freeSpin -> freeSpinResultList.add(buildFreeSpinsInfo(freeSpin, canceledFreeSpins, freeSpinsIdApplied)));
        allFreeSpins.stream()
                .filter(fs -> !isFreeSpinExist(fs, playerIdFreeSpinsId))
                .forEach(fs -> freeSpinResultList.add(buildFreeSpinsInfo(fs, canceledFreeSpins, freeSpinsIdApplied)));
        return freeSpinResultList;
    }

    private FreeSpinsInfo buildFreeSpinsInfo(FreeSpinsInfo freeSpin, List<FreeSpinsInfo> canceledFreeSpins,
            List<String> freeSpinsIdApplied) {
        boolean isApplied = freeSpinsIdApplied.contains(freeSpin.getFreeSpinsId());
        String message = isApplied ? null : getCancelErrorMessage(canceledFreeSpins, freeSpin);
        return FreeSpinsInfo.builder()
                .playerId(freeSpin.getPlayerId())
                .freeSpinsId(freeSpin.getFreeSpinsId())
                .message(message)
                .isApplied(isApplied)
                .build();
    }

    private String getCancelErrorMessage(List<FreeSpinsInfo> canceledFreeSpins, FreeSpinsInfo freeSpin) {
        return canceledFreeSpins.stream()
                .filter(fs -> fs.getFreeSpinsId().equals(freeSpin.getFreeSpinsId()))
                .map(FreeSpinsInfo::getMessage)
                .findAny().orElse(null);
    }

    private List<FreeSpinsInfo> getAllFreeSpinsByBonusCode(OperatorSpec operator, CancelFreeSpinsRequest req) {
        var allFreeSpins = readFreeSpinsByBonusCode(req.getCampaign(), operator, provider());
        if (allFreeSpins.isEmpty()) {
            logger.warn("FreeSpinsId not found");
        }
        return allFreeSpins.stream()
                .filter(fs -> req.getFreeSpinsList().stream()
                        .anyMatch(freeSpinUnique -> freeSpinUnique.getFreeSpinsId().equals(fs.getFreeSpinsId())))
                .toList();
    }

    private boolean isFreeSpinExist(FreeSpinsInfo freeSpin, Map<String, String> playerIdFreeSpinsId) {
        return playerIdFreeSpinsId.containsKey(freeSpin.getPlayerId())
                && playerIdFreeSpinsId.get(freeSpin.getPlayerId()).equals(freeSpin.getFreeSpinsId());
    }
}
