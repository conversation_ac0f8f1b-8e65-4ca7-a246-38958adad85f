package gaming.booming.api.freespins;

import com.turbospaces.ups.PlainServiceInfo;

import gaming.booming.api.freespins.model.CancelFreeSpinsBoomingRequest;
import gaming.booming.api.freespins.model.CreateFreeSpinsBoomingRequest;
import gaming.booming.api.freespins.model.CreateFreeSpinsBoomingResponse;

public interface BoomingFreeSpinsApi {

    CreateFreeSpinsBoomingResponse createFreeSpins(PlainServiceInfo psi, CreateFreeSpinsBoomingRequest request) throws Exception;

    void cancelFreeSpins(PlainServiceInfo psi, String freeSpinsId, CancelFreeSpinsBoomingRequest request) throws Exception;
}
