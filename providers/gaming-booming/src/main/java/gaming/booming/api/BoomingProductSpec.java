package gaming.booming.api;

import static common.utils.BaseMapper.addPrefixToGameId;

import java.util.Objects;

import common.model.GameType;
import common.model.ProductSpec;
import common.model.ProviderSpec;

public class BoomingProductSpec implements ProductSpec {
    private final BoomingProduct payload;

    public BoomingProductSpec(BoomingProduct payload) {
        this.payload = Objects.requireNonNull(payload);
    }

    @Override
    public String code() {
        return addPrefixToGameId(payload.getGameId(), ProviderSpec.BOOMING);
    }

    @Override
    public String title() {
        return payload.getName();
    }

    @Override
    public String type() {
        return GameType.SLOTS.code();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.BOOMING;
    }

    @Override
    public String supplier() {
        return provider().code() + "-games";
    }

    @Override
    public String toString() {
        return payload.toString();
    }
}
