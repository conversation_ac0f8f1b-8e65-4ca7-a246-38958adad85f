package gaming.booming.api.launch;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BoomingGameLaunchRequest {
    @JsonProperty("game_id")
    private String gameId;
    private String balance;
    private String currency;
    private String locale;
    @JsonProperty("player_id")
    private String playerId;
    @JsonProperty("player_ip")
    private String playerIp;
    private String callback;
    @JsonProperty("rollback_callback")
    private String rollbackUrl;
    private String variant;
    @JsonProperty("operator_launch_data")
    private String operatorLaunchData;
    private boolean demo;
}
