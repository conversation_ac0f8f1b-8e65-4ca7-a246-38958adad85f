package gaming.booming.api;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.http.HttpStatus;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.google.common.collect.ImmutableList;
import com.google.common.net.HttpHeaders;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.okhttp.UnexpectedHttpClientException;
import com.turbospaces.ups.PlainServiceInfo;

import common.AbstractGamingProviderApi;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.ProductUpdater;
import common.model.ProviderSpec;
import common.model.launch.GameLaunchInfo;
import common.model.launch.GameLaunchResponse;
import gaming.booming.BoomingCurrencySpec;
import gaming.booming.Mappers;
import gaming.booming.api.freespins.BoomingFreeSpinsApi;
import gaming.booming.api.freespins.model.CancelFreeSpinsBoomingRequest;
import gaming.booming.api.freespins.model.CreateFreeSpinsBoomingRequest;
import gaming.booming.api.freespins.model.CreateFreeSpinsBoomingResponse;
import gaming.booming.api.launch.BoomingGameLaunchResponse;
import gaming.booming.utils.SignatureUtilsBooming;
import jakarta.inject.Inject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Service
public class DefaultBoomingApi extends AbstractGamingProviderApi<BoomingProductSpec> implements BoomingApi, BoomingGameLaunchApi, BoomingFreeSpinsApi {
    private static final String PATH_GET_GAMES = "/v3/gamelist";
    private static final String PATH_FREE_SPINS = "/v3/campaigns";
    private static final String PATH_CANCEL_FREE_SPINS = "/v3/campaigns/%s/stop";
    private static final String X_BG_API_KEY = "X-Bg-Api-Key";
    private static final String X_BG_NONCE = "X-Bg-Nonce";
    private static final String X_BG_SIGNATURE = "X-Bg-Signature";
    private static final String CONTENT_TYPE_FREE_SPINS = "application/vnd.api+json";

    @Inject
    public DefaultBoomingApi(AggregatorServerProperties props, DynamicCloud cloud, OkHttpClient httpClient, CommonObjectMapper mapper) {
        super(props, cloud, httpClient, mapper);
    }

    @Override
    public Collection<BoomingProductSpec> getProducts(String operator, PlainServiceInfo si) throws Exception {
        var get = getGamesHttpRequest(si, PATH_GET_GAMES);
        String resp = sendRequest(get);
        return new ArrayList<>(toGames(resp));
    }

    @Override
    public GameLaunchResponse launch(GameLaunchInfo gameLaunchInfo, String operator) throws Exception {
        var siByCurrency = AggregatorWildcardUPSs.<PlainServiceInfo> findCurrencyScopedIrgsServiceInfo(
                BoomingCurrencySpec.fromString(gameLaunchInfo.getCurrency()).shortCode().toLowerCase(), cloud, operator, ProviderSpec.BOOMING.code());

        var baseCallbackUrl = Mappers.toBaseCallbackUrl(gameLaunchInfo.getCallbackHost(), operator, provider().code());
        var boomingRequest = mapper.writeValueAsString(Mappers.toBoomingGameLaunchRequest(gameLaunchInfo, baseCallbackUrl));

        var post = createPostRequest(siByCurrency, boomingRequest,
                PATH_LAUNCH, ContentType.APPLICATION_JSON.getMimeType());
        try {
            String resp = sendLaunchRequest(post);
            var launchResponse = mapper.readValue(resp, BoomingGameLaunchResponse.class);
            return Mappers.toGameLaunchResponse(launchResponse);
        } catch (Exception error) {
            logger.warn(error.getMessage(), error);
            return Mappers.toGameLaunchErrorResponse(toErrorCode(error), error.getMessage());
        }
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.BOOMING;
    }

    @Override
    public CreateFreeSpinsBoomingResponse createFreeSpins(PlainServiceInfo si, CreateFreeSpinsBoomingRequest request) throws Exception {
        var post = createPostRequest(si, mapper.writeValueAsString(request), PATH_FREE_SPINS, CONTENT_TYPE_FREE_SPINS);

        String response = sendRequest(post);
        return mapper.readValue(response, CreateFreeSpinsBoomingResponse.class);
    }

    @Override
    public void cancelFreeSpins(PlainServiceInfo si, String freeSpinsId, CancelFreeSpinsBoomingRequest request) throws Exception {
        String path = String.format(PATH_CANCEL_FREE_SPINS, freeSpinsId);
        var post = createPostRequest(si, mapper.writeValueAsString(request), path, CONTENT_TYPE_FREE_SPINS);

        sendRequest(post);
    }

    private Request createPostRequest(PlainServiceInfo si, String body, String path, String contentType) throws Exception {
        var httpRequest = new Request.Builder()
                .post(RequestBody.create(body, getMediaType(contentType)))
                .url(newBuilder(si, path).build().toString())
                .addHeader(HttpHeaders.ACCEPT, contentType)
                .addHeader(HttpHeaders.CONTENT_TYPE, contentType);
        signRequest(si, path, body, httpRequest);

        return httpRequest.build();
    }

    private void signRequest(PlainServiceInfo si, String path, String body, Request.Builder httpRequest) throws Exception {
        String secret = SignatureUtilsBooming.getSecretFromPsi(si);
        String apiKey = si.getPassword();
        String nonce = String.valueOf(Instant.now().toEpochMilli());
        logger.info("sending request with nonce: {}", nonce);
        httpRequest.addHeader(DefaultBoomingApi.X_BG_API_KEY, apiKey);
        httpRequest.addHeader(DefaultBoomingApi.X_BG_NONCE, nonce);
        httpRequest.addHeader(DefaultBoomingApi.X_BG_SIGNATURE, SignatureUtilsBooming.generateSignature(secret, nonce, body, path));
    }

    public List<BoomingProductSpec> toGames(String payload) throws Exception {
        ImmutableList.Builder<BoomingProductSpec> items = ImmutableList.builder();
        ArrayNode tree = (ArrayNode) mapper.readTree(payload);
        Iterator<JsonNode> it = tree.elements();
        while (it.hasNext()) {
            JsonNode node = it.next();
            if (!(node instanceof NullNode)) {
                BoomingProduct item = mapper.readValue(node.traverse(), BoomingProduct.class);

                items.add(new BoomingProductSpec(item));
            }
        }
        return items.build();
    }

    private Request getGamesHttpRequest(PlainServiceInfo si, String path) throws Exception {
        var httpRequest = new Request.Builder()
                .get()
                .url(newBuilder(si, path).build().toString())
                .addHeader(HttpHeaders.ACCEPT, ContentType.APPLICATION_JSON.getMimeType());
        signRequest(si, PATH_GET_GAMES, "", httpRequest);

        return httpRequest.build();
    }

    private String sendLaunchRequest(Request request) throws IOException {
        logger.info("Request to {}: {}, body: {}", provider(), request, request.body());

        MDC.put(MdcTags.MDC_PATH, request.url().uri().getPath());
        try (Response httpResp = httpClient.newCall(request).execute()) {
            String response = getResponse(httpResp.body());
            logger.info("Response from {}: response: {}, entity: {}", provider(), httpResp, response);
            if (httpResp.isSuccessful()
                    || HttpStatus.SC_BAD_REQUEST == httpResp.code()
                    || HttpStatus.SC_UNPROCESSABLE_ENTITY == httpResp.code()) {
                return response;
            }
            logger.warn("Unexpected error from {}: response: {}, entity: {}", provider(), httpResp, response);
            throw new UnexpectedHttpClientException(httpResp, response);
        } finally {
            MDC.remove(MdcTags.MDC_PATH);
        }
    }

    private static URIBuilder newBuilder(PlainServiceInfo serviceInfo, String path) throws Exception {
        URIBuilder builder = new URIBuilder();
        builder.setScheme(serviceInfo.getScheme());
        if (serviceInfo.getPort() > 0) {
            builder.setPort(serviceInfo.getPort());
        }
        builder.setHost(serviceInfo.getHost());
        builder.setPath(path);
        ProductUpdater.addProxyParam(builder, serviceInfo);

        return builder;
    }
}
