package gaming.booming.api;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

import org.jctools.maps.NonBlockingHashMap;
import org.springframework.stereotype.Service;

@Service
public class DefaultNonceService implements NonceService {
    private final Map<String, AtomicLong> nonceMap;

    public DefaultNonceService() {
        this.nonceMap = new NonBlockingHashMap<>();
    }

    @Override
    public long getValidNonce(String operator) {
        if (nonceMap.containsKey(operator)) {
            return nonceMap.get(operator).incrementAndGet();
        }
        long nonce = System.currentTimeMillis();
        nonceMap.put(operator, new AtomicLong(nonce));
        return nonce;
    }
}
