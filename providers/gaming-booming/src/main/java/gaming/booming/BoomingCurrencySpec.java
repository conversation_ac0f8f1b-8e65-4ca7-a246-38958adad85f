package gaming.booming;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum BoomingCurrencySpec {
    SSC("SC"),
    GCC("GC");

    private final String code;
    private final String shortCode;

    BoomingCurrencySpec(String shortCode) {
        this.shortCode = shortCode;
        this.code = name().toLowerCase().intern();
    }

    public String code() {
        return code;
    }

    public String shortCode() {
        return shortCode;
    }

    @JsonCreator
    public static BoomingCurrencySpec fromString(String name) {
        for (BoomingCurrencySpec currency : BoomingCurrencySpec.values()) {
            if (currency.code.equalsIgnoreCase(name) || currency.shortCode.equalsIgnoreCase(name)) {
                return currency;
            }
        }
        throw new IllegalArgumentException("unknown booming currency: " + name);
    }
}
