package gaming.booming;

import static common.model.ProviderSpec.BOOMING;

import java.util.Optional;

import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;

import com.google.cloud.spring.data.spanner.core.SpannerReadOptions;
import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.util.concurrent.FluentFuture;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.http.UnexpectedJaxrsException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.model.Account;
import aggregator.model.WalletSessionWithTransactions;
import aggregator.repo.DefaultSpannerTransactionalCallback;
import aggregator.wallet.patrianna.UamSeamlessWalletClient;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionResponseInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import common.AbstractGamingService;
import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.WalletSessionUtils;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedFunction0;
import io.vavr.Tuple2;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultBoomingGamingService extends AbstractGamingService implements BoomingGamingService {
    @Inject
    public DefaultBoomingGamingService(
            AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamSeamlessWalletClient client,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager) {
        super(props, cloud, meterRegistry, spannerTemplate, cacheManager, client, BOOMING);
    }

    @Override
    public FluentFuture<WalletSessionResponseInfo> submit(
            WalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<WalletSessionResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);

            WalletSessionResponseInfo resp = client.submit(psi, request, traceId, routingKey);
            upsertUamWalletSession(spannerTemplate, cacheManager, request, operator, provider());
            return resp;
        }));
    }

    @Override
    public FluentFuture<AccountBalanceResponseInfo> refund(
            CancelWalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit((CheckedFunction0<AccountBalanceResponseInfo>) () -> {
            PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
            String accountId = request.getIdentity().accountId();

            var tuple = spannerTemplate.performReadOnlyTransaction(new DefaultSpannerTransactionalCallback<>(cacheManager,
                    callback -> {
                        var brandRepo = callback.brandRepo();
                        var walletSessionRepo = callback.walletSessionRepo();
                        var isAccountExist = brandRepo.accountExists(operator.code(), accountId);
                        Optional<WalletSessionWithTransactions> optWs = Optional.empty();
                        Optional<Account> optAcc = Optional.empty();
                        if (isAccountExist) {
                            var account = brandRepo.getOrCreateAccount(operator.code(), accountId);
                            optAcc = Optional.of(account);
                            optWs = walletSessionRepo.walletSessionWithTransactions(
                                    account.getHash(), provider().code(), request.getSessionId(), request.currency());
                        }
                        return new Tuple2<>(optAcc, optWs);

                    }), new SpannerReadOptions());

            var optAcc = tuple._1();
            Optional<WalletSessionWithTransactions> optWs = tuple._2();

            if (optAcc.isEmpty() || optWs.isEmpty()) {
                return getBalance(psi, request.getIdentity(), traceId, routingKey);
            }

            if (WalletSessionUtils.isRefundOriginTypeEnabled(props, operator)) {
                WalletSessionUtils.addOriginTypeToRefundTransactions(request.getTransactions(), optWs.get());
            }

            try {
                CancelWalletSessionResponseInfo resp = client.refund(psi, request, traceId, routingKey);
                upsertRefundedUamWalletSession(spannerTemplate, request, optAcc.get(), optWs.get(), provider());

                return new AccountBalanceResponseInfo(resp);
            } catch (UnexpectedJaxrsException err) {
                log.warn(err.getMessage(), err);

                return processException(err, psi, request.getIdentity(), traceId, routingKey);
            }
        }));
    }

    private ProviderSpec provider() {
        return ProviderSpec.BOOMING;
    }
}
