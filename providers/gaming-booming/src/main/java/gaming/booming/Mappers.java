package gaming.booming;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.apache.http.client.utils.URIBuilder;

import com.fasterxml.jackson.databind.JsonNode;

import aggregator.model.FreeSpinCampaign;
import common.model.ProviderSpec;
import common.model.freespins.CreateFreeSpinsRequest;
import common.model.freespins.FreeSpinsInfo;
import common.model.launch.GameLaunchInfo;
import common.model.launch.GameLaunchResponse;
import common.utils.BaseMapper;
import gaming.booming.api.freespins.model.CreateFreeSpinsBoomingRequest;
import gaming.booming.api.launch.BoomingGameLaunchRequest;
import gaming.booming.api.launch.BoomingGameLaunchResponse;
import gaming.booming.b2b.BoomingEndpoint;

public interface Mappers {
    String HARDCODED_PLAYER_IP = "***************";
    DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
    ZoneId ZONE_ID = ZoneId.of("UTC");

    static String toDebitReference(String txId) {
        return String.format("%s%s", txId, "-dt");
    }

    static String toCreditReference(String txId) {
        return String.format("%s%s", txId, "-ct");
    }

    static BoomingGameLaunchRequest toBoomingGameLaunchRequest(GameLaunchInfo gameLaunchInfo, String uri) {
        BoomingGameLaunchRequest boomReq = new BoomingGameLaunchRequest();
        boomReq.setGameId(BaseMapper.removePrefixFromGameId(gameLaunchInfo.getGameId(), ProviderSpec.BOOMING));
        boomReq.setPlayerId(gameLaunchInfo.getPlayerId());
        boomReq.setCurrency(BoomingCurrencySpec.fromString(gameLaunchInfo.getCurrency()).code().toUpperCase());
        boomReq.setLocale(BaseMapper.toProviderLocale(gameLaunchInfo.getLocale(), ProviderSpec.BOOMING));
        boomReq.setBalance(gameLaunchInfo.getBalance());
        boomReq.setVariant(gameLaunchInfo.getType());
        boomReq.setCallback(toCallbackUri(uri));
        boomReq.setRollbackUrl(toRollbackUri(uri));
        boomReq.setOperatorLaunchData(BaseMapper.removePrefixFromGameId(gameLaunchInfo.getGameId(), ProviderSpec.BOOMING));
        boomReq.setPlayerIp(HARDCODED_PLAYER_IP);
        boomReq.setDemo(gameLaunchInfo.isDemo());

        return boomReq;
    }

    static GameLaunchResponse toGameLaunchResponse(BoomingGameLaunchResponse responseBoom) {
        GameLaunchResponse response = new GameLaunchResponse();
        response.setGameUrl(responseBoom.getGameUrl());
        response.setSessionId(responseBoom.getSessionId());
        response.setError(responseBoom.getCode());
        response.setMsg(responseBoom.getMessage());
        return response;
    }

    static GameLaunchResponse toGameLaunchErrorResponse(int code, String message) {
        GameLaunchResponse response = new GameLaunchResponse();
        response.setError(Integer.toString(code));
        response.setMsg(message);
        return response;
    }

    static String toHttpsCallbackUri(URI uri) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(uri);
        uriBuilder.setScheme("https");
        return uriBuilder.build().toString();
    }

    static String toCallbackUri(String uri) {
        return uri + BoomingEndpoint.CALLBACK_PATH;
    }

    static String toRollbackUri(String uri) {
        return uri + BoomingEndpoint.ROLLBACK_PATH;
    }

    static String toBaseCallbackUrl(String host, String operator, String provider) throws URISyntaxException {
        return new URIBuilder()
                .setScheme("https")
                .setHost(host)
                .setPath(String.format("/v1/%s/%s", operator, provider))
                .build().toString();
    }

    static String toProviderDate(LocalDateTime localDateTime) {
        return localDateTime.atZone(ZONE_ID).format(FORMATTER);
    }

    static CreateFreeSpinsBoomingRequest toCreateFreeSpinsRequest(
            FreeSpinCampaign freeSpinCampaign,
            JsonNode gameBetOptions,
            List<String> playerIds) {
        String endDate = toProviderDate(freeSpinCampaign.getExpirationDate());
        return CreateFreeSpinsBoomingRequest.builder()
                .name(freeSpinCampaign.getBonusCode())
                .campaignOperatorData(freeSpinCampaign.getBonusCode())
                .startDate(toProviderDate(freeSpinCampaign.getStartDate()))
                .endDate(endDate)
                .spinsExpireDate(endDate)
                .openToAll(false)
                .totalSpins(freeSpinCampaign.getSpins())
                .playerList(String.join(",", playerIds))
                .gameBetOptions(gameBetOptions)
                .build();
    }

    static List<FreeSpinsInfo> toFreeSpinsInfoList(CreateFreeSpinsRequest request, String freeSpinsId) {
        return request.getPlayerIdList().stream()
                .map(playerId -> FreeSpinsInfo.builder()
                        .playerId(playerId)
                        .freeSpinsId(freeSpinsId)
                        .isApplied(true)
                        .build())
                .toList();
    }
}
