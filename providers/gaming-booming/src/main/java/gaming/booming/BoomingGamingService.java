package gaming.booming;

import com.google.common.util.concurrent.FluentFuture;

import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.CancelWalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import common.GamingService;
import common.model.OperatorSpec;
import io.netty.util.AsciiString;

public interface BoomingGamingService extends GamingService {

    FluentFuture<WalletSessionResponseInfo> submit(
            WalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<AccountBalanceResponseInfo> refund(
            CancelWalletSessionRequestInfo request,
            OperatorSpec operator,
            AsciiString routingKey);
}
