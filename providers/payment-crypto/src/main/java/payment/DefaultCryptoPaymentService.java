package payment;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import api.v1.ApplicationException;
import api.v1.Code;
import lombok.extern.slf4j.Slf4j;
import payment.context.order.ConfirmPaymentOrderContext;
import payment.context.order.CreatePaymentOrderContext;
import payment.context.order.DeclinePaymentOrderContext;
import payment.context.order.RefreshPaymentOrderContext;
import payment.context.redeem.ConfirmWithdrawContext;
import payment.context.redeem.CreateWithdrawContext;
import payment.context.redeem.DeclineWithdrawContext;
import payment.context.redeem.OnConfirmWithdrawContext;
import payment.context.redeem.WithdrawContext;
import payment.crypto.CryptoCurrency;
import payment.crypto.CryptoNetwork;
import payment.crypto.CryptoProviderReceiver;
import payment.crypto.MinimalCryptoTxDetails;
import payment.type.PurchaseProviderSpec;
import payment.type.RedeemProviderSpec;
import payment.withdraws.CryptoWithdrawMethodInfo;
import uam.api.v1.CryptoWithdrawMethod;
import uam.api.v1.PaymentProvider;
import uam.api.v1.RedeemProvider;

@Slf4j
public class DefaultCryptoPaymentService implements PaymentService, WithdrawService {

    private final CryptoHelperService cryptoHelperService;
    private final List<PurchaseProviderSpec> providers;
    private final CryptoPropertiesFragment cryptoProperties;

    public DefaultCryptoPaymentService(
            CryptoHelperService cryptoHelperService,
            CryptoPropertiesFragment cryptoProperties
    ) {
        this.cryptoHelperService = cryptoHelperService;
        this.cryptoProperties = cryptoProperties;
        this.providers = supportedProviders();
    }
    @Override
    public List<RedeemProviderSpec> getRedeemProviders() {
        return List.of(RedeemProviderSpec.CRYPTO, RedeemProviderSpec.ORBITAL);
    }

    @Override
    public void onConfirm(OnConfirmWithdrawContext ctx) throws Exception {
        var receiver = cryptoHelperService.resolveByProviderRequired(ctx.provider());
        receiver.onConfirm(ctx);
    }

    @Override
    public void confirm(ConfirmWithdrawContext ctx) throws Throwable {
        var receiver = resolveReceiverAndSetAsProvider(ctx);
        receiver.confirm(ctx);
    }

    @Override
    public Optional<String> resolveMethodCode(CreateWithdrawContext ctx) throws Exception {
        var methodInfo = resolveMethodInfo(ctx);
        return Optional.of(methodInfo.toCode());
    }

    @Override
    public void create(CreateWithdrawContext ctx) throws Throwable {

        var methodInfo = resolveMethodInfo(ctx);
        if (ctx.methodCodeOpt().isEmpty() || !Objects.equals(methodInfo.toCode(), ctx.methodCodeOpt().get())) {
            ctx.getMethodCtx().setMethodCode(methodInfo.toCode());
            ctx.getMethodCtx().setWithdrawMethodInfo(methodInfo);
        }
        ctx.getMethodCtx().rememberMethod();

        //store minimal available data
        ctx.setCryptoPurchaseDetails(MinimalCryptoTxDetails.builder()
                .status("new")
                .network(methodInfo.getNetwork())
                .targetCurrency(methodInfo.getCurrency().name())
                .build()
        );
        var r = resolveReceiverAndSetAsProvider(ctx);
        r.onCreate(ctx);
    }

    @Override
    public void decline(DeclineWithdrawContext ctx) throws Exception {
        //provider could be set or not
        var receiverMaybe = cryptoHelperService.resolveByProvider(ctx.provider());
        if (receiverMaybe.isPresent()) {
            receiverMaybe.get().decline(ctx);
        }
        ctx.getMethodCtx().forgetWithdrawMethod();
    }

    @Override
    public List<PurchaseProviderSpec> getPurchaseProviders() {
        return providers;
    }

    @Override
    public void beforeCreate(CreatePaymentOrderContext ctx) throws Throwable {
        setOrderProvider(ctx);

        // data for GetPaymentOrderRequest while payment in progress
        var receiver = cryptoHelperService.resolveByProviderRequired(ctx.provider());
        var paymentData = receiver.resolvePaymentInfo(ctx);

        CryptoCurrency currency = paymentData.getKey();
        CryptoNetwork network = paymentData.getRight();
        ctx.setCryptoPurchaseDetails(MinimalCryptoTxDetails.builder()
                .status("new")
                .network(network)
                .sourceCurrency(currency.name())
                .build()
        );
    }

    @Override
    public void onDecline(DeclinePaymentOrderContext ctx) throws Throwable {
        var receiver = cryptoHelperService.resolveByProviderRequired(ctx.provider());
        receiver.onDecline(ctx);
    }

    @Override
    public void create(CreatePaymentOrderContext ctx) throws Throwable {
       var receiver = cryptoHelperService.resolveByProviderRequired(ctx.provider());
        receiver.create(ctx);
    }

    @Override
    public void refresh(RefreshPaymentOrderContext ctx) throws Throwable {
       var receiver = cryptoHelperService.resolveByProviderRequired(ctx.provider());
       receiver.refresh(ctx);
    }

    @Override
    public void confirm(ConfirmPaymentOrderContext ctx) throws Throwable {
        var receiver = cryptoHelperService.resolveByProviderRequired(ctx.provider());
        receiver.confirm(ctx);
        ctx.getPmCtx().rememberPaymentMethod();
    }

    private CryptoProviderReceiver resolveReceiverAndSetAsProvider(WithdrawContext ctx) throws Throwable {
        var receiver = cryptoHelperService.resolveWithdrawReceiverWithProviderChecking(ctx)
                .orElseThrow(ApplicationException.orElseThrow(cryptoProperties.NO_RECEIVERS_MESSAGE.get(), Code.ERR_PAYMENT));
        if (RedeemProvider.CRYPTO_REDEEM == ctx.provider()) {
            ctx.setProvider(receiver.getRedeemProvider());
        }
        return receiver;
    }

    private CryptoWithdrawMethodInfo resolveMethodInfo(CreateWithdrawContext ctx) throws Exception {
        CryptoWithdrawMethod method = (CryptoWithdrawMethod) ctx.withdrawMethod();

        var methodInfo = new CryptoWithdrawMethodInfo();
        methodInfo.setWallet(method.getWallet());
        methodInfo.setCurrency(CryptoCurrency.valueOf(method.getCurrency().name().toUpperCase()));
        methodInfo.setNetwork(CryptoNetwork.valueOf(method.getNetwork().name().toUpperCase()));
        validateMethod(methodInfo);
        return methodInfo;
    }

    private static List<PurchaseProviderSpec> supportedProviders() {
        return Arrays.stream(PurchaseProviderSpec.values())
                .filter(PurchaseProviderSpec::isCrypto)
                .toList();
    }

    private void setOrderProvider(CreatePaymentOrderContext ctx) throws Throwable {
        if (PaymentProvider.CRYPTO == ctx.originalProvider()) {
            ctx.setProvider(cryptoHelperService.resolvePurchaseReceiverWithProviderChecking(ctx)
                    .orElseThrow(ApplicationException.orElseThrow(cryptoProperties.NO_RECEIVERS_MESSAGE.get(), Code.ERR_PAYMENT)).getProvider());
        }
    }

    private void validateMethod(CryptoWithdrawMethodInfo method) throws ApplicationException {
        if (!cryptoHelperService.isWalletValid(method.getWallet(), method.getNetwork())) {
            log.error("Invalid wallet [{}] fro network [{}]", method.getWallet(), method.getNetwork());
            throw ApplicationException.of("Invalid wallet", Code.ERR_BAD_REQUEST);
        }
    }
}
