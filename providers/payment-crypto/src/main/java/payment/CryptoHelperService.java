package payment;

import java.util.Optional;
import java.util.regex.Pattern;

import payment.context.order.CreatePaymentOrderContext;
import payment.context.order.GenericAuthorizedPaymentContext;
import payment.context.redeem.WithdrawContext;
import payment.crypto.CryptoCurrencyInfo;
import payment.crypto.CryptoNetwork;
import payment.crypto.CryptoProviderReceiver;
import payment.model.ProviderTypeSpec;
import uam.api.v1.PaymentProvider;
import uam.api.v1.RedeemProvider;

public interface CryptoHelperService {

    String walletValidationRegexp(CryptoNetwork cryptoNetwork);

    default boolean isWalletValid(String wallet, CryptoNetwork cryptoNetwork) {
        return Pattern.compile(walletValidationRegexp(cryptoNetwork)).matcher(wallet).matches();
    }
    CryptoCurrencyInfo getMergedCurrencyInfo(GenericAuthorizedPaymentContext ctx, ProviderTypeSpec typeSpec) throws Throwable;

    Optional<CryptoProviderReceiver> resolvePurchaseReceiverWithProviderChecking(CreatePaymentOrderContext ctx) throws Throwable;
    Optional<CryptoProviderReceiver> resolveWithdrawReceiverWithProviderChecking(WithdrawContext ctx) throws Throwable;

    default CryptoProviderReceiver resolveByProviderRequired(PaymentProvider provider) {
        return resolveByProvider(provider).orElseThrow(() -> new IllegalStateException("Can't resolve crypto receiver for provider: "+ provider));
    }
    Optional<CryptoProviderReceiver> resolveByProvider(PaymentProvider provider);

    default CryptoProviderReceiver resolveByProviderRequired(RedeemProvider provider) {
        return resolveByProvider(provider).orElseThrow(() -> new IllegalStateException("Can't resolve crypto receiver for provider: " + provider));
    }
    Optional<CryptoProviderReceiver> resolveByProvider(RedeemProvider provider);
}
