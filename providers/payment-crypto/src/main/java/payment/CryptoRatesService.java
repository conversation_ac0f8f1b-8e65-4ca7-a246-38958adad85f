package payment;

import java.math.BigDecimal;

import payment.crypto.CryptoCurrency;

public interface CryptoRatesService {

    //USD->currency
    BigDecimal getBaseToCurrencyRate(CryptoCurrency currency) throws Throwable;
    //USD amount
    BigDecimal getBaseAmount(CryptoCurrency currency, BigDecimal amount) throws Throwable;

    BigDecimal getCryptoAmount(CryptoCurrency currency, BigDecimal usdAmount) throws Throwable;
}
