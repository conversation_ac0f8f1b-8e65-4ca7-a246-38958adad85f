package payment;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.cfg.FragmentProperties;
import com.turbospaces.cfg.ScopedProperty;

public final class CryptoPropertiesFragment implements FragmentProperties {

    public CryptoPropertiesFragment(DynamicPropertyFactory pf) {
        CRYPTO_WALLET_REGEXP = pf.getScoped("crypto.wallet_regexp", String.class, "^.+$");
        NO_RECEIVERS_MESSAGE = pf.get("crypto.no_receivers_error_message", String.class)
                .orElse( "Crypto payment method is currently not available due to an ongoing maintenance on provider's end. Will be available again in 1-2 hours.");
    }

    public final ScopedProperty<String> CRYPTO_WALLET_REGEXP;
    public final Property<String> NO_RECEIVERS_MESSAGE;
}
