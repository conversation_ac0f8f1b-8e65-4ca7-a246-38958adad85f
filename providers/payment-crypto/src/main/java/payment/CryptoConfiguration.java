package payment;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.turbospaces.cfg.ApplicationConfig;

import payment.orbital.OrbitalConfiguration;

@Configuration
@Import(OrbitalConfiguration.class)
public class CryptoConfiguration {

    @Bean
    public PaymentService cryptoPaymentService(CryptoHelperService receiverResolver, CryptoPropertiesFragment cryptoProperties) {
        return new DefaultCryptoPaymentService(receiverResolver, cryptoProperties);
    }

    @Bean
    public WithdrawService cryptoWithdrawalService(CryptoHelperService receiverResolver, CryptoPropertiesFragment cryptoProperties) {
        return new DefaultCryptoPaymentService(receiverResolver, cryptoProperties);
    }
}
