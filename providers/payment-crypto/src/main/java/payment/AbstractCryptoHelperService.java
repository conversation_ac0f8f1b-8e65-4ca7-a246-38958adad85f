package payment;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.tuple.Pair;

import lombok.extern.slf4j.Slf4j;
import payment.context.order.CreatePaymentOrderContext;
import payment.context.order.GenericAuthorizedPaymentContext;
import payment.context.order.GenericPaymentContext;
import payment.context.redeem.WithdrawContext;
import payment.crypto.CryptoCurrency;
import payment.crypto.CryptoCurrencyInfo;
import payment.crypto.CryptoNetwork;
import payment.crypto.CryptoProviderReceiver;
import payment.model.ProviderTypeSpec;
import payment.withdraws.CryptoWithdrawMethodInfo;
import uam.api.v1.PaymentProvider;
import uam.api.v1.RedeemProvider;

@Slf4j
public abstract class AbstractCryptoHelperService implements CryptoHelperService {

    private final CryptoRatesService cryptoRatesService;
    private final CryptoPropertiesFragment cryptoPropertiesFragment;
    private final List<CryptoProviderReceiver> receivers;

    protected AbstractCryptoHelperService(CryptoRatesService cryptoRatesService,
                                          CryptoPropertiesFragment cryptoPropertiesFragment,
                                          List<CryptoProviderReceiver> receivers) {
        this.cryptoRatesService = cryptoRatesService;
        this.cryptoPropertiesFragment = cryptoPropertiesFragment;
        this.receivers = receivers;
    }

    @Override
    public String walletValidationRegexp(CryptoNetwork cryptoNetwork) {
        return cryptoPropertiesFragment.CRYPTO_WALLET_REGEXP.get(cryptoNetwork.name());
    }

    @Override
    public CryptoCurrencyInfo getMergedCurrencyInfo(GenericAuthorizedPaymentContext ctx, ProviderTypeSpec typeSpec) throws Throwable {
        CryptoCurrencyInfo result = new CryptoCurrencyInfo();
        for (CryptoProviderReceiver receiver : receivers) {
            if (providerIsSupported(ctx, receiver.getProvider(), typeSpec)) {
                result = result.mergeWith(receiver.currencyInfo(ctx, typeSpec));
            }
        }
        return result;
    }

    @Override
    public Optional<CryptoProviderReceiver> resolvePurchaseReceiverWithProviderChecking(CreatePaymentOrderContext ctx) throws Throwable {

        for (CryptoProviderReceiver receiver : receivers) {
            log.debug("Checking purchase receiver {}", receiver.getProvider());
            if (providerIsSupported(ctx, receiver.getProvider(), ProviderTypeSpec.PURCHASE)) {
                Pair<CryptoCurrency, CryptoNetwork> paymentInfo = receiver.resolvePaymentInfo(ctx);
                BigDecimal cryptoAmount = cryptoRatesService.getCryptoAmount(paymentInfo.getKey(), ctx.baseAmount());
                if (receiver.supportsPurchase(ctx, paymentInfo.getKey(), paymentInfo.getValue(), cryptoAmount)) {
                    return Optional.of(receiver);
                }
                log.debug("Receiver [{}] not supported for currency [{}], network [{}], amount [{}]", receiver.getProvider(), paymentInfo.getKey(), paymentInfo.getValue(), cryptoAmount);
            }
            log.debug("Purchase provider [{}] not supported for brand [{}] and country [{}]", receiver.getProvider(), ctx.brand(), ctx.country());
        }
        return Optional.empty();
    }

    @Override
    public Optional<CryptoProviderReceiver> resolveWithdrawReceiverWithProviderChecking(WithdrawContext ctx) throws Throwable {

        for (CryptoProviderReceiver receiver : receivers) {
            log.debug("Checking withdraw receiver {}", receiver.getProvider());
            if (providerIsSupported(ctx, receiver.getProvider(), ProviderTypeSpec.WITHDRAW)) {
                CryptoWithdrawMethodInfo methodInfo = (CryptoWithdrawMethodInfo) ctx.getMethodCtx().getInfo();
                Pair<CryptoCurrency, CryptoNetwork> paymentInfo = Pair.of(methodInfo.getCurrency(), methodInfo.getNetwork());
                BigDecimal cryptoAmount = cryptoRatesService.getCryptoAmount(paymentInfo.getKey(), ctx.getBaseAmount());
                if (receiver.supportsRedeem(ctx, paymentInfo.getKey(), paymentInfo.getValue(), cryptoAmount)) {
                    return Optional.of(receiver);
                }
                log.debug("Receiver [{}] not supported for currency [{}], network [{}], amount [{}]", receiver.getProvider(), paymentInfo.getKey(), paymentInfo.getValue(), cryptoAmount);
            }
            log.debug("Withdraw provider [{}] not supported for brand [{}] and country [{}]", receiver.getProvider(), ctx.brand(), ctx.country());
        }
        return Optional.empty();
    }

    @Override
    public Optional<CryptoProviderReceiver> resolveByProvider(PaymentProvider provider) {
        return receivers.stream().filter(receiver -> receiver.getProvider() == provider).findFirst();
    }

    @Override
    public Optional<CryptoProviderReceiver> resolveByProvider(RedeemProvider provider) {
        return receivers.stream().filter(receiver -> receiver.getRedeemProvider() == provider).findFirst();
    }

    public abstract boolean providerIsSupported(GenericPaymentContext ctx, PaymentProvider pp, ProviderTypeSpec typeSpec) throws Throwable;

}
