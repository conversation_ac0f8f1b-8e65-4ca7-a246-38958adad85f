<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.patrianna.uam</groupId>
        <artifactId>crm-providers-parent</artifactId>
        <version>25.06.2-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>crm-social-google</artifactId>
    <name>crm ::: ${project.artifactId}</name>
    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turbospaces.boot</groupId>
            <artifactId>bootstrap-api-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.patrianna.uam</groupId>
            <artifactId>crm-server-api-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.api-ads</groupId>
            <artifactId>google-ads</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>grpc-netty-shaded</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.google.api-ads</groupId>
            <artifactId>google-ads-stubs-v18</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.google.api</groupId>
            <artifactId>gax</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.api.grpc</groupId>
            <artifactId>proto-google-common-protos</artifactId>
        </dependency>
    </dependencies>
</project>
