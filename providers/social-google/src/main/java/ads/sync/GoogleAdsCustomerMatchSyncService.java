package ads.sync;

import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicReference;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cloud.DynamicCloud;
import org.springframework.util.CollectionUtils;

import com.google.ads.googleads.lib.GoogleAdsClient;
import com.google.ads.googleads.v18.common.CustomerMatchUserListMetadata;
import com.google.ads.googleads.v18.enums.OfflineUserDataJobTypeEnum;
import com.google.ads.googleads.v18.errors.GoogleAdsFailure;
import com.google.ads.googleads.v18.resources.OfflineUserDataJob;
import com.google.ads.googleads.v18.services.AddOfflineUserDataJobOperationsRequest;
import com.google.ads.googleads.v18.services.AddOfflineUserDataJobOperationsResponse;
import com.google.ads.googleads.v18.services.GoogleAdsRow;
import com.google.ads.googleads.v18.services.OfflineUserDataJobOperation;
import com.google.ads.googleads.v18.services.OfflineUserDataJobServiceClient;
import com.google.ads.googleads.v18.utils.ErrorUtils;
import com.turbospaces.ups.RawServiceInfo;
import com.turbospaces.ups.ServiceInfoSubscription;

import io.vavr.CheckedConsumer;
import uam.CrmProto;

public class GoogleAdsCustomerMatchSyncService implements InitializingBean, DisposableBean {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final DynamicCloud cloud;
    private ServiceInfoSubscription<RawServiceInfo> si;
    private final AtomicReference<String> customerId = new AtomicReference<>();
    private final AtomicReference<GoogleAdsClient> googleAdsClient = new AtomicReference<>();
    protected MessageDigest digest;

    public GoogleAdsCustomerMatchSyncService(DynamicCloud cloud) {
        this.cloud = Objects.requireNonNull(cloud);
    }
    @Override
    public void destroy() throws Exception {
        si.dispose();
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        si = ServiceInfoSubscription.of(cloud, CrmProto.UPS_GOOGLE_ADS, new CheckedConsumer<RawServiceInfo>() {
            @Override
            public void accept(RawServiceInfo psi) throws Throwable {
                Properties props = new Properties();
                try (Reader reader = psi.openBufferedStream()) {
                    props.load(reader);
                    googleAdsClient.set(GoogleAdsClient.newBuilder().fromProperties(props).build());
                    customerId.set(props.getProperty("api.googleads.loginCustomerId"));
                }
            }
        });
        digest = MessageDigest.getInstance("SHA-256");
    }

    public void runOfflineUserDataJob(String currentJobName) {
        // Issues an asynchronous request to run the offline user data job for executing all added operations.
        try (var client = googleJobClient()) {
            client.runOfflineUserDataJobAsync(currentJobName);
        }
    }
    private OfflineUserDataJobServiceClient googleJobClient() {
        return googleAdsClient.get().getLatestVersion().createOfflineUserDataJobServiceClient();
    }

    public String createOfflineUserDataJob(String listName) {
        try (var client = googleJobClient()) {
            // Creates a new offline user data job.
            var userList = CustomerMatchUserListMetadata.newBuilder().setUserList(listName);
            var job = OfflineUserDataJob.newBuilder()
                    .setType(OfflineUserDataJobTypeEnum.OfflineUserDataJobType.CUSTOMER_MATCH_WITH_ATTRIBUTES)
                    .setCustomerMatchUserListMetadata(userList)
                    .build();
            var response = client.createOfflineUserDataJob(customerId.get(), job);
            String resourceName = response.getResourceName();
            logger.info("Created an offline user data job with resource name: {}", resourceName);
            return resourceName;
        }
    }

    public int addNewBatchToJobClient(String jobName, List<OfflineUserDataJobOperation> operations) {
        // recommend adding up to 10,000 identifiers in a single call
        var request = AddOfflineUserDataJobOperationsRequest.newBuilder()
                .setResourceName(jobName)
                .setEnablePartialFailure(true)
                .addAllOperations(operations)
                .build();

        // send adding request
        var response = sendAddOfflineUserDataJobOperationsRequest(request);

        // Prints the status message if any partial failure error is returned.
        if (response.hasPartialFailureError()) {
            GoogleAdsFailure googleAdsFailure = ErrorUtils.getInstance().getGoogleAdsFailure(response.getPartialFailureError());

            logFailedOperations(operations, googleAdsFailure);

            throw new IllegalStateException(response.getPartialFailureError().getMessage());
        }
        logger.info("Successfully added {} operations to the offline user data job: {}", operations.size(), jobName);
        return operations.size();
    }

    /**
     * Returns the result of normalizing and then hashing the string using the provided digest.
     * Private customer data must be hashed during upload, as described at
     * https://support.google.com/google-ads/answer/7474263.
     *
     * @param s the string to normalize and hash.
     */
    public String normalizeAndHash(String s) {
        // Normalizes by removing leading and trailing whitespace and converting all characters to
        // lower case.
        String normalized = s.trim().toLowerCase();
        // Hashes the normalized string using the hashing algorithm.
        byte[] hash = digest.digest(normalized.getBytes(StandardCharsets.UTF_8));
        StringBuilder result = new StringBuilder();
        for (byte b : hash) {
            result.append(String.format("%02x", b));
        }

        return result.toString();
    }

    public boolean isRunning(String jobName) {
        if (jobName == null) {
            return false;
        }
        try (var client = googleAdsClient.get().getLatestVersion().createGoogleAdsServiceClient()) {
            String query = String.format("""
                SELECT offline_user_data_job.resource_name, \
                offline_user_data_job.status \
                FROM offline_user_data_job \
                WHERE offline_user_data_job.resource_name = '%s'""", jobName);

            // Issues the query and gets the GoogleAdsRow containing the job from the response.
            var search = client.search(customerId.get(), query);
            for (GoogleAdsRow row : search.iterateAll()) {
                var job = row.getOfflineUserDataJob();

                var status = job.getStatus();
                switch (status) {
                    case FAILED -> {
                        var reason = job.getFailureReason().name();
                        logger.error("Job {} failed, reason {}", jobName, reason);
                        throw new RuntimeException(reason);
                    }
                    case RUNNING -> {
                        logger.info("Job {} is still running", jobName);
                        return true;
                    }
                    case PENDING, SUCCESS -> logger.info("Job {} is {} ", jobName, status);
                    default -> throw new UnsupportedOperationException();
                }
            }

            return false;
        }
    }
    private AddOfflineUserDataJobOperationsResponse sendAddOfflineUserDataJobOperationsRequest(AddOfflineUserDataJobOperationsRequest request) {
        try (var client = googleJobClient()) {
            return client.addOfflineUserDataJobOperations(request);
        }
    }
    private void logFailedOperations(List<OfflineUserDataJobOperation> operations, GoogleAdsFailure failure) {
        for (var e : failure.getErrorsList()) {
            var elements = e.getLocation().getFieldPathElementsList();
            if (!CollectionUtils.isEmpty(elements)) {
                int index = elements.get(0).getIndex();
                logger.warn("GoogleAds failed operation: {}", operations.get(index));
            }
        }
    }

}
