package gaming.netent;

import com.google.common.util.concurrent.FluentFuture;

import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import common.GamingService;
import common.model.OperatorSpec;
import io.netty.util.AsciiString;
import jakarta.ws.rs.core.MultivaluedMap;

public interface NetentGamingService extends GamingService {

    FluentFuture<AccountBalanceResponseInfo> getBalance(
            AccountBalanceRequestInfo req,
            MultivaluedMap<String, String> requestHeaders,
            OperatorSpec operator,
            AsciiString routingKey);

    FluentFuture<WalletSessionResponseInfo> submit(
            WalletSessionRequestInfo request,
            MultivaluedMap<String, String> requestHeaders,
            OperatorSpec operator,
            AsciiString routingKey);

}
