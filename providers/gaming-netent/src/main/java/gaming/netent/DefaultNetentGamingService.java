package gaming.netent;

import static common.model.ProviderSpec.NETENT;

import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.google.cloud.spring.data.spanner.core.SpannerTemplate;
import com.google.common.util.concurrent.FluentFuture;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.wallet.patrianna.UamSeamlessWalletClient;
import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import common.AbstractGamingService;
import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.util.AsciiString;
import io.vavr.CheckedFunction0;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.MultivaluedMap;

@Service
public class DefaultNetentGamingService extends AbstractGamingService implements NetentGamingService {
    @Inject
    public DefaultNetentGamingService(
            AggregatorServerProperties props,
            DynamicCloud cloud,
            MeterRegistry meterRegistry,
            UamSeamlessWalletClient client,
            SpannerTemplate spannerTemplate,
            CacheManager cacheManager) {
        super(props, cloud, meterRegistry, spannerTemplate, cacheManager, client, NETENT);
    }

    @Override
    public FluentFuture<AccountBalanceResponseInfo> getBalance(
            AccountBalanceRequestInfo request,
            MultivaluedMap<String, String> requestHeaders,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit(new CheckedFunction0<AccountBalanceResponseInfo>() {
            @Override
            public AccountBalanceResponseInfo apply() throws Throwable {
                PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
                return client.balance(psi, request, traceId, routingKey);
            }
        }));
    }

    @Override
    public FluentFuture<WalletSessionResponseInfo> submit(
            WalletSessionRequestInfo request,
            MultivaluedMap<String, String> requestHeaders,
            OperatorSpec operator,
            AsciiString routingKey) {
        var si = operator.igpUps();
        var traceId = MDC.get(MdcTags.MDC_TRACE_ID);
        return FluentFuture.from(executor.submit(new CheckedFunction0<WalletSessionResponseInfo>() {
            @Override
            public WalletSessionResponseInfo apply() throws Throwable {
                PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);

                WalletSessionResponseInfo resp = client.submit(psi, request, traceId, routingKey);
                upsertUamWalletSession(spannerTemplate, cacheManager, request, operator, ProviderSpec.NETENT);
                return resp;
            }
        }));
    }
}
