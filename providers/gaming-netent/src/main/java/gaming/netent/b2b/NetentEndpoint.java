package gaming.netent.b2b;

import com.turbospaces.annotations.ApiEndpoint;
import com.turbospaces.http.HttpProto;

import common.AggregatorServerProperties;
import common.model.OperatorSpec;
import io.netty.channel.ChannelHandlerContext;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.UriInfo;

@Path(HttpProto.V1 + "/{operator}/netent")
@ApiEndpoint(ipWhitelistKey = AggregatorServerProperties.IP_WHITELIST_NETENT_KEY)
public interface NetentEndpoint {
    String X_API_KEY = "X-Api-Key";
    String BALANCE_PATH = "/balance";
    String GAME_ROUND_PATH = "/gameRound";

    @GET
    @Path(BALANCE_PATH)
    @Produces(MediaType.APPLICATION_JSON)
    void balance(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @QueryParam("playerId") String playerId) throws Exception;

    @POST
    @Path(GAME_ROUND_PATH)
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    void gameRound(@Suspended AsyncResponse async,
            @Context UriInfo uriInfo,
            @Context ChannelHandlerContext ctx,
            @Context HttpHeaders headers,
            @PathParam("operator") OperatorSpec operator,
            @Valid @NotNull GameRoundRequest req) throws Exception;
}
