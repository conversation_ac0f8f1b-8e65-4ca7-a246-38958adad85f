package gaming.netent.b2b;

import java.math.BigDecimal;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BalanceResponse extends AbstractNetentResponse {
    private String currency;
    private BigDecimal balance;

    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("currency", currency).append("balance", balance).append("errorCode", errorCode).append("errorMessage", errorMessage).toString();
    }
}
