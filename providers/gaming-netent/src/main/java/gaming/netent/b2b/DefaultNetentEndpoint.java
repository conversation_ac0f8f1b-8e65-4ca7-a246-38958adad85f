package gaming.netent.b2b;

import java.util.ArrayList;
import java.util.Objects;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.MDC;
import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.UriBasedServiceInfo;
import org.springframework.stereotype.Component;

import com.google.common.collect.Iterables;
import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.MoreExecutors;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.http.UnexpectedHttpStatusException;
import com.turbospaces.mdc.MdcTags;
import com.turbospaces.ups.PlainServiceInfo;
import com.turbospaces.ups.UPSs;

import aggregator.wallet.patrianna.types.AccountBalanceRequestInfo;
import aggregator.wallet.patrianna.types.AccountBalanceResponseInfo;
import aggregator.wallet.patrianna.types.AtInfo;
import aggregator.wallet.patrianna.types.IdentityByAccountIdInfo;
import aggregator.wallet.patrianna.types.IdentityInfo;
import aggregator.wallet.patrianna.types.WalletSessionRequestInfo;
import aggregator.wallet.patrianna.types.WalletSessionResponseInfo;
import aggregator.wallet.patrianna.types.WalletTransactionInfo;
import common.AbstractApiEndpoint;
import common.AggregatorServerProperties;
import common.AggregatorWildcardUPSs;
import common.flatten.AccountRoutingCurrencyFlatten;
import common.limiter.ErrorRateLimiter;
import common.model.OperatorSpec;
import common.model.ProviderSpec;
import common.utils.HiddenModeUtils;
import common.utils.WalletSessionUtils;
import gaming.netent.NetentGamingService;
import io.netty.channel.ChannelHandlerContext;
import io.netty.util.AsciiString;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;

@Component
public class DefaultNetentEndpoint extends AbstractApiEndpoint implements NetentEndpoint {

    private final NetentGamingService netentGamingService;
    private final ErrorRateLimiter errorRateLimiter;

    public DefaultNetentEndpoint(AggregatorServerProperties props,
            DynamicCloud cloud,
            NetentGamingService netentGamingService,
            ErrorRateLimiter errorRateLimiter) {
        super(props, cloud);
        this.netentGamingService = Objects.requireNonNull(netentGamingService);
        this.errorRateLimiter = Objects.requireNonNull(errorRateLimiter);
    }

    @Override
    public void balance(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            String playerId) throws Exception {

        checkAuthHeader(headers, operator);

        var flatten = AccountRoutingCurrencyFlatten.standard().readExternal(playerId);
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());
        var identityByAccountIdInfo = new IdentityByAccountIdInfo(flatten.getAccountId(), remoteIp(ctx, headers));
        var balanceRequest = new AccountBalanceRequestInfo(new IdentityInfo(identityByAccountIdInfo));

        netentGamingService.getBalance(
                balanceRequest,
                headers.getRequestHeaders(),
                operator,
                AsciiString.cached(flatten.getRoutingKey())).addCallback(new FutureCallback<>() {
                    @Override
                    public void onSuccess(AccountBalanceResponseInfo sresp) {
                        try {
                            for (var balance : sresp.getBalances()) {
                                if (balance.getCurrency().equals(flatten.getCurrency())) {
                                    var resp = new BalanceResponse(flatten.getCurrency(), balance.getBalance());
                                    MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                    async.resume(Response.status(Response.Status.OK).entity(resp).build());
                                }
                            }
                        } catch (Throwable t) {
                            onFailure(t);
                        }
                    }

                    @Override
                    public void onFailure(Throwable throwable) {
                        MDC.remove(MdcTags.MDC_ROUTING_KEY);
                        async.resume(toErrorBalanceResponse(logAndMapToErrorCode(throwable), throwable.getMessage()));
                    }
                }, MoreExecutors.directExecutor());
    }

    @Override
    public void gameRound(AsyncResponse async,
            UriInfo uriInfo,
            ChannelHandlerContext ctx,
            HttpHeaders headers,
            OperatorSpec operator,
            GameRoundRequest req) throws Exception {

        checkAuthHeader(headers, operator);

        var playerId = req.getPlayerId();
        var betAmount = req.getBetAmount().abs();
        var winAmount = req.getWinAmount().abs();
        var gameRoundId = req.getGameRoundId().toString();
        var gameId = req.getGameId();

        var flatten = AccountRoutingCurrencyFlatten.standard();
        flatten.readExternal(playerId);
        MDC.put(MdcTags.MDC_ROUTING_KEY, flatten.getRoutingKey());

        var identityByAccountIdInfo = new IdentityByAccountIdInfo(flatten.getAccountId(), remoteIp(ctx, headers));

        WalletSessionRequestInfo sreqb = new WalletSessionRequestInfo();
        sreqb.setTransactions(new ArrayList<>());
        sreqb.setIdentity(new IdentityInfo(identityByAccountIdInfo));
        sreqb.setSessionId(gameRoundId);
        sreqb.setSource(determineProviderSpec().code());
        sreqb.getTransactions().add(
                WalletSessionUtils.toTransactionInfo(WalletTransactionInfo.TYPE_DEBIT, flatten.getCurrency(), gameRoundId, betAmount));
        sreqb.getTransactions().add(
                WalletSessionUtils.toTransactionInfo(WalletTransactionInfo.TYPE_CREDIT, flatten.getCurrency(), gameRoundId, winAmount));
        if (StringUtils.isNotEmpty(gameId)) {
            sreqb.setProduct(gameId);
        }
        sreqb.setAt(new AtInfo(PlatformUtil.toLocalUTCDate()));
        sreqb.setComplete(true);

        netentGamingService.submit(
                sreqb,
                headers.getRequestHeaders(),
                operator,
                AsciiString.cached(flatten.getRoutingKey())).addCallback(
                        new FutureCallback<WalletSessionResponseInfo>() {
                            @Override
                            public void onSuccess(WalletSessionResponseInfo result) {
                                try {
                                    for (var balance : result.getBalances()) {
                                        if (balance.getCurrency().equals(flatten.getCurrency())) {
                                            var resp = new GameRoundResponse(flatten.getCurrency(), balance.getBalance());
                                            MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                            async.resume(Response.status(Response.Status.OK).entity(resp).build());
                                        }
                                    }
                                } catch (Throwable t) {
                                    onFailure(t);
                                }
                            }

                            @Override
                            public void onFailure(Throwable throwable) {
                                MDC.remove(MdcTags.MDC_ROUTING_KEY);
                                async.resume(toErrorBalanceResponse(logAndMapToErrorCode(throwable), throwable.getMessage()));
                            }
                        }, MoreExecutors.directExecutor());
    }

    private ProviderSpec determineProviderSpec() {
        return HiddenModeUtils.determineProviderByHiddenMode(props, ProviderSpec.NETENT);
    }

    private static Response toErrorBalanceResponse(int code, String message) {
        BalanceResponse response = new BalanceResponse();
        response.errorCode = code;
        response.errorMessage = message;

        return Response.status(Response.Status.OK).entity(response).build();
    }

    private int logAndMapToErrorCode(Throwable origin) {
        int codeToReturn = AbstractNetentResponse.NOT_FOUND;

        Throwable cause = getRootCause(origin);

        if (cause instanceof UnexpectedHttpStatusException ex) {
            MDC.put(MdcTags.MDC_ERROR_CODE, String.valueOf(ex.getCode()));

            codeToReturn = switch (ex.getCode()) {
                case HttpStatus.SC_PAYMENT_REQUIRED -> AbstractNetentResponse.INSUFFICIENT_BALANCE;
                case HttpStatus.SC_CONFLICT -> AbstractNetentResponse.DUPLICATE_GAME_ROUND_ID;
                // expected HttpStatus.SC_UNAUTHORIZED, HttpStatus.SC_FORBIDDEN, HttpStatus.SC_NOT_FOUND
                // will be also here
                default -> AbstractNetentResponse.NOT_FOUND;
            };
        }

        // ~ log to sentry if necessary
        if (codeToReturn == AbstractNetentResponse.NOT_FOUND) {
            logError(cause, errorRateLimiter.check(cause));
        } else {
            logWarn(cause);
        }

        return codeToReturn;
    }

    void checkAuthHeader(HttpHeaders headers, OperatorSpec operator) {
        String si = String.format("%s-%s-%s", AggregatorWildcardUPSs.IRGS_PREFIX, operator.code(), ProviderSpec.NETENT.code());
        PlainServiceInfo psi = UPSs.findRequiredServiceInfoByName(cloud, si);
        Function<PlainServiceInfo, String> expected = UriBasedServiceInfo::getPassword;
        String xApiKeyTheir = Iterables.getOnlyElement(headers.getRequestHeader(X_API_KEY));
        String xApiKeyOur = expected.apply(psi);
        malformed(xApiKeyOur, xApiKeyTheir);
    }
}
