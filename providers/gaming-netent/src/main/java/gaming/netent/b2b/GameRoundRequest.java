package gaming.netent.b2b;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GameRoundRequest {
    @NotEmpty
    private String playerId;

    @NotNull
    private Long gameRoundId;

    @NotEmpty
    private String gameId;

    @NotNull
    private BigDecimal betAmount;

    @NotNull
    private BigDecimal winAmount;

    private Date gameRoundStartDate;

    private Date gameRoundEndDate;

    @Override
    public String toString() {
        ToStringBuilder toString = new ToStringBuilder(this, ToStringStyle.NO_CLASS_NAME_STYLE);
        return toString.append("playerId", playerId)
                .append("gameRoundId", gameRoundId)
                .append("gameId", gameId)
                .append("betAmount", betAmount)
                .append("winAmount", winAmount)
                .append("gameRoundStartDate", gameRoundStartDate)
                .append("gameRoundEndDate", gameRoundEndDate)
                .toString();
    }
}
