package gaming.netent.b2b;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class AbstractNetentResponse {
    public static final int NOT_FOUND = 0;
    public static final int INSUFFICIENT_BALANCE = 1;
    public static final int DUPLICATE_GAME_ROUND_ID = 2;

    protected Integer errorCode;
    protected String errorMessage;
}
