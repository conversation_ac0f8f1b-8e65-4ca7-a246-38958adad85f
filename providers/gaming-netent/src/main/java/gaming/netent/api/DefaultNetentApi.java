package gaming.netent.api;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.apache.http.client.utils.URIBuilder;
import org.springframework.cloud.DynamicCloud;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.NullNode;
import com.google.common.collect.ImmutableList;
import com.google.common.net.HttpHeaders;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.ups.PlainServiceInfo;

import common.AbstractGamingProviderApi;
import common.AggregatorServerProperties;
import common.ProductUpdater;
import common.model.ProviderSpec;
import jakarta.inject.Inject;
import okhttp3.OkHttpClient;
import okhttp3.Request;

@Service
public class DefaultNetentApi extends AbstractGamingProviderApi<NetentProductSpec> implements NetentApi {

    @Inject
    public DefaultNetentApi(AggregatorServerProperties props, DynamicCloud cloud, OkHttpClient httpClient, CommonObjectMapper mapper) {
        super(props, cloud, httpClient, mapper);
    }

    private static URIBuilder newBuilder(PlainServiceInfo serviceInfo, String path) throws Exception {
        URIBuilder builder = new URIBuilder();
        builder.setScheme(serviceInfo.getScheme());
        if (serviceInfo.getPort() > 0) {
            builder.setPort(serviceInfo.getPort());
        }
        builder.setHost(serviceInfo.getHost());
        builder.setPath("/netent-social/v1/" + serviceInfo.getUserName() + "/" + path);
        ProductUpdater.addProxyParam(builder, serviceInfo);

        return builder;
    }

    @Override
    public Collection<NetentProductSpec> getProducts(String operator, PlainServiceInfo si) throws Exception {
        Request get = getGamesHttpRequest(si);

        String text = sendRequest(get);
        return new ArrayList<>(toGames(text));
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.NETENT;
    }

    public List<NetentProductSpec> toGames(String payload) throws Exception {
        ImmutableList.Builder<NetentProductSpec> items = ImmutableList.builder();
        ArrayNode tree = (ArrayNode) mapper.readTree(payload);
        Iterator<JsonNode> it = tree.elements();
        while (it.hasNext()) {
            JsonNode node = it.next();
            if (!(node instanceof NullNode)) {
                NetentProduct item = mapper.readValue(node.traverse(), NetentProduct.class);
                logger.debug(node.toString());

                items.add(new NetentProductSpec(item));
            }
        }
        return items.build();
    }

    public Request getGamesHttpRequest(PlainServiceInfo si) throws Exception {
        return new Request.Builder()
                .get()
                .url(newBuilder(si, "games").build().toString())
                .addHeader(HttpHeaders.ACCEPT, "application/json")
                .addHeader("X-Api-Key", si.getPassword())
                .build();
    }
}
