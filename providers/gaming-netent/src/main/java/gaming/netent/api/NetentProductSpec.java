package gaming.netent.api;

import java.util.Objects;

import common.model.GameType;
import common.model.ProductSpec;
import common.model.ProviderSpec;

public class NetentProductSpec implements ProductSpec {
    private final NetentProduct payload;

    public NetentProductSpec(NetentProduct payload) {
        this.payload = Objects.requireNonNull(payload);
    }

    @Override
    public String code() {
        return payload.getGameId();
    }

    @Override
    public String title() {
        return payload.getGameName();
    }

    @Override
    public String type() {
        return GameType.SLOTS.code();
    }

    @Override
    public ProviderSpec provider() {
        return ProviderSpec.NETENT;
    }

    @Override
    public String supplier() {
        return provider().code();
    }

    @Override
    public String toString() {
        return payload.toString();
    }
}
