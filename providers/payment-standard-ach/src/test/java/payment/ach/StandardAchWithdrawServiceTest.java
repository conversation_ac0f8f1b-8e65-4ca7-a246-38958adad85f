package payment.ach;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.mockito.Mockito.*;

import java.util.Optional;

import payment.ach.receiver.StandardAchReceiver;
import payment.context.redeem.ConfirmWithdrawContext;
import payment.type.RedeemProviderSpec;

class StandardAchWithdrawServiceTest {
    private StandardAchWithdrawRoutingService routingService;
    private StandardAchWithdrawService service;

    @BeforeEach
    void setUp() {
        routingService = mock(StandardAchWithdrawRoutingService.class);
        service = new StandardAchWithdrawService(routingService);
    }

    @Test
    void testConfirmWithValidReceiverNotRoutableErrorCode() throws Throwable {
        ConfirmWithdrawContext ctx = mock(ConfirmWithdrawContext.class);
        when(ctx.getLockedByAgent()).thenReturn("other");
        when(ctx.getPreConfirmedBy()).thenReturn("system");
        when(ctx.providerCode()).thenReturn(RedeemProviderSpec.PAYNEARME_ACH.name());

        StandardAchReceiver receiver = mock(StandardAchReceiver.class);
        StandardAchConfirmWithdrawMethodContext routingCtx = mock(StandardAchConfirmWithdrawMethodContext.class);

        when(routingService.getReceiver(ctx)).thenReturn(Optional.of(receiver));
        when(receiver.getRedeemProvider()).thenReturn(RedeemProviderSpec.PAYNEARME_ACH);
        when(routingService.resolveContext(ctx, "paynearme_ach")).thenReturn(Optional.of(routingCtx));
        when(routingCtx.isTokenShouldBeSaved()).thenReturn(true);

        service.confirm(ctx);

        verify(ctx).setManualInterventionNeeded();
        verify(ctx).setAndThrowError(any(), contains("System agent attempted to confirm a withdraw method locked by another agent, but the error code is not eligible for retry routing"), eq(payment.util.WithdrawErrorType.PRE_CONFIRM), any(), isNull());
    }

    @Test
    void testConfirmWithValidReceiver() throws Throwable {
        ConfirmWithdrawContext ctx = mock(ConfirmWithdrawContext.class);
        when(ctx.getLockedByAgent()).thenReturn("system");
        when(ctx.getPreConfirmedBy()).thenReturn("system");
        when(ctx.providerCode()).thenReturn(RedeemProviderSpec.PAYNEARME_ACH.name());

        StandardAchReceiver receiver = mock(StandardAchReceiver.class);
        StandardAchConfirmWithdrawMethodContext routingCtx = mock(StandardAchConfirmWithdrawMethodContext.class);

        when(routingService.getReceiver(ctx)).thenReturn(Optional.of(receiver));
        when(receiver.getRedeemProvider()).thenReturn(RedeemProviderSpec.PAYNEARME_ACH);
        when(routingService.resolveContext(ctx, "paynearme_ach")).thenReturn(Optional.of(routingCtx));
        when(routingCtx.isTokenShouldBeSaved()).thenReturn(true);

        service.confirm(ctx);

        verify(receiver).confirm(routingCtx);
        verify(routingService).saveAchProviderToken(routingCtx);
    }

    @Test
    void testConfirmEmptyResolvedContext() throws Throwable {
        ConfirmWithdrawContext ctx = mock(ConfirmWithdrawContext.class);
        when(ctx.getLockedByAgent()).thenReturn("system");
        when(ctx.getPreConfirmedBy()).thenReturn("system");
        when(ctx.providerCode()).thenReturn(RedeemProviderSpec.PAYNEARME_ACH.name());

        StandardAchReceiver receiver = mock(StandardAchReceiver.class);
        when(routingService.getReceiver(ctx)).thenReturn(Optional.of(receiver));
        when(receiver.getRedeemProvider()).thenReturn(RedeemProviderSpec.PAYNEARME_ACH);
        when(routingService.resolveContext(ctx, "paynearme_ach")).thenReturn(Optional.empty());

        service.confirm(ctx);

        verify(ctx).setAndThrowError(any(), contains("Provider not active anymore"), eq(payment.util.WithdrawErrorType.PRE_CONFIRM), any(), isNull());
    }
}