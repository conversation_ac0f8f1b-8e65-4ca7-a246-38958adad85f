package payment.ach.receiver;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.ach.StandardAchConfirmWithdrawMethodContext;
import payment.type.RedeemProviderSpec;

@Slf4j
@RequiredArgsConstructor
public class AeropayStandardAchReceiver implements StandardAchReceiver {
    @Override
    public RedeemProviderSpec getRedeemProvider() {
        return RedeemProviderSpec.AEROPAY_ACH;
    }

    @Override
    public void confirm(StandardAchConfirmWithdrawMethodContext ctx) throws Exception {
        ctx.setTokenShouldBeSaved(false);
    }
}
