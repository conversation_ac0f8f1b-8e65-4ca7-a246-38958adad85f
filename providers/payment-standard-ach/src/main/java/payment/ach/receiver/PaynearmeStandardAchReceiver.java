package payment.ach.receiver;

import com.patrianna.uam.service.PaynearmeStandardAchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.ach.StandardAchConfirmWithdrawMethodContext;
import payment.type.RedeemProviderSpec;

@Slf4j
@RequiredArgsConstructor
public class PaynearmeStandardAchReceiver implements StandardAchReceiver {

    private final PaynearmeStandardAchService paynearmeStandardAchService;

    @Override
    public RedeemProviderSpec getRedeemProvider() {
        return RedeemProviderSpec.PAYNEARME_ACH;
    }

    @Override
    public void confirm(StandardAchConfirmWithdrawMethodContext ctx) throws Exception {
        paynearmeStandardAchService.payout(ctx);
    }
}
