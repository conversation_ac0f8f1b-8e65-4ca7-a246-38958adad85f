package payment.ach.receiver;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.safecharge.exception.SafechargeException;
import com.safecharge.model.MerchantDetails;
import com.safecharge.model.UrlDetails;
import com.safecharge.model.UserDetailsCashier;
import com.safecharge.model.UserPaymentOption;
import com.safecharge.request.AddUPOAPMRequest;
import com.safecharge.request.CreateUserRequest;
import com.safecharge.request.PayoutRequest;
import com.safecharge.response.AddUPOAPMResponse;
import com.safecharge.response.PayoutResponse;
import com.safecharge.response.SafechargeResponse;
import com.safecharge.response.UserResponse;
import com.safecharge.util.Constants;

import api.v1.ApplicationException;
import api.v1.Code;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.AchDetails;
import payment.ach.NuveiService;
import payment.ach.StandardAchConfirmWithdrawMethodContext;
import payment.nuvei.sdk.ApiExceptionUtils;
import payment.nuvei.sdk.NuveiPaymentService;
import payment.nuvei.sdk.TransactionStatus;
import payment.type.RedeemProviderSpec;
import payment.util.WithdrawErrorType;
import payment.withdraws.StandardAchWithdrawMethodInfo;

@Slf4j
@RequiredArgsConstructor
public class MazoomaStandardAchReceiver implements StandardAchReceiver {
    private final NuveiPaymentService nuveiPaymentService;
    private final NuveiService nuveiService;

    @Override
    public RedeemProviderSpec getRedeemProvider() {
        return RedeemProviderSpec.NUVEI_MAZOOMA_ACH;
    }

    @Override
    public void confirm(StandardAchConfirmWithdrawMethodContext ctx) throws Throwable {
        AchDetails achDetails = ctx.methodAchDetails();

        List<String> redeemUpoList =  new ArrayList<>();
        boolean userHasUpo = false;
        if (nuveiService.isPaymentMethodExist(ctx.accountId()) || ctx.providerToken().token() != null) {
            var userUpos = nuveiPaymentService.getUserUPOs(ctx, ctx.rk());
            userHasUpo = !userUpos.getPaymentMethods().isEmpty();
            redeemUpoList = nuveiPaymentService.getMatchedUserPaymentOptionIds(achDetails, userUpos);
        }

        if (redeemUpoList.isEmpty()) {
            if (!userHasUpo) {
                createUser(ctx);
            }
            addPaymentOption(ctx);
        } else {
            if (redeemUpoList.size() > 1) {
                log.warn("Found multiple UPOs, using first token in the list: userTokenId=[{}], upo=[{}], details=[{}]",
                        nuveiPaymentService.toUserTokenId(ctx.rk()), redeemUpoList, achDetails);
            }
            ctx.getMethodCtx().setMethodToken(redeemUpoList.iterator().next());
        }
        ctx.setTokenShouldBeSaved(true);
        if (StringUtils.isBlank(ctx.getExtCode())) {
            payout(ctx);
        } else {
            log.info("Payout has been skipped according to presence of extCode: [{}]", ctx.getExtCode());
        }
    }

    private void createUser(StandardAchConfirmWithdrawMethodContext ctx) throws ApplicationException, SafechargeException {
        CreateUserRequest req = prepareCreateUserRequest(ctx);
        UserResponse resp = (UserResponse) nuveiPaymentService.execute(ctx, req);
        if (isFailed(resp)) {
            var errorMsg = ApiExceptionUtils.logException(log, resp);
            throw ApplicationException.of(errorMsg, Code.ERR_PAYMENT);
        }
    }

    private void addPaymentOption(StandardAchConfirmWithdrawMethodContext ctx) throws SafechargeException, ApplicationException {
        AddUPOAPMRequest req = prepareAddUPOAPMRequest(ctx);
        AddUPOAPMResponse resp = (AddUPOAPMResponse) nuveiPaymentService.execute(ctx, req);
        if (isFailed(resp)) {
            throw ApplicationException.of("Could not add UPO %s", Code.ERR_PAYMENT, ApiExceptionUtils.logException(log, resp));
        }
        ctx.getMethodCtx().setMethodToken(resp.getUserPaymentOptionId().toString());
    }

    private void payout(StandardAchConfirmWithdrawMethodContext ctx) throws Exception {
        PayoutRequest pr = preparePayoutRequest(ctx);
        PayoutResponse payoutResponse = (PayoutResponse) nuveiPaymentService.execute(ctx, pr);

        if (isFailed(payoutResponse)) {
            var msg = ApiExceptionUtils.logException(log, payoutResponse);
            ApiExceptionUtils.buildError(ctx, payoutResponse, WithdrawErrorType.PRE_CONFIRM,
                    ApplicationException.of("Payout via mazooma has failed " + msg, Code.ERR_PAYMENT));
        }

        TransactionStatus txStatus = TransactionStatus.valueOf(payoutResponse.getTransactionStatus());

        if (TransactionStatus.isApproved(txStatus)) {
            ctx.setCode(payoutResponse.getExternalTransactionId());
            ctx.setExtCode(payoutResponse.getTransactionId());
        } else if (TransactionStatus.isPending(txStatus)) {
            var msg = ApiExceptionUtils.logPayoutException(log, payoutResponse);
            ApiExceptionUtils.buildError(ctx, payoutResponse, WithdrawErrorType.PRE_CONFIRM,
                    ApplicationException.of("Payout via mazooma has been pending " + msg, Code.ERR_PAYMENT));
        } else if (TransactionStatus.isDeclined(txStatus)) {
            var msg = ApiExceptionUtils.logPayoutException(log, payoutResponse);
            ApiExceptionUtils.buildError(ctx, payoutResponse, WithdrawErrorType.PRE_CONFIRM,
                    ApplicationException.of("Payout via mazooma has been declined " + msg, Code.ERR_PAYMENT));
        } else if (TransactionStatus.isError(txStatus)) {
            var msg = ApiExceptionUtils.logPayoutException(log, payoutResponse);
            ApiExceptionUtils.buildError(ctx, payoutResponse, WithdrawErrorType.PRE_CONFIRM,
                    ApplicationException.of("Payout via mazooma has failed " + msg, Code.ERR_PAYMENT));
        }
    }

    private static boolean isFailed(SafechargeResponse resp) {
        return !Constants.APIResponseStatus.SUCCESS.equals(resp.getStatus());
    }

    private CreateUserRequest prepareCreateUserRequest(StandardAchConfirmWithdrawMethodContext ctx) {
        return (CreateUserRequest) CreateUserRequest.builder()
                .userTokenId(getUserTokenId(ctx))
                .firstName(ctx.personalInfo().firstName())
                .lastName(ctx.personalInfo().lastName())
                .country(ctx.personalInfo().country())
                .city(ctx.personalInfo().city().orElse(null))
                .address(ctx.personalInfo().address().orElse(null))
                .state(ctx.personalInfo().state().orElse(null))
                .zip(ctx.personalInfo().zip().orElse(null))
                .email(ctx.personalInfo().email())
                .phone(ctx.getAccCtx().phone())
                .dateOfBirth(ctx.personalInfo().birthDay().map(LocalDate::toString).orElse(null))
                .addClientRequestId(ctx.getCode())
                .addMerchantInfo(nuveiPaymentService.getMerchantInfo(ctx)).build();
    }

    public AddUPOAPMRequest prepareAddUPOAPMRequest(StandardAchConfirmWithdrawMethodContext ctx) {
        UserDetailsCashier userDetails = new UserDetailsCashier();
        userDetails.setFirstName(ctx.personalInfo().firstName());
        userDetails.setLastName(ctx.personalInfo().lastName());
        userDetails.setCountryCode(ctx.personalInfo().country());
        userDetails.setCity(ctx.personalInfo().city().orElse(null));
        userDetails.setAddress(ctx.personalInfo().address().orElse(null));
        userDetails.setState(ctx.personalInfo().state().orElse(null));
        userDetails.setZip(ctx.personalInfo().zip().orElse(null));
        userDetails.setEmail(ctx.getEmail());
        userDetails.setPhone(ctx.getAccCtx().phone());
        userDetails.setBirthdate(ctx.personalInfo().birthDay().map(LocalDate::toString).orElse(null));

        var method = (StandardAchWithdrawMethodInfo) ctx.getMethodCtx().getInfo();
        String bankAccountType = nuveiPaymentService.toMazoomaBankAccountTypeFormat(method.getBankAccountType());
        return (AddUPOAPMRequest) AddUPOAPMRequest.builder()
                .addUserTokenId(getUserTokenId(ctx))
                .addClientRequestId(ctx.getCode())
                .addPaymentMethodName(nuveiPaymentService.getPaymentMethodValue(ctx))
                .addApmData(nuveiPaymentService.getApmData(method.getBankAccountNumber(), bankAccountType, method.getBankAccountRouting()))
                .addBillingAddress(userDetails)
                .addMerchantInfo(nuveiPaymentService.getMerchantInfo(ctx)).build();
    }

    private PayoutRequest preparePayoutRequest(StandardAchConfirmWithdrawMethodContext ctx) {
        var paymentOption = new UserPaymentOption();
        paymentOption.setUserPaymentOptionId(Objects.requireNonNull(ctx.getMethodCtx().getToken()));

        var urlDetails = new UrlDetails();
        urlDetails.setNotificationUrl(Objects.requireNonNull(ctx.termURL().toExternalForm()));

        var merchantDetails = new MerchantDetails();
        merchantDetails.setCustomField1(ctx.getId().toString());

        return PayoutRequest.builder()
                .addAmountAndCurrency(ctx.getAmount().toPlainString(), ctx.currency())
                .addUrlDetails(urlDetails)
                .addMerchantDetails(merchantDetails)
                .addClientUniqueId(String.valueOf(ctx.getId()))
                .addUserTokenId(getUserTokenId(ctx))
                .addClientRequestId(String.valueOf(ctx.getId()))
                .addUserPaymentOption(paymentOption)
                .addMerchantInfo(nuveiPaymentService.getMerchantInfo(ctx)).build();
    }

    private static String getUserTokenId(StandardAchConfirmWithdrawMethodContext ctx) {
        return ctx.rk().replace("/", "_");
    }

}
