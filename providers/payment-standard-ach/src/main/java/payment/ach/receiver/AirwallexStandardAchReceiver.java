package payment.ach.receiver;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.ach.StandardAchConfirmWithdrawMethodContext;
import payment.airwallex.AirwallexAchService;
import payment.type.RedeemProviderSpec;

@Slf4j
@RequiredArgsConstructor
public class AirwallexStandardAchReceiver implements StandardAchReceiver {

    private final AirwallexAchService airwallexAchService;
    private final RedeemProviderSpec redeemProviderSpec;

    @Override
    public RedeemProviderSpec getRedeemProvider() {
        return redeemProviderSpec;
    }

    @Override
    public void confirm(StandardAchConfirmWithdrawMethodContext ctx) throws Exception {
       ctx.setTokenShouldBeSaved(false);
       airwallexAchService.payout(ctx);
    }
}
