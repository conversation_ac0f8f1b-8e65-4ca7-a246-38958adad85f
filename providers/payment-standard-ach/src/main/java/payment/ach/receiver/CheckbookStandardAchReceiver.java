package payment.ach.receiver;

import lombok.extern.slf4j.Slf4j;
import payment.ach.StandardAchConfirmWithdrawMethodContext;
import payment.type.RedeemProviderSpec;

@Slf4j
public class CheckbookStandardAchReceiver implements StandardAchReceiver {
    @Override
    public RedeemProviderSpec getRedeemProvider() {
        return RedeemProviderSpec.CHECKBOOK_ACH;
    }

    @Override
    public void confirm(StandardAchConfirmWithdrawMethodContext ctx) throws Exception {
        ctx.setTokenShouldBeSaved(false);
    }
}
