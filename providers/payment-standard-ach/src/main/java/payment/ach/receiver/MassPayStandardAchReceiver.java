package payment.ach.receiver;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.masspay.MassPayAchService;
import payment.ach.StandardAchConfirmWithdrawMethodContext;
import payment.type.RedeemProviderSpec;

@Slf4j
@RequiredArgsConstructor
public class MassPayStandardAchReceiver implements StandardAchReceiver {

    private final MassPayAchService delegate;
    private final RedeemProviderSpec provider;

    @Override
    public RedeemProviderSpec getRedeemProvider() {
        return provider;
    }

    @Override
    public void confirm(StandardAchConfirmWithdrawMethodContext ctx) throws Exception {
        delegate.confirm(ctx);
        if (ctx.providerToken().token() == null) {
            ctx.setTokenShouldBeSaved(false);
        }
    }
}
