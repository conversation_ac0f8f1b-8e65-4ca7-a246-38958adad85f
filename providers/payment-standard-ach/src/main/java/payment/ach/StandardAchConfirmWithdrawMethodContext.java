package payment.ach;

import payment.AchDetails;
import payment.context.redeem.ConfirmWithdrawContext;
import payment.context.order.RoutingProviderToken;

public interface StandardAchConfirmWithdrawMethodContext extends ConfirmWithdrawContext {
    void setTokenShouldBeSaved(boolean tokenShouldBeSaved);
    boolean isTokenShouldBeSaved();
    RoutingProviderToken providerToken();
    AchDetails methodAchDetails();
}
