package payment.ach;

import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

import api.v1.ApplicationException;
import api.v1.Code;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import payment.WithdrawMethodInfo;
import payment.WithdrawService;
import payment.context.redeem.ConfirmWithdrawContext;
import payment.context.redeem.CreateWithdrawContext;
import payment.context.redeem.WithdrawMethodResetContext;
import payment.type.RedeemProviderSpec;
import payment.util.WithdrawErrorType;
import payment.withdraws.StandardAchWithdrawMethodInfo;
import payment.withdraws.StandardBsbAchWithdrawMethodInfo;
import payment.withdraws.StandardCanadaAchWithdrawMethodInfo;
import uam.api.v1.StandardAchWithdrawMethod;
import uam.api.v1.StandardBsbAchWithdrawMethod;
import uam.api.v1.StandardCanadaAchWithdrawMethod;

@Slf4j
@RequiredArgsConstructor
public class StandardAchWithdrawService implements WithdrawService {

    public static final String SYSTEM_AGENT = "system";
    private final StandardAchWithdrawRoutingService routingService;

    @Override
    public List<RedeemProviderSpec> getRedeemProviders() {
        // masspay mazooma etc are separate WithdrawServices
        return List.of(
                RedeemProviderSpec.STANDARD_ACH,
                RedeemProviderSpec.AIRWALLEX_ACH,
                RedeemProviderSpec.AIRWALLEX_ACH_2,
                RedeemProviderSpec.MASSPAY_ACH,
                RedeemProviderSpec.MASSPAY_ACH_2,
                RedeemProviderSpec.PAYNEARME_ACH
        );
    }

    @Override
    public void create(CreateWithdrawContext ctx) throws Exception {
        if (ctx.getMethodCtx().isRemember()) {
            return;
        }
        var methodInfo = resolveWithdrawMethodInfo(ctx);
        var hash = methodInfo.hash().toString();
        if (ctx.methodCodeOpt().isEmpty() || !Objects.equals(hash, ctx.methodCodeOpt().get())) {
            ctx.getMethodCtx().setMethodCode(hash);
            ctx.getMethodCtx().setWithdrawMethodInfo(methodInfo);
        }
        validateMethod(methodInfo);
        ctx.getMethodCtx().rememberMethod();
    }

    @Override
    public void confirm(ConfirmWithdrawContext ctx) throws Throwable {
        if (SYSTEM_AGENT.equals(ctx.getPreConfirmedBy()) && !SYSTEM_AGENT.equals(ctx.getLockedByAgent())) {
            boolean isErrorCodeEligibleForRetryRouting = routingService.isErrorCodeEligibleForRetryRouting(ctx.getErrorCode(), ctx.getErrorMessage(),
                    ctx.getErrorAdditionalCodes(), ctx.brand(), RedeemProviderSpec.fromString(ctx.providerCode()));
            if (!isErrorCodeEligibleForRetryRouting) {
                log.warn("System agent attempted to confirm a withdraw method locked by another agent, but the error code is not eligible for retry routing : {}",
                        ctx.getLockedByAgent());
                ctx.setManualInterventionNeeded();
                ctx.setAndThrowError(null,
                        "System agent attempted to confirm a withdraw method locked by another agent, but the error code is not eligible for retry routing",
                        WithdrawErrorType.PRE_CONFIRM,
                        ApplicationException.of("Withdraw method locked", Code.ERR_PAYMENT),
                        null);
                return;
            }
        }

        var receiver = routingService.getReceiver(ctx);
        if (receiver.isEmpty()) {
            ctx.setManualInterventionNeeded();
            ctx.setAndThrowError(null,
                    "Empty routing chain. Please check configuration",
                    WithdrawErrorType.PRE_CONFIRM,
                    ApplicationException.of("Empty routing chain", Code.ERR_PAYMENT),
                    null);
        }

        String provider = receiver.get().getRedeemProvider().name().toLowerCase();
        log.debug("Start routing to: {}", provider);
        var resolvedCtx = routingService.resolveContext(ctx, provider);
        if (resolvedCtx.isEmpty()) {
            log.error("Provider not active anymore: {}", provider);
            ctx.setAndThrowError(null,
                    "Provider not active anymore: " + provider,
                    WithdrawErrorType.PRE_CONFIRM,
                    ApplicationException.of("Provider inactive", Code.ERR_PAYMENT),
                    null);
            return;
        }
        var routingCtx = resolvedCtx.get();
        receiver.get().confirm(routingCtx);
        if (routingCtx.isTokenShouldBeSaved()) {
            routingService.saveAchProviderToken(routingCtx);
        }
    }

    @Override
    public void onMethodReset(WithdrawMethodResetContext ctx) throws Throwable {
        routingService.forgetTokens(ctx.accountId());
    }

    private static WithdrawMethodInfo resolveWithdrawMethodInfo(CreateWithdrawContext ctx) {
        if (ctx.withdrawMethod() instanceof StandardAchWithdrawMethod method) {
            if (!ctx.country().equals("US")) {
                throw new IllegalStateException("Unsupported StandardAchWithdrawMethod method for country: " + ctx.country());
            }
            StandardAchWithdrawMethodInfo methodInfo = new StandardAchWithdrawMethodInfo();
            methodInfo.setBankAccountType(method.getBankAccountType().name().toLowerCase());
            methodInfo.setBankAccountNumber(method.getBankAccountNumber());
            methodInfo.setBankAccountRouting(method.getBankAccountRouting());
            return methodInfo;
        } else if (ctx.withdrawMethod() instanceof StandardCanadaAchWithdrawMethod method) {
            if (!ctx.country().equals("CA")) {
                throw new IllegalStateException("Unsupported StandardAchWithdrawMethod method for country: " + ctx.country());
            }
            StandardCanadaAchWithdrawMethodInfo methodInfo = new StandardCanadaAchWithdrawMethodInfo();
            methodInfo.setTransitNumber(method.getTransitNumber());
            methodInfo.setBankAccountNumber(method.getBankAccountNumber());
            methodInfo.setInstitutionNumber(method.getInstitutionNumber());
            return methodInfo;
        }
        else if (ctx.withdrawMethod() instanceof StandardBsbAchWithdrawMethod method) {
            if (!ctx.country().equals("AU")) {
                throw new IllegalStateException("Unsupported StandardBsbAchWithdrawMethod method for country: " + ctx.country());
            }
            StandardBsbAchWithdrawMethodInfo methodInfo = new StandardBsbAchWithdrawMethodInfo();
            methodInfo.setBsbRoutingNumber(method.getBsbRoutingNumber());
            methodInfo.setBankAccountNumber(method.getBankAccountNumber());
            return methodInfo;
        }
        throw new IllegalStateException("Not supported withdrawal method: " + ctx.withdrawMethod().getClass());
    }
    private static void validateMethod(WithdrawMethodInfo method) throws ApplicationException {
        if (method instanceof StandardAchWithdrawMethodInfo stdAchMethod) {
            Pattern routingNumber = Pattern.compile("^((0[0-9])|(1[0-2])|(2[1-9])|(3[0-2])|(6[1-9])|(7[0-2])|80)([0-9]{7})$");
            if (!routingNumber.matcher(stdAchMethod.getBankAccountRouting()).matches()) {
                log.warn("Invalid Bank Routing Number [{}]", stdAchMethod.getBankAccountRouting());
                throw ApplicationException.of("Invalid Bank Routing Number", Code.ERR_BAD_REQUEST);
            }
            Pattern accountNumber = Pattern.compile("^[0-9]+$");
            if (!accountNumber.matcher(stdAchMethod.getBankAccountNumber()).matches()) {
                log.warn("Invalid Bank Account Account [{}]", stdAchMethod.getBankAccountNumber());
                throw ApplicationException.of("Invalid Bank Account Number", Code.ERR_BAD_REQUEST);
            }
        }
        else if (method instanceof StandardCanadaAchWithdrawMethodInfo stdCaAchMethod) {
            Pattern accountNumber = Pattern.compile("^[0-9]{1,14}$");
            if (!accountNumber.matcher(stdCaAchMethod.getBankAccountNumber()).matches()) {
                log.warn("Invalid Bank Account Account [{}]", stdCaAchMethod.getBankAccountNumber());
                throw ApplicationException.of("Invalid Bank Account Number", Code.ERR_BAD_REQUEST);
            }
            Pattern institutionNumber = Pattern.compile("[0-9]{3}");
            if (!institutionNumber.matcher(stdCaAchMethod.getInstitutionNumber()).matches()) {
                log.warn("Invalid Institution Number [{}]", stdCaAchMethod.getInstitutionNumber());
                throw ApplicationException.of("Invalid Institution Number", Code.ERR_BAD_REQUEST);
            }
            Pattern transitNumber = Pattern.compile("^[0-9]{5}$");
            if (!transitNumber.matcher(stdCaAchMethod.getTransitNumber()).matches()) {
                log.warn("Invalid transit Number [{}]", stdCaAchMethod.getTransitNumber());
                throw ApplicationException.of("Invalid transit Number", Code.ERR_BAD_REQUEST);
            }
        }
        else if (method instanceof StandardBsbAchWithdrawMethodInfo stdBsbAchMethod) {
            Pattern accountNumber = Pattern.compile("^[0-9]+$");
            if (!accountNumber.matcher(stdBsbAchMethod.getBankAccountNumber()).matches()) {
                log.warn("Invalid Bank Account Account [{}]", stdBsbAchMethod.getBankAccountNumber());
                throw ApplicationException.of("Invalid Bank Account Number", Code.ERR_BAD_REQUEST);
            }
            Pattern bsbNumber = Pattern.compile("^[0-9]{3}-?[0-9]{3}$");
            if (!bsbNumber.matcher(stdBsbAchMethod.getBsbRoutingNumber()).matches()) {
                log.warn("Invalid bsb Number [{}]", stdBsbAchMethod.getBsbRoutingNumber());
                throw ApplicationException.of("Invalid bsb number", Code.ERR_BAD_REQUEST);
            }
        }
    }

}
