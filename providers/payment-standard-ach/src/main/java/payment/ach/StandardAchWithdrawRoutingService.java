package payment.ach;

import java.util.Map;
import java.util.Optional;

import payment.ach.receiver.StandardAchReceiver;
import payment.context.redeem.ConfirmWithdrawContext;

public interface StandardAchWithdrawRoutingService {
    Optional<StandardAchReceiver> get<PERSON><PERSON><PERSON><PERSON>(ConfirmWithdrawContext ctx) throws Throwable;
    Optional<StandardAchConfirmWithdrawMethodContext> resolveContext(ConfirmWithdrawContext ctx, String providerCode) throws Throwable;
    void saveAchProviderToken(StandardAchConfirmWithdrawMethodContext ctx) throws Throwable;
    void forgetTokens(Long accountId) throws Throwable;
    boolean isErrorCodeEligibleForRetryRouting(String errorCode, String errorMessage, Map<String, String> additionalCodesMap, String brand,
            payment.type.RedeemProviderSpec redeemProviderSpec);
}
