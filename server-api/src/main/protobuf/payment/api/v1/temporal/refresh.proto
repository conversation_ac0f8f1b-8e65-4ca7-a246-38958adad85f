syntax = "proto3";

package payment.api.v1.internal.temporal;

import "payment/api/v1/payment_common.proto";
import "payment/api/v1/payment_event_stream.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message RefreshPaymentOrderRequestInput {
  uam.api.v1.RefreshPaymentOrderRequest req = 1;
}

message RefreshPaymentOrderRequestOutput {
  uam.api.v1.RefreshPaymentOrderResponse resp = 1;
}

