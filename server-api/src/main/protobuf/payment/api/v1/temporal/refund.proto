syntax = "proto3";

package payment.api.v1.internal.temporal;

import "payment/api/v1/payment_common.proto";
import "payment/api/v1/payment_event_stream.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message RefundAccountPersonalInfoInput {
  string orderSn = 1;
}

//
// ~ verify
//

message RefundVerifyCheckInput {
  string orderSn = 1;
  string country = 2;
}

message RefundVerifyCheckOutput {
  bool refundable = 1;
  string code = 2;
  string amount = 3;
  string currency = 4;
  string error = 5;
  string errorCode = 6;
}

message RefundVerifySaveCheckErrorInput {
  string orderSn = 1;
  RefundVerifyCheckOutput checkReply = 2;
}

//
// ~ refund
//

message RefundValidateInput {
  string orderSn = 1;
  uam.api.v1.RefundTransactionRequest request = 2;
}

message RefundValidateOutput {
  bool refunded = 1;
  bool hasChargeback = 2;
}

message RefundInput {
  string orderSn = 1;
  uam.api.v1.RefundTransactionRequest request = 2;
  string country = 3;
}

message RefundOutput {
  bool refunded = 1;
  .uam.api.v1.internal.OfferRefundEvent event = 2;
}
