syntax = "proto3";

package payment.api.v1.internal.temporal;

import "payment/api/v1/payment_common.proto";
import "payment/api/v1/payment_event_stream.proto";
import "payment/api/v1/routing.proto";
import "uam/api/v1/account.proto";
import "payment/api/v1/notifications.proto";
import "payment/api/v1/event.proto";
import "fraud/api/v1/fraud_common.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message DeclinePaymentOrderIn {
    bool removeSavedCards = 1;
    string code = 2;
    int64 orderId = 3;
    uam.api.v1.PaymentOrderError error = 4;
    string errMsg = 5;
    uam.api.v1.GetAccountPersonalInfoResponse personalInfo = 6;
    uam.api.v1.GetAccountEngagementInfoResponse engagementInfo = 7;
    bool kyc = 8;
    fraud.api.v1.KYCInfo kycInfo = 9;
}

message DeclinePaymentOrderOut {
    string declineReason = 1;
}

message OnPaymentOrderDeclineIn {
    int64 orderId = 1;
    string declineReason = 2;
    uam.api.v1.PaymentOrderError error = 3;
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 4;
}

message OnPaymentOrderDeclineOut {
    oneof notification {
        uam.api.v1.OfferDeclineNotification offerDeclineNotification = 1;
        PurchaseDeclineNotification purchaseDeclineNotification = 2;
    }
    PurchaseDeclineEvent declineEvent = 3;
}

