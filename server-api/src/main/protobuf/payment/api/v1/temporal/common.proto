syntax = "proto3";

package payment.api.v1.internal.temporal;

import "payment/api/v1/payment_common.proto";
import "payment/api/v1/payment_event_stream.proto";
import "payment/api/v1/routing.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message LabelFraudRequestIn {
    string fraudRequestId = 1;
    string reason = 2;
    string brand = 3;
    string hash = 4;
}

message LabelFraudRequestOut {
    string fraudRequestId = 1;
    string reason = 2;
}

message CompleteOrderIn {
    int64 orderId = 1;
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRoutingInfo = 2;
}

message CompleteOrderOut {
}
