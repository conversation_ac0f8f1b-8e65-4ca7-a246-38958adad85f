syntax = "proto3";

package payment.api.v1.internal.temporal;

import "payment/api/v1/event.proto";
import "payment/api/v1/payment_common.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message SaveChargebackInfoIn {
  uam.api.v1.SetChargebackInfoRequest request = 1;
}

message SaveChargebackInfoOut {
  string brand = 1;
  string routingKey = 2;
  optional string fraudRequestId = 3;
  bool noChbBefore = 4;
}

message BuildChargebackEventIn {
  string orderSn = 1;
  string agentName = 2;
  bool noChbBefore = 3;
}

message BuildChargebackEventOut {
  ChargebackEvent event = 1;
}

message LabelTransactionIn {
  bool skipLabel = 1;
  optional string fraudRequestId = 2;
  string status = 3;
  string hash = 4;
  string brand = 5;
}

message LabelTransactionOut {
}

