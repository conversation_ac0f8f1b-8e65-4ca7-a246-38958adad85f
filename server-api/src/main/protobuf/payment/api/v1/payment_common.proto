syntax = "proto3";

package uam.api.v1;

import "api/v1/obfuscation.proto";
import "api/v1/common.proto";
import "api/v1/identity.proto";
import "api/v1/routing.proto";
import "payment/api/v1/routing.proto";
import "payment/api/v1/payment_common_tmp.proto";
import "payment/api/v1/payment_event_stream.proto";
import "payment/api/v1/payment_method.proto";
import "payment/api/v1/internal.proto";
import "uam/api/v1/wallet.proto";
import "uam/api/v1/account.proto";
import "uam/api/v1/common.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message CurrencyType {
    enum Enum {
        FIAT = 0;
        CRYPTO = 1;
    }
}

message CryptoPurchaseData {
    optional string amount = 1;
    optional string wallet = 2;
    optional string txHash = 3;
    optional uam.api.v1.CryptoCurrency.Enum currency = 4;
    optional uam.api.v1.CryptoNetwork.Enum network = 5;
}

message RedeemMoneyInfo {
    int64 id = 1;
    string provider = 2;
    string currency = 3;
    string amount = 4;
    string status = 5;
    int64 createdAt = 6;
    bool isFirstRedeem = 7;
    string paymentDetails = 8;
    string baseCurrency = 9;
    string baseAmount = 10;
    string transactionId = 11;
    int64 fraudResponseId = 12;
    int32 fraudScore = 13;
    string email = 14;
    string uamStatus = 15;
    string lockedByAgent = 16;
    string confirmationReason = 17;
    int64 modifiedAt = 18;
    int64 requestedAt = 19;
    int64 lockedAt = 20;
    int64 preConfirmedAt = 21;
    int64 confirmedAt = 22;
    int64 declinedAt = 23;
    int64 cancelledAt = 24;
    optional CryptoPurchaseData cryptoPaymentData = 25;
    int64 providerId = 26;
}

enum PaymentProvider {
    DO_NOT_CHANGE_PROVIDER = 0;
    SKRILL = 5;
    SPREEDLY = 12;
    FISERV = 13;
    SPREEDLY_RAPYD = 15;
    SPREEDLY_RAPYD_2 = 16;
    SPREEDLY_RAPYD_3 = 30;
    SPREEDLY_RAPYD_4 = 95;
    SPREEDLY_RAPYD_5 = 96;
    FISERV_GOOGLE_PAY = 17;
    SPREEDLY_FISERV = 18;
    FISERV_APPLE_PAY = 19;
    NUVEI_MAZOOMA_ACH = 23;
    SPREEDLY_FISERV_2 = 24;
    SPREEDLY_FISERV_3 = 28;
    SPREEDLY_FISERV_4 = 29;
    SPREEDLY_FISERV_5 = 41;
    SPREEDLY_FISERV_6 = 42;
    SPREEDLY_EMERCHANTPAY = 25;
    SPREEDLY_EMERCHANTPAY_2 = 47;
    SPREEDLY_EMERCHANTPAY_3 = 71;
    SPREEDLY_EMERCHANTPAY_4 = 72;
    SPREEDLY_EMERCHANTPAY_5 = 73;
    SPREEDLY_EMERCHANTPAY_6 = 74;
    SPREEDLY_APPLE_PAY = 26;
    SPREEDLY_FISERV_APPLE_PAY = 27;
    SPREEDLY_FISERV_APPLE_PAY_2 = 32;
    SPREEDLY_FISERV_APPLE_PAY_3 = 33;
    SPREEDLY_FISERV_APPLE_PAY_4 = 34;
    SPREEDLY_FISERV_APPLE_PAY_5 = 43;
    SPREEDLY_FISERV_APPLE_PAY_6 = 44;
    SPREEDLY_GOOGLE_PAY = 1;
    SPREEDLY_FISERV_GOOGLE_PAY = 2;
    SPREEDLY_FISERV_GOOGLE_PAY_2 = 35;
    SPREEDLY_FISERV_GOOGLE_PAY_3 = 36;
    SPREEDLY_FISERV_GOOGLE_PAY_4 = 37;
    SPREEDLY_FISERV_GOOGLE_PAY_5 = 45;
    SPREEDLY_FISERV_GOOGLE_PAY_6 = 46;
    APPLE_IN_APP = 3;
    SPREEDLY_TEST_GATEWAY = 31;
    SPREEDLY_TEST_GATEWAY_2 = 51;
    SPREEDLY_TEST_GATEWAY_3 = 52;
    SPREEDLY_CHECKOUT = 53;
    SPREEDLY_CHECKOUT_GOOGLE_PAY = 54;
    SPREEDLY_CHECKOUT_APPLE_PAY = 55;
    SPREEDLY_WORLDPAY = 38;
    SPREEDLY_WORLDPAY_2 = 50;
    SPREEDLY_WORLDPAY_3 = 84;
    SPREEDLY_WORLDPAY_4 = 85;
    SPREEDLY_WORLDPAY_APPLE_PAY = 39;
    SPREEDLY_WORLDPAY_APPLE_PAY_2 = 48;
    SPREEDLY_WORLDPAY_APPLE_PAY_3 = 86;
    SPREEDLY_WORLDPAY_APPLE_PAY_4 = 87;
    SPREEDLY_WORLDPAY_GOOGLE_PAY = 40;
    SPREEDLY_WORLDPAY_GOOGLE_PAY_2 = 49;
    SPREEDLY_WORLDPAY_GOOGLE_PAY_3 = 88;
    SPREEDLY_WORLDPAY_GOOGLE_PAY_4 = 89;
    SPREEDLY_EMERCHANTPAY_APPLE_PAY = 4;
    SPREEDLY_EMERCHANTPAY_APPLE_PAY_2 = 61;
    SPREEDLY_EMERCHANTPAY_APPLE_PAY_3 = 64;
    SPREEDLY_EMERCHANTPAY_APPLE_PAY_4 = 65;
    SPREEDLY_EMERCHANTPAY_APPLE_PAY_5 = 75;
    SPREEDLY_EMERCHANTPAY_APPLE_PAY_6 = 76;
    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY = 6;
    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_2 = 62;
    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_3 = 66;
    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_4 = 67;
    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_5 = 77;
    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_6 = 78;
    SPREEDLY_PAYNEARME = 81;
    SPREEDLY_PAYNEARME_2 = 92;
    SPREEDLY_PAYNEARME_APPLE_PAY = 82;
    SPREEDLY_PAYNEARME_APPLE_PAY_2 = 93;
    SPREEDLY_PAYNEARME_GOOGLE_PAY = 83;
    SPREEDLY_PAYNEARME_GOOGLE_PAY_2 = 94;
    TRUSTLY = 63;
    SPREEDLY_NUVEI = 68;
    SPREEDLY_NUVEI_2 = 69;
    PAYPER = 70;
    STANDARD_ACH = 79;
    ANDROID_IN_APP = 80;
    CRYPTO = 90;
    ORBITAL = 91;
    AEROPAY = 97;
}

enum RedeemProvider {
    DO_NOT_CHANGE_REDEEM_PROVIDER = 0;
    SKRILL_REDEEM = 1;
    PRIZEOUT_REDEEM = 3;
    NUVEI_MAZOOMA_ACH_REDEEM = 4;
    MASSPAY_ACH_REDEEM = 5;
    TRUSTLY_REDEEM = 6;
    PAYPER_REDEEM = 7;
    STANDARD_ACH_REDEEM = 8;
    AIRWALLEX_ACH_REDEEM = 9;
    AEROPAY_ACH_REDEEM = 10;
    NSC_REDEEM = 11;
    CRYPTO_REDEEM = 12;
    ORBITAL_REDEEM = 13;
    AIRWALLEX_ACH_2_REDEEM = 14;
    CHECKBOOK_ACH_REDEEM = 15;
    MASSPAY_ACH_2_REDEEM = 16;
    PAYNEARME_ACH_REDEEM = 17;
    AEROPAY_REDEEM = 18;
}

message AeroPayPaymentMethod {
    string bankAccountName = 1;
    string bankAccountNumber = 2;
    string bankName = 3;
    string phone = 4;
    string accountId = 5;
}

message TrustlyPaymentMethod {
    string code = 1;
    string bankName = 2;
    string paymentProviderId = 3;
    string accountName = 4;
    string accountNumberLast4 = 5;
    int64 lastUsageAt = 6;
}

message CryptoPaymentMethod {
    string wallet = 1;
    CryptoCurrency.Enum currency = 2;
    CryptoNetwork.Enum network = 3;
}
message CryptoWithdrawMethod {
    string wallet = 1;
    CryptoCurrency.Enum currency = 2;
    CryptoNetwork.Enum network = 3;
}

message AeroPayWithdrawMethod {
    string bankAccountName = 1;
    string bankAccountNumber = 2;
    string bankName = 3;
    string phone = 4;
    string accountId = 5;
}

message PayperPaymentMethod {
    string email = 1;
    string phone = 2;
}

message SkrillPaymentMethod {
    string email = 1 [(.api.v1.sensitive_mode).sensitive = true];
    int64 lastUsageAt = 2;
    string firstname = 3;
    string lastname = 4;
    string customerId = 5;
}

message NuveiMazoomaPaymentMethod {
    string fiAccType = 11;
    string fiAccLabel = 12;
    string fiName = 13;
}

message PaymentMethod {
    string code = 1;
    string provider = 2;
    bool tokenize = 4;
    int64 createdAt = 5;

    oneof type {
        CardPaymentMethod card = 10;
        SkrillPaymentMethod skrill = 12;
        NuveiMazoomaPaymentMethod mazooma = 13;
        TrustlyPaymentMethod trustly = 14;
        PayperPaymentMethod payper = 15;
        CryptoPaymentMethod crypto = 16;
        AeroPayPaymentMethod aeroPay = 17;
    }
}

message CreateNewAeroPayMethodRequest {
    Identity identity = 1;
    string accountId = 2;
}

message CreateNewAeroPayMethodResponse {
    PaymentMethod paymentMethod = 1;
    WithdrawMethod withdrawMethod = 2;
}

message BlockedPaymentMethod {
    string bin = 1;
    string lastFour = 2;
    string status = 3;
}

message PrizeoutWithdrawMethod {
    string email = 1 [(.api.v1.sensitive_mode).sensitive = true];
}

message SkrillWithdrawMethod {
    string email = 1 [(.api.v1.sensitive_mode).sensitive = true];
    string firstname = 2;
    string lastname = 3;
    string customerId = 4;
}

message TrustlyWithdrawMethod {
    string merchantReference = 1;
}

message PayperWithdrawMethod {
    string email = 1;
    string phone = 2;
}

message StandardAchWithdrawMethod {
    BankAccountType bankAccountType = 1;
    string bankAccountNumber = 2;
    string bankAccountRouting = 3;
}
message StandardCanadaAchWithdrawMethod {
    string bankAccountNumber = 1;
    string institutionNumber = 2;
    string transitNumber = 3;
}
message StandardBsbAchWithdrawMethod {
    string bankAccountNumber = 1;
    string bsbRoutingNumber = 2;
}

message NuveiMazoomaACHWithdrawMethod {
    string fiAccountNumber = 1;
    string fiAccountType = 2;
    string fiRouting = 3;
    string fiName = 4;
    string userTokenId = 5;
    string userPaymentOptionId = 6;
    string amount = 7;
    string reference = 8;
    string ip = 9;
    string currency = 10;

    string firstName = 11;
    string lastName = 12;
    string address = 13;
    string state = 14;
    string city = 15;
    string zip = 16;
    string countryCode = 17;
    string phone = 18;
    string email = 19;
    string dateOfBirth = 20;
}

// The following message is deprecated, but it should not be deleted for backward compatibility.
message MassPayACHWithdrawMethod {
    string bankAccountNumber = 1;
    BankAccountType bankAccountType = 2;
    string bankAccountRouting = 3;
}

message WithdrawMethod {
    string code = 1;

    oneof type {
        SkrillWithdrawMethod skrill = 12;
        PrizeoutWithdrawMethod prizeout = 15;
        NuveiMazoomaACHWithdrawMethod mazoomaAch = 17;
        MassPayACHWithdrawMethod massPayAch = 18; //deprecated, used for backward compatibility
        TrustlyWithdrawMethod trustly = 19;
        PayperWithdrawMethod payper = 20;
        StandardAchWithdrawMethod standardAch = 21;
        StandardCanadaAchWithdrawMethod standardCanadaAch = 22;
        StandardBsbAchWithdrawMethod standardBsbAch = 23;
        CryptoWithdrawMethod crypto = 24;
        AeroPayWithdrawMethod aeroPay = 25;
    }
}

message PaymentIdentityByTransactionId {
    string transactionId = 1;
    PaymentProvider provider = 2;
}

message PaymentIdentityByOrderSn {
    string orderSn = 1;
}

message PaymentIdentityByArn {
    string arn = 1;
}

message PaymentIdentityByCode {
    //deprecated. use providers
    PaymentProvider provider = 1;
    string code = 2;
    repeated PaymentProvider providers = 3;
}

message WithdrawIdentityById {
    int64 id = 1;
}

message WithdrawIdentityByCode {
    string provider = 1;
    string code = 2;
}

message WithdrawIdentityByTransactionId {
    string provider = 1;
    string transactionId = 2;
}

message PaymentMethodInfo {
    string city = 1;
    string zip = 2;
    payment.api.v1.PurchaseMethod method = 3;
}

message WithdrawMethodPolicy {
    oneof type {
        WithdrawMethodPolicyDropdown skrill = 3;
        WithdrawMethodPolicyDropdown prizeout = 6;
        WithdrawMethodPolicyDropdown mazoomaAch = 7;
        WithdrawMethodPolicyDropdown massPayAch = 8;
        WithdrawMethodPolicyDropdown trustly = 9;
        WithdrawMethodPolicyDropdown payper = 10;
        WithdrawMethodPolicyDropdown standardAch = 11;
        WithdrawMethodPolicyDropdown standardCanadaAch = 12;
        WithdrawMethodPolicyDropdown standardBsbAch = 13;
        WithdrawMethodPolicyDropdown crypto = 14;
        WithdrawMethodPolicyDropdown aeroPay = 15;
    }
}

message WithdrawMethodPolicyDropdown {
    RedeemMoneyPolicy sweepstakeRedeemPolicy = 1;
    RedeemMoneyPolicy fiatRedeemPolicy = 2;
    RedeemMoneyPolicy nonMonetaryRedeemPolicy = 3;
}

message ResetAccountPurchaseAndWithdrawMethodsRequest {
    Identity identity = 1;
    string provider = 2;
}

message ResetAccountPurchaseAndWithdrawMethodsResponse {
}

message GetAccountPaymentRoutingInfoRequest {
    oneof type {
        AccountRoutingByWithdrawRequest byWithdrawRequest = 1;
        AccountRoutingByPaymentOrder byPaymentOrder = 2;
    }
}

message GetAccountPaymentRoutingInfoResponse {
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 4;

    oneof type {
        RedeemMoneyInfo redeem = 2;
        PaymentOrderInfo order = 3;
    }
}

message AccountRoutingByPaymentOrder {
    oneof type {
        PaymentIdentityByTransactionId byTransactionId = 10;
        PaymentIdentityByCode byCode = 11;
        PaymentIdentityByOrderSn byOrderSn = 12;
        PaymentIdentityByArn byArn = 13;
    }
}

message AccountRoutingByWithdrawRequest {
    oneof type {
        WithdrawIdentityByCode byCode = 11;
        WithdrawIdentityByTransactionId byTransactionId = 12;
        WithdrawIdentityById byId = 13;
    }
}

message RefundTransactionRequest {
    string transactionId = 1;
    string amount = 2;
    string currency = 3;
    string orderSn = 4;
    string reason = 5;
    bool forceChargebackRefund = 6;
    string refundedBy = 7;
}

message RefundTransactionResponse {
    string transactionId = 1;
    bool refunded = 2;
    string orderSn = 3;
}

message SaveOfferTemplateRequest {
    string brandName = 1;
    payment.api.v1.OfferTemplateAdminInfo offer = 2;
    bool selfApproved = 3;
    ApprovalStatus statusApproval = 4;
}

message SaveOfferTemplateResponse {
}

message UpdateOfferTemplateRequest {
    string brandName = 1;
    payment.api.v1.OfferTemplateAdminInfo offer = 2;
    ApprovalStatus statusApproval = 3;
}

message UpdateOfferTemplateResponse {
}

message SpreedlyScaAuthenticateRequest {
    string browserInfo = 1;
    string paymentToken = 2;
}

message ScaAuthenticateResponse {
    string authenticateTxId = 1;
    ScaAuthenticateState state = 2;
}

enum ScaAuthenticateState {
    PENDING = 0;
    SUCCEEDED = 1;
    FAILED = 2;
}

message CreatePaymentOrderRequest {
    Identity identity = 1;
    string token = 2;
    string transactionId = 3;
    string offer = 6;
    .api.v1.AgentInfo agent = 9;
    PaymentProvider provider = 10;
    int64 at = 15;
    string referrer = 17;
    BillingAddress billingAddress = 24;
    string session = 25;
    string validationUrl = 26;
    string sourceId = 28;
    bool cvvEntered = 29;
    string quickPurchaseSupportedType = 30;
    oneof threeDsAuthType {
        // deprecated. use sourceId
        string authenticateTxId = 31;
        SpreedlyScaAuthenticateRequest spreedly = 32;
    };
    string amount = 33;
    // should be set for crypto purchases
    optional CryptoData cryptoData = 34;
    bool isMobileApp = 35;
    repeated string rewardCodes = 36;
    AppMetadata metadata = 37;
}

message CryptoData {
    CryptoCurrency.Enum currency = 1;
    CryptoNetwork.Enum network = 2;
}

message CreatePaymentOrderResponse {
    string code = 1;
    string transactionId = 2;
    string status = 3;
    string supplier = 4;
    bool secure3d = 5;
    string termUrl = 6;
    string tempToken = 7;
    string redirectUrl = 8;
    string paymentData = 9;

    string limitAmount = 11;
    string limitPeriod = 12;
    string limitAvailable = 13;
    .api.v1.Date limitEnd = 14;

    string clientToken = 15;
    string publicKeyBase64 = 16;

    bool requestKyc = 17;
    string completeMerchantValidation = 19;
    string sourceId = 21;
    bool cvvEntered = 22;
    PaymentOrderError error = 23;
    string offerCode = 24;
    string offerPrice = 25;
    string billingDescriptor = 26;
    OfferTemplateInfo offer = 27;
    string currency = 28;
    string provider = 29;
    string amount = 30;
    int64 firstDeposit = 31;
    bool isFirstDeposit = 32;
    string city = 33;
    string zip = 34;
    ScaAuthenticateResponse scaAuthenticateResponse = 35;
    string paymentToken = 36;
    string quickPurchaseSupportedType = 37;
    PaymentProvider originalProvider = 38;
    string description = 39;
    payment.api.v1.PurchaseMethod method = 40;
    OrderStatusSpec.Enum internalStatus = 41;
}

message Capture3DsPaymentOrderRequest {
    uam.api.v1.Identity identity = 1;
    bool cvvEntered = 2;
    oneof type {
        uam.api.v1.PaymentIdentityByTransactionId byTransactionId = 3;
        uam.api.v1.PaymentIdentityByCode byCode = 4;
    }
    bool isMobileApp = 5;
}

message Capture3DsPaymentOrderResponse {
    string paymentToken = 1;
    string sourceId = 2;
    string offerCode = 3;
    string authenticateTxId = 4;
    string quickPurchaseSupportedType = 5;
    PaymentProvider originalProvider = 6;
    PaymentOrderError error = 7;
}

message RefreshPaymentOrderRequest {
    Identity identity = 1;
    //todo how used?
    //fix in PAY-5666
    string recurrentCode = 2;
    string apiError = 3;
    string code = 4;
    string token = 5;

    oneof type {
        PaymentIdentityByTransactionId byTransactionId = 10;
        PaymentIdentityByCode byCode = 11;
    }
    PaymentOrderError error = 20;
}

message RefreshPaymentOrderResponse {
}

message PaymentOrderError {
    string errorCode = 1;
    string exceptionClass = 2;
    string responseCode = 3;
    string message = 4;
    string status = 5;
    int32 httpCode = 6;
    string cardNetworkError = 7;
    string apiError = 9;
    repeated PaymentProvider rescueProviders = 10;
    optional string extendedErrorCode = 11;
}

message NuveiMazoomaParams {
    string fiAccType = 3;
    string fiName = 4;
    string fiAccLabel = 17;
}

message PayperParams {
    string bankName = 1;
}

message SkrillParams {
    string firstname = 1;
    string lastname = 2;
    string customerId = 3;
}

message AeroPayParams {
    string userId = 1;
}

message ConfirmWithdrawMethodRequest {
    Identity identity = 1;
    string type = 2;
    string reference = 3;
    string code = 4;
}

message ConfirmRedeemMethodResponse {
    bool applied = 1;
}

message CancelRedeemMoneyRequest {
    Identity identity = 1;
    int64 id = 2;
    bool doNotSendEmail = 3;
    string comments = 4;
}

message CancelRedeemMoneyResponse {
    RedeemMoneyInfo redeem = 1;
}

message CancelAllRedeemMoneyRequest {
    Identity identity = 1;
}

message CancelAllRedeemMoneyResponse {
}

message UpdateRedeemRiskStatusRequest {
    Identity identity = 1;
    int64 id = 2;
    RedeemRiskStatus riskStatus = 3;
    optional string comments = 4;
    optional string automationDeclineReason = 5;
    optional RedeemRiskDeclineMode declineMode = 6;
    string modifiedBy = 7;
}

enum RedeemRiskStatus {
    UNKNOWN = 0;
    RISK_APPROVED = 1;
    RISK_HOLD = 2;
    BI_APPROVED = 3;
    BI_DECLINED = 4;
    RISK_DECLINED = 5;
    RISK_IN_REVIEW = 6;
    PAYMENT_HOLD = 7;
}

enum RedeemRiskDeclineMode {
    DEFAULT = 0;
    NON_COMPLIANCE = 1;
}

message UpdateRedeemRiskStatusResponse {
}

message GetRedeemMoneyHistoryRequest {
    Identity identity = 1;
    .api.v1.Date from = 2;
    .api.v1.Date to = 3;
    .api.v1.PagingInfo pagingInfo = 4;
}

message GetRedeemMoneyHistoryResponse {
    repeated RedeemMoneyInfo redeems = 1;
}

message GetRedeemHistoryRequest {
    Identity identity = 1;
    .api.v1.PagingInfo pagingInfo = 4;
    repeated string statuses = 5;
}

message GetRedeemHistoryResponse {
    repeated RedeemMoneyInfo redeems = 1;
    int64 totalCount = 2;
}

message GetRedeemDetailsRequest {
    string provider = 1;
    string code = 2;
}

message GetRedeemDetailsResponse {
    RedeemMoneyInfo redeem = 1;
}

message AccountPaymentSettingsResponse {
    optional Secure3dAction secure3dAction = 1;
}
message GetPendingRedeemCountRequest {
    Identity identity = 1;
}

message GetPendingRedeemCountResponse {
    int64 count = 1;
    string totalAmount = 2;
    string totalBaseAmount = 3;
    int64 fiatRedeemCount = 4;
    string fiatRedeemTotalAmount = 5;
    string fiatRedeemTotalBaseAmount = 6;
}

message GetPaymentOrderRequest {
    Identity identity = 1;
    oneof type {
        PaymentIdentityByTransactionId byTransactionId = 10;
        PaymentIdentityByCode byCode = 11;
    }
}

message GetPaymentOrderResponse {
    PaymentOrderInfo order = 1;
    PaymentMethodInfo paymentMethod = 2;
    string billingDescriptor = 3;
    PaymentOrderError error = 4;
    payment.api.v1.internal.AccountPaymentInfo accountPaymentInfo = 5;
}

message GetPaymentMethodsRequest {
    Identity identity = 1;
}

message GetPaymentMethodsResponse {
    repeated PaymentMethod data = 1;
    string quickPurchaseSupportedType = 2;
    bool verificationRequired = 3;
}

message GetIframeSecurityDataRequest {
    Identity identity = 1;
    string nonce = 2;
    int64 timestamp = 3;
}

message GetIframeSecurityDataResponse {
    string certificateToken = 1;
    string signature = 2;
    bool secureDataEnabled = 3;
}

message GetWithdrawMethodsRequest {
    Identity identity = 1;
}

message GetWithdrawMethodsResponse {
    repeated WithdrawMethod data = 1;
    repeated WithdrawMethodPolicy policy = 2;
}

message DeletePaymentMethodRequest {
    Identity identity = 1;
    string code = 2;
    string fingerprint = 3;
}

message DeletePaymentMethodResponse {
    bool applied = 1;
}

message DeleteWithdrawMethodRequest {
    Identity identity = 1;
    string provider = 2;
    string code = 3;
}

message DeleteWithdrawMethodResponse {
    bool applied = 1;
}

message CaptureRedeemOptionRequest {
    Identity identity = 1;
    string currency = 2;
    string amount = 3;
    .uam.api.v1.RedeemProvider provider = 4;
    string referer = 5;
    int64 at = 6;
    AppMetadata metadata = 7;
}

message CaptureRedeemOptionResponse {
    int64 id = 1;
    string redirectUrl = 2;
    string tmpToken = 3;
    WithdrawMethod method = 4;
    bool kyc = 5;
    string kycStatus = 6;
    string provider = 7;
}

message RedeemMoneyRequest {
    Identity identity = 1;
    string currency = 2;
    string amount = 3;
    string email = 4 [(.api.v1.sensitive_mode).sensitive = true];
    WithdrawMethod method = 5;
    string code = 6;
    string session = 7;
    bool locked = 8;
    string token = 9;
    int64 at = 11;
    oneof type {
        uam.api.v1.WithdrawIdentityByCode byCode = 12;
        uam.api.v1.WithdrawIdentityById byId = 13;
        uam.api.v1.WithdrawIdentityByTransactionId byTransactionId = 14;
    }
    AppMetadata metadata = 15;
    bool isMobileApp = 16;
}

message RedeemMoneyResponse {
    int64 id = 1;
    bool kyc = 2;
    string kycUrl = 3;
    string provider = 4;
    string kycStatus = 5;
}

message SetRedeemFraudCheckRequest {
    string remoteIp = 5;
    string session = 6;
    int64 redeemId = 10;
}

message FraudCheckBankDetails {
    string bankName = 1;
    string bankAccountNumber = 2;
    string bankAccountName = 5;
}

message SetRedeemFraudCheckResponse {
}

message PreConfirmRedeemMoneyRequest {
    Identity identity = 1;
    int64 id = 2;
    string code = 3;
    string preConfirmedByAgent = 4;
    bool isSuperAdmin = 5;
    string extCode = 6;
    optional string comment = 7;
}

message PreConfirmRedeemMoneyResponse {
    string brand = 2;
}

message UpdateStandardAchRedeemMoneyRequest {
    string brand = 1;
    repeated int64 ids = 2;
    RedeemProvider redeemProvider = 3;
    string modifiedBy = 4;
}

message UpdateStandardAchRedeemMoneyResponse {
    map<int64, string> errors = 1;
    map<int64, string> oldProviders = 2;
    bool applied = 3;
}

message GetAvailableAchProvidersRequest {
    string brand = 1;
}

message GetAvailableAchProvidersResponse {
    repeated RedeemProvider achProviders = 1;
}

message ConfirmRedeemMoneyRequest {
    Identity identity = 1;
    string code = 4;
    string reason = 5;
    string confirmedByAgent = 6;
    bool isSuperAdmin = 7;
    optional bool rtpFlag = 11;
    oneof type {
        uam.api.v1.WithdrawIdentityById byId = 8;
        uam.api.v1.WithdrawIdentityByCode byCode = 9;
        uam.api.v1.WithdrawIdentityByTransactionId byTransactionId = 10;
    }
}

message ConfirmRedeemMoneyResponse {
    string brand = 3;
}

message DeclineRedeemMoneyRequest {
    Identity identity = 1;
    bool manual = 3;
    bool doNotSendEmail = 4;
    bool toReset = 5;
    bool allowResetOfApproved = 6;
    ApiError apiError = 7;
    string reason = 8;
    string declinedByAgent = 9;
    bool isSuperAgent = 10;
    oneof type {
        uam.api.v1.WithdrawIdentityById byId = 11;
        uam.api.v1.WithdrawIdentityByCode byCode = 12;
        uam.api.v1.WithdrawIdentityByTransactionId byTransactionId = 13;
    }
    bool declinedByPaymentProvider = 14;
    optional string comment = 15;
    bool declinedByRiskStatusRejecting = 16;
    bool isNonCompliance = 17;
}

message DeclineRedeemMoneyResponse {
    bool applied = 1;
    string brand = 3;
}

message LockRedeemMoneyRequest {
    Identity identity = 1;
    int64 id = 2;
    string lockedByAgent = 3;
    optional string comment = 4;
}

message LockRedeemMoneyResponse {
    bool applied = 1;
}

message UnlockRedeemMoneyRequest {
    Identity identity = 1;
    int64 id = 2;
    string unlockedByAgent = 3;
    optional string comment = 4;
}

message UnlockRedeemMoneyResponse {
    bool applied = 1;
}

message RedeemLimitPolicy {
    string brand = 1;
    string provider = 2;
    string currency = 3;
    double maxAmount = 4;
}

message SaveRedeemLimitPolicyRequest {
    RedeemLimitPolicy policy = 1;
}

message SaveRedeemLimitPolicyResponse {
}

message GetRedeemLimitPolicyRequest {
    string brand = 1;
    string provider = 2;
    string currency = 3;
}

message GetRedeemLimitPolicyResponse {
    RedeemLimitPolicy policy = 1;
}

message ApiError {
    string type = 1;
    string code = 2;
    string message = 3;
    string exceptionClass = 4;
}


message GetAggregatedPaymentInfoRequest {
    Identity identity = 1;
    int32 periodOfDays = 2;
}

message GetAggregatedPaymentInfoResponse {
    string sumOfPurchasesForPeriod = 1;
    int64 depositCountForAllHistory = 2;
}

message SaveOrderFraudInfoRequest {
    string orderSn = 1;
    string fraudCode = 2;
    int64 fraudDate = 3;
    optional int64 tc40ReceivedAt = 4;
}

message SaveOrderFraudInfoResponse {
}

message GetBlockedPaymentMethodsRequest {
    Identity identity = 1;
}

message GetBlockedPaymentMethodsResponse {
    repeated BlockedPaymentMethod data = 1;
}

message GetCardPaymentMethodsRequest {
    Identity identity = 1;
}

message GetCardPaymentMethodsResponse {
    repeated PaymentMethodInfoData info = 1;
}

message PaymentMethodInfoData {
    string provider = 1;
    string bin = 2;
    string lastFour = 3;
    string fingerprint = 4;
    string verificationStatus = 5;
    string binLevel = 6;
    int64 createdAt = 7;
    optional Secure3dAction secure3dAction = 8;
    int64 id = 9;
}

message RefundTransactionVerifyRequest {
    string orderSn = 1;
}

message SetRefundInfoRequest {
    .uam.api.v1.internal.OfferRefundEvent event = 1;
}

message SetRefundInfoResponse {
}

message SetChargebackInfoRequest {
    string orderSn = 1;
    string status = 2;
    string agentName = 4;
    int64 date = 6;
    string reason = 7;
    string externalId = 8;
    string providerStatus = 9;
    string ethocaId = 10;
    string ethocaAlertType = 11;
    bool skipLabel = 12;
    bool skipChargebackEvent = 13;
    string reasonCode = 14;
}

message SetChargebackInfoResponse {
}

message RefundTransactionVerifyResponse {
    string transactionId = 1;
    string amount = 2;
    string currency = 3;
    bool refundable = 4;
    string orderSn = 5;
}

message PaymentOrderInfo {
    int64 id = 1;
    string transactionId = 2;
    string code = 3;
    string provider = 4;
    string status = 5;
    bool success = 6;
    string currency = 7;
    string amount = 8;
    int64 createdAt = 9;
    OfferTemplateInfo offer = 10;
    string orderSn = 11;
    .api.v1.Date at = 14;
    bool sweepstake = 16;
    string description = 17;
    string orderType = 18;
    string platform = 19;
    int64 accountId = 22;
    int64 modifiedAt = 23;
    string userAgent = 24;
    string paymentMode = 25;
    bool isFistDeposit = 26;
    string failReason = 27;
    optional CryptoPurchaseData cryptoPaymentData = 28;
    optional string goldCoins = 29;
    optional string sweepsTakeCoins = 30;
    OrderStatusSpec.Enum statusSpec = 31;
    bool refunded = 32;
}

message OrderStatusSpec {
    enum Enum {
        CREATED = 0;
        PENDING = 1;
        SUCCESS = 2;
        FAILED = 3;
    }
}

message OfferPurchaseNotification {
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 16;
    OfferTemplateInfo offer = 2;
    string transactionId = 5;
    string currency = 6;
    string provider = 7;
    string amount = 8;
    int64 firstDeposit = 9;
    bool isFirstDeposit = 11;
    string city = 12;
    string zip = 13;
    string billingDescriptor = 14;
    payment.api.v1.PurchaseMethod method = 15;
}

message OfferDeclineNotification {
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 11;
    OfferTemplateInfo offer = 2;
    string provider = 3;
    string reason = 4;
    string code = 5;
    string transactionId = 7;
    string tempToken = 8;
    string errCode = 9;
    PaymentOrderError error = 10;
}

message OfferTemplateInfo {
    int64 id = 1;
    string code = 2;
    string title = 3;
    string price = 4;
    string goldAmount = 5;
    string goldFirstAmount = 6;
    string sweepstakeAmount = 7;
    string sweepstakeFirstAmount = 8;
    int32 freeSpins = 9;
    string vipPoints = 10;
    int32 vipLevel = 11;
    string offerType = 12;
    repeated string tags = 13;
    string bannerImageUrl = 14;
    string popUpImageUrl = 15;
    repeated string vipLevels = 16;
    string segment = 17;
    repeated string segmentTags = 18;
    string type = 19;
    int32 priority = 20;
    bool showStickybar = 21;
    string specialOfferImageURL = 22;
    string oldPrice = 23;
    string baseOldPrice = 30;
    bool showTimeLeft = 24;
    int64 endAt = 25;
    string currency = 26;
    string basePrice = 27;
    string baseCurrency = 28;
    OfferTemplateInfo upgradeOffer = 29;
    string externalRewardCode = 31;
    string iconImageURL = 32;
    string backgroundImageURL = 33;
    string backgroundBorder = 34;
    string platform = 35;
    string homepageBannerImageURL = 36;
}

message CreateAccountPaymentMethodResponse {
}

message CreateAccountPaymentMethodRequest {
    Identity identity = 1;
    string token = 2;
    PaymentMethodType paymentMethodType = 3;
    CardData cardData = 4;
}


message CardData {
    string firstName = 1;
    string lastName = 2;
    string firstSixDigits = 3;
    string firstEightDigits = 10;
    string lastFour = 4;
    int32 month = 5;
    int32 year = 6;
    string cardType = 7;
    // Fingerprint is optional. Not used in Apple and Google pay
    string fingerprint = 8;
    BillingAddress billingAddress = 9;
}

message CreatePayperPaymentMethodRequest {
    Identity identity = 1;
    string email = 2;
    string phone = 3;
}

message CreatePayperPaymentMethodResponse {
    string token = 1;
}

enum PaymentMethodType {
    SPREEDLY_GATEWAY = 0;
    SPREEDLY_GATEWAY_APPLE_PAY = 1;
    SPREEDLY_GATEWAY_GOOGLE_PAY = 2;
}

enum Secure3dAction {
    FORCE = 0;
    SKIP = 1;
    NOT_APPLY = 2;
}
message UpdatePaymentMethodMetaInfoRequest {
    int64 paymentMethodId = 1;
    optional Secure3dAction secure3dAction = 2;
}

message UpdateAccountsPaymentMethodMetaInfoRequest {
    repeated UpdatePaymentMethodMetaInfoRequest paymentMethodUpdates = 1;
}

message SetAccountPaymentSettingsRequest {
    Identity identity = 1;
    optional Secure3dAction secure3dAction = 2;
    optional bool rtpEnabled = 3;
    string modifiedBy = 4;
}

message UpdateDynamicSecure3dCheckRequest {
    DynamicSecure3dCheckData data = 1;
}

message MassUpdateDynamicSecure3dCheckRequest {
    repeated DynamicSecure3dCheckData data = 1;
}

message MassUpdateDynamicSecure3dCheckResponse {
}

message GetDynamicSecure3dCheckRequest {

}
message GetDynamicSecure3dCheckResponse {
    repeated DynamicSecure3dCheckData data = 1;
}

message DynamicSecure3dCheckData {
    optional int64 id = 1;
    string brand = 2;
    optional Secure3dAction secure3dAction = 3;
    string condition = 4;
    repeated string countries = 5;
    bool allowHighFraudScore = 6;
}

message SetAccountPaymentMethodStatusRequest {
    Identity identity = 1;
    repeated string identifiers = 2;
    string status = 3;
}

message SetAccountPaymentMethodStatusResponse {
    bool applied = 1;
}

message GetAccountPaymentSettingsRequest {
    Identity identity = 1;
}

message WithdrawSyncRequest {
    Identity identity = 1;
    int64 withdrawMoneyRequestId = 2;
}

message GetCardsAggregationRequest {
    Identity identity = 1;
    int64 fromTimestamp = 2;
}

message GetCardsAggregationResponse {
    repeated CardAggregation card = 2;
}

message CardAggregation {
    string fingerprint = 1;
    string cardBin = 2;
    string lastFour = 3;
    .api.v1.Date createdAt = 4;
    int32 purchaseCount = 5;
    string purchaseAmount = 6;
}

message GetPaymentServiceBankDetailsRequest {
    uam.api.v1.Identity identity = 1;
}

message GetPaymentServiceBankDetailsResponse {
    bool success = 1;
    string errorMsg = 2;
    repeated LinkBankAccountResponse bankAccounts = 3;
}

message GetWidgetRequest {
    uam.api.v1.Identity identity = 1;
}

message GetWidgetResponse {
    string widgetFastlinkURL = 1;
    string widgetToken = 2;
    string widgetUsername = 3;
}

message ConfirmPaymentUserAccountRequest {
    Identity identity = 1;
    string code = 2;
}

message ConfirmPaymentUserAccountResponse {
    string errorMsg = 1;
    repeated LinkBankAccountResponse bankAccounts = 2;
}

message CryptoCurrency {
    enum Enum {
        DO_NOT_CHANGE_CURRENCY = 0;
        USDT = 1;
        USDC = 2;
        ETH = 3;
        BTC = 4;
        LTC = 5;
        BCH = 6;
        TST = 7;
        EOS = 8;
        CRO = 9;
        APE = 10;
        UNI = 11;
        POL = 12;
        XRP = 13;
    }
}

message CryptoNetwork {
    enum Enum {
        DO_NOT_CHANGE_NETWORK = 0;
        BTC = 1;
        ETH = 2;
        BNB = 3;
        LTC = 4;
        TRX = 5;
        POLYGON = 6;
        SOL = 7;
        BCH = 8;
        AVAX = 9;
        TETH = 10;
        TON = 11;
    }
}

message SetOfferApprovalStatusRequest {
    string brandName = 1;
    string code = 2;
    string comment = 3;
    ApprovalStatus status = 4;
    string approver = 5;
}

message SetOfferActiveStatusRequest {
    string brandName = 1;
    string code = 2;
    bool inactive = 3;
}

message SetOfferApprovalStatusResponse {
}

message SetOfferActiveStatusResponse {
}

message RegisterPaymentUserAccountRequest {
    Identity identity = 1;
    string phone = 2;
}

message RegisterPaymentUserAccountResponse {
    bool otpRequired = 1;
    string widgetFastlinkURL = 2;
    string widgetToken = 3;
    string widgetUsername = 4;
}

message LinkBankAccountRequest {
    Identity identity = 1;
    string userId = 2;
    string userPassword = 3;
    string phone = 4;
    string transactionId = 5;
}

message LinkBankAccountResponse {
    string bankName = 1;
    string accountNumber = 2;
    bool selected = 3;
    string accountId = 4;
}

message AppMetadata {
    string platform = 1;
    string appName = 2;
    string appVersion = 3;
}

enum ApprovalStatus {
    SENT_FOR_APPROVAL = 0;
    APPROVED = 1;
    REJECTED = 2;
    DRAFT = 3;
    AUTO_APPROVED = 4;
}

message MassUpdateAccount3dsRequest {
    repeated int64 accountIds = 1;
    optional Secure3dAction secure3dAction = 2;
    string modifiedBy = 3;
}

message MassUpdateAccount3dsResponse {
    repeated int64 notFoundAccountIds = 1;
}
