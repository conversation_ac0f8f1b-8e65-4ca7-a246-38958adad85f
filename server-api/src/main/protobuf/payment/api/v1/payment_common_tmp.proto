syntax = "proto3";

package payment.api.v1;

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message OfferTemplateAdminInfo {
  string code = 1;
  string title = 2;
  double price = 3;
  string type = 4;
  int64 startAt = 5;
  int64 endAt = 6;
  double goldAmount = 7;
  double goldFirstAmount = 8;
  double sweepstakeAmount = 9;
  double sweepstakeFirstAmount = 10;
  string segment = 11;
  repeated string segmentTags = 12;
  string excludeSegment = 24;
  repeated string excludeSegmentTags = 25;
  repeated string vipLevels = 13;
  double vipPoints = 14;
  repeated string tags = 15;
  string bannerImageUrl = 16;
  string popUpImageUrl = 17;
  bool inactive = 18;
  repeated string rules = 19;
  int32 vipLevel = 20;
  int64 id = 21;
  bool applicable = 22;
  int32 priority = 23;
  string platform = 28;
  bool showStickybar = 27;
  string specialOfferImageURL = 29;
  repeated string xpLevels = 26;
  double oldPrice = 30;
  bool showTimeLeft = 31;
  int64 minWeeklyWageredGoldCoins = 32;
  int64 capacityPerPlayer = 33;
  int64 capacityPerOffer = 34;
  repeated string countries = 35;
  string upgradeCode = 36;
  bool inboxNotification = 37;
  string externalRewardCode = 38;
  string iconImageURL = 39;
  string backgroundImageURL = 40;
  string backgroundBorder = 41;
  string approvalStatus = 42;
  string creator = 43;
  string approver = 44;
  string approverComment = 45;
  int64 approverActionedAt = 46;
  string welcomeOfferType = 47;
  string utmSource = 48;
  string utmMedium = 49;
  string utmCampaign = 50;
  string utmContent = 51;
  string utmTerm = 52;
  string affiliateCxd = 53;
  string referralCode = 54;
  string homepageBannerImageURL = 55;
  int32 freeSpins = 56;
}

message PaymentAccountInfo {
  int64 lastPurchase = 1;
  int64 totalPurchases = 2;
  int64 totalDepositAmount = 3;
}
