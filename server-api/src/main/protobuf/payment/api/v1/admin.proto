syntax = "proto3";

package payment.api.v1;

import "api/v1/obfuscation.proto";
import "api/v1/identity.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message InvalidateCacheRequest {
  bool preserveSimple = 1;
}

message InvalidateCacheResponse {
}

message RemoteAdminSetAccountPaymentMethodStatusRequest {
  uam.api.v1.Identity identity = 1;
  int64 recordId = 2;
  string status = 3;
}

message RemoteAdminSetAccountPaymentMethodStatusResponse {
  bool success = 1;
}

// Messages for adding a Spreedly receiver
message RegisterProviderInSpreedlyRequest {
  string brandName = 1;
  string accessKey = 2;
  string secretKey = 3;
  string integrationType = 4;
  bool isDev = 5;
}

message RegisterProviderInSpreedlyResponse {
  string token = 1;
  string login = 2;
  string pass = 3;
}
