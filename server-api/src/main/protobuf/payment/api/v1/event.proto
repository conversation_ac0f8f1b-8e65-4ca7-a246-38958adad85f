syntax = "proto3";

package payment.api.v1;

import "api/v1/routing.proto";
import "payment/api/v1/routing.proto";
import "payment/api/v1/payment_common.proto";
import "api/v1/common.proto";
import "payment/api/v1/payment_method.proto";
import "payment/api/v1/internal.proto";
import "uam/api/v1/event_stream.proto";
import "api/v1/identity.proto";


option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message PaymentOrderCreatedEvent {
    int64 paymentOrderId = 2;
    string offerCode = 3;
    string amount = 4;
    string provider = 5;
}

message PaymentOrderCompletedEvent {
    int64 paymentOrderId = 1;
    string offerCode = 3;
    string amount = 4;
    string baseAmount = 25;
    string scAmount = 5;
    string provider = 6;
    string fraudRequestId = 7;
    bool success = 8;
    string status = 9;
    string gcAmount = 10;
    string currency =11;
    int64 completedAt = 12;
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 29;
    uam.api.v1.PaymentOrderError error = 14;
    bool firstPurchase = 15;
    bool firstOfferPurchase = 16;
    payment.api.v1.PaymentMethodType paymentMethodType = 17;
    string fingerprint = 18;
    optional string sourceId = 19;
    string paymentOrderTransactionId = 20;
    string remoteIp = 21;
    string platform = 22;
    AccountInfo accountInfo = 23;
    optional string lastFour = 24;
    int32 vipLevel = 26;
    string vipPoints = 27;
    string externalRewardCode = 28;
    int32 freeSpins = 30;
}

message PromotionNotificationStatusEvent {
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 1;
    bool notificationEnabled = 2;
    int64 calculatedAt = 3;
}

message AccountInfo {
    string accountMode = 1;
    optional uam.api.v1.internal.AccountPaymentRoutingInfo paymentRoutingInvitedBy = 3;
}

message RedeemConfirmEvent {
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 3;
    uam.api.v1.RedeemMoneyInfo redeem = 2;
}

message RedeemPreConfirmEvent {
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 3;
    uam.api.v1.RedeemMoneyInfo redeem = 2;
}

message RedeemStatusUpdateEvent {
    uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 4;
    uam.api.v1.RedeemMoneyInfo redeem = 2;
    string prevStatus = 3;
}

message GetPurchaseTrackEventsRequest {
    int64 accountId = 1;
    .api.v1.Date accountSignUpDate = 2;
}

message GetPurchaseTrackEventsResponse {
    repeated string codes = 1;
}

message AddPurchaseTrackEventRequest {
    int64 accountId = 1;
    string code = 2;
}

message AddPurchaseTrackEventResponse {
}

message PurchaseSuccessEvent {
    payment.api.v1.internal.AccountPaymentInfo accountPaymentInfo = 3;
    payment.api.v1.internal.OrderInfo orderInfo = 4;
    payment.api.v1.internal.OfferInfo offerInfo = 5;
    payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 7;
    repeated string rewardCodes = 8;
}

message PurchaseDeclineEvent {
    payment.api.v1.internal.OrderInfo orderInfo = 2;
    payment.api.v1.internal.OfferInfo offerInfo = 3;
    payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 4;
}

message AccountPaymentMethodUpdateEvent {
    uam.api.v1.Identity identity = 1;
    repeated PaymentMethodInfo paymentMethodInfo = 2;
    string agentName = 3;
    string hash = 5;
}

message PaymentMethodInfo {
    string fingerprint = 1;
    string cardBin = 2;
    string lastFour = 3;
    string status = 4;
    string methodType = 5;
    string code = 6;
}

message ChargebackEvent {
    string email = 2;
    string agentName = 3;
    string brand = 4;
    string status = 5;
    string orderSn = 7;
    string cardFingerprint = 8;
    int64 accountId = 9;
    string hash = 10;
    bool noChbBefore = 11;
    bool isOfferPurchase = 12;
    string orderTransactionId = 13;
    int64 offerId = 14;
    string externalRewardCode = 15;
    bool isAlert = 16;
    string amount = 17;
    int64 orderCreatedAt = 18;
    int64 chargebackAt = 19;
    string cardBrand = 20;
}

message PaymentMethodBlacklistEvent {
    string orderSn = 2;
    string fingerprint = 3;
    string billingDetails = 4;
    string remoteIp = 5;
    int64 fraudResponseId = 6;
    payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 7;
}

message AccountPossibleLockEvent {
    int64 at = 2;
    string reason = 3;
    bool lockAutomatically = 4;
    payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 5;
}

message WithdrawMoneyRiskStatusUpdatedEvent {
    payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 1;
    uam.api.v1.RedeemRiskStatus riskStatus = 2;
}