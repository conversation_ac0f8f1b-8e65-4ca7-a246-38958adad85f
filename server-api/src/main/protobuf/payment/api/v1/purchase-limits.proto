syntax = "proto3";

package payment.api.v1;

import "api/v1/common.proto";
import "api/v1/identity.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message GetAccountPurchaseLimitRequest {
    uam.api.v1.Identity identity = 1;
}

message GetAccountPurchaseLimitsRequest {
    uam.api.v1.Identity identity = 1;
}

message GetAccountPurchaseLimitResponse {
    string limitAmount = 3;
    string limitPeriod = 4;
    string limitAvailable = 5;
    .api.v1.Date limitEnd = 6;
    .api.v1.Date limitReset = 7;
}

message AccountPurchaseLimit {
    string limitAmount = 3;
    string limitPeriod = 4;
    .api.v1.Date limitEnd = 6;
    .api.v1.Date limitReset = 7;
}

message ConsumedLimit {
    string limitPeriod = 1;
    string limitConsumed = 2;
    string limitAmount = 3;
    .api.v1.Date limitEnd = 6;
    .api.v1.Date limitReset = 7;
}

message AccountDeferredPurchaseLimit {
    string limitAmount = 1;
    string limitPeriod = 2;
    string type = 3;
    .api.v1.Date startAt = 4;
}

message GetAccountPurchaseLimitsResponse {
    repeated AccountPurchaseLimit internalLimits = 1;
    repeated AccountPurchaseLimit memberRequestLimits = 2;
    repeated ConsumedLimit consumedLimits = 3;
    repeated AccountDeferredPurchaseLimit deferredRequestLimits = 4;
}

message SetAccountPurchaseLimitRequest {
    uam.api.v1.Identity identity = 1;
    string threshold = 2;
    string period = 3;
    bool inactive = 4;
    string reason = 6;
    .api.v1.Date limitEnd = 7;
    string agentName = 8;
}

message SetAccountPurchaseLimitResponse {
    int64 limitId = 1;
    int64 deferredLimitId = 2;
}

message SetAccountPurchaseLimitsRequest {
    uam.api.v1.Identity identity = 1;
    repeated AccountPurchaseLimitRequest accountPurchaseLimit = 2;
    string agentName = 3;
    bool notDeferrable = 4;
}

message AccountPurchaseLimitRequest {
    string threshold = 1;
    string period = 2;
    string reason = 3;
    bool inactive = 4;
    .api.v1.Date limitEnd = 5;
}

message SetAccountPurchaseLimitsResponse {
    repeated AccountPurchaseLimitResponse accountPurchaseLimit = 1;
    repeated AccountPurchaseLimitResponse accountDeferredPurchaseLimit = 2;
}

message AccountPurchaseLimitResponse {
    int64 limitId = 1;
    string period = 2;
}

message DeactivateAccountPurchaseLimitRequest {
    uam.api.v1.Identity identity = 1;
    string reason = 2;
    string agentName = 3;
    bool notDeferrable = 4;
}

message DeactivateAccountPurchaseLimitResponse {
    repeated string limitIds = 1;
    repeated string deferredLimitIds = 2;
}

message ResetAccountPurchaseLimitRequest {
    uam.api.v1.Identity identity = 1;
    string reason = 2;
}

message ResetAccountPurchaseLimitResponse {
    repeated string limitIds = 1;
}

message ApplyAccountDeferredPurchaseLimitRequest {
    uam.api.v1.Identity identity = 1;
    repeated int64 deferredLimitIds = 2;;
}

message ApplyAccountDeferredPurchaseLimitResponse {
    bool applied = 1;
}
