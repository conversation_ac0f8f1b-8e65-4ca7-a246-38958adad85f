syntax = "proto3";
package payment.api.v1;

import "api/v1/routing.proto";
import "payment/api/v1/routing.proto";
import "payment/api/v1/payment_common.proto";
import "payment/api/v1/internal.proto";
import "payment/api/v1/payment.proto";
import "payment/api/v1/payment_method.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message UpdatePaymentMethodsNotification {
  string brand = 1;
  oneof provider {
    uam.api.v1.PaymentProvider payment = 2;
    uam.api.v1.RedeemProvider redeem = 3;
  }
}

message RedeemAuthorizedNotification {
  uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 6;
  int64 id = 2;
  string provider = 3;
  bool kyc = 4;
  string kycStatus = 5;
}
message InboxNotificationsUpdatedNotification {
  uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 2;
}

message PurchaseSuccessNotification {
  uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 15;
  string city = 8;
  string zip = 9;
  string billingDescriptor = 10;
  string balance = 11;
  payment.api.v1.internal.OrderInfo orderInfo = 12;
  payment.api.v1.internal.AccountPaymentInfo accountPaymentInfo = 13;
  payment.api.v1.PurchaseMethod method = 14;
}

message PurchaseRefreshNotification {
  uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 1;
  string provider = 2;
  string code = 3;
  string transactionId = 4;
  string tempToken = 5;
  uam.api.v1.OfferTemplateInfo offer = 6;
}

message PurchaseDeclineNotification {
  uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 14;
  string provider = 2;
  string reason = 3;
  string code = 4;
  string transactionId = 5;
  string tempToken = 6;
  string errCode = 7;
  string amount = 8;
  string currency = 9;
  string description = 10;
  payment.api.v1.PurchaseMethod method = 11;
  string totalDepositAmount = 12;
  uam.api.v1.PaymentOrderError error = 13;
}

message RewardOfferUpdateNotification {
  uam.api.v1.internal.AccountPaymentRoutingInfo routing = 1;
  uam.api.v1.OfferTemplateInfo offer = 2;
  int32 maxPurchaseCount = 3;
  optional int64 expireAt = 4;
  OfferRewardStatus status = 5;
}

message CancelAllRedeemMoneyNotification {
  uam.api.v1.internal.AccountPaymentRoutingInfo routing = 1;
  repeated int64 redeemIds = 2;
  repeated int64 cancelledIds = 3;
  CancellationType eventType = 4;
  string eventMessage = 5;
}

enum CancellationType {
  ALL_CANCELLED = 0;
  PARTIAL_CANCELLED = 1;
  NONE_CANCELLED = 2;
  ERROR = 3;
}
