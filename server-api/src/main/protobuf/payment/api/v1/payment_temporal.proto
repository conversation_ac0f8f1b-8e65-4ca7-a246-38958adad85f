syntax = "proto3";

package payment.api.v1.internal;

import "api/v1/obfuscation.proto";
import "api/v1/identity.proto";
import "uam/api/v1/account.proto";
import "payment/api/v1/payment.proto";
import "payment/api/v1/payment_common.proto";
import "payment/api/v1/payment_event_stream.proto";
import "fraud/api/v1/fraud_common.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message GetAccountPersonalInfoInput {
  uam.api.v1.Identity identity = 1;
  string routingKey = 2;
}

message GetAccountPersonalInfoOutput {
  uam.api.v1.GetAccountPersonalInfoResponse reply = 1;
}

message GetAccountPaymentInfoInput {
  uam.api.v1.Identity identity = 1;
  string routingKey = 2;
}

message GetAccountPaymentInfoOutput {
  uam.api.v1.GetAccountPaymentInfoResponse reply = 1;
}

message SaveSpreedlyPaymentMethodInput {
  uam.api.v1.Identity identity = 1;
  AddSpreedlyPaymentMethodOutput paymentMethod = 2;
  int64 timestamp = 3;
}

message SaveSpreedlyPaymentMethodOutput {
  string code = 1;
}

message AddSpreedlyGooglePayPaymentMethodInput {
  GetAccountPersonalInfoOutput accountInfo = 1;
  TokenizeGooglePayPaymentMethodRequest tokenizeRequest = 2;
}

message AddSpreedlyApplePayPaymentMethodInput {
  string brand = 1;
  string currency = 2;
  GetAccountPaymentInfoOutput paymentInfo = 3;
  TokenizeApplePayPaymentMethodRequest tokenizeRequest = 4;
}

message AddSpreedlyPaymentMethodOutput {
  string token = 1 [(.api.v1.sensitive_mode).sensitive = true];
  uam.api.v1.PaymentMethodType paymentMethodType = 2;
  uam.api.v1.CardData cardData = 3;
}

message GetAccountInternalInfoInput {
  uam.api.v1.Identity identity = 1;
  string routingKey = 2;
}

message GetAccountInternalInfoOutput {
  uam.api.v1.GetAccountPersonalInfoResponse personalInfo = 1;
  uam.api.v1.GetAccountEngagementInfoResponse engagementInfo = 2;
  bool kyc = 3;
  fraud.api.v1.KYCInfo kycInfo = 4;
}
