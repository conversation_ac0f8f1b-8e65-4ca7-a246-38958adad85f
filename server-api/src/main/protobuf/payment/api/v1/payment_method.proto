syntax = "proto3";

package payment.api.v1;

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

enum PaymentMethodType {
  SPREEDLY_GATEWAY = 0;
  SPREEDLY_GATEWAY_APPLE_PAY = 1;
  SPREEDLY_GATEWAY_GOOGLE_PAY = 2;
  PAY_WITH_MY_BANK = 3;
  TRUSTLY = 4;
  FISERV_CARD = 5;
  FISERV_GOOGLE_PAY = 6;
  FISERV_APPLE_PAY = 7;
  NUVEI_GATEWAY = 8;
  SKRILL = 9;
  APPLE_IN_APP = 10;
  PAYPER = 11;
  ANDROID_IN_APP = 12;
  CRYPTO = 13;
  AEROPAY = 14;
}

message PurchaseMethod {
  //PaymentMethodType
  string paymentMode = 1;
  string integrationType = 2;
  string cardBrand = 3;
  string cardNumberMask = 4;
  string accountNumberMask = 5;
  string email = 6;
}
