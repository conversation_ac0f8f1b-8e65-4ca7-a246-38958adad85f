syntax = "proto3";

package payment.api.v1;

import "api/v1/obfuscation.proto";
import "api/v1/identity.proto";
import "api/v1/common.proto";
import "payment/api/v1/payment_common_tmp.proto";
import "payment/api/v1/purchase-limits.proto";
import "payment/api/v1/payment_common.proto";
import "payment/api/v1/payment_method.proto";
import "uam/api/v1/wallet.proto";
import "payment/api/v1/routing.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message ValidateApplePayMerchantRequest {
    uam.api.v1.Identity identity = 1;
    string validationUrl = 2;
    string homePageUrl = 3;
}

message ValidateApplePayMerchantResponse {
    string validationResponse = 1;
}

message TokenizeApplePayPaymentMethodRequest {
    string brand = 1;
    string applePayTokenRaw = 2 [(.api.v1.sensitive_mode).sensitive = true];
    BillingAddress billingDetails = 3;
    uam.api.v1.Identity identity = 4;
}

message TokenizeApplePayPaymentMethodResponse {
    string token = 1;
}

message BillingAddress {
    string country = 1;
    string stateOrProvince = 2;
    string city = 3;
    string postalCode = 4;
    string address1 = 5 [(.api.v1.sensitive_mode).sensitive = true];
    string address2 = 6 [(.api.v1.sensitive_mode).sensitive = true];
    string firstName = 7;
    string lastName = 8;
}

message TokenizeGooglePayPaymentMethodRequest {
    string googlePayTokenRaw = 1 [(.api.v1.sensitive_mode).sensitive = true];
    string brand = 2;
    string city = 3;
    string zip = 4;
    uam.api.v1.Identity identity = 5;
    string country = 6;
    string state = 7;
    string address1 = 8;
    string address2 = 9;
    string fullName = 10;
}

message TokenizeGooglePayPaymentMethodResponse {
    string token = 1;
}

message AddOrUpdateRoutingErrorConfigRequest {
    RoutingErrorConfig routingError = 1;
}

message AddOrUpdateRoutingErrorConfigResponse {
}

message RoutingErrorConfig {
    int32 id = 1;
    string brand = 2;
    string errorType = 3;
    string providerType = 4;
    string integrationType = 5;
    string provider = 6;
    string errorCode = 7;
    string errorDescription = 8;
    string retryCondition = 9;
    repeated string countries = 10;
    string failPolicy = 11;
    bool inactive = 12;
}

message DeleteRoutingErrorConfigRequest {
    int32 id = 1;
}

message DeleteRoutingErrorConfigResponse {
}

message ErrorMapping {
    string errorCode = 1;
    string description = 2;
    string type = 3;
    string message = 4;
    string mapped_error_code = 5;
    int32 id = 6;
}

message DeleteErrorMappingRequest {
    string errorCode = 1;
    string type = 3;
}

message DeleteErrorMappingResponse {
}

message AddOrUpdateErrorMappingRequest {
    ErrorMapping errorMapping = 1;
}

message AddOrUpdateErrorMappingResponse {
}

message GetErrorMappingRequest {
    string errorCode = 1;
    string type = 2;
}

message GetErrorMappingResponse {
    ErrorMapping errorMapping = 1;
}

message GetRoutingConfigInfoRequest {
    string brand = 1;
    string type = 2;
    string member = 3;
}

message GetRoutingConfigInfoResponse {
    string id = 1;
    string currentParent = 2;
    string sequence = 3;
    string weight = 4;
    bool isActive = 5;
    repeated string parents = 6;
}

message AddOrUpdateRoutingMemberRequest {
    string id = 1;
    string brand = 2;
    string type = 3;
    string member = 4;
    string parent = 5;
    string sequence = 6;
    string weight = 7;
    bool isActive = 8;
}

message AddOrUpdateRoutingMemberResponse {
}

message GetPaymentProviderRequest {
    string code = 1;
    string brand = 2;
    string type = 3;
}

message GetPaymentProviderResponse {
    string id = 1;
    string code = 2;
    string brand = 3;
    string type = 4;
    string displayName = 5;
    string description = 6;
    string billingDescription = 8;
    bool isActive = 7;
    string integrationType = 9;
    repeated string countries = 10;
    bool skipFirstOrder = 11;
}

message GetAccountProviderBlackListRequest {
    int64 accountId = 1;
}

message GetAccountProviderBlackListResponse {
    repeated string withdrawalProviders = 1;
    repeated string purchaseProviders = 2;
}

message SetAccountProviderBlackListRequest {
    int64 accountId = 1;
    repeated string withdrawalProviders = 2;
    repeated string purchaseProviders = 3;
    string comments = 4;
    string updatedBy = 5;
}

message SetAccountProviderBlackListResponse {
}

message AddOrUpdateTransactionLimitRequest {
    string id = 1;
    bool inactive = 2;
    string brand = 3;
    repeated string countries = 4;
    string currency = 5;
    string type = 6;
    string min = 7;
    string max = 8;
}

message AddOrUpdateTransactionLimitResponse {

}

message AddOrUpdatePaymentProviderRequest {
    string id = 1;
    string code = 2;
    string brand = 3;
    string type = 4;
    string displayName = 5;
    string description = 6;
    string billingDescriptor = 8;
    bool isActive = 7;
    string integrationType = 9;
    repeated string countries = 10;
    bool skipFirstOrder = 11;
}

message AddOrUpdatePaymentProviderResponse {
}

message AddOrUpdateRoutingRuleRequest {
    string id = 1;
    string ruleType = 2;
    string routingType = 3;
    string priority = 4;
    string brand = 5;
    repeated string countries = 6;
    string currency = 7;
    repeated string integrationTypes = 8;
    repeated string providerCodes = 9;
    string ruleId = 10;
    string condition = 11;
    string description = 12;
    bool active = 13;
    bool brandSpecific = 14;
}

message AddOrUpdateRoutingRuleResponse {

}

message SetPaymentProviderActivityRequest {
    string brand = 1;
    string code = 2;
    bool inactive = 3;
}

message SetPaymentProviderActivityResponse {
}

message GetAllOffersTemplatesRequest {
    string brandName = 1;
}

message GetAllOffersTemplatesResponse {
    repeated payment.api.v1.OfferTemplateAdminInfo allOffers = 1;
}

message GetOfferTemplateListRequest {
    string brandName = 1;
    repeated string code = 2;
}

message GetOfferTemplateListResponse {
    repeated payment.api.v1.OfferTemplateAdminInfo offerTemplate = 1;
}

message GetUnknownUserOfferTemplatesRequest {
    string brandName = 1;
    uam.api.v1.Identity identity = 2;
}

message GetUnknownUserOfferTemplatesResponse {
    repeated uam.api.v1.OfferTemplateInfo filteredOffers = 1;
}

message GetAccountOffersTemplatesRequest {
    string brandName = 1;
    uam.api.v1.Identity identity = 2;
}

message GetAccountOffersTemplatesResponse {
    repeated uam.api.v1.OfferTemplateInfo offers = 1;
    repeated uam.api.v1.OfferTemplateInfo appliedOffers = 2;
    uam.api.v1.OfferTemplateInfo lastPurchasedOffer = 3;
    bool coinStoreViewExperimentGroup = 4;
}

message GetAccountOfferTemplateForPurchaseRequest {
    string brandName = 1;
    string code = 2;
    uam.api.v1.Identity identity = 3;
}

message GetAccountOfferTemplateForPurchaseResponse {
    GetAccountPurchaseLimitResponse limits = 6;
}

message GetPaymentMetaInfoRequest {
    uam.api.v1.Identity identity = 1;
}

message GetPaymentMetaInfoResponse {
    PaymentMetaInfo paymentMetaInfo = 1;
}

message GetUsedCardsRequest {
    uam.api.v1.Identity identity = 1;
    bool includeVerificationStatus = 2;
}

message GetUsedCardsResponse {
    repeated UsedCard card = 1;
}

message GetVolumeAllocationConfigRequest {
    string brand = 1;
    string country = 2;
    string currency = 3;
    string integrationType = 4;
    string paymentMode = 6;
}

message AddOrUpdateRescueProviderRequest {
    optional int64 id = 1;
    optional string brand = 2;
    repeated string countries = 3;
    repeated string excludeErrorCodes = 4;
    PaymentMethodType paymentMethodType = 5;
    repeated uam.api.v1.PaymentProvider rescueProviders = 6;
    repeated string excludeInternalErrorCodes = 7;
}
message DeleteRescueProviderRequest {
    int64 id = 1;
}
message DeleteRescueProviderResponse {
}

message GetVolumeAllocationConfigResponse {
    string brand = 1;
    string country = 2;
    string currency = 3;
    string integrationType = 4;
    int32 allocation = 5;
    string paymentMode = 6;
    string provider = 7;
}

message DeleteVolumeAllocationConfigRequest {
    string brand = 1;
    string country = 2;
    string currency = 3;
    string integrationType = 4;
    string paymentMode = 6;
    string provider = 7;
}

message DeleteVolumeAllocationConfigResponse {
}

message AddVolumeAllocationConfigRequest {
    string brand = 1;
    string country = 2;
    string currency = 3;
    string integrationType = 4;
    int32 allocation = 5;
    string paymentMode = 6;
    string provider = 7;
}

message AddVolumeAllocationConfigResponse {
}

message UpdateVolumeAllocationConfigRequest {
    repeated VolumeAllocation volumeAllocations = 1;
}
message VolumeAllocation {
    int32 id = 1;
    int32 allocation = 2;
    string paymentMode = 3;
    string provider = 4;
    string integration = 5;
}
message UpdateVolumeAllocationConfigResponse {
    repeated VolumeAllocation volumeAllocations = 1;
}

message AddOrUpdateRescueProviderResponse {
}

message UsedCard {
    string fingerprint = 1;
    string cardBin = 2;
    string lastFour = 3;
    string type = 4;
    int64 createdAt = 5;
    optional string verificationStatus = 6;
}

message PaymentMetaInfo {
    int64 firstDeposit = 1;
    int64 lastDeposit = 2;
    int64 depositCount = 3;
    int64 withdrawCount = 4;
    string withdrawTotalAmount = 5;
    int64 firstWithdraw = 6;
    int64 firstSuccessfulPurchase = 7;
    int64 firstSuccessfulDeposit = 8;
    string lastSuccessPurchaseOfferCode = 9;
    string lastSuccessPurchasePrice = 10;
    int64 lastSuccessPurchaseDate = 11;
}

message DeclinePaymentOrderRequest {
    uam.api.v1.Identity identity = 1;
    string apiError = 3;
    string errMsg = 4;
    bool removeSavedCards = 5;
    string session = 6;

    oneof type {
        uam.api.v1.PaymentIdentityByTransactionId byTransactionId = 7;
        uam.api.v1.PaymentIdentityByCode byCode = 8;
    }

    uam.api.v1.PaymentOrderError error = 9;
    string code = 10;
}

message DeclinePaymentOrderResponse {
}

message UpdateScaTransactionsRequest {
    repeated string token = 1;
    string brand = 2;
}
message RefreshInboxNotificationsRequest {
    uam.api.v1.Identity identity = 1;
    bool sendNotifyEventIfDataUpdated = 2;
}

message RefreshInboxNotificationsResponse {
    bool updated = 1;
}
message UpdateInboxNotificationRequest {
    uam.api.v1.Identity identity = 1;
    InboxNotificationStatus status = 2;
    string token = 3;
}

message UpdateInboxNotificationResponse {
    InboxNotificationInfo notificationUpdated = 1;
}

message UpdateInboxNotificationOnOrderCompleteRequest {
     int64 orderId = 1;
     uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 2;
}

message UpdateInboxNotificationOnOrderCompleteResponse {
}

message UpdateInboxNotificationOnOrderRefreshRequest {
     int64 orderId = 1;
     uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 2;
}

message UpdateInboxNotificationOnOrderRefreshResponse {
}

message GetInboxNotificationsRequest {
    uam.api.v1.Identity identity = 1;
}

message GetInboxNotificationsResponse {
    repeated InboxNotificationInfo notifications = 1;
}

message InboxNotificationInfo {
    InboxNotificationStatus status = 1;
    string token = 2;
    int64 createdAt = 3;
    int64 expiresAt = 4;
    oneof data {
        uam.api.v1.OfferTemplateInfo offer = 5;
        uam.api.v1.PaymentOrderInfo cryptoPurchase = 6;
    }
}

enum InboxNotificationStatus {
    DO_NOT_CHANGE_STATUS = 0;
    UNREAD = 1;
    READ = 2;
    CLAIMED = 3;
    EXPIRED = 4;
    REMOVED = 5;
}

message ConfirmPaymentOrderRequest {
    uam.api.v1.Identity identity = 1;
    string tempToken = 2;
    string code = 3;
    bool capture = 4;
    string recurrentCode = 5;
    string session = 6;
    oneof type {
        uam.api.v1.PaymentIdentityByTransactionId byTransactionId = 7;
        uam.api.v1.PaymentIdentityByCode byCode = 8;
    }
    uam.api.v1.BillingAddress billingAddress = 9;
    oneof pspParams {
        uam.api.v1.NuveiMazoomaParams nuveiMazoomaParams = 10;
        uam.api.v1.PayperParams payperParams = 14;
        uam.api.v1.SkrillParams skrillParams = 15;
        uam.api.v1.AeroPayParams aeroPayParams = 18;
    }
    uam.api.v1.PaymentMethod data = 11;
    uam.api.v1.Verification verification = 12;
    string amount = 13;
    string merchantAdviceCode = 16;
    string tempCode = 17;
}

message ConfirmPaymentOrderResponse {
    string redirectUrl = 1;
    string mode = 2;
    string token = 3;
    string status = 4;
    string errorMsg = 5;
    string billingDescriptor = 6;
    uam.api.v1.OfferTemplateInfo offer = 7;
    string currency = 8;
    string provider = 9;
    string amount = 10;
    int64 firstDeposit = 11;
    bool isFirstDeposit = 12;
    string city = 13;
    string zip = 14;
}

message CapturePaymentOrderRequest {
    uam.api.v1.Identity identity = 1;
    string tempToken = 2;
    string code = 3;
    string recurrentCode = 9;
    string reference = 10;
    oneof type {
        uam.api.v1.PaymentIdentityByTransactionId byTransactionId = 4;
        uam.api.v1.PaymentIdentityByCode byCode = 5;
    }
    uam.api.v1.BillingAddress billingAddress = 6;
    uam.api.v1.PaymentMethod data = 7;
    uam.api.v1.Verification verification = 8;
}

message CapturePaymentOrderResponse {
    string status = 1;
    string errorMsg = 2;
}

message SaveAccountWeeklyWagerDataRequest {
    uam.api.v1.Identity identity = 1;
    .api.v1.Date from = 2;
    .api.v1.Date to = 3;
    int64 weeklyWageredGoldCoins = 4;
}

message SaveAccountWeeklyWagerDataResponse {

}

message SaveCardPaymentMethodMetaInfoRequest {
    uam.api.v1.Identity identity = 1;
    string bin = 2;
    string lastFour = 3;
    BlockCardPaymentMethod blocked = 4;
    ThirdPartyCardVerificationStatus thirdPartyCardVerificationStatus = 6;
    string updatedBy = 7;
}

message SaveCardPaymentMethodMetaInfoResponse {
}

enum BlockCardPaymentMethod {
    DO_NOT_CHANGE_BLOCK_CARD_PAYMENT_METHOD = 0;
    BLOCK = 1;
    UNBLOCK = 2;
}

enum ThirdPartyCardVerificationStatus {
    DO_NOT_CHANGE_THIRD_PARTY_CARD_VERIFICATION_STATUS = 0;
    VERIFIED = 1;
    VERIFICATION_FAILED = 2;
}

message GetLastPaymentDateRequest {
  uam.api.v1.Identity identity = 1;
}

message GetLastPaymentDateResponse {
  int64 lastPaymentDate = 1;
  int64 userRegisteredAt = 2;
}

message ReconcilePaymentOrderRequest {
    int64 orderId = 1;
    string transactionId = 2;
}

message SaveOrUpdateCurrencyRateRequest {
    string agentName = 1;
    string currencyCode = 2;
    string rate = 3;
    string brand = 4;
}

message SaveOrUpdateCurrencyRateResponse {
}

message UpdateCryptoCurrencyRateRequest {
    repeated CryptoCurrencyRate cryptoCurrencyRate = 1;
}

message UpdateCryptoCurrencyRateResponse {
}

message CryptoCurrencyRate {
    string provider = 1;
    string buyPrice = 2;
    string fromCurrency = 3;
    string toCurrency = 4;
}

message GetCurrencyRateRequest {
    string brand = 1;
    string currency = 2;
}

message GetCurrencyRateResponse {
    string rate = 1;
}

message SaveFXCurrencyRatesRequest {
    repeated CurrencyRate currencyRates = 1;
}

message SaveFXCurrencyRatesResponse {
}

message CurrencyRate {
    string currency = 1;
    string rate = 2;
}

message GetCurrencyRateForAccountRequest {
    uam.api.v1.Identity identity = 1;
}

message GetCurrencyRateForAccountResponse {
    CurrencyRate rate = 1;
}

message GetAccountBaseQuoteRequest {
    uam.api.v1.Identity identity = 1;
    string amount = 2;
    string currency = 3;
}

message GetAccountBaseQuoteResponse {
    string baseAmount = 1;
}

message GetPurchaseHistoryRequest {
    uam.api.v1.Identity identity = 1;
    .api.v1.Date from = 2;
    .api.v1.Date to = 3;
    .api.v1.PagingInfo pagingInfo = 5;

    //filter
    OrderType orderType = 6;
    enum OrderType {
        ALL_ORDER_TYPES = 0;
        DEPOSIT = 1;
        OFFER_PURCHASE = 2;
    }
    repeated uam.api.v1.CurrencyType.Enum currencyTypes = 7;
}

enum TransactionStatus {
    TRANSACTION_STATUS_UNKNOWN = 0;
    PENDING = 1;
    SUCCESS = 2;
    FAILED = 3;
}

message BalanceChange {
    string amount = 1;
    string currency = 2;
}

message Price {
    string amount = 1;
    string currency = 2;
    string baseAmount = 3;
    string baseCurrency = 4;
}

message Pageable {
    int32 totalRecords = 1;
    int32 currentPage = 2;
    int32 totalPages = 3;
    bool hasNextPage = 4;
    bool hasPrevPage = 5;
}

message Purchase {
    string transactionId = 1;
    int64 completedAt = 4;
    repeated BalanceChange balanceChange = 5;
    repeated Price price = 6;
    PurchaseMethod method = 7;
    optional uam.api.v1.CryptoPurchaseData cryptoPurchaseData = 8;
    uam.api.v1.OrderStatusSpec.Enum statusSpec = 9;
}

message GetPurchaseHistoryResponse {
    Pageable pageable = 1;
    repeated Purchase purchases = 2;
}

message GetRedeemsRequest {
    uam.api.v1.Identity identity = 1;
    .api.v1.Date from = 2;
    .api.v1.Date to = 3;
    .api.v1.PagingInfo pagingInfo = 5;
    //filter
    RedeemType redeemType = 6;
    enum RedeemType {
        ALL_REDEEM_TYPES = 0;
        SC_REDEEM = 1;
        FIAT_REDEEM = 2;
    }
    repeated uam.api.v1.CurrencyType.Enum currencyTypes = 7;

}

message GetRedeemsResponse {
    Pageable pageable = 1;
    repeated uam.api.v1.RedeemMoneyInfo redeems = 2;
}

message TransactionLimit {
    string operation = 1;
    string currency = 2;
    string min = 3;
    string max = 4;
}

message GetTransactionLimitRequest {
    uam.api.v1.Identity identity = 1;
}

message GetTransactionLimitResponse {
    repeated TransactionLimit limits = 1;
}

message SetRedeemProcessingDataRequest {
    int64 redeemId = 1;
    int32 delaySeconds = 2;
    string automationDeclineReason = 3;
}

message SetRedeemProcessingDataResponse {
}

message SetCardTransactionDetailsRequest {
    string transactionId = 1;
    string acquirerReferenceNumber = 2;
}

message SetCardTransactionDetailsResponse {
}
message ChargebackBadDeptRequest {
    uam.api.v1.Identity identity = 1;
    string transactionId = 2;
}

message ChargebackBadDeptResponse {

}

message StartChargebackCollectionRequest {
    string transactionId = 1;
    string status = 2;
}

message StartChargebackCollectionResponse {

}

message RefundTransactionRequest {
    string orderSn = 1;
    string reason = 2;
    bool forceChargebackRefund = 3;
}

message RefundTransactionResponse {
    bool refunded = 1;
}

message AcceptAccountPaymentTermRequest {
    uam.api.v1.Identity identity = 1;
    string code = 2;
}

message AcceptAccountPaymentTermResponse {
}

message GetAccountAcceptedPaymentTermsRequest {
    uam.api.v1.Identity identity = 1;
}

message GetAccountAcceptedPaymentTermsResponse {
    repeated AcceptedPaymentTerm acceptedPaymentTerm = 1;
}

message AcceptedPaymentTerm {
    string code = 1;
    int64 acceptedAt = 2;
}

message GetPaymentBrandSettingsRequest {
    string brand = 1;
}

message GetPaymentBrandSettingsResponse {
    bool offerApprovalFlowEnabled = 1;
    string backgroundImageUrlTooltip = 2;
    string storeIconImageUrlTooltip = 3;
    string backgroundBorderCssTooltip = 4;
    int32 sweepstakeThresholdPercentage = 5;
    map<string, int32> scAllowedRestrictions = 6;
    map<string, int32> scMaxRestrictions = 7;
    string homepageBannerImageUrlTooltip = 8;
    bool offerDraftFlowEnabled = 9;
}

message CreateOfferRewardRequest {
    uam.api.v1.Identity identity = 1;
    string offerCode = 2;
    int32 maxPurchaseCount = 3;
    optional int64 expireAt = 4;
    string source = 5;
    string reference = 6;
    string requestId = 7;
}

message CreateOfferRewardResponse {
    bool applied = 1;
}

message OfferRewardRef {
    string offerCode = 1;
    string reference = 2;
}

message OfferReward {
    OfferRewardRef ref = 1;
    int32 maxPurchaseCount = 2;
    optional int64 availableFrom = 3;
    optional int64 expireAt = 4;
}

message ReplaceOfferRewardsRequest {
    uam.api.v1.Identity identity = 1;
    repeated OfferRewardRef existingOfferRewardRefs = 2;
    repeated OfferReward newOfferRewards = 3;
    string source = 4;
    string requestId = 5;
}

message ReplaceOfferRewardsResponse {
    bool applied = 1;
}

message SetOfferRewardStatusRequest {
    uam.api.v1.Identity identity = 1;
    string offerCode = 2;
    string requestId = 3;
    OfferRewardStatus status = 4;
}

enum OfferRewardStatus {
    OFFER_REWARD_STATUS_UNSPECIFIED = 0;
    OFFER_REWARD_STATUS_CREATED = 1;
    OFFER_REWARD_STATUS_CANCELLED = 2;
    OFFER_REWARD_STATUS_EXPIRED = 3;
    OFFER_REWARD_STATUS_USED = 4;
}

message SetOfferRewardStatusResponse {
    bool applied = 1;
}

message CancelOfferRewardsRequest {
    uam.api.v1.Identity identity = 1;
    repeated OfferRewardRef offerRewardRefs = 2;
    string source = 3;
    string requestId = 4;
}

message CancelOfferRewardsResponse {
    bool applied = 1;
}

message AddOrUpdateBlackListRequest {
    optional int64 id = 1;
    string brand = 2;
    string country = 3;
    string type = 4;
    string excludedValue = 5;
    bool inactive = 6;
    bool brandSpecific = 7;
    string updatedBy = 8;
}

message AddOrUpdateBlackListResponse {
    int64 id = 1;
}

message Verify3dsRefundRequest {
    repeated string orderSns = 1;
}

message Verify3dsRefundResponse {
    string confirmationMessage = 1;
}