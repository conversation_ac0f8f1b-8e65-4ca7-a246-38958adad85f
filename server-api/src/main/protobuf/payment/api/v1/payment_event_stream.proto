syntax = "proto3";

package uam.api.v1.internal;

import "api/v1/obfuscation.proto";
import "api/v1/routing.proto";
import "payment/api/v1/routing.proto";
import "payment/api/v1/internal.proto";
import "payment/api/v1/payment_common_tmp.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message PurchaseLimitUpdateEvent {
  repeated PurchaseLimit purchaseLimit = 2;
  payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 3;
}

message PurchaseLimit {
  string threshold = 1;
  string period = 2;
  string reason = 3;
}

message ErrorMappingChangedEvent {
  ErrorMapping oldValue = 1;
  ErrorMapping newValue = 2;
}

message ErrorMapping {
  string errorCode = 1;
  string description = 2;
  string type = 3;
  string message = 4;
}

message RedeemMoneyEvent {
  int64 at = 2;
  int64 id = 3;
  string currency = 4;
  string amount = 5;
  string email = 6 [(.api.v1.sensitive_mode).sensitive = true];
  string provider = 7;
  string baseCurrency = 8;
  string baseAmount = 9;
  string status = 10;
  string uamStatus = 11;
  int32 fraudScore = 12;
  optional int64 fraudResponseId = 13;
  string transactionId = 14;
  int32 providerId = 15;
  int64 paymentMethodId = 16;
  payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 17;
}

message CancelRedeemMoneyEvent {
  int64 id = 2;
  string currency = 3;
  string amount = 4;
  uam.api.v1.internal.AccountPaymentRoutingInfo paymentRouting = 7;
  int64 createdAt = 6;
  int64 cancelledAt = 8;
}

message OfferRefundEvent {
  int64 refundedAt = 2;
  int64 id = 3;
  string transactionId = 4;
  string currency = 5;
  string amount = 6;
  string description = 7;
  string offer = 8;
  string provider = 9;
  string code = 10;
  string reason = 11;
  bool refunded = 13;
  uam.api.v1.internal.RefundError error = 14;
  payment.api.v1.internal.PaymentAccountInfo paymentAccountInfo = 15;
  string refundedBy = 16;
}

message RefundError {
  string message = 1;
  string code = 2;
}
