syntax = "proto3";
package uam.api.v1.internal;

import "api/v1/obfuscation.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message DeactivatePaymentProviderEvent {
  string brand = 1;
  string provider = 2;
  int32 deactivationTime = 3;
}

message ActivatePaymentProviderEvent {
  string brand = 1;
  string provider = 2;
}

message PaymentProviderSequenceUpdatedEvent {
  string brand = 1;
  string provider = 2;
  int32 sequence = 3;
}

message RoutingMemberChangedEvent {
  RoutingMember oldMember = 1;
  RoutingMember newMember = 2;
}

message RoutingMember {
  string brand = 1;
  string provider = 2;
  string group = 3;
  int32 weight = 4;
  int32 sequence = 5;
  string type = 6;
  bool active = 7;
}

message RoutingErrorChangedEvent {
  RoutingError oldValue = 1;
  RoutingError newValue = 2;
}

message RoutingError {
  string brand = 1;
  string errorType = 2;
  string routingMember = 3;
  string errorCode = 4;
  string errorDescription = 5;
  string configType = 6;
  string integrationType = 7;
  int64 expirationSec = 8;
  string failPolicy = 9;
  string provider = 10;
}

message AccountPaymentRoutingInfo {
  int64 id = 1;
  string hash = 2;
  string remoteIp = 4 [(.api.v1.sensitive_mode).sensitive = true];
  string brand = 5;
  string country = 9;
}
