syntax = "proto3";

package payment.api.v1.internal;

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message PaymentAccountInfo {
  int64 accountId = 1;
  string routingKey = 2;
  string brandName = 3;
  string email = 4;
  string phone = 5;
  string realEmail = 8;
  int64 createdAt = 9;
  bool admin = 10;
}

message OrderInfo {
  int64 at = 1;
  int64 orderId = 2;
  string transactionId = 3;
  string code = 4;
  string provider = 5;
  string amount = 6;
  string baseAmount = 7;
  string currency = 8;
  string description = 9;
  string billingDescriptor = 10;
  string fraudScore = 11;
  string userAgent = 12;
  string status = 13;
  string platform = 14;
  string gcAmount = 15;
  string scAmount = 16;
  string ip = 17;
}

message OfferInfo {
  string code = 1;
  string externalRewardCode = 2;
}

message AccountPaymentInfo {
  bool isFirstPurchase = 1;
  int64 lastSuccessPurchaseDate = 2;
  int64 totalPurchaseCount = 3;
  int64 totalPurchaseAmount = 4;
  int64 firstPurchaseDate = 5;
  string totalPurchaseAmountStr = 6;
  AccountPurchaseInfo offerPurchaseInfo = 7;
  AccountPurchaseInfo depositInfo = 8;
}

message AccountPurchaseInfo {
  int64 firstPurchaseDate = 1;
  int64 lastSuccessPurchaseDate = 2;
  int64 successPurchaseCount = 3;
  string successPurchaseAmount = 4;
  bool isFirstPurchase = 5;
}
