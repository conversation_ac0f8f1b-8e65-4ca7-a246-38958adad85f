syntax = "proto3";
package payment.api.v1;

import "api/v1/identity.proto";
import "payment/api/v1/payment_common.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message GetCryptoPaymentServiceInfoRequest {
  uam.api.v1.Identity identity = 1;
}

message GetCryptoPaymentServiceInfoResponse {
  repeated CryptoServiceInfoItem purchaseData = 1;
  repeated CryptoServiceInfoItem withdrawData = 2;
}

message CryptoServiceInfoItem {
  uam.api.v1.CryptoCurrency.Enum currency = 1;
  optional double minLimitInBaseCurrency = 2;
  optional double rateToBaseCurrency = 3;
  repeated CryptoNetworkInfo networks = 4;
}
message CryptoNetworkInfo {
  uam.api.v1.CryptoNetwork.Enum network = 1;
  string walletValidationRegexp = 2;
}

//use from common proto
message CurrencyType {
  enum Enum {
    FIAT = 0 [deprecated = true];
    CRYPTO = 1 [deprecated = true];
  }
}

message GetPaymentProvidersRequest {
  string brandName = 1;
  uam.api.v1.Identity identity = 2;
}

message GetPaymentProvidersResponse {
  PurchaseProviders purchaseProviders = 1;
  WithdrawProviders withdrawProviders = 2;
  bool thirdPartyCheckEnabled = 3;
  bool softKycRequiredEnabled = 4;
  bool softKycAutoCompleteAddress = 5;
  bool cardRegistrationModify = 6;

  repeated CurrencyType.Enum supportedCurrencies = 99;
}

message WithdrawProviders {
  Skrill skrill = 1;
  Prizeout prizeout = 2;
  Nuvei nuvei = 4;
  MassPay massPay = 5;
  Trustly trustly = 6;
  Payper payper = 7;
  StandardAch standardAch = 8;
  Crypto crypto = 9;
  AeroPay aeroPay = 10;
}

message PurchaseProviders {
  Spreedly spreedly = 1;
  Fiserv fiserv = 2;
  FiservGooglePay fiservGooglePay = 3;
  FiservApplePay fiservApplePay = 4;
  Skrill skrill = 5;
  Nuvei nuvei = 7;
  SpreedlyApplePay spreedlyApplePay = 8;
  SpreedlyGooglePay spreedlyGooglePay = 9;
  AppleInApp appleInApp = 10;
  Trustly trustly = 11;
  Payper payper = 12;
  AndroidInApp androidInApp = 13;
  Crypto crypto = 14;
  AeroPay aeroPay = 15;
  repeated uam.api.v1.PaymentProvider eligibleProviders = 100;
}


message Prizeout {
  string id = 1;
  string key = 2;
  RedeemMoneyPolicy policy = 3;
  string withdrawMethodName = 4;
}

message Skrill {
}

message Crypto {
}

message SkrillRedeem {
  RedeemMoneyPolicy policy = 1;
  string withdrawMethodName = 2;
}

message Trustly {
  string id = 1;
  string merchantId = 2;
  string notificationUrl = 3;
}

message TrustlyRedeem {
  string id = 1;
  string merchantId = 2;
  string notificationUrl = 3;
  RedeemMoneyPolicy policy = 4;
  string withdrawMethodName = 5;
}

message PayperRedeem {
  RedeemMoneyPolicy policy = 1;
  string withdrawMethodName = 2;
}

message Payper {
}

message StandardAch {
  RedeemMoneyPolicy policy = 1;
  string withdrawMethodName = 2;
}
message CryptoRedeem {
  RedeemMoneyPolicy policy = 1;
  string withdrawMethodName = 2;
}
message AeroPayRedeem {
  RedeemMoneyPolicy policy = 1;
  string withdrawMethodName = 2;
}
message Spreedly {
  string id = 1;
}

message Fiserv {
  string id = 1;
}

message Nuvei {
  string id = 1;
}

message AeroPay {
}

message NuveiRedeem {
  string id = 1;
  RedeemMoneyPolicy policy = 2;
  string withdrawMethodName = 3;
}

message MassPay {
  RedeemMoneyPolicy policy = 1;
  string withdrawMethodName = 2;
}

message FiservApp {
  string id = 1;
}

message FiservGooglePay {
  string merchant = 1;
  string gateway = 2;
  string merchantId = 3;
  string merchantName = 4;
}

message FiservApplePay {
  string merchantId = 1;
}

message SpreedlyApplePay {
  string merchantId = 1;
}

message SpreedlyGooglePay {
  string merchant = 1;
  string gateway = 2;
  string merchantId = 3;
  string merchantName = 4;
}

message AppleInApp {
}

message AndroidInApp {
}

message GetWithdrawProvidersRequest {
  string brandName = 1;
  uam.api.v1.Identity identity = 2;
}

message GetWithdrawProvidersResponse {
  SkrillRedeem skrill = 1;
  Prizeout prizeout = 2;
  NuveiRedeem nuvei = 4;
  MassPay massPay = 5;
  TrustlyRedeem trustly = 6;
  PayperRedeem payper = 7;
  bool enabled = 8;
  StandardAch standardAch = 9;
  CryptoRedeem crypto = 10;
  AeroPayRedeem aeroPay = 11;

  repeated CurrencyType.Enum supportedCurrencies = 99;
}

message RedeemMoneyPolicy {
  string minAmount = 1;
  string maxAmount = 2;
  string availableAmount = 3;
  string state = 4;
}

