syntax = "proto3";
package creator.api.v1;

import "api/v1/identity.proto";
import "api/v1/routing.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message CreatorRegisteredEvent {
  int64 accountId = 1;
  string handle = 2;
  string routingKey = 3;
}

message CreatorFollowedOrUnfollowedEvent {
  optional int64 creatorAccountId = 1;
  int64 followerAccountId = 2;
  string brandName = 3;
  bool followedEvent = 4;
  int64 createdAt = 5;
  string creatorCode = 6;
  string creatorHandle = 7;
}