syntax = "proto3";
package rgs.api.v1;

import "api/v1/common.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message WhoiamRequest {}
message WhoiamResponse {}

message LaunchGameRequest {
  string gameId = 1;
  string token = 2;
  string operator = 3;
  string currency = 4;
  double baseUnit = 5;
  optional string lang = 6;
  optional string returnUrl = 7;
  optional string lobbyUrl = 8;
  string remoteIp = 9;
  int64 when = 10;
  optional int32 loadSleepMs = 12;  // for load tests only
}

message LaunchGameResponse {
  string sessionId = 1;
  repeated Mode modes = 2;
  string gameId = 3;
  repeated BetLevelItem betLevels = 4;
  User user = 5;
  optional Outcome outcome = 6;
  optional google.protobuf.Struct initData = 7;
  int32 defaultBetLevel = 8;
}

message SyncRequest {
  string sessionId = 1;
  string remoteIp = 2;
}

message GameCommandRequest {
  bool sound = 1;
  bool autoPlay = 2;
  bool hotkey = 3;
  bool mobile = 4;
  bool portrait = 5;
  bool quickSpin = 6;
  int32 betLevel = 7;
  string sessionId = 8;
  string requestId = 9;
  string remoteIp = 10;
  Action action = 11;
  optional int32 loadSleepMs = 12;  // for load tests only
  optional string visitorInfo = 13;
  optional string signature = 14;
}

message GameCommandResponse {
  User user = 1;
  optional Action lastAction = 2;
  optional Action lastWinAction = 3;
  optional double lastWin = 4;
  Outcome outcome = 5;
}

message WalletCreditRetryRequest {
  string si = 1;
  string jsonPayload = 2;
  int64 roundId = 3;
  int64 roundEpochDay = 4;
}

message WalletCreditRetryResp {}

message WalletRefundRetryRequest {
  string si = 1;
  string jsonPayload = 2;
  int64 roundId = 3;
  int64 roundEpochDay = 4;
}

message WalletRefundRetryResp {}

message Outcome {
  double betAmount = 1;
  double roundWinAmount = 2;
  double totalWin = 3;
  optional google.protobuf.Struct board = 4;
}

message Action {
  ActionName name = 1;
  google.protobuf.Struct parameters = 2;
}

message User {
  double balance = 1;
  string currency = 2;
  optional bool show = 3;
}

message BetLevelItem {
  int32 level = 1;
  double multiplier = 2;
}

enum Mode {
  auto = 0;
  play = 1;
  freebet = 2;
}

enum ActionName {
  spin = 0;
  bet = 1;
  skip = 2;
  cashout = 3;
}