syntax = "proto3";
package api.v1;

import "api/v1/obfuscation.proto";
import "google/protobuf/descriptor.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message LocationInfo {
  string ip = 1 [(.api.v1.sensitive_mode).sensitive = true];
  string country = 2;
  string city = 3;
  string state = 4;
  double longitude = 5;
  double latitude = 6;
}

message StringArrayFomatterTag {
  repeated string items = 1;
}

message NumberArrayFomatterTag {
  repeated int64 items = 1;
}

message TextFomatterTag {
  string value = 1;
}

message TimestampFomatterTag {
  int64 value = 1;
  string format = 2;
  bool date = 3;
}

message NumberFomatterTag {
  int64 value = 1;
}

message Int32MapEntry {
  int32 key = 1;
  bytes value = 2;
}

message Int64MapEntry {
  int64 key = 1;
  bytes value = 2;
}

message DoubleMapEntry {
  double key = 1;
  bytes value = 2;
}

message Int32Map {
  repeated Int32MapEntry entries = 1;
}

message Int64Map {
  repeated Int64MapEntry entries = 1;
}

message DoubleMap {
  repeated DoubleMapEntry entries = 1;
}

message Date {
  int32 year = 1;
  int32 month = 2;
  int32 day = 3;
}

message PagingInfo {
  int32 offset = 1;
  int32 limit = 2;
}
