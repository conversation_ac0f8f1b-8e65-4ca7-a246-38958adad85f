syntax = "proto3";
package api.v1;

import "google/protobuf/any.proto";
import "api/v1/caching.proto";
import "api/v1/headers.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message ResponseWrapper {
  Headers headers = 1;
  google.protobuf.Any body = 2;
  Status status = 3;
  .api.v1.CacheControl cacheControl = 4;
}

message Status {
  Code errorCode = 1;
  string errorText = 2;
  string stackTrace = 3;
}

enum Code {
  ERR_OK = 0;

  ERR_SYSTEM = 1;
  ERR_AUTH = 2;
  ERR_BAD_REQUEST = 3;
  ERR_DENIED = 4;
  ERR_FROZEN = 5;
  ERR_NOT_FOUND = 6;
  ERR_TIMEOUT = 7;
  ERR_DUPLICATE = 8;
  ERR_INSUFFICIENT_FUNDS = 9;
}
