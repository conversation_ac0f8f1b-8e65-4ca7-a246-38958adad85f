package creator;

import com.google.protobuf.Message;
import com.turbospaces.api.MutableReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.ApiResponse;
import com.turbospaces.rpc.DefaultApiResponse;
import com.turbospaces.rpc.QueuePostTemplate;
import com.turbospaces.rpc.WrappedQueuePost;

import api.v1.ApiFactory;
import creator.api.v1.ConnectCreatorStreamsRequest;
import creator.api.v1.ConnectCreatorStreamsResponse;
import creator.api.v1.CreateAwsStageAndPublishTokenRequest;
import creator.api.v1.CreateAwsStageAndPublishTokenResponse;
import creator.api.v1.CreateAwsSubscribeTokenRequest;
import creator.api.v1.CreateAwsSubscribeTokenResponse;
import creator.api.v1.CreateOrUpdateAllowedProductRequest;
import creator.api.v1.CreateOrUpdateAllowedProductResponse;
import creator.api.v1.CreateOrUpdateCurrencySettingsRequest;
import creator.api.v1.CreateOrUpdateCurrencySettingsResponse;
import creator.api.v1.CreatePlayTogetherSessionRequest;
import creator.api.v1.CreatePlayTogetherSessionResponse;
import creator.api.v1.CreatorFollowerRequest;
import creator.api.v1.CreatorFollowerResponse;
import creator.api.v1.GetAccountInfoRequest;
import creator.api.v1.GetAccountInfoResponse;
import creator.api.v1.GetAccountRoutingInfoRequest;
import creator.api.v1.GetAccountRoutingInfoResponse;
import creator.api.v1.GetAllowedProductsRequest;
import creator.api.v1.GetAllowedProductsResponse;
import creator.api.v1.GetBackofficeEventsRequest;
import creator.api.v1.GetBackofficeEventsResponse;
import creator.api.v1.GetCreatorCurrentEarningsRequest;
import creator.api.v1.GetCreatorCurrentEarningsResponse;
import creator.api.v1.GetCreatorMultiplierHistoryRequest;
import creator.api.v1.GetCreatorMultiplierHistoryResponse;
import creator.api.v1.GetCreatorRoundStatsRequest;
import creator.api.v1.GetCreatorRoundStatsResponse;
import creator.api.v1.GetCreatorStreamChannelsRequest;
import creator.api.v1.GetCreatorStreamChannelsResponse;
import creator.api.v1.GetCreatorsRequest;
import creator.api.v1.GetCreatorsResponse;
import creator.api.v1.GetCurrencySettingsRequest;
import creator.api.v1.GetCurrencySettingsResponse;
import creator.api.v1.GetLimitedToCreatorStreamChannelsRequest;
import creator.api.v1.GetLimitedToCreatorStreamChannelsResponse;
import creator.api.v1.GetLiveAndUpcomingStreamsRequest;
import creator.api.v1.GetLiveAndUpcomingStreamsResponse;
import creator.api.v1.GetNotFollowedCreatorsRequest;
import creator.api.v1.GetPlayTogetherSessionsRequest;
import creator.api.v1.GetPlayTogetherSessionsResponse;
import creator.api.v1.GetProductIsAllowedRequest;
import creator.api.v1.GetProductIsAllowedResponse;
import creator.api.v1.GetTurnInfoRequest;
import creator.api.v1.GetTurnInfoResponse;
import creator.api.v1.GetWagerSettingsRequest;
import creator.api.v1.GetWagerSettingsResponse;
import creator.api.v1.PlayTogetherSessionAliveRequest;
import creator.api.v1.PlayTogetherSessionAliveResponse;
import creator.api.v1.RegisterCreatorRequest;
import creator.api.v1.RegisterCreatorResponse;
import creator.api.v1.ResetImagesRequest;
import creator.api.v1.ResetImagesResponse;
import creator.api.v1.ScheduleStreamCreateRequest;
import creator.api.v1.ScheduleStreamCreateResponse;
import creator.api.v1.ScheduleStreamDeleteRequest;
import creator.api.v1.ScheduleStreamDeleteResponse;
import creator.api.v1.ScheduleStreamListRequest;
import creator.api.v1.ScheduleStreamListResponse;
import creator.api.v1.ScheduleStreamUpdateRequest;
import creator.api.v1.ScheduleStreamUpdateResponse;
import creator.api.v1.SetLiveStreamStatusRequest;
import creator.api.v1.SetLiveStreamStatusRequestByAccountId;
import creator.api.v1.SetLiveStreamStatusResponse;
import creator.api.v1.SetReferralRequest;
import creator.api.v1.SetReferralResponse;
import creator.api.v1.StopPlayTogetherSessionRequest;
import creator.api.v1.StopPlayTogetherSessionResponse;
import creator.api.v1.StreamWatchActivityRequest;
import creator.api.v1.StreamWatchActivityResponse;
import creator.api.v1.UpdateCreatorImageRequest;
import creator.api.v1.UpdateCreatorImageResponse;
import creator.api.v1.UpdateCreatorRequest;
import creator.api.v1.UpdateCreatorResponse;
import io.netty.util.AsciiString;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DefaultCreatorServiceApi implements CreatorServiceApi {
    private final ApplicationProperties props;
    private final QueuePostTemplate<?> postTemplate;
    private final ApiFactory apiFactory;
    private final MutableReplyTopic replyTo;

    @Override
    public ApiResponse<RegisterCreatorResponse> register(RegisterCreatorRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, RegisterCreatorResponse.class);
    }

    @Override
    public ApiResponse<ConnectCreatorStreamsResponse> connectCreatorStreams(ConnectCreatorStreamsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, ConnectCreatorStreamsResponse.class);
    }

    @Override
    public ApiResponse<GetCreatorsResponse> getCreators(GetCreatorsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetCreatorsResponse.class);
    }

    @Override
    public ApiResponse<GetCreatorsResponse> getNotFollowedCreators(GetNotFollowedCreatorsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetCreatorsResponse.class);
    }

    @Override
    public ApiResponse<GetWagerSettingsResponse> getCreatorsWagerSettings(GetWagerSettingsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetWagerSettingsResponse.class);
    }

    @Override
    public ApiResponse<GetAccountInfoResponse> getAccountInfo(GetAccountInfoRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetAccountInfoResponse.class);
    }

    @Override
    public ApiResponse<CreatePlayTogetherSessionResponse> createPlayTogetherSession(CreatePlayTogetherSessionRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, CreatePlayTogetherSessionResponse.class);
    }

    @Override
    public ApiResponse<GetPlayTogetherSessionsResponse> getPlayTogetherSessions(GetPlayTogetherSessionsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetPlayTogetherSessionsResponse.class);
    }

    @Override
    public ApiResponse<StopPlayTogetherSessionResponse> stopPlayTogetherSession(StopPlayTogetherSessionRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, StopPlayTogetherSessionResponse.class);
    }

    @Override
    public ApiResponse<PlayTogetherSessionAliveResponse> keepPlayTogetherSessionAlive(PlayTogetherSessionAliveRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, PlayTogetherSessionAliveResponse.class);
    }

    @Override
    public ApiResponse<UpdateCreatorImageResponse> updateImage(UpdateCreatorImageRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, UpdateCreatorImageResponse.class);
    }

    @Override
    public ApiResponse<ResetImagesResponse> resetImages(ResetImagesRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, ResetImagesResponse.class);
    }

    @Override
    public ApiResponse<SetLiveStreamStatusResponse> setLiveStreamStatus(SetLiveStreamStatusRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, SetLiveStreamStatusResponse.class);
    }

    @Override
    public ApiResponse<SetLiveStreamStatusResponse> setLiveStreamStatusByAccountId(SetLiveStreamStatusRequestByAccountId req, AsciiString routingKey) {
        return sendReq(req, routingKey, SetLiveStreamStatusResponse.class);
    }

    @Override
    public ApiResponse<GetAccountRoutingInfoResponse> getAccountRoutingInfoByStreamId(GetAccountRoutingInfoRequest req) {
        return sendReq(req, null, GetAccountRoutingInfoResponse.class);
    }

    @Override
    public ApiResponse<GetCreatorStreamChannelsResponse> getStreamChannelsByCreatorCode(GetCreatorStreamChannelsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetCreatorStreamChannelsResponse.class);
    }

    @Override
    public ApiResponse<GetLimitedToCreatorStreamChannelsResponse> getLimitedToCreatorStreamChannels(GetLimitedToCreatorStreamChannelsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetLimitedToCreatorStreamChannelsResponse.class);
    }

    @Override
    public ApiResponse<UpdateCreatorResponse> updateCreator(UpdateCreatorRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, UpdateCreatorResponse.class);
    }

    @Override
    public ApiResponse<CreatorFollowerResponse> creatorFollower(CreatorFollowerRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, CreatorFollowerResponse.class);
    }

    @Override
    public ApiResponse<GetCreatorCurrentEarningsResponse> getCreatorCurrentEarnings(GetCreatorCurrentEarningsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetCreatorCurrentEarningsResponse.class);
    }

    @Override
    public ApiResponse<GetCreatorRoundStatsResponse> getCreatorRoundStats(GetCreatorRoundStatsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetCreatorRoundStatsResponse.class);
    }

    @Override
    public ApiResponse<SetReferralResponse> setReferral(SetReferralRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, SetReferralResponse.class);
    }

    @Override
    public ApiResponse<ScheduleStreamCreateResponse> scheduleStreamCreate(ScheduleStreamCreateRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, ScheduleStreamCreateResponse.class);
    }

    @Override
    public ApiResponse<ScheduleStreamUpdateResponse> scheduleStreamUpdate(ScheduleStreamUpdateRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, ScheduleStreamUpdateResponse.class);
    }

    @Override
    public ApiResponse<ScheduleStreamDeleteResponse> scheduleStreamDelete(ScheduleStreamDeleteRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, ScheduleStreamDeleteResponse.class);
    }

    @Override
    public ApiResponse<ScheduleStreamListResponse> scheduleStreamList(ScheduleStreamListRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, ScheduleStreamListResponse.class);
    }

    @Override
    public ApiResponse<StreamWatchActivityResponse> logStreamWatchActivity(StreamWatchActivityRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, StreamWatchActivityResponse.class);
    }

    @Override
    public ApiResponse<GetTurnInfoResponse> getTurnInfo(GetTurnInfoRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetTurnInfoResponse.class);
    }

    @Override
    public ApiResponse<GetLiveAndUpcomingStreamsResponse> getLiveAndUpcomingStreams(GetLiveAndUpcomingStreamsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetLiveAndUpcomingStreamsResponse.class);
    }

    @Override
    public ApiResponse<GetProductIsAllowedResponse> getProductIsAllowed(GetProductIsAllowedRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetProductIsAllowedResponse.class);
    }

    @Override
    public ApiResponse<GetAllowedProductsResponse> getAllowedProducts(GetAllowedProductsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetAllowedProductsResponse.class);
    }

    @Override
    public ApiResponse<CreateOrUpdateAllowedProductResponse> createOrUpdateAllowedProduct(CreateOrUpdateAllowedProductRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, CreateOrUpdateAllowedProductResponse.class);
    }

    @Override
    public ApiResponse<GetCurrencySettingsResponse> getCurrencySettings(GetCurrencySettingsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetCurrencySettingsResponse.class);
    }

    @Override
    public ApiResponse<CreateOrUpdateCurrencySettingsResponse> createOrUpdateCurrencySettings(CreateOrUpdateCurrencySettingsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, CreateOrUpdateCurrencySettingsResponse.class);
    }

    @Override
    public ApiResponse<CreateAwsStageAndPublishTokenResponse> createAwsStageAndPublishToken(CreateAwsStageAndPublishTokenRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, CreateAwsStageAndPublishTokenResponse.class);
    }

    @Override
    public ApiResponse<CreateAwsSubscribeTokenResponse> createAwsSubscribeToken(CreateAwsSubscribeTokenRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, CreateAwsSubscribeTokenResponse.class);
    }

    @Override
    public ApiResponse<GetBackofficeEventsResponse> getBackofficeEvents(GetBackofficeEventsRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetBackofficeEventsResponse.class);
    }

    @Override
    public ApiResponse<GetCreatorMultiplierHistoryResponse> getCreatorMultiplierHistory(GetCreatorMultiplierHistoryRequest req, AsciiString routingKey) {
        return sendReq(req, routingKey, GetCreatorMultiplierHistoryResponse.class);
    }

    private <T extends Message> ApiResponse<T> sendReq(Message request, AsciiString routingKey, Class<T> response) {
        WrappedQueuePost post = postTemplate.sendReq(CreatorTopics.REQ, replyTo, request, routingKey);
        return new DefaultApiResponse<>(props, post, apiFactory, response);
    }
}
