package creator;

import com.turbospaces.api.ServiceApi;
import com.turbospaces.rpc.ApiResponse;

import creator.api.v1.ConnectCreatorStreamsRequest;
import creator.api.v1.ConnectCreatorStreamsResponse;
import creator.api.v1.CreateAwsStageAndPublishTokenRequest;
import creator.api.v1.CreateAwsStageAndPublishTokenResponse;
import creator.api.v1.CreateAwsSubscribeTokenRequest;
import creator.api.v1.CreateAwsSubscribeTokenResponse;
import creator.api.v1.CreateOrUpdateAllowedProductRequest;
import creator.api.v1.CreateOrUpdateAllowedProductResponse;
import creator.api.v1.CreateOrUpdateCurrencySettingsRequest;
import creator.api.v1.CreateOrUpdateCurrencySettingsResponse;
import creator.api.v1.CreatePlayTogetherSessionRequest;
import creator.api.v1.CreatePlayTogetherSessionResponse;
import creator.api.v1.CreatorFollowerRequest;
import creator.api.v1.CreatorFollowerResponse;
import creator.api.v1.GetAccountInfoRequest;
import creator.api.v1.GetAccountInfoResponse;
import creator.api.v1.GetAccountRoutingInfoRequest;
import creator.api.v1.GetAccountRoutingInfoResponse;
import creator.api.v1.GetAllowedProductsRequest;
import creator.api.v1.GetAllowedProductsResponse;
import creator.api.v1.GetBackofficeEventsRequest;
import creator.api.v1.GetBackofficeEventsResponse;
import creator.api.v1.GetCreatorCurrentEarningsRequest;
import creator.api.v1.GetCreatorCurrentEarningsResponse;
import creator.api.v1.GetCreatorMultiplierHistoryRequest;
import creator.api.v1.GetCreatorMultiplierHistoryResponse;
import creator.api.v1.GetCreatorRoundStatsRequest;
import creator.api.v1.GetCreatorRoundStatsResponse;
import creator.api.v1.GetCreatorStreamChannelsRequest;
import creator.api.v1.GetCreatorStreamChannelsResponse;
import creator.api.v1.GetCreatorsRequest;
import creator.api.v1.GetCreatorsResponse;
import creator.api.v1.GetCurrencySettingsRequest;
import creator.api.v1.GetCurrencySettingsResponse;
import creator.api.v1.GetLimitedToCreatorStreamChannelsRequest;
import creator.api.v1.GetLimitedToCreatorStreamChannelsResponse;
import creator.api.v1.GetLiveAndUpcomingStreamsRequest;
import creator.api.v1.GetLiveAndUpcomingStreamsResponse;
import creator.api.v1.GetNotFollowedCreatorsRequest;
import creator.api.v1.GetPlayTogetherSessionsRequest;
import creator.api.v1.GetPlayTogetherSessionsResponse;
import creator.api.v1.GetProductIsAllowedRequest;
import creator.api.v1.GetProductIsAllowedResponse;
import creator.api.v1.GetTurnInfoRequest;
import creator.api.v1.GetTurnInfoResponse;
import creator.api.v1.GetWagerSettingsRequest;
import creator.api.v1.GetWagerSettingsResponse;
import creator.api.v1.PlayTogetherSessionAliveRequest;
import creator.api.v1.PlayTogetherSessionAliveResponse;
import creator.api.v1.RegisterCreatorRequest;
import creator.api.v1.RegisterCreatorResponse;
import creator.api.v1.ResetImagesRequest;
import creator.api.v1.ResetImagesResponse;
import creator.api.v1.ScheduleStreamCreateRequest;
import creator.api.v1.ScheduleStreamCreateResponse;
import creator.api.v1.ScheduleStreamDeleteRequest;
import creator.api.v1.ScheduleStreamDeleteResponse;
import creator.api.v1.ScheduleStreamListRequest;
import creator.api.v1.ScheduleStreamListResponse;
import creator.api.v1.ScheduleStreamUpdateRequest;
import creator.api.v1.ScheduleStreamUpdateResponse;
import creator.api.v1.SetLiveStreamStatusRequest;
import creator.api.v1.SetLiveStreamStatusRequestByAccountId;
import creator.api.v1.SetLiveStreamStatusResponse;
import creator.api.v1.SetReferralRequest;
import creator.api.v1.SetReferralResponse;
import creator.api.v1.StopPlayTogetherSessionRequest;
import creator.api.v1.StopPlayTogetherSessionResponse;
import creator.api.v1.StreamWatchActivityRequest;
import creator.api.v1.StreamWatchActivityResponse;
import creator.api.v1.UpdateCreatorImageRequest;
import creator.api.v1.UpdateCreatorImageResponse;
import creator.api.v1.UpdateCreatorRequest;
import creator.api.v1.UpdateCreatorResponse;
import io.netty.util.AsciiString;

public interface CreatorServiceApi extends ServiceApi {
    ApiResponse<RegisterCreatorResponse> register(RegisterCreatorRequest req, AsciiString routingKey);

    ApiResponse<ConnectCreatorStreamsResponse> connectCreatorStreams(ConnectCreatorStreamsRequest req, AsciiString routingKey);

    ApiResponse<GetCreatorsResponse> getCreators(GetCreatorsRequest req, AsciiString routingKey);

    ApiResponse<GetCreatorsResponse> getNotFollowedCreators(GetNotFollowedCreatorsRequest req, AsciiString routingKey);

    ApiResponse<GetAccountInfoResponse> getAccountInfo(GetAccountInfoRequest req, AsciiString routingKey);

    ApiResponse<GetWagerSettingsResponse> getCreatorsWagerSettings(GetWagerSettingsRequest req, AsciiString routingKey);

    ApiResponse<CreatePlayTogetherSessionResponse> createPlayTogetherSession(CreatePlayTogetherSessionRequest req, AsciiString routingKey);

    ApiResponse<GetPlayTogetherSessionsResponse> getPlayTogetherSessions(GetPlayTogetherSessionsRequest req, AsciiString routingKey);

    ApiResponse<StopPlayTogetherSessionResponse> stopPlayTogetherSession(StopPlayTogetherSessionRequest req, AsciiString routingKey);

    ApiResponse<PlayTogetherSessionAliveResponse> keepPlayTogetherSessionAlive(PlayTogetherSessionAliveRequest req, AsciiString routingKey);

    ApiResponse<UpdateCreatorImageResponse> updateImage(UpdateCreatorImageRequest req, AsciiString routingKey);

    ApiResponse<ResetImagesResponse> resetImages(ResetImagesRequest req, AsciiString routingKey);

    ApiResponse<SetLiveStreamStatusResponse> setLiveStreamStatus(SetLiveStreamStatusRequest req, AsciiString routingKey);

    ApiResponse<SetLiveStreamStatusResponse> setLiveStreamStatusByAccountId(SetLiveStreamStatusRequestByAccountId req, AsciiString routingKey);

    ApiResponse<GetAccountRoutingInfoResponse> getAccountRoutingInfoByStreamId(GetAccountRoutingInfoRequest req);

    ApiResponse<GetCreatorStreamChannelsResponse> getStreamChannelsByCreatorCode(GetCreatorStreamChannelsRequest build, AsciiString routingKey);

    ApiResponse<GetLimitedToCreatorStreamChannelsResponse> getLimitedToCreatorStreamChannels(GetLimitedToCreatorStreamChannelsRequest build, AsciiString routingKey);

    ApiResponse<UpdateCreatorResponse> updateCreator(UpdateCreatorRequest req, AsciiString routingKey);

    ApiResponse<CreatorFollowerResponse> creatorFollower(CreatorFollowerRequest req, AsciiString routingKey);

    ApiResponse<GetCreatorCurrentEarningsResponse> getCreatorCurrentEarnings(GetCreatorCurrentEarningsRequest req, AsciiString routingKey);

    ApiResponse<GetCreatorRoundStatsResponse> getCreatorRoundStats(GetCreatorRoundStatsRequest req, AsciiString routingKey);

    ApiResponse<SetReferralResponse> setReferral(SetReferralRequest req, AsciiString routingKey);

    ApiResponse<ScheduleStreamCreateResponse> scheduleStreamCreate(ScheduleStreamCreateRequest req, AsciiString routingKey);

    ApiResponse<ScheduleStreamUpdateResponse> scheduleStreamUpdate(ScheduleStreamUpdateRequest req, AsciiString routingKey);

    ApiResponse<ScheduleStreamDeleteResponse> scheduleStreamDelete(ScheduleStreamDeleteRequest req, AsciiString routingKey);

    ApiResponse<ScheduleStreamListResponse> scheduleStreamList(ScheduleStreamListRequest req, AsciiString routingKey);

    ApiResponse<StreamWatchActivityResponse> logStreamWatchActivity(StreamWatchActivityRequest req, AsciiString routingKey);

    ApiResponse<GetTurnInfoResponse> getTurnInfo(GetTurnInfoRequest req, AsciiString routingKey);

    ApiResponse<GetLiveAndUpcomingStreamsResponse> getLiveAndUpcomingStreams(GetLiveAndUpcomingStreamsRequest req, AsciiString routingKey);

    ApiResponse<GetProductIsAllowedResponse> getProductIsAllowed(GetProductIsAllowedRequest req, AsciiString routingKey);

    ApiResponse<GetAllowedProductsResponse> getAllowedProducts(GetAllowedProductsRequest req, AsciiString routingKey);

    ApiResponse<CreateOrUpdateAllowedProductResponse> createOrUpdateAllowedProduct(CreateOrUpdateAllowedProductRequest req, AsciiString routingKey);

    ApiResponse<GetCurrencySettingsResponse> getCurrencySettings(GetCurrencySettingsRequest req, AsciiString routingKey);

    ApiResponse<CreateOrUpdateCurrencySettingsResponse> createOrUpdateCurrencySettings(CreateOrUpdateCurrencySettingsRequest req, AsciiString routingKey);

    ApiResponse<CreateAwsStageAndPublishTokenResponse> createAwsStageAndPublishToken(CreateAwsStageAndPublishTokenRequest req, AsciiString routingKey);

    ApiResponse<CreateAwsSubscribeTokenResponse> createAwsSubscribeToken(CreateAwsSubscribeTokenRequest req, AsciiString routingKey);

    ApiResponse<GetBackofficeEventsResponse> getBackofficeEvents(GetBackofficeEventsRequest req, AsciiString routingKey);

    ApiResponse<GetCreatorMultiplierHistoryResponse> getCreatorMultiplierHistory(GetCreatorMultiplierHistoryRequest req, AsciiString routingKey);

}
