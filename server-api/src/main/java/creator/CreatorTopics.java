package creator;

import java.util.concurrent.TimeUnit;

import org.apache.kafka.common.config.TopicConfig;

import com.turbospaces.api.ReplyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationConfig;

import io.netty.util.AsciiString;
import lombok.experimental.UtilityClass;

@UtilityClass
public class CreatorTopics {
    public static final Topic REQ = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("creator-req");
        }

        @Override
        public void configure(ApplicationConfig cfg) {
            cfg.setDefaultProperty(name() + "." + Topic.PARTITIONS, 16);
            cfg.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, 2);
            cfg.setDefaultProperty(name() + "." + TopicConfig.RETENTION_MS_CONFIG, Topic.DEFAULT_RETENTION_MS);
            cfg.setDefaultProperty(name() + "." + TopicConfig.MAX_MESSAGE_BYTES_CONFIG, Topic.DEFAULT_MAX_MESSAGE_BYTES);
        }
    };
    public static final ReplyTopic RESP = new ReplyTopic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("creator-server-resp");
        }
    };

    public static final Topic CREATOR_SINK_DEADLETTER = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("creator-sink-deadletter");
        }

        @Override
        public void configure(ApplicationConfig cfg) {
            cfg.setDefaultProperty(name() + "." + Topic.PARTITIONS, 4);
            cfg.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, 2);
            cfg.setDefaultProperty(name() + "." + "retention.ms", TimeUnit.DAYS.toMillis(1));
            cfg.setDefaultProperty(name() + "." + "max.message.bytes", Topic.DEFAULT_MAX_MESSAGE_BYTES);
        }
    };
    public static final Topic NOTIFY = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("creator-notify");
        }
        @Override
        public void configure(ApplicationConfig cfg) {

        }
    };
}
