package api.facade;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang3.exception.ExceptionUtils;

import com.google.common.base.Suppliers;
import com.google.protobuf.Any;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import api.v1.CacheControl;
import api.v1.ObfuscatePrinter;
import api.v1.ResponseWrapper;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import io.cloudevents.SpecVersion;
import io.cloudevents.core.builder.CloudEventBuilder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultResponseWrapperFacade implements ResponseWrapperFacade {
    private final com.google.common.base.Supplier<CloudEvent> event;
    private final ResponseWrapper delegate;
    private final ResponseStatusFacade status;
    private final ThreadLocal<com.google.common.base.Supplier<Message>> cache = new ThreadLocal<>();

    public DefaultResponseWrapperFacade(CloudEventBuilder eventTemplate, ResponseWrapper delegate) {
        this.delegate = Objects.requireNonNull(delegate);
        this.status = new DefaultResponseStatusFacade(delegate.getStatus());
        this.event = Suppliers.memoize(new com.google.common.base.Supplier<CloudEvent>() {
            @Override
            public CloudEvent get() {
                return eventTemplate
                        .withId(delegate.getHeaders().getMessageId())
                        .withType(body().getTypeUrl())
                        .withData(new CloudEventData() {
                            @Override
                            public byte[] toBytes() {
                                return delegate.toByteArray();
                            }
                        }).build();
            }
        });
    }
    @Override
    public ByteString toByteString() {
        return delegate.toByteString();
    }
    @Override
    public void writeTo(OutputStream output) throws IOException {
        delegate.writeTo(output);
    }
    @Override
    public api.v1.Headers headers() {
        return delegate.getHeaders();
    }
    @Override
    public boolean hasBody() {
        return delegate.hasBody();
    }
    @Override
    public Any body() {
        return delegate.getBody();
    }
    @Override
    public CacheControl cacheControl() {
        return delegate.getCacheControl();
    }
    @Override
    @SuppressWarnings("unchecked")
    public <T extends Message> T unpack(Class<T> type) throws IOException {
        if (Objects.isNull(cache.get())) {
            cache.set(Suppliers.memoize(new com.google.common.base.Supplier<Message>() {
                @Override
                public Message get() {
                    try {
                        Any body = delegate.getBody();
                        T unpack = body.unpack(type);
                        if (log.isTraceEnabled()) {
                            log.trace("unpack: {}", ObfuscatePrinter.shortDebugString(unpack.toBuilder()));
                        }
                        return unpack;
                    } catch (InvalidProtocolBufferException err) {
                        throw new RuntimeException(err);
                    }
                }
            }));
        }

        try {
            var supplier = cache.get();
            return (T) supplier.get();
        } catch (Exception err) {
            Throwable rootCause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(rootCause)) {
                rootCause = err;
            }
            throw new IOException(rootCause);
        }
    }
    @Override
    public ResponseStatusFacade status() {
        return status;
    }
    @Override
    public CloudEventData getData() {
        return event.get().getData();
    }
    @Override
    public Object getExtension(String extensionName) {
        return event.get().getExtension(extensionName);
    }
    @Override
    public Set<String> getExtensionNames() {
        return event.get().getExtensionNames();
    }
    @Override
    public SpecVersion getSpecVersion() {
        return event.get().getSpecVersion();
    }
    @Override
    public String getId() {
        return event.get().getId();
    }
    @Override
    public String getType() {
        return event.get().getType();
    }
    @Override
    public URI getSource() {
        return event.get().getSource();
    }
    @Override
    public String getDataContentType() {
        return event.get().getDataContentType();
    }
    @Override
    public URI getDataSchema() {
        return event.get().getDataSchema();
    }
    @Override
    public String getSubject() {
        return event.get().getSubject();
    }
    @Override
    public OffsetDateTime getTime() {
        return event.get().getTime();
    }
    @Override
    public Object getAttribute(String attributeName) {
        return event.get().getAttribute(attributeName);
    }
    @Override
    public boolean equals(Object obj) {
        return delegate.equals(obj);
    }
    @Override
    public int hashCode() {
        return delegate.hashCode();
    }
    @Override
    public String toString() {
        return delegate.toString();
    }
}
