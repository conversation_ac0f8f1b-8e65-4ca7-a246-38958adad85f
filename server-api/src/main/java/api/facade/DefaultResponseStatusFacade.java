package api.facade;

import java.util.List;
import java.util.Objects;

import com.google.protobuf.Internal.EnumLite;
import com.google.protobuf.Message;
import com.google.protobuf.ProtocolMessageEnum;
import com.turbospaces.api.facade.ResponseStatusFacade;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.Status;

public class DefaultResponseStatusFacade implements ResponseStatusFacade {
    private final Status delegate;

    public DefaultResponseStatusFacade(Status.Builder delegate) {
        this.delegate = delegate.build();
    }
    public DefaultResponseStatusFacade(Status delegate) {
        this.delegate = Objects.requireNonNull(delegate);
    }
    @Override
    public ApplicationException toException() {
        return ApplicationException.of(delegate.getErrorText(), delegate.getErrorCode());
    }
    @Override
    public ProtocolMessageEnum errorCode() {
        return delegate.getErrorCode();
    }
    @Override
    public String errorText() {
        return delegate.getErrorText();
    }
    @Override
    public boolean isOK() {
        return Code.ERR_OK.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isSystem() {
        return Code.ERR_SYSTEM.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isTimeout() {
        return Code.ERR_TIMEOUT.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isAuth() {
        return Code.ERR_AUTH.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isDuplicate() {
        return Code.ERR_DUPLICATE.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isNotFound() {
        return Code.ERR_NOT_FOUND.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isBadRequest() {
        return Code.ERR_BAD_REQUEST.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isDenied() {
        return Code.ERR_DENIED.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isInsufficientFunds() {
        return Code.ERR_INSUFFICIENT_FUNDS.equals(delegate.getErrorCode());
    }
    @Override
    public boolean equals(Object obj) {
        return delegate.equals(obj);
    }
    @Override
    public int hashCode() {
        return delegate.hashCode();
    }
    @Override
    public String toString() {
        return delegate.toString();
    }
    @Override
    public EnumLite errorReason() {
        throw new UnsupportedOperationException();
    }
    @Override
    public List<? extends Message> errorDetails() {
        throw new UnsupportedOperationException();
    }
}
