package api.mappers;

import java.io.InputStream;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.api.mappers.ResponseFacadeMapper;
import com.turbospaces.executor.WorkUnit;

import api.facade.DefaultResponseWrapperFacade;
import api.v1.ApiFactory;
import api.v1.Code;
import api.v1.Headers;
import api.v1.ResponseWrapper;
import api.v1.Status;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class DefaultResponseFacadeMapper implements ResponseFacadeMapper {
    private final ApiFactory apiFactory;

    @Override
    public ResponseWrapperFacade unpack(WorkUnit workUnit) throws Exception {
        try (InputStream io = workUnit.value().openStream()) {
            ResponseWrapper rw = ResponseWrapper.newBuilder().mergeFrom(io).build();
            return new DefaultResponseWrapperFacade(apiFactory.eventTemplate(), rw);
        }
    }
    @Override
    public ResponseWrapperFacade toTimeoutReplyWithoutBody(Throwable cause) {
        String errorText = cause.getMessage();

        Status.Builder status = Status.newBuilder();
        status.setStackTrace(ExceptionUtils.getStackTrace(cause));
        status.setErrorCode(Code.ERR_TIMEOUT);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        ResponseWrapper.Builder wrapperb = ResponseWrapper.newBuilder();
        wrapperb.setHeaders(Headers.newBuilder().setMessageId(ApiFactory.UUID.generate().toString()));
        wrapperb.setStatus(status);
        return new DefaultResponseWrapperFacade(apiFactory.eventTemplate(), wrapperb.build());
    }
}
