package api.mappers;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.google.protobuf.Any;
import com.google.protobuf.Internal.EnumLite;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.AbstractRequestFacadeMapper;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import api.facade.DefaultResponseWrapperFacade;
import api.v1.ApiFactory;
import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.Headers;
import api.v1.ReplyUtil;
import api.v1.ResponseWrapper;
import api.v1.Status;
import io.cloudevents.core.builder.CloudEventBuilder;

public class DefaultRequestFacadeMapper extends AbstractRequestFacadeMapper {
    public DefaultRequestFacadeMapper(ApiFactory apiFactory) {
        super(apiFactory);
    }
    @Override
    public ResponseWrapperFacade toReply(
            CloudEventBuilder eventTemplate,
            api.v1.Headers headers,
            Message message,
            api.v1.CacheControl cacheControl) {
        ResponseWrapper.Builder wrapperb = ResponseWrapper.newBuilder();
        wrapperb.setHeaders(headers);
        wrapperb.setBody(Any.pack(message));
        wrapperb.setStatus(Status.newBuilder().setErrorCode(Code.ERR_OK));
        wrapperb.setCacheControl(cacheControl);
        return new DefaultResponseWrapperFacade(eventTemplate, wrapperb.build());
    }
    @Override
    public ResponseWrapperFacade toReply(
            CloudEventBuilder eventTemplate,
            api.v1.Headers headers,
            Message message,
            ResponseStatusFacade status) {
        ResponseWrapper.Builder wrapperb = ResponseWrapper.newBuilder();
        wrapperb.setHeaders(headers);
        wrapperb.setBody(Any.pack(message));
        wrapperb.setStatus(api.v1.Status.newBuilder().setErrorCode(api.v1.Code.forNumber(status.errorCode().getNumber())).setErrorText(status.errorText()));
        return new DefaultResponseWrapperFacade(eventTemplate, wrapperb.build());
    }
    @Override
    public ResponseWrapperFacade toExceptionalReply(
            CloudEventBuilder eventTemplate,
            api.v1.Headers headers,
            Message message,
            EnumLite errorCode,
            String errorText) {
        ResponseWrapper.Builder wrapperb = ResponseWrapper.newBuilder();
        wrapperb.setHeaders(headers);
        wrapperb.setBody(Any.pack(message));
        wrapperb.setStatus(api.v1.Status.newBuilder().setErrorCode(api.v1.Code.forNumber(errorCode.getNumber())).setErrorText(errorText));
        return new DefaultResponseWrapperFacade(eventTemplate, wrapperb.build());
    }
    @Override
    public ResponseWrapperFacade toExceptionalReply(
            CloudEventBuilder eventTemplate,
            api.v1.Headers headers,
            Message message,
            Throwable cause) {
        Code errorCode = Code.ERR_SYSTEM;
        String errorText = String.format(ReplyUtil.ERROR_FORMAT, ApiFactory.UUID.generate());

        if (cause instanceof ApplicationException app) {
            errorCode = api.v1.Code.forNumber(app.getCode().getNumber());
            errorText = cause.getMessage();
        } else {
            String messageId = headers.getMessageId();
            errorText = String.format(ReplyUtil.ERROR_FORMAT, messageId);
        }

        Status.Builder status = Status.newBuilder();
        status.setStackTrace(ExceptionUtils.getStackTrace(cause));
        status.setErrorCode(errorCode);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        ResponseWrapper.Builder wrapperb = ResponseWrapper.newBuilder();
        wrapperb.setHeaders(headers);
        wrapperb.setStatus(status);
        wrapperb.setBody(Any.pack(message));
        return new DefaultResponseWrapperFacade(eventTemplate, wrapperb.build());
    }
    @Override
    public ResponseWrapperFacade toExceptionalAuthReply(CloudEventBuilder eventTemplate, Headers headers, Message message, String errorText) {
        return toExceptionalReply(eventTemplate, headers, message, Code.ERR_AUTH, errorText);
    }
    @Override
    public ResponseWrapperFacade toExceptionalNotFoundReply(CloudEventBuilder eventTemplate, Headers headers, Message message, String errorText) {
        return toExceptionalReply(eventTemplate, headers, message, Code.ERR_NOT_FOUND, errorText);
    }
    @Override
    public ResponseWrapperFacade toExceptionalBadRequestReply(CloudEventBuilder eventTemplate, Headers headers, Message message, String errorText) {
        return toExceptionalReply(eventTemplate, headers, message, Code.ERR_BAD_REQUEST, errorText);
    }
    @Override
    public ResponseWrapperFacade toExceptionalTimeoutReply(CloudEventBuilder eventTemplate, Headers headers, Message message, String errorText) {
        return toExceptionalReply(eventTemplate, headers, message, Code.ERR_TIMEOUT, errorText);
    }
    @Override
    public ResponseWrapperFacade toExceptionalSystemReply(CloudEventBuilder eventTemplate, Headers headers, Message message, String errorText) {
        return toExceptionalReply(eventTemplate, headers, message, Code.ERR_SYSTEM, errorText);
    }
}
