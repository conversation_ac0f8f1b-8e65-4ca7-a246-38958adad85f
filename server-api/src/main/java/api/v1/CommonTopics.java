package api.v1;

import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationConfig;

import io.netty.util.AsciiString;

public interface CommonTopics extends Topic {
    CommonTopics NOTIFY = new CommonTopics() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("rgs-notify");
        }
        @Override
        public void configure(ApplicationConfig cfg) {

        }
    };
    CommonTopics EVENT_STREAMING = new CommonTopics() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("rgs-events");
        }
        @Override
        public void configure(ApplicationConfig cfg) {

        }
    };
}
