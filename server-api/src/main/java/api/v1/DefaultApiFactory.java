package api.v1;

import com.turbospaces.api.Topic;
import com.turbospaces.api.mappers.RequestFacadeMapper;
import com.turbospaces.api.mappers.ResponseFacadeMapper;
import com.turbospaces.cfg.ApplicationProperties;

import api.mappers.DefaultRequestFacadeMapper;
import api.mappers.DefaultResponseFacadeMapper;

public class DefaultApiFactory extends AbstractApiFactory {
    public DefaultApiFactory(ApplicationProperties props) {
        super(props);
    }
    @Override
    public Topic notifyTopic() {
        return api.v1.CommonTopics.NOTIFY;
    }
    @Override
    public Topic eventsTopic() {
        return api.v1.CommonTopics.EVENT_STREAMING;
    }
    @Override
    public RequestFacadeMapper requestMapper() {
        return new DefaultRequestFacadeMapper(this);
    }
    @Override
    public ResponseFacadeMapper responseMapper() {
        return new DefaultResponseFacadeMapper(this);
    }
}
