package payment.api.temporal;

import org.springframework.cloud.DynamicCloud;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.temporal.CustomDataConverter;
import com.turbospaces.temporal.TemporalClientFactoryBean;

import io.micrometer.core.instrument.MeterRegistry;

public class PaymentTemporalClientFactoryBean extends TemporalClientFactoryBean {

    public static final String TEMPORAL_NAMESPACE_PAYMENT_SERVER = "payment-server";

    public PaymentTemporalClientFactoryBean(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry, CustomDataConverter customDataConverter) {
        super(props, TEMPORAL_NAMESPACE_PAYMENT_SERVER, cloud, meterRegistry, customDataConverter);
    }
}
