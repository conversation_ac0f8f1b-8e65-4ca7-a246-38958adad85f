package payment.api.temporal;

import java.time.Duration;
import java.util.function.Supplier;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.temporal.WorkflowPostTemplate;

import api.v1.ApiFactory;

public class PaymentWorkflowPostTemplate extends WorkflowPostTemplate {
    public PaymentWorkflowPostTemplate(ApplicationProperties props, ApiFactory apiFactory, Supplier<Duration> timeout, PaymentTemporalClientFactoryBean factory) throws Exception {
        super(props, apiFactory, timeout, factory.getObject(), PaymentTemporalClientFactoryBean.TEMPORAL_NAMESPACE_PAYMENT_SERVER);
    }
}
