package payment.api;

import com.google.protobuf.Empty;
import com.turbospaces.api.ServiceApi;
import com.turbospaces.rpc.ApiResponse;

import io.netty.util.AsciiString;
import payment.api.v1.AcceptAccountPaymentTermRequest;
import payment.api.v1.AcceptAccountPaymentTermResponse;
import payment.api.v1.AddOrUpdateBlackListRequest;
import payment.api.v1.AddOrUpdateBlackListResponse;
import payment.api.v1.AddOrUpdateErrorMappingRequest;
import payment.api.v1.AddOrUpdateErrorMappingResponse;
import payment.api.v1.AddOrUpdatePaymentProviderRequest;
import payment.api.v1.AddOrUpdatePaymentProviderResponse;
import payment.api.v1.AddOrUpdateRescueProviderResponse;
import payment.api.v1.AddOrUpdateRoutingErrorConfigRequest;
import payment.api.v1.AddOrUpdateRoutingErrorConfigResponse;
import payment.api.v1.AddOrUpdateRoutingMemberRequest;
import payment.api.v1.AddOrUpdateRoutingMemberResponse;
import payment.api.v1.AddOrUpdateRoutingRuleRequest;
import payment.api.v1.AddOrUpdateRoutingRuleResponse;
import payment.api.v1.AddOrUpdateTransactionLimitRequest;
import payment.api.v1.AddOrUpdateTransactionLimitResponse;
import payment.api.v1.AddPurchaseTrackEventRequest;
import payment.api.v1.AddPurchaseTrackEventResponse;
import payment.api.v1.AddVolumeAllocationConfigRequest;
import payment.api.v1.AddVolumeAllocationConfigResponse;
import payment.api.v1.ApplyAccountDeferredPurchaseLimitRequest;
import payment.api.v1.ApplyAccountDeferredPurchaseLimitResponse;
import payment.api.v1.CancelOfferRewardsRequest;
import payment.api.v1.CancelOfferRewardsResponse;
import payment.api.v1.CapturePaymentOrderRequest;
import payment.api.v1.CapturePaymentOrderResponse;
import payment.api.v1.ChargebackBadDeptRequest;
import payment.api.v1.ChargebackBadDeptResponse;
import payment.api.v1.ConfirmPaymentOrderResponse;
import payment.api.v1.CreateOfferRewardRequest;
import payment.api.v1.CreateOfferRewardResponse;
import payment.api.v1.DeactivateAccountPurchaseLimitRequest;
import payment.api.v1.DeactivateAccountPurchaseLimitResponse;
import payment.api.v1.DeclinePaymentOrderRequest;
import payment.api.v1.DeclinePaymentOrderResponse;
import payment.api.v1.DeleteErrorMappingRequest;
import payment.api.v1.DeleteErrorMappingResponse;
import payment.api.v1.DeleteRescueProviderResponse;
import payment.api.v1.DeleteRoutingErrorConfigRequest;
import payment.api.v1.DeleteRoutingErrorConfigResponse;
import payment.api.v1.GetAccountAcceptedPaymentTermsRequest;
import payment.api.v1.GetAccountAcceptedPaymentTermsResponse;
import payment.api.v1.GetAccountBaseQuoteRequest;
import payment.api.v1.GetAccountBaseQuoteResponse;
import payment.api.v1.GetAccountOfferTemplateForPurchaseRequest;
import payment.api.v1.GetAccountOfferTemplateForPurchaseResponse;
import payment.api.v1.GetAccountOffersTemplatesRequest;
import payment.api.v1.GetAccountOffersTemplatesResponse;
import payment.api.v1.GetAccountProviderBlackListRequest;
import payment.api.v1.GetAccountProviderBlackListResponse;
import payment.api.v1.GetAccountPurchaseLimitRequest;
import payment.api.v1.GetAccountPurchaseLimitResponse;
import payment.api.v1.GetAccountPurchaseLimitsRequest;
import payment.api.v1.GetAccountPurchaseLimitsResponse;
import payment.api.v1.GetAllOffersTemplatesRequest;
import payment.api.v1.GetAllOffersTemplatesResponse;
import payment.api.v1.GetCryptoPaymentServiceInfoRequest;
import payment.api.v1.GetCryptoPaymentServiceInfoResponse;
import payment.api.v1.GetCurrencyRateForAccountRequest;
import payment.api.v1.GetCurrencyRateForAccountResponse;
import payment.api.v1.GetCurrencyRateRequest;
import payment.api.v1.GetCurrencyRateResponse;
import payment.api.v1.GetErrorMappingRequest;
import payment.api.v1.GetErrorMappingResponse;
import payment.api.v1.GetInboxNotificationsResponse;
import payment.api.v1.GetLastPaymentDateRequest;
import payment.api.v1.GetLastPaymentDateResponse;
import payment.api.v1.GetOfferTemplateListRequest;
import payment.api.v1.GetOfferTemplateListResponse;
import payment.api.v1.GetPaymentBrandSettingsRequest;
import payment.api.v1.GetPaymentBrandSettingsResponse;
import payment.api.v1.GetPaymentMetaInfoRequest;
import payment.api.v1.GetPaymentMetaInfoResponse;
import payment.api.v1.GetPaymentProviderRequest;
import payment.api.v1.GetPaymentProviderResponse;
import payment.api.v1.GetPaymentProvidersRequest;
import payment.api.v1.GetPaymentProvidersResponse;
import payment.api.v1.GetPurchaseHistoryRequest;
import payment.api.v1.GetPurchaseHistoryResponse;
import payment.api.v1.GetRedeemsRequest;
import payment.api.v1.GetRedeemsResponse;
import payment.api.v1.GetRoutingConfigInfoRequest;
import payment.api.v1.GetRoutingConfigInfoResponse;
import payment.api.v1.GetTransactionLimitRequest;
import payment.api.v1.GetTransactionLimitResponse;
import payment.api.v1.GetUnknownUserOfferTemplatesRequest;
import payment.api.v1.GetUnknownUserOfferTemplatesResponse;
import payment.api.v1.GetUsedCardsRequest;
import payment.api.v1.GetUsedCardsResponse;
import payment.api.v1.GetWithdrawProvidersRequest;
import payment.api.v1.GetWithdrawProvidersResponse;
import payment.api.v1.InvalidateCacheRequest;
import payment.api.v1.InvalidateCacheResponse;
import payment.api.v1.ReconcilePaymentOrderRequest;
import payment.api.v1.RefreshInboxNotificationsResponse;
import payment.api.v1.RegisterProviderInSpreedlyRequest;
import payment.api.v1.RegisterProviderInSpreedlyResponse;
import payment.api.v1.RemoteAdminSetAccountPaymentMethodStatusRequest;
import payment.api.v1.RemoteAdminSetAccountPaymentMethodStatusResponse;
import payment.api.v1.ReplaceOfferRewardsRequest;
import payment.api.v1.ReplaceOfferRewardsResponse;
import payment.api.v1.ResetAccountPurchaseLimitRequest;
import payment.api.v1.ResetAccountPurchaseLimitResponse;
import payment.api.v1.SaveAccountWeeklyWagerDataRequest;
import payment.api.v1.SaveAccountWeeklyWagerDataResponse;
import payment.api.v1.SaveCardPaymentMethodMetaInfoRequest;
import payment.api.v1.SaveCardPaymentMethodMetaInfoResponse;
import payment.api.v1.SaveFXCurrencyRatesRequest;
import payment.api.v1.SaveFXCurrencyRatesResponse;
import payment.api.v1.SaveOrUpdateCurrencyRateRequest;
import payment.api.v1.SaveOrUpdateCurrencyRateResponse;
import payment.api.v1.SetAccountProviderBlackListRequest;
import payment.api.v1.SetAccountProviderBlackListResponse;
import payment.api.v1.SetAccountPurchaseLimitRequest;
import payment.api.v1.SetAccountPurchaseLimitResponse;
import payment.api.v1.SetAccountPurchaseLimitsRequest;
import payment.api.v1.SetAccountPurchaseLimitsResponse;
import payment.api.v1.SetCardTransactionDetailsRequest;
import payment.api.v1.SetCardTransactionDetailsResponse;
import payment.api.v1.SetOfferRewardStatusRequest;
import payment.api.v1.SetOfferRewardStatusResponse;
import payment.api.v1.SetPaymentProviderActivityRequest;
import payment.api.v1.SetPaymentProviderActivityResponse;
import payment.api.v1.SetRedeemProcessingDataRequest;
import payment.api.v1.SetRedeemProcessingDataResponse;
import payment.api.v1.StartChargebackCollectionRequest;
import payment.api.v1.StartChargebackCollectionResponse;
import payment.api.v1.TokenizeApplePayPaymentMethodRequest;
import payment.api.v1.TokenizeApplePayPaymentMethodResponse;
import payment.api.v1.TokenizeGooglePayPaymentMethodRequest;
import payment.api.v1.TokenizeGooglePayPaymentMethodResponse;
import payment.api.v1.UpdateCryptoCurrencyRateRequest;
import payment.api.v1.UpdateCryptoCurrencyRateResponse;
import payment.api.v1.UpdateInboxNotificationOnOrderCompleteRequest;
import payment.api.v1.UpdateInboxNotificationOnOrderCompleteResponse;
import payment.api.v1.UpdateInboxNotificationOnOrderRefreshRequest;
import payment.api.v1.UpdateInboxNotificationOnOrderRefreshResponse;
import payment.api.v1.UpdateInboxNotificationRequest;
import payment.api.v1.UpdateInboxNotificationResponse;
import payment.api.v1.UpdateVolumeAllocationConfigRequest;
import payment.api.v1.UpdateVolumeAllocationConfigResponse;
import payment.api.v1.ValidateApplePayMerchantRequest;
import payment.api.v1.ValidateApplePayMerchantResponse;
import payment.api.v1.Verify3dsRefundRequest;
import payment.api.v1.Verify3dsRefundResponse;
import uam.api.v1.AccountPaymentSettingsResponse;
import uam.api.v1.CancelAllRedeemMoneyRequest;
import uam.api.v1.CancelAllRedeemMoneyResponse;
import uam.api.v1.CancelRedeemMoneyRequest;
import uam.api.v1.CancelRedeemMoneyResponse;
import uam.api.v1.Capture3DsPaymentOrderRequest;
import uam.api.v1.Capture3DsPaymentOrderResponse;
import uam.api.v1.CaptureRedeemOptionRequest;
import uam.api.v1.CaptureRedeemOptionResponse;
import uam.api.v1.CleanUpInactiveAccountRequest;
import uam.api.v1.CleanUpInactiveAccountResponse;
import uam.api.v1.ConfirmPaymentUserAccountRequest;
import uam.api.v1.ConfirmPaymentUserAccountResponse;
import uam.api.v1.ConfirmRedeemMoneyRequest;
import uam.api.v1.ConfirmRedeemMoneyResponse;
import uam.api.v1.CreateAccountPaymentMethodRequest;
import uam.api.v1.CreateAccountPaymentMethodResponse;
import uam.api.v1.CreateNewAeroPayMethodRequest;
import uam.api.v1.CreateNewAeroPayMethodResponse;
import uam.api.v1.CreatePaymentOrderRequest;
import uam.api.v1.CreatePaymentOrderResponse;
import uam.api.v1.CreatePayperPaymentMethodRequest;
import uam.api.v1.CreatePayperPaymentMethodResponse;
import uam.api.v1.DeclineRedeemMoneyRequest;
import uam.api.v1.DeclineRedeemMoneyResponse;
import uam.api.v1.DeletePaymentMethodRequest;
import uam.api.v1.DeletePaymentMethodResponse;
import uam.api.v1.DeleteWithdrawMethodRequest;
import uam.api.v1.DeleteWithdrawMethodResponse;
import uam.api.v1.GetAccountPaymentRoutingInfoRequest;
import uam.api.v1.GetAccountPaymentRoutingInfoResponse;
import uam.api.v1.GetAccountPaymentSettingsRequest;
import uam.api.v1.GetAggregatedPaymentInfoRequest;
import uam.api.v1.GetAggregatedPaymentInfoResponse;
import uam.api.v1.GetAvailableAchProvidersRequest;
import uam.api.v1.GetAvailableAchProvidersResponse;
import uam.api.v1.GetBlockedPaymentMethodsRequest;
import uam.api.v1.GetBlockedPaymentMethodsResponse;
import uam.api.v1.GetCardPaymentMethodsRequest;
import uam.api.v1.GetCardPaymentMethodsResponse;
import uam.api.v1.GetCardsAggregationRequest;
import uam.api.v1.GetCardsAggregationResponse;
import uam.api.v1.GetDynamicSecure3dCheckRequest;
import uam.api.v1.GetDynamicSecure3dCheckResponse;
import uam.api.v1.GetIframeSecurityDataRequest;
import uam.api.v1.GetIframeSecurityDataResponse;
import uam.api.v1.GetPaymentMethodsRequest;
import uam.api.v1.GetPaymentMethodsResponse;
import uam.api.v1.GetPaymentOrderRequest;
import uam.api.v1.GetPaymentOrderResponse;
import uam.api.v1.GetPaymentServiceBankDetailsRequest;
import uam.api.v1.GetPaymentServiceBankDetailsResponse;
import uam.api.v1.GetPendingRedeemCountRequest;
import uam.api.v1.GetPendingRedeemCountResponse;
import uam.api.v1.GetRedeemDetailsRequest;
import uam.api.v1.GetRedeemDetailsResponse;
import uam.api.v1.GetRedeemHistoryRequest;
import uam.api.v1.GetRedeemHistoryResponse;
import uam.api.v1.GetRedeemLimitPolicyRequest;
import uam.api.v1.GetRedeemLimitPolicyResponse;
import uam.api.v1.GetRedeemMoneyHistoryRequest;
import uam.api.v1.GetRedeemMoneyHistoryResponse;
import uam.api.v1.GetWidgetRequest;
import uam.api.v1.GetWidgetResponse;
import uam.api.v1.GetWithdrawMethodsRequest;
import uam.api.v1.GetWithdrawMethodsResponse;
import uam.api.v1.LinkBankAccountRequest;
import uam.api.v1.LinkBankAccountResponse;
import uam.api.v1.LockRedeemMoneyRequest;
import uam.api.v1.LockRedeemMoneyResponse;
import uam.api.v1.MassUpdateAccount3dsRequest;
import uam.api.v1.MassUpdateAccount3dsResponse;
import uam.api.v1.MassUpdateDynamicSecure3dCheckRequest;
import uam.api.v1.PreConfirmRedeemMoneyRequest;
import uam.api.v1.PreConfirmRedeemMoneyResponse;
import uam.api.v1.RedeemMoneyRequest;
import uam.api.v1.RedeemMoneyResponse;
import uam.api.v1.RefreshPaymentOrderRequest;
import uam.api.v1.RefreshPaymentOrderResponse;
import uam.api.v1.RefundTransactionVerifyRequest;
import uam.api.v1.RefundTransactionVerifyResponse;
import uam.api.v1.RegisterPaymentUserAccountRequest;
import uam.api.v1.RegisterPaymentUserAccountResponse;
import uam.api.v1.ResetAccountPurchaseAndWithdrawMethodsRequest;
import uam.api.v1.ResetAccountPurchaseAndWithdrawMethodsResponse;
import uam.api.v1.SaveOfferTemplateRequest;
import uam.api.v1.SaveOfferTemplateResponse;
import uam.api.v1.SaveOrderFraudInfoRequest;
import uam.api.v1.SaveOrderFraudInfoResponse;
import uam.api.v1.SaveRedeemLimitPolicyRequest;
import uam.api.v1.SaveRedeemLimitPolicyResponse;
import uam.api.v1.SetAccountPaymentMethodStatusRequest;
import uam.api.v1.SetAccountPaymentMethodStatusResponse;
import uam.api.v1.SetChargebackInfoRequest;
import uam.api.v1.SetChargebackInfoResponse;
import uam.api.v1.SetOfferActiveStatusRequest;
import uam.api.v1.SetOfferActiveStatusResponse;
import uam.api.v1.SetOfferApprovalStatusRequest;
import uam.api.v1.SetOfferApprovalStatusResponse;
import uam.api.v1.SetRedeemFraudCheckRequest;
import uam.api.v1.SetRedeemFraudCheckResponse;
import uam.api.v1.SetRefundInfoRequest;
import uam.api.v1.SetRefundInfoResponse;
import uam.api.v1.UnlockRedeemMoneyRequest;
import uam.api.v1.UnlockRedeemMoneyResponse;
import uam.api.v1.UpdateAccountsPaymentMethodMetaInfoRequest;
import uam.api.v1.UpdateDynamicSecure3dCheckRequest;
import uam.api.v1.UpdateOfferTemplateRequest;
import uam.api.v1.UpdateOfferTemplateResponse;
import uam.api.v1.UpdatePaymentMethodMetaInfoRequest;
import uam.api.v1.UpdateRedeemRiskStatusRequest;
import uam.api.v1.UpdateRedeemRiskStatusResponse;
import uam.api.v1.UpdateStandardAchRedeemMoneyRequest;
import uam.api.v1.UpdateStandardAchRedeemMoneyResponse;
import uam.api.v1.WithdrawSyncRequest;

public interface PaymentServiceApi extends ServiceApi {
    ApiResponse<InvalidateCacheResponse> invalidateCache(InvalidateCacheRequest request, AsciiString routingKey);

    ApiResponse<CleanUpInactiveAccountResponse> cleanUpInactiveAccountInBackground(CleanUpInactiveAccountRequest request, AsciiString routingKey);
    ApiResponse<CaptureRedeemOptionResponse> captureRedeemOption(CaptureRedeemOptionRequest request, AsciiString routingKey);
    ApiResponse<RedeemMoneyResponse> createRedeemMoney(RedeemMoneyRequest request, AsciiString routingKey);
    ApiResponse<SetRedeemFraudCheckResponse> redeemFraudCheck(SetRedeemFraudCheckRequest request, AsciiString routingKey);
    ApiResponse<ConfirmRedeemMoneyResponse> confirmRedeemMoney(ConfirmRedeemMoneyRequest request, AsciiString routingKey);
    ApiResponse<PreConfirmRedeemMoneyResponse> preConfirmRedeemMoney(PreConfirmRedeemMoneyRequest request, AsciiString routingKey);
    ApiResponse<DeclineRedeemMoneyResponse> declineRedeemMoney(DeclineRedeemMoneyRequest request, AsciiString routingKey);
    ApiResponse<GetRedeemDetailsResponse> getRedeemDetails(GetRedeemDetailsRequest request, AsciiString routingKey);
    ApiResponse<GetDynamicSecure3dCheckResponse> getDynamicSecure3dCheck(GetDynamicSecure3dCheckRequest request, AsciiString routingKey);
    ApiResponse<AccountPaymentSettingsResponse> getAccountPaymentSettings(GetAccountPaymentSettingsRequest request, AsciiString routingKey);
    ApiResponse<Empty> setAccountPaymentSettings(uam.api.v1.SetAccountPaymentSettingsRequest request, AsciiString routingKey);
    ApiResponse<Empty> updateDynamicSecure3dCheck(UpdateDynamicSecure3dCheckRequest request, AsciiString routingKey);
    ApiResponse<Empty> massUpdateDynamicSecure3dCheck(MassUpdateDynamicSecure3dCheckRequest request, AsciiString routingKey);
    ApiResponse<AddOrUpdateRoutingErrorConfigResponse> addOrUpdateRoutingErrorConfig(AddOrUpdateRoutingErrorConfigRequest request, AsciiString routingKey);
    ApiResponse<GetErrorMappingResponse> getErrorMapping(GetErrorMappingRequest request, AsciiString routingKey);
    ApiResponse<AddOrUpdateErrorMappingResponse> addOrUpdateErrorMapping(AddOrUpdateErrorMappingRequest request, AsciiString routingKey);
    ApiResponse<DeleteErrorMappingResponse> deleteErrorMapping(DeleteErrorMappingRequest request, AsciiString routingKey);
    ApiResponse<DeleteRoutingErrorConfigResponse> deleteRoutingErrorConfig(DeleteRoutingErrorConfigRequest request, AsciiString routingKey);
    ApiResponse<GetRoutingConfigInfoResponse> getRoutingConfig(GetRoutingConfigInfoRequest request, AsciiString routingKey);
    ApiResponse<AddOrUpdateRoutingMemberResponse> addOrUpdateRoutingMember(AddOrUpdateRoutingMemberRequest request, AsciiString routingKey);
    ApiResponse<GetAccountPaymentRoutingInfoResponse> getAccountPaymentRoutingInfo(GetAccountPaymentRoutingInfoRequest request);
    ApiResponse<SetChargebackInfoResponse> setChargebackInfo(SetChargebackInfoRequest request, AsciiString routing);
    ApiResponse<ConfirmPaymentOrderResponse> confirmPaymentOrder(payment.api.v1.ConfirmPaymentOrderRequest request, AsciiString routing);
    ApiResponse<GetInboxNotificationsResponse> getInboxNotifications(payment.api.v1.GetInboxNotificationsRequest request, AsciiString routing);
    ApiResponse<RefreshInboxNotificationsResponse> refreshInboxNotifications(payment.api.v1.RefreshInboxNotificationsRequest request, AsciiString routing);
    ApiResponse<Empty> updateScaTransactions(payment.api.v1.UpdateScaTransactionsRequest request, AsciiString routing);
    ApiResponse<Capture3DsPaymentOrderResponse> capture3DsPaymentOrder(Capture3DsPaymentOrderRequest request, AsciiString routing);
    ApiResponse<DeclinePaymentOrderResponse> declinePaymentOrder(DeclinePaymentOrderRequest request, AsciiString routing);
    ApiResponse<GetPaymentMetaInfoResponse> getPaymentMetaInfo(GetPaymentMetaInfoRequest request, AsciiString routing);
    ApiResponse<GetPaymentProviderResponse> getPaymentProvider(GetPaymentProviderRequest request, AsciiString routingKey);
    ApiResponse<AddOrUpdatePaymentProviderResponse> addOrUpdatePaymentProvider(AddOrUpdatePaymentProviderRequest request, AsciiString routingKey);
    ApiResponse<SaveOfferTemplateResponse> addOrUpdateOfferTemplate(SaveOfferTemplateRequest request, AsciiString routingKey);
    ApiResponse<UpdateOfferTemplateResponse> updateOfferTemplate(UpdateOfferTemplateRequest request, AsciiString routingKey);
    ApiResponse<RefreshPaymentOrderResponse> refreshPaymentOrder(RefreshPaymentOrderRequest req, AsciiString routingKey);
    ApiResponse<SetAccountPaymentMethodStatusResponse> setAccountPaymentMethodStatus(SetAccountPaymentMethodStatusRequest req, AsciiString routingKey);
    ApiResponse<ResetAccountPurchaseAndWithdrawMethodsResponse> resetAccPurchaseAndWithdrawMethod(ResetAccountPurchaseAndWithdrawMethodsRequest req, AsciiString routingKey);
    ApiResponse<SetRefundInfoResponse> setRefundInfo(SetRefundInfoRequest req, AsciiString routingKey);
    ApiResponse<uam.api.v1.RefundTransactionResponse> refundTransaction(uam.api.v1.RefundTransactionRequest req, AsciiString routingKey);
    ApiResponse<RefundTransactionVerifyResponse> verifyRefundTransaction(RefundTransactionVerifyRequest req, AsciiString routingKey);
    ApiResponse<Verify3dsRefundResponse> verify3dsRefund(Verify3dsRefundRequest req, AsciiString routingKey);
    ApiResponse<ResetAccountPurchaseLimitResponse>  resetPurchaseLimit(ResetAccountPurchaseLimitRequest req, AsciiString routingKey);
    ApiResponse<SetAccountPurchaseLimitResponse> setPurchaseLimit(SetAccountPurchaseLimitRequest req, AsciiString routingKey);
    ApiResponse<SetAccountPurchaseLimitsResponse> setPurchaseLimits(SetAccountPurchaseLimitsRequest req, AsciiString routingKey);
    ApiResponse<DeactivateAccountPurchaseLimitResponse> deactivateAccountPurchaseLimit(DeactivateAccountPurchaseLimitRequest req, AsciiString routingKey);
    ApiResponse<ApplyAccountDeferredPurchaseLimitResponse> applyAccountDeferredPurchaseLimit(ApplyAccountDeferredPurchaseLimitRequest req, AsciiString routingKey);
    ApiResponse<GetAllOffersTemplatesResponse> getAllOffersTemplate(GetAllOffersTemplatesRequest req, AsciiString routingKey);
    ApiResponse<UnlockRedeemMoneyResponse> unlockRedeemMoney(UnlockRedeemMoneyRequest req, AsciiString routingKey);
    ApiResponse<LockRedeemMoneyResponse> lockRedeemMoney(LockRedeemMoneyRequest req, AsciiString routingKey);
    ApiResponse<GetPaymentProvidersResponse> getPaymentProviders(GetPaymentProvidersRequest req, AsciiString routingKey);
    ApiResponse<GetCryptoPaymentServiceInfoResponse> getCryptoPaymentServiceInfo(GetCryptoPaymentServiceInfoRequest req, AsciiString routingKey);
    ApiResponse<UpdateCryptoCurrencyRateResponse> updateCryptoCurrencyRate(UpdateCryptoCurrencyRateRequest req, AsciiString routingKey);
    ApiResponse<TokenizeApplePayPaymentMethodResponse> tokenizeApplePayPaymentMethod(TokenizeApplePayPaymentMethodRequest req, AsciiString routingKey);
    ApiResponse<TokenizeGooglePayPaymentMethodResponse> tokenizeGooglePayPaymentMethod(TokenizeGooglePayPaymentMethodRequest req, AsciiString routingKey);
    ApiResponse<ValidateApplePayMerchantResponse> validateApplePayMerchant(ValidateApplePayMerchantRequest req, AsciiString routingKey);
    ApiResponse<CancelRedeemMoneyResponse> cancelRedeemMoney(CancelRedeemMoneyRequest req, AsciiString routingKey);
    ApiResponse<CancelAllRedeemMoneyResponse> cancelAllRedeemMoney(CancelAllRedeemMoneyRequest req, AsciiString routingKey);
    ApiResponse<UpdateInboxNotificationResponse> updateInboxNotification(UpdateInboxNotificationRequest req, AsciiString routingKey);
    ApiResponse<UpdateInboxNotificationOnOrderCompleteResponse> updateInboxNotificationOnOrderComplete(UpdateInboxNotificationOnOrderCompleteRequest req, AsciiString routingKey);
    ApiResponse<UpdateInboxNotificationOnOrderRefreshResponse> updateInboxNotificationOnOrderRefresh(UpdateInboxNotificationOnOrderRefreshRequest req, AsciiString routingKey);
    ApiResponse<GetRedeemMoneyHistoryResponse> getRedeemMoneyHistory(GetRedeemMoneyHistoryRequest req, AsciiString routingKey);
    ApiResponse<GetAccountProviderBlackListResponse> getAccountProviderBlackList(GetAccountProviderBlackListRequest req, AsciiString routingKey);
    ApiResponse<SetAccountProviderBlackListResponse> setAccountProviderBlackList(SetAccountProviderBlackListRequest req, AsciiString routingKey);
    ApiResponse<GetRedeemHistoryResponse> getRedeemHistory(GetRedeemHistoryRequest req, AsciiString routingKey);
    ApiResponse<GetPendingRedeemCountResponse> getPendingRedeemCount(GetPendingRedeemCountRequest req, AsciiString routingKey);
    ApiResponse<GetPaymentMethodsResponse> getPaymentMethods(GetPaymentMethodsRequest req, AsciiString routingKey);
    ApiResponse<GetIframeSecurityDataResponse> getIframeSecurityData(GetIframeSecurityDataRequest req, AsciiString routingKey);
    ApiResponse<GetUnknownUserOfferTemplatesResponse> getUnknownUserOfferTemplates(GetUnknownUserOfferTemplatesRequest req);
    ApiResponse<GetAccountOffersTemplatesResponse> getAccountOffersTemplates(GetAccountOffersTemplatesRequest req, AsciiString routingKey);
    ApiResponse<GetAccountPurchaseLimitResponse> getAccountPurchaseLimit(GetAccountPurchaseLimitRequest req, AsciiString routingKey);
    ApiResponse<GetAccountPurchaseLimitsResponse> getAccountPurchaseLimits(GetAccountPurchaseLimitsRequest req, AsciiString routingKey);
    @Deprecated
    ApiResponse<GetAccountOfferTemplateForPurchaseResponse> getAccountOfferTemplateForPurchase(GetAccountOfferTemplateForPurchaseRequest req, AsciiString routingKey);
    ApiResponse<GetWithdrawMethodsResponse> getWithdrawMethods(GetWithdrawMethodsRequest req, AsciiString routingKey);
    ApiResponse<GetPaymentOrderResponse> getPaymentOrder(GetPaymentOrderRequest req, AsciiString routingKey);
    ApiResponse<DeletePaymentMethodResponse> deletePaymentMethod(DeletePaymentMethodRequest req, AsciiString routingKey);
    ApiResponse<DeleteWithdrawMethodResponse> deleteWithdrawMethod(DeleteWithdrawMethodRequest req, AsciiString routingKey);
    ApiResponse<CreatePaymentOrderResponse> createPaymentOrder(CreatePaymentOrderRequest req, AsciiString routingKey);
    ApiResponse<CapturePaymentOrderResponse> capturePaymentOrder(CapturePaymentOrderRequest req, AsciiString routingKey);
    ApiResponse<GetAggregatedPaymentInfoResponse> getAggregatedPaymentInfo(GetAggregatedPaymentInfoRequest req, AsciiString routingKey);
    ApiResponse<SaveOrderFraudInfoResponse> saveOrderFraudInfo(SaveOrderFraudInfoRequest request, AsciiString routingKey);
    ApiResponse<SaveAccountWeeklyWagerDataResponse> saveAccWeeklyWagerData(SaveAccountWeeklyWagerDataRequest req, AsciiString routingKey);
    ApiResponse<GetBlockedPaymentMethodsResponse> getBlockedPaymentMethods(GetBlockedPaymentMethodsRequest req, AsciiString routingKey);
    ApiResponse<SaveCardPaymentMethodMetaInfoResponse> saveCardPaymentMethodMetaInfo(SaveCardPaymentMethodMetaInfoRequest req, AsciiString routingKey);
    ApiResponse<GetLastPaymentDateResponse> getLastPaymentDate(GetLastPaymentDateRequest request, AsciiString routing);
    ApiResponse<Empty> updatePaymentMethodMetaInfoRequest(UpdatePaymentMethodMetaInfoRequest req, AsciiString routingKey);
    ApiResponse<Empty> massUpdatePaymentMethodMetaInfoRequest(UpdateAccountsPaymentMethodMetaInfoRequest req, AsciiString routingKey);
    ApiResponse<GetCardPaymentMethodsResponse> getCardPaymentMethods(GetCardPaymentMethodsRequest req, AsciiString routingKey);
    ApiResponse<Empty> reconcilePaymentOrder(ReconcilePaymentOrderRequest req, AsciiString routingKey);
    ApiResponse<CreateAccountPaymentMethodResponse> createAccountPaymentMethodRequest(CreateAccountPaymentMethodRequest request, AsciiString routingKey);
    ApiResponse<CreatePayperPaymentMethodResponse> createPayperPaymentMethod(CreatePayperPaymentMethodRequest request, AsciiString routingKey);
    ApiResponse<GetCurrencyRateResponse> getCurrencyRateRequest(GetCurrencyRateRequest request, AsciiString routingKey);
    ApiResponse<SaveOrUpdateCurrencyRateResponse> saveOrUpdateCurrencyRateRequest(SaveOrUpdateCurrencyRateRequest request, AsciiString routingKey);
    ApiResponse<SaveFXCurrencyRatesResponse> saveFxCurrencyRateRequest(SaveFXCurrencyRatesRequest request, AsciiString routingKey);
    ApiResponse<GetWithdrawProvidersResponse> getWithdrawProviders(GetWithdrawProvidersRequest request, AsciiString routingKey);
    ApiResponse<GetCurrencyRateForAccountResponse> getAccountCurrencyRate(GetCurrencyRateForAccountRequest request, AsciiString routingKey);
    ApiResponse<SaveRedeemLimitPolicyResponse> saveRedeemLimitPolicy(SaveRedeemLimitPolicyRequest request, AsciiString routingKey);
    ApiResponse<GetRedeemLimitPolicyResponse> getRedeemLimitPolicy(GetRedeemLimitPolicyRequest request, AsciiString routingKey);
    ApiResponse<GetPurchaseHistoryResponse> getPurchaseHistory(GetPurchaseHistoryRequest request, AsciiString routingKey);
    ApiResponse<GetRedeemsResponse> getRedeems(GetRedeemsRequest request, AsciiString routingKey);
    ApiResponse<Empty> withdrawSync(WithdrawSyncRequest request, AsciiString routingKey);
    ApiResponse<RemoteAdminSetAccountPaymentMethodStatusResponse> remoteAdminSetAccountPaymentMethodStatus(RemoteAdminSetAccountPaymentMethodStatusRequest req, AsciiString routingKey);
    ApiResponse<RegisterProviderInSpreedlyResponse> registerProviderInSpreedly(RegisterProviderInSpreedlyRequest req, AsciiString routingKey);
    ApiResponse<AddVolumeAllocationConfigResponse> addVolumeAllocationConfig(AddVolumeAllocationConfigRequest request, AsciiString routingKey);
    ApiResponse<UpdateVolumeAllocationConfigResponse> updateVolumeAllocationConfig(UpdateVolumeAllocationConfigRequest req, AsciiString routingKey);
    ApiResponse<AddOrUpdateRescueProviderResponse> addOrUpdateRescueProvider(payment.api.v1.AddOrUpdateRescueProviderRequest request, AsciiString routingKey);
    ApiResponse<DeleteRescueProviderResponse> deleteRescueProvider(payment.api.v1.DeleteRescueProviderRequest request, AsciiString routingKey);
    ApiResponse<AddPurchaseTrackEventResponse> addPurchaseTrackEvent(AddPurchaseTrackEventRequest request, AsciiString routingKey);
    ApiResponse<GetAvailableAchProvidersResponse> getAvailableAchProviders(GetAvailableAchProvidersRequest request, AsciiString routingKey);
    ApiResponse<UpdateStandardAchRedeemMoneyResponse> updateStandardAchRedeemMoneyRequest(UpdateStandardAchRedeemMoneyRequest request, AsciiString routingKey);
    ApiResponse<GetCardsAggregationResponse> getCardAggregation(GetCardsAggregationRequest request, AsciiString routingKey);
    ApiResponse<GetAccountBaseQuoteResponse> getAccountBaseQuoteRequest(GetAccountBaseQuoteRequest request, AsciiString routingKey);
    ApiResponse<GetUsedCardsResponse> getUsedCards(GetUsedCardsRequest request, AsciiString routingKey);
    ApiResponse<AddOrUpdateTransactionLimitResponse> addOrUpdateTransactionLimit(AddOrUpdateTransactionLimitRequest request, AsciiString routingKey);
    ApiResponse<GetTransactionLimitResponse> getTransactionLimit(GetTransactionLimitRequest request, AsciiString routingKey);
    ApiResponse<UpdateRedeemRiskStatusResponse> updateRedeemMoneyRiskStatus(UpdateRedeemRiskStatusRequest request, AsciiString routingKey);
    ApiResponse<SetPaymentProviderActivityResponse> setPaymentProviderActivity(SetPaymentProviderActivityRequest request, AsciiString routingKey);
    ApiResponse<SetRedeemProcessingDataResponse> setRedeemProcessingData(SetRedeemProcessingDataRequest request, AsciiString routingKey);
    ApiResponse<SetCardTransactionDetailsResponse> setCardTransactionDetails(SetCardTransactionDetailsRequest request, AsciiString routingKey);
    ApiResponse<ChargebackBadDeptResponse> chargebackBadDept(ChargebackBadDeptRequest request, AsciiString routingKey);
    ApiResponse<StartChargebackCollectionResponse> startChargebackCollectionProcess(StartChargebackCollectionRequest request, AsciiString routingKey);
    ApiResponse<GetOfferTemplateListResponse> getOfferTemplateList(GetOfferTemplateListRequest request, AsciiString routingKey);
    ApiResponse<AddOrUpdateRoutingRuleResponse> addOrUpdateRoutingRule(AddOrUpdateRoutingRuleRequest request, AsciiString routingKey);
    ApiResponse<AcceptAccountPaymentTermResponse> acceptPaymentTermResponse(AcceptAccountPaymentTermRequest req, AsciiString routingKey);
    ApiResponse<GetAccountAcceptedPaymentTermsResponse> getPaymentTermsResponse(GetAccountAcceptedPaymentTermsRequest req, AsciiString routingKey);
    ApiResponse<GetPaymentBrandSettingsResponse> getPaymentBrandSettings(GetPaymentBrandSettingsRequest req, AsciiString routingKey);

    ApiResponse<SetOfferApprovalStatusResponse> setOfferApprovalStatus(SetOfferApprovalStatusRequest req, AsciiString routingKey);
    ApiResponse<SetOfferActiveStatusResponse> setOfferActiveStatus(SetOfferActiveStatusRequest req, AsciiString routingKey);
    ApiResponse<CreateOfferRewardResponse> createOfferReward(CreateOfferRewardRequest req, AsciiString routingKey);
    ApiResponse<ReplaceOfferRewardsResponse> replaceOfferRewards(ReplaceOfferRewardsRequest req, AsciiString routingKey);
    ApiResponse<SetOfferRewardStatusResponse> setOfferRewardStatus(SetOfferRewardStatusRequest req, AsciiString routingKey);
    ApiResponse<CancelOfferRewardsResponse> cancelOfferRewards(CancelOfferRewardsRequest req, AsciiString routingKey);

    ApiResponse<AddOrUpdateBlackListResponse> addOrUpdateBlackList(AddOrUpdateBlackListRequest req, AsciiString routingKey);
    ApiResponse<RegisterPaymentUserAccountResponse> registerPaymentUserAccount(RegisterPaymentUserAccountRequest req, AsciiString routingKey);
    ApiResponse<LinkBankAccountResponse> linkBankAccount(LinkBankAccountRequest req, AsciiString routingKey);
    ApiResponse<MassUpdateAccount3dsResponse> massUpdateAccount3ds(MassUpdateAccount3dsRequest request, AsciiString routingKey);
    ApiResponse<GetPaymentServiceBankDetailsResponse> getPaymentServiceBankDetails(GetPaymentServiceBankDetailsRequest request, AsciiString routingKey);
    ApiResponse<ConfirmPaymentUserAccountResponse> confirmPaymentUserAccount(ConfirmPaymentUserAccountRequest req, AsciiString routingKey);
    ApiResponse<GetWidgetResponse> getWidget(GetWidgetRequest req, AsciiString routingKey);
    ApiResponse<CreateNewAeroPayMethodResponse> createNewAeroPayMethod(CreateNewAeroPayMethodRequest req, AsciiString routingKey);
}
