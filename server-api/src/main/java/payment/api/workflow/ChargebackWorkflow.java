package payment.api.workflow;

import com.turbospaces.api.ServiceApi;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import uam.api.v1.SetChargebackInfoRequest;
import uam.api.v1.SetChargebackInfoResponse;

@WorkflowInterface
public interface ChargebackWorkflow extends ServiceApi {

    @WorkflowMethod
    SetChargebackInfoResponse setChargebackInfo(SetChargebackInfoRequest request) throws Throwable;

}
