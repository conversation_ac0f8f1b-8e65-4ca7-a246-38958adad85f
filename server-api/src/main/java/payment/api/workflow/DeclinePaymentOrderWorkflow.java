package payment.api.workflow;

import com.turbospaces.api.ServiceApi;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import payment.api.v1.DeclinePaymentOrderRequest;
import payment.api.v1.DeclinePaymentOrderResponse;

@WorkflowInterface
public interface DeclinePaymentOrderWorkflow extends ServiceApi {

    @WorkflowMethod
    DeclinePaymentOrderResponse declinePaymentOrder(DeclinePaymentOrderRequest request) throws Throwable;

}
