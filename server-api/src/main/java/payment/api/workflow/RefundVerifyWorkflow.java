package payment.api.workflow;

import com.turbospaces.api.ServiceApi;

import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import uam.api.v1.RefundTransactionVerifyRequest;
import uam.api.v1.RefundTransactionVerifyResponse;

@WorkflowInterface
public interface RefundVerifyWorkflow extends ServiceApi {

    @WorkflowMethod
    RefundTransactionVerifyResponse refundVerify(RefundTransactionVerifyRequest request) throws Throwable;

}
