package payment.api.workflow;

import com.google.protobuf.Empty;
import com.turbospaces.api.ServiceApi;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import payment.api.v1.ReconcilePaymentOrderRequest;

@WorkflowInterface
public interface ReconcilePaymentWorkflow extends ServiceApi {

    @WorkflowMethod
    Empty reconcilePaymentOrder(ReconcilePaymentOrderRequest request) throws Throwable;
}
