package payment.api.workflow;

import com.turbospaces.api.ServiceApi;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import payment.api.v1.TokenizeGooglePayPaymentMethodRequest;
import payment.api.v1.TokenizeGooglePayPaymentMethodResponse;

@WorkflowInterface
public interface TokenizeGooglePayPaymentMethodWorkflow extends ServiceApi {

    @WorkflowMethod
    TokenizeGooglePayPaymentMethodResponse tokenize(TokenizeGooglePayPaymentMethodRequest request) throws Throwable;

}
