package payment.api.workflow;

import com.turbospaces.api.ServiceApi;

import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import payment.api.v1.TokenizeApplePayPaymentMethodRequest;
import payment.api.v1.TokenizeApplePayPaymentMethodResponse;

@WorkflowInterface
public interface TokenizeApplePayPaymentMethodWorkflow extends ServiceApi {
    @WorkflowMethod
    TokenizeApplePayPaymentMethodResponse tokenize(TokenizeApplePayPaymentMethodRequest request) throws Throwable;
}
