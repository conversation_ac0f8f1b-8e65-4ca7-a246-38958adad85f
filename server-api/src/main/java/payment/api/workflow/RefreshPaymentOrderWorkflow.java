package payment.api.workflow;

import com.turbospaces.api.ServiceApi;

import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import uam.api.v1.RefreshPaymentOrderRequest;
import uam.api.v1.RefreshPaymentOrderResponse;

@WorkflowInterface
public interface RefreshPaymentOrderWorkflow extends ServiceApi {

    @WorkflowMethod
    RefreshPaymentOrderResponse refresh(RefreshPaymentOrderRequest request) throws Throwable;
}
