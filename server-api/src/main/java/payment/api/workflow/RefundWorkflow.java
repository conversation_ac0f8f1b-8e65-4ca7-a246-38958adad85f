package payment.api.workflow;

import com.turbospaces.api.ServiceApi;

import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;
import uam.api.v1.RefundTransactionRequest;
import uam.api.v1.RefundTransactionResponse;

@WorkflowInterface
public interface RefundWorkflow extends ServiceApi {

    @WorkflowMethod
    RefundTransactionResponse refundTransaction(RefundTransactionRequest request) throws Throwable;

}
