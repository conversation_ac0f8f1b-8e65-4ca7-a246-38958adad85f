package payment.crypto;

import io.ebean.annotation.DbEnumValue;

public enum CryptoNetwork {
    //order is mandatory for Ui sort
    TRX,
    SOL,
    ETH,
    BNB,
    BTC,
    LTC,
    POLYGON,
    BCH,
    AVAX,
    TON,
    TETH;

    private final String code;

    CryptoNetwork() {
        this.code = name().toLowerCase().intern();
    }

    @DbEnumValue
    public String code() {
        return code;
    }
}
