package payment.crypto;

import io.ebean.annotation.DbEnumValue;

public enum CryptoCurrency {
    //order is mandatory for Ui sort
    LTC, USDT, USDC,
    ETH, BCH, BTC,
    TST,
    CRO, APE, UNI,
    POL, XRP;

    public boolean isStableCoin() {
        return this == CryptoCurrency.USDT || this == USDC;
    }

    public boolean isTestCoin() {
        return this == CryptoCurrency.TST;
    }
    private final String code;

    CryptoCurrency() {
        this.code = name().toLowerCase().intern();
    }

    @DbEnumValue
    public String code() {
        return code;
    }

    public static CryptoCurrency fromString(String name) {
        String n = name.toLowerCase();
        for (CryptoCurrency currency : CryptoCurrency.values()) {
            if (currency.name().equalsIgnoreCase(n)) {
                return currency;
            }
        }
        throw new IllegalArgumentException("unknown crypto currency: " + n);
    }
}
