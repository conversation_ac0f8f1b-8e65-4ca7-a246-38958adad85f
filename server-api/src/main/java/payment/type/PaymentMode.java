package payment.type;

import io.ebean.annotation.DbEnumValue;
import lombok.Getter;

public enum PaymentMode {
    CARD("card"),
    SKRILL("skrill"),
    WIRE_TRANSFER("wire_transfer"),
    VIRTUAL_WALLET_APPLE_PAY("virtual_wallet"),
    VIRTUAL_WALLET_GOOGLE_PAY("virtual_wallet"),
    NON_MONETARY("non_monetary"),
    IN_APP_PURCHASE("in-app-purchase"),
    CRYPTO("crypto");

    @Getter
    private final String type;
    private final String code;

    PaymentMode(String type) {
        this.type = type;
        this.code = name().toLowerCase().intern();
    }
    @DbEnumValue
    public String code() {
        return code;
    }

    public static PaymentMode fromCode(String code) {
        for (PaymentMode type : PaymentMode.values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown paymentMode: " + code);
    }
}
