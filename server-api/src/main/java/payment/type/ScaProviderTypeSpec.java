package payment.type;

import io.ebean.annotation.DbEnumValue;

public enum ScaProviderTypeSpec {
    SPREEDLY_SCA_EMERCHANTPAY("spreedly_sca_emerchantpay"),
    SPREEDLY_SCA_RAPYD("spreedly_sca_rapyd"),
    SPREEDLY_SCA_FISERV("spreedly_sca_fiserv"),
    SPREEDLY_SCA_WORLDPAY("spreedly_sca_worldpay"),
    SPREEDLY_SCA_TEST("spreedly_sca_test");

    ScaProviderTypeSpec(String code) {
        this.code = code;
    }
    @DbEnumValue
    public String code() {
        return code;
    }
    private final String code;
}
