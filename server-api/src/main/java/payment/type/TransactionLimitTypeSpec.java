package payment.type;

import java.util.EnumSet;
import java.util.Set;

import io.ebean.annotation.DbEnumValue;
import lombok.Getter;

public enum TransactionLimitTypeSpec {
    SINGLE_REDEEM("redeem"),
    SINGLE_DEPOSIT("deposit"),
    TOTAL_DEPOSIT_BEFORE_KYC("deposit"),
    TOTAL_DEPOSIT_DAILY("deposit");

    private final String dbValue;
    @Getter
    private final String operation;

    public static final Set<TransactionLimitTypeSpec> DEPOSIT_LIMITS = EnumSet.of(SINGLE_DEPOSIT, TOTAL_DEPOSIT_BEFORE_KYC, TOTAL_DEPOSIT_DAILY);
    public static final Set<TransactionLimitTypeSpec> REDEEM_LIMITS = EnumSet.of(SINGLE_REDEEM);

    TransactionLimitTypeSpec(String operation) {
        this.dbValue = name().toLowerCase().intern();
        this.operation = operation;
    }
    
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

}
