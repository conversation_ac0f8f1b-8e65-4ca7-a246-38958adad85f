package payment.type;

import com.fasterxml.jackson.annotation.JsonCreator;

import io.ebean.annotation.DbEnumValue;

public enum OfferTypeSpec {
    ONE_TIME,
    @Deprecated
    SUBSCRIPTION,
    PERMANENT,
    PERSONALIZED,
    DAILY,
    WEEKLY,
    FIXED_DAILY,
    FIXED_WEEKLY,
    REWARD;

    private final String code;

    OfferTypeSpec() {
        this.code = name().toLowerCase().intern();
    }
    @DbEnumValue
    public String code() {
        return code;
    }
    public boolean isOneTimeOrPermanent() {
        return isOneTime() || isPermanent();
    }
    public boolean isOneTime() {
        return this == ONE_TIME;
    }
    public boolean isPermanent() {
        return this == PERMANENT;
    }
    public boolean isPersonalized() {
        return this == PERSONALIZED;
    }
    public boolean isFixed() {
        return this == FIXED_DAILY || this == FIXED_WEEKLY;
    }
    public boolean isReward() {
        return this == REWARD;
    }
    @JsonC<PERSON>
    public static OfferTypeSpec fromString(String name) {
        String n = name.toLowerCase();
        for (OfferTypeSpec type : OfferTypeSpec.values()) {
            if (type.code.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown offer type: " + n);
    }

}
