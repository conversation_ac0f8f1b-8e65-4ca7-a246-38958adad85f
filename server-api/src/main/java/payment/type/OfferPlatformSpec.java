package payment.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import io.ebean.annotation.DbEnumValue;

public enum OfferPlatformSpec {
    WEB,
    ANDROID,
    IOS,
    NATIVE;

    private final String code;

    OfferPlatformSpec() {
        this.code = name().toLowerCase().intern();
    }

    @DbEnumValue
    public String code() {
        return this.code;
    }

    public boolean isWeb() {
        return this == WEB;
    }

    public boolean isIos() {
        return this == IOS;
    }

    public boolean isAndroid() {
        return this == ANDROID;
    }

    public boolean isNative() {
        return this == NATIVE;
    }

    @JsonCreator
    public static OfferPlatformSpec fromString(String name) {
        String code = name.toLowerCase();
        for (var type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown platform: " + name);
    }
}
