package payment.type;

import com.fasterxml.jackson.annotation.JsonCreator;

import io.ebean.annotation.DbEnumValue;

public enum WelcomeOfferTypeSpec {
    NONE,
    DEFAULT,
    UTM,
    AFFILIATE,
    REFERRAL_CODE,
    RAF;

    private final String code;

    WelcomeOfferTypeSpec() {
        this.code = name().toLowerCase().intern();
    }
    @DbEnumValue
    public String code() {
        return code;
    }
    @JsonCreator
    public static WelcomeOfferTypeSpec fromString(String name) {
        String n = name.toLowerCase();
        for (WelcomeOfferTypeSpec type : WelcomeOfferTypeSpec.values()) {
            if (type.code.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown offer type: " + n);
    }

}
