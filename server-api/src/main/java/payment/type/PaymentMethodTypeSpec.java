package payment.type;

import java.util.List;

import io.ebean.annotation.DbEnumValue;
import lombok.Getter;

@Getter
public enum PaymentMethodTypeSpec {

    SPREEDLY_GATEWAY("SpreedlyGateway"), // ~ card
    SPREEDLY_GATEWAY_APPLE_PAY("SpreedlyGatewayApplePay"),
    SPREEDLY_GATEWAY_GOOGLE_PAY("SpreedlyGatewayGooglePay"),
    PAY_WITH_MY_BANK("PayWithMyBank"), // ~ only for compatibility
    TRUSTLY("Trustly"),
    FISERV_CARD("FiservCard"),
    FISERV_GOOGLE_PAY("FiservGooglePay"),
    FISERV_APPLE_PAY("FiservApplePay"),
    NUVEI_GATEWAY("NuveiGateway"),
    SKRILL("Skrill"),
    APPLE_IN_APP("AppleInApp"),
    ANDROID_IN_APP("AndroidInApp"),
    <PERSON><PERSON>ER("Payper"),
    CRYPTO("Crypto"),
    AEROPAY("AeroPay");

    public static final List<String> CARD_METHOD_TYPES = List.of(PaymentMethodTypeSpec.SPREEDLY_GATEWAY.code, PaymentMethodTypeSpec.FISERV_CARD.code);
    public static final List<String> VIRTUAL_WALLET_TYPES = List.of(PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY.code, PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY.code, PaymentMethodTypeSpec.FISERV_APPLE_PAY.code, PaymentMethodTypeSpec.FISERV_GOOGLE_PAY.code);
    private final String code;

    PaymentMethodTypeSpec(String code) {
        this.code = code;
    }
    @DbEnumValue
    public String code() {
        return code;
    }

    public static PaymentMethodTypeSpec fromCode(String code) {
        for (PaymentMethodTypeSpec type : PaymentMethodTypeSpec.values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown code: " + code);
    }

    public boolean isSpreedly() {
        return this == SPREEDLY_GATEWAY || this == SPREEDLY_GATEWAY_APPLE_PAY || this == SPREEDLY_GATEWAY_GOOGLE_PAY;
    }

    public boolean isSpreedlyVirtualCard() {
        return this == SPREEDLY_GATEWAY_APPLE_PAY || this == SPREEDLY_GATEWAY_GOOGLE_PAY;
    }
    
}
