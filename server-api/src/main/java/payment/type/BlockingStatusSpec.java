package payment.type;

public enum BlockingStatusSpec {
    ACTIVE("Active", false),
    BLOCKED("Blocked", true);

    final String value;
    final boolean blocked;

    BlockingStatusSpec(String value, boolean blocked) {
        this.value = value;
        this.blocked = blocked;
    }

    public String getValue() {
        return value;
    }

    public boolean isBlocked() {
        return blocked;
    }
}
