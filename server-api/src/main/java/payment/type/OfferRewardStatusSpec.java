package payment.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import io.ebean.annotation.DbEnumValue;
import lombok.Getter;

public enum OfferRewardStatusSpec {
    CREATED(false),
    CANCELLED(true),
    USED(true),
    EXPIRED(true);

    private final String code;
    @Getter
    private final boolean isFinal;

    OfferRewardStatusSpec(boolean isFinal) {
        this.code = name().toLowerCase().intern();
        this.isFinal = isFinal;
    }
    
    @DbEnumValue
    public String code() {
        return code;
    }

    @JsonCreator
    public static OfferRewardStatusSpec fromString(String name) {
        var code = name.toLowerCase();
        for (var type : OfferRewardStatusSpec.values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown offer reward status: " + code);
    }
    
}
