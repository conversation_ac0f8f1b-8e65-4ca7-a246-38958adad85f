package payment.type;

public record CardIdentifier(String bin, String lastFour) {
    public static final String STARS = "******";
    public static final int CARD_NUMBER_SIZE = 16;
    @Override
    public String toString() {
        return String.format("%s%s%s", bin, STARS, lastFour);
    }
    public static CardIdentifier parse(String line) {
        if (line == null || line.length() != CARD_NUMBER_SIZE) {
            throw new IllegalArgumentException("Wrong payment instrument format + " + line);
        }
        return createCardIdentifierByString(line);
    }

    public static CardIdentifier parseWithStatus(String line) {
        if (line == null || !line.contains(" ") || line.substring(0, line.indexOf(" ")).length() != CARD_NUMBER_SIZE) {
            throw new IllegalArgumentException("Wrong payment instrument format + " + line);
        }
        return createCardIdentifierByString(line);
    }

    private static CardIdentifier createCardIdentifierByString(String line) {
        return new CardIdentifier(line.substring(0, 6), line.substring(12, 16));
    }
}