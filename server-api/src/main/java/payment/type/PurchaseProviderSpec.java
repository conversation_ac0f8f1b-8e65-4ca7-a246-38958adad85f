package payment.type;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.ebean.annotation.DbEnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import payment.PaymentProto;
import payment.model.ProviderIntegrationTypeSpec;
import uam.api.v1.PaymentProvider;

@AllArgsConstructor
@Getter
public enum PurchaseProviderSpec {
    SKRILL(
            PaymentProvider.SKRILL,
            PaymentProto.UPS_SKRILL,
            PaymentMode.SKRILL,
            PaymentMethodTypeSpec.SKRILL,
            ProviderIntegrationTypeSpec.SKRILL,
            CurrencyType.FIAT),
    TRUSTLY(
            PaymentProvider.TRUSTLY,
            PaymentProto.UPS_TRUSTLY,
            PaymentMode.WIRE_TRANSFER,
            PaymentMethodTypeSpec.TRUSTLY,
            ProviderIntegrationTypeSpec.TRUSTLY,
            CurrencyType.FIAT),
    NUVEI_MAZOOMA_ACH(
            PaymentProvider.NUVEI_MAZOOMA_ACH,
            PaymentProto.UPS_NUVEI,
            PaymentMode.WIRE_TRANSFER,
            PaymentMethodTypeSpec.NUVEI_GATEWAY,
            ProviderIntegrationTypeSpec.NUVEI_MAZOOMA_ACH,
            CurrencyType.FIAT),
    FISERV(
            PaymentProvider.FISERV,
            PaymentProto.UPS_FISERV,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.FISERV_CARD,
            ProviderIntegrationTypeSpec.FISERV,
            CurrencyType.FIAT),
    SPREEDLY(
            PaymentProvider.SPREEDLY,
            PaymentProto.UPS_SPREEDLY,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY,
            CurrencyType.FIAT),
    SPREEDLY_RAPYD(
            PaymentProvider.SPREEDLY_RAPYD,
            PaymentProto.UPS_SPREEDLY_RAPYD,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_RAPYD,
            CurrencyType.FIAT),
    SPREEDLY_RAPYD_2(
            PaymentProvider.SPREEDLY_RAPYD_2,
            PaymentProto.UPS_SPREEDLY_RAPYD_2,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_RAPYD,
            CurrencyType.FIAT),
    SPREEDLY_RAPYD_3(
            PaymentProvider.SPREEDLY_RAPYD_3,
            PaymentProto.UPS_SPREEDLY_RAPYD_3,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_RAPYD,
            CurrencyType.FIAT),
    SPREEDLY_RAPYD_4(
            PaymentProvider.SPREEDLY_RAPYD_4,
            PaymentProto.UPS_SPREEDLY_RAPYD_4,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_RAPYD,
            CurrencyType.FIAT),
    SPREEDLY_RAPYD_5(
            PaymentProvider.SPREEDLY_RAPYD_5,
            PaymentProto.UPS_SPREEDLY_RAPYD_5,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_RAPYD,
            CurrencyType.FIAT),
    SPREEDLY_FISERV(
            PaymentProvider.SPREEDLY_FISERV,
            PaymentProto.UPS_SPREEDLY_FISERV,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_2(
            PaymentProvider.SPREEDLY_FISERV_2,
            PaymentProto.UPS_SPREEDLY_FISERV_2,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_3(
            PaymentProvider.SPREEDLY_FISERV_3,
            PaymentProto.UPS_SPREEDLY_FISERV_3,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_4(
            PaymentProvider.SPREEDLY_FISERV_4,
            PaymentProto.UPS_SPREEDLY_FISERV_4,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_5(
            PaymentProvider.SPREEDLY_FISERV_5,
            PaymentProto.UPS_SPREEDLY_FISERV_5,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_6(
            PaymentProvider.SPREEDLY_FISERV_6,
            PaymentProto.UPS_SPREEDLY_FISERV_6,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_EMERCHANTPAY(
            PaymentProvider.SPREEDLY_EMERCHANTPAY,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),
    SPREEDLY_EMERCHANTPAY_2(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_2,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_2,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),
    SPREEDLY_EMERCHANTPAY_3(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_3,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_3,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),
    SPREEDLY_EMERCHANTPAY_4(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_4,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_4,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),
    SPREEDLY_EMERCHANTPAY_5(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_5,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_5,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),
    SPREEDLY_EMERCHANTPAY_6(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_6,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_6,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),
    FISERV_GOOGLE_PAY(
            PaymentProvider.FISERV_GOOGLE_PAY,
            PaymentProto.UPS_FISERV_GOOGLE_PAY,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.FISERV_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.FISERV,
            CurrencyType.FIAT),
    FISERV_APPLE_PAY(
            PaymentProvider.FISERV_APPLE_PAY,
            PaymentProto.UPS_FISERV_APPLE_PAY,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.FISERV_APPLE_PAY,
            ProviderIntegrationTypeSpec.FISERV,
            CurrencyType.FIAT),
    SPREEDLY_APPLE_PAY(
            PaymentProvider.SPREEDLY_APPLE_PAY,
            PaymentProto.UPS_SPREEDLY_APPLE_PAY,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_APPLE_PAY(
            PaymentProvider.SPREEDLY_FISERV_APPLE_PAY,
            PaymentProto.UPS_SPREEDLY_FISERV_APPLE_PAY,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_APPLE_PAY_2(
            PaymentProvider.SPREEDLY_FISERV_APPLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_FISERV_APPLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_APPLE_PAY_3(
            PaymentProvider.SPREEDLY_FISERV_APPLE_PAY_3,
            PaymentProto.UPS_SPREEDLY_FISERV_APPLE_PAY_3,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_APPLE_PAY_4(
            PaymentProvider.SPREEDLY_FISERV_APPLE_PAY_4,
            PaymentProto.UPS_SPREEDLY_FISERV_APPLE_PAY_4,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_APPLE_PAY_5(
            PaymentProvider.SPREEDLY_FISERV_APPLE_PAY_5,
            PaymentProto.UPS_SPREEDLY_FISERV_APPLE_PAY_5,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_APPLE_PAY_6(
            PaymentProvider.SPREEDLY_FISERV_APPLE_PAY_6,
            PaymentProto.UPS_SPREEDLY_FISERV_APPLE_PAY_6,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_GOOGLE_PAY(
            PaymentProvider.SPREEDLY_GOOGLE_PAY,
            PaymentProto.UPS_SPREEDLY_GOOGLE_PAY,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_GOOGLE_PAY(
            PaymentProvider.SPREEDLY_FISERV_GOOGLE_PAY,
            PaymentProto.UPS_SPREEDLY_FISERV_GOOGLE_PAY,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_GOOGLE_PAY_2(
            PaymentProvider.SPREEDLY_FISERV_GOOGLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_FISERV_GOOGLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_GOOGLE_PAY_3(
            PaymentProvider.SPREEDLY_FISERV_GOOGLE_PAY_3,
            PaymentProto.UPS_SPREEDLY_FISERV_GOOGLE_PAY_3,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_GOOGLE_PAY_4(
            PaymentProvider.SPREEDLY_FISERV_GOOGLE_PAY_4,
            PaymentProto.UPS_SPREEDLY_FISERV_GOOGLE_PAY_4,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_GOOGLE_PAY_5(
            PaymentProvider.SPREEDLY_FISERV_GOOGLE_PAY_5,
            PaymentProto.UPS_SPREEDLY_FISERV_GOOGLE_PAY_5,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_FISERV_GOOGLE_PAY_6(
            PaymentProvider.SPREEDLY_FISERV_GOOGLE_PAY_6,
            PaymentProto.UPS_SPREEDLY_FISERV_GOOGLE_PAY_6,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_FISERV,
            CurrencyType.FIAT),
    SPREEDLY_TEST_GATEWAY(
            PaymentProvider.SPREEDLY_TEST_GATEWAY,
            PaymentProto.UPS_SPREEDLY_TEST_GATEWAY,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY,
            CurrencyType.FIAT),
    SPREEDLY_TEST_GATEWAY_2(
            PaymentProvider.SPREEDLY_TEST_GATEWAY_2,
            PaymentProto.UPS_SPREEDLY_TEST_GATEWAY_2,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY,
            CurrencyType.FIAT),
    SPREEDLY_TEST_GATEWAY_3(
            PaymentProvider.SPREEDLY_TEST_GATEWAY_3,
            PaymentProto.UPS_SPREEDLY_TEST_GATEWAY_3,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY,
            CurrencyType.FIAT),
    SPREEDLY_WORLDPAY(
            PaymentProvider.SPREEDLY_WORLDPAY,
            PaymentProto.UPS_SPREEDLY_WORLDPAY,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),
    SPREEDLY_CHECKOUT(
            PaymentProvider.SPREEDLY_CHECKOUT,
            PaymentProto.UPS_SPREEDLY_CHECKOUT,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_CHECKOUT,
            CurrencyType.FIAT),
    SPREEDLY_CHECKOUT_APPLE_PAY(
            PaymentProvider.SPREEDLY_CHECKOUT_APPLE_PAY,
            PaymentProto.UPS_SPREEDLY_CHECKOUT_APPLE_PAY,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_CHECKOUT,
            CurrencyType.FIAT),
    SPREEDLY_CHECKOUT_GOOGLE_PAY(
            PaymentProvider.SPREEDLY_CHECKOUT_GOOGLE_PAY,
            PaymentProto.UPS_SPREEDLY_CHECKOUT_GOOGLE_PAY,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_CHECKOUT,
            CurrencyType.FIAT),
    SPREEDLY_WORLDPAY_2(
            PaymentProvider.SPREEDLY_WORLDPAY_2,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_2,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),
    SPREEDLY_WORLDPAY_3(
            PaymentProvider.SPREEDLY_WORLDPAY_3,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_3,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),
    SPREEDLY_WORLDPAY_4(
            PaymentProvider.SPREEDLY_WORLDPAY_4,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_4,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_APPLE_PAY(
            PaymentProvider.SPREEDLY_WORLDPAY_APPLE_PAY,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_APPLE_PAY,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_APPLE_PAY_2(
            PaymentProvider.SPREEDLY_WORLDPAY_APPLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_APPLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_APPLE_PAY_3(
            PaymentProvider.SPREEDLY_WORLDPAY_APPLE_PAY_3,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_APPLE_PAY_3,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_APPLE_PAY_4(
            PaymentProvider.SPREEDLY_WORLDPAY_APPLE_PAY_4,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_APPLE_PAY_4,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_GOOGLE_PAY(
            PaymentProvider.SPREEDLY_WORLDPAY_GOOGLE_PAY,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_GOOGLE_PAY,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_GOOGLE_PAY_2(
            PaymentProvider.SPREEDLY_WORLDPAY_GOOGLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_GOOGLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_GOOGLE_PAY_3(
            PaymentProvider.SPREEDLY_WORLDPAY_GOOGLE_PAY_3,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_GOOGLE_PAY_3,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_WORLDPAY_GOOGLE_PAY_4(
            PaymentProvider.SPREEDLY_WORLDPAY_GOOGLE_PAY_4,
            PaymentProto.UPS_SPREEDLY_WORLDPAY_GOOGLE_PAY_4,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_APPLE_PAY(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_APPLE_PAY,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_APPLE_PAY,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_APPLE_PAY_2(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_APPLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_APPLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_APPLE_PAY_3(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_APPLE_PAY_3,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_APPLE_PAY_3,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_APPLE_PAY_4(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_APPLE_PAY_4,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_APPLE_PAY_4,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_APPLE_PAY_5(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_APPLE_PAY_5,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_APPLE_PAY_5,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_APPLE_PAY_6(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_APPLE_PAY_6,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_APPLE_PAY_6,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_GOOGLE_PAY,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_GOOGLE_PAY,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_2(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_3(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_3,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_3,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_4(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_4,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_4,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_5(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_5,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_5,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_6(
            PaymentProvider.SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_6,
            PaymentProto.UPS_SPREEDLY_EMERCHANTPAY_GOOGLE_PAY_6,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY,
            CurrencyType.FIAT),

    SPREEDLY_NUVEI(
            PaymentProvider.SPREEDLY_NUVEI,
            PaymentProto.UPS_SPREEDLY_NUVEI,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_NUVEI,
            CurrencyType.FIAT),
    SPREEDLY_NUVEI_2(
            PaymentProvider.SPREEDLY_NUVEI_2,
            PaymentProto.UPS_SPREEDLY_NUVEI_2,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_NUVEI,
            CurrencyType.FIAT),
    SPREEDLY_PAYNEARME(
            PaymentProvider.SPREEDLY_PAYNEARME,
            PaymentProto.UPS_SPREEDLY_PAYNEARME,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME,
            CurrencyType.FIAT),
    SPREEDLY_PAYNEARME_2(
            PaymentProvider.SPREEDLY_PAYNEARME_2,
            PaymentProto.UPS_SPREEDLY_PAYNEARME_2,
            PaymentMode.CARD,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY,
            ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME,
            CurrencyType.FIAT),
    SPREEDLY_PAYNEARME_APPLE_PAY(
            PaymentProvider.SPREEDLY_PAYNEARME_APPLE_PAY,
            PaymentProto.UPS_SPREEDLY_PAYNEARME_APPLE_PAY,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME,
            CurrencyType.FIAT),
    SPREEDLY_PAYNEARME_APPLE_PAY_2(
            PaymentProvider.SPREEDLY_PAYNEARME_APPLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_PAYNEARME_APPLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_APPLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_APPLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME,
            CurrencyType.FIAT),
    SPREEDLY_PAYNEARME_GOOGLE_PAY(
            PaymentProvider.SPREEDLY_PAYNEARME_GOOGLE_PAY,
            PaymentProto.UPS_SPREEDLY_PAYNEARME_GOOGLE_PAY,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME,
            CurrencyType.FIAT),
    SPREEDLY_PAYNEARME_GOOGLE_PAY_2(
            PaymentProvider.SPREEDLY_PAYNEARME_GOOGLE_PAY_2,
            PaymentProto.UPS_SPREEDLY_PAYNEARME_GOOGLE_PAY_2,
            PaymentMode.VIRTUAL_WALLET_GOOGLE_PAY,
            PaymentMethodTypeSpec.SPREEDLY_GATEWAY_GOOGLE_PAY,
            ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME,
            CurrencyType.FIAT),
    PAYPER(
            PaymentProvider.PAYPER,
            PaymentProto.UPS_PAYPER,
            PaymentMode.WIRE_TRANSFER,
            PaymentMethodTypeSpec.PAYPER,
            ProviderIntegrationTypeSpec.PAYPER,
            CurrencyType.FIAT),

    APPLE_IN_APP(
            PaymentProvider.APPLE_IN_APP,
            PaymentProto.UPS_APPLE_IN_APP,
            PaymentMode.IN_APP_PURCHASE,
            PaymentMethodTypeSpec.APPLE_IN_APP,
            ProviderIntegrationTypeSpec.APPLE_IN_APP,
            CurrencyType.FIAT),
    ANDROID_IN_APP(
            PaymentProvider.ANDROID_IN_APP,
            PaymentProto.UPS_ANDROID_IN_APP,
            PaymentMode.IN_APP_PURCHASE,
            PaymentMethodTypeSpec.ANDROID_IN_APP,
            ProviderIntegrationTypeSpec.ANDROID_IN_APP,
            CurrencyType.FIAT),
    CRYPTO(
            PaymentProvider.CRYPTO,
            null,
            PaymentMode.CRYPTO,
            PaymentMethodTypeSpec.CRYPTO,
            ProviderIntegrationTypeSpec.CRYPTO,
            CurrencyType.CRYPTO),
    ORBITAL(
            PaymentProvider.ORBITAL,
            PaymentProto.UPS_ORBITAL_HPP,
            PaymentMode.CRYPTO,
            PaymentMethodTypeSpec.CRYPTO,
            ProviderIntegrationTypeSpec.ORBITAL,
            CurrencyType.CRYPTO),
    AEROPAY(
            PaymentProvider.AEROPAY,
            PaymentProto.UPS_AEROPAY_DIRECT,
            PaymentMode.WIRE_TRANSFER,
            PaymentMethodTypeSpec.AEROPAY,
            ProviderIntegrationTypeSpec.AEROPAY,
            CurrencyType.FIAT);

    private static final Map<PaymentProvider, PurchaseProviderSpec> SEVER_API_PROVIDER_MAPPING = Arrays.stream(values())
            .collect(Collectors.toMap(v -> v.paymentProviderServerApi, Function.identity()));
    public static final List<PaymentProvider> SPREEDLY_FISERV_CARD_MIDS = Arrays.stream(PurchaseProviderSpec.values())
            .filter(p -> p.getIntegrationTypeSpec().equals(ProviderIntegrationTypeSpec.SPREEDLY_FISERV))
            .filter(p -> p.getPaymentMode().equals(PaymentMode.CARD))
            .map(PurchaseProviderSpec::getPaymentProviderServerApi)
            .collect(Collectors.toList());
    public static final List<PaymentProvider> SPREEDLY_PAYNEARME_CARD_MIDS = Arrays.stream(PurchaseProviderSpec.values())
            .filter(p -> p.getIntegrationTypeSpec().equals(ProviderIntegrationTypeSpec.SPREEDLY_PAYNEARME))
            .filter(p -> p.getPaymentMode().equals(PaymentMode.CARD))
            .map(PurchaseProviderSpec::getPaymentProviderServerApi)
            .collect(Collectors.toList());
    public static final List<PaymentProvider> SPREEDLY_WORLDPAY_CARD_MIDS = Arrays.stream(PurchaseProviderSpec.values())
            .filter(p -> p.getIntegrationTypeSpec().equals(ProviderIntegrationTypeSpec.SPREEDLY_WORLDPAY))
            .filter(p -> p.getPaymentMode().equals(PaymentMode.CARD))
            .map(PurchaseProviderSpec::getPaymentProviderServerApi)
            .collect(Collectors.toList());
    public static final List<PaymentProvider> SPREEDLY_RAPYD_CARD_MIDS = Arrays.stream(PurchaseProviderSpec.values())
            .filter(p -> p.getIntegrationTypeSpec().equals(ProviderIntegrationTypeSpec.SPREEDLY_RAPYD))
            .filter(p -> p.getPaymentMode().equals(PaymentMode.CARD))
            .map(PurchaseProviderSpec::getPaymentProviderServerApi)
            .collect(Collectors.toList());
    public static final List<PaymentProvider> SPREEDLY_EMERCHANTPAY_ALL_MIDS = Arrays.stream(PurchaseProviderSpec.values())
            .filter(p -> p.getIntegrationTypeSpec().equals(ProviderIntegrationTypeSpec.SPREEDLY_EMERCHANTPAY))
            .filter(p -> p.getPaymentMode().equals(PaymentMode.CARD))
            .map(PurchaseProviderSpec::getPaymentProviderServerApi)
            .collect(Collectors.toList());

    public static final List<List<PaymentProvider>> PROVIDERS_WITH_SAME_TOKENIZATION = List.of(SPREEDLY_FISERV_CARD_MIDS, SPREEDLY_WORLDPAY_CARD_MIDS,
            SPREEDLY_PAYNEARME_CARD_MIDS);
    public static final List<List<PaymentProvider>> PROVIDERS_WITH_CUSTOMER_REUSE = List.of(SPREEDLY_FISERV_CARD_MIDS, SPREEDLY_RAPYD_CARD_MIDS);
    public static final List<PurchaseProviderSpec> TEST_GATEWAY_PROVIDERS = List.of(SPREEDLY_TEST_GATEWAY, SPREEDLY_TEST_GATEWAY_2, SPREEDLY_TEST_GATEWAY_3);

    private final PaymentProvider paymentProviderServerApi;
    private final String secret;
    private final PaymentMode paymentMode;
    private final PaymentMethodTypeSpec methodType;
    private final ProviderIntegrationTypeSpec integrationTypeSpec;
    private final CurrencyType currencyType;

    @DbEnumValue
    public String code() {
        return name().toLowerCase().intern();
    }

    public static PurchaseProviderSpec fromServerApi(PaymentProvider provider) {
        return SEVER_API_PROVIDER_MAPPING.get(provider);
    }

    public static PurchaseProviderSpec fromServerApi(String provider) {
        return SEVER_API_PROVIDER_MAPPING.get(PaymentProvider.valueOf(provider.toUpperCase()));
    }

    public static PurchaseProviderSpec fromCode(String code) {
        return Arrays.stream(values()).filter(p -> p.code().equals(code)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("unknown PurchaseProviderSpec: " + code));
    }

    public List<PaymentProvider> getProvidersWithCustomerReuse() {
        var provider = getPaymentProviderServerApi();
        return PROVIDERS_WITH_CUSTOMER_REUSE.stream()
                .filter(val -> val.contains(provider))
                .findFirst()
                .orElse(List.of(provider))
                .reversed();
    }

    public List<PaymentProvider> getProvidersWithSameTokenization() {
        var provider = getPaymentProviderServerApi();
        return PROVIDERS_WITH_SAME_TOKENIZATION.stream()
                .filter(val -> val.contains(provider))
                .findFirst()
                .orElse(List.of(provider))
                .reversed();
    }

    public static List<PurchaseProviderSpec> forProviderMode(PaymentMode mode) {
        return Arrays.stream(values()).filter(p -> p.getPaymentMode() == mode).toList();
    }
    public static List<PurchaseProviderSpec> forCurrencyType(CurrencyType type) {
        return Arrays.stream(values()).filter(p -> p.getCurrencyType() == type).toList();
    }

    public static List<PurchaseProviderSpec> forIntegration(ProviderIntegrationTypeSpec integrationTypeSpec) {
        return Arrays.stream(values()).filter(p -> p.getIntegrationTypeSpec() == integrationTypeSpec).toList();
    }

    public static List<PurchaseProviderSpec> getGatewayProviders() {
        return List.of(SPREEDLY, SPREEDLY_APPLE_PAY, SPREEDLY_GOOGLE_PAY);
    }

    public boolean isCrypto() {
        return getCurrencyType() == CurrencyType.CRYPTO;
    }
    public boolean isFiat() {
        return getCurrencyType() == CurrencyType.FIAT;
    }
}
