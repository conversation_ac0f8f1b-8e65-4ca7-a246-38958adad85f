package payment.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;
import payment.PaymentProto;
import payment.model.ProviderIntegrationTypeSpec;
import payment.model.WithdrawMethodSpec;
import uam.api.v1.RedeemProvider;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static payment.PaymentProto.UPS_MASSPAY;

@AllArgsConstructor
@Getter
public enum RedeemProviderSpec {
    SKRILL(RedeemProvider.SKRILL_REDEEM, PaymentProto.UPS_SKRILL, PaymentMode.SKRILL, WithdrawMethodSpec.SKRILL, ProviderIntegrationTypeSpec.SKRILL, CurrencyType.FIAT),
    TRUSTLY(RedeemProvider.TRUSTLY_REDEEM, PaymentProto.UPS_TRUSTLY, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.TRUSTLY, ProviderIntegrationTypeSpec.TRUSTLY, CurrencyType.FIAT),
    PRIZEOUT(RedeemProvider.PRIZEOUT_REDEEM, PaymentProto.UPS_PRIZEOUT, PaymentMode.NON_MONETARY, WithdrawMethodSpec.PRIZEOUT, ProviderIntegrationTypeSpec.PRIZEOUT, CurrencyType.FIAT),
    NUVEI_MAZOOMA_ACH(RedeemProvider.NUVEI_MAZOOMA_ACH_REDEEM, PaymentProto.UPS_NUVEI, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.NUVEI_MAZOOMA_ACH, ProviderIntegrationTypeSpec.NUVEI_MAZOOMA_ACH, CurrencyType.FIAT),
    MASSPAY_ACH(RedeemProvider.MASSPAY_ACH_REDEEM, UPS_MASSPAY, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.MASSPAY_ACH, ProviderIntegrationTypeSpec.MASSPAY_ACH, CurrencyType.FIAT),
    MASSPAY_ACH_2(RedeemProvider.MASSPAY_ACH_2_REDEEM, PaymentProto.UPS_MASSPAY_2, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.MASSPAY_ACH, ProviderIntegrationTypeSpec.MASSPAY_ACH, CurrencyType.FIAT),
    PAYPER(RedeemProvider.PAYPER_REDEEM, PaymentProto.UPS_PAYPER, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.PAYPER, ProviderIntegrationTypeSpec.PAYPER, CurrencyType.FIAT),
    STANDARD_ACH(RedeemProvider.STANDARD_ACH_REDEEM, null, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.STANDARD_ACH, ProviderIntegrationTypeSpec.STANDARD_ACH, CurrencyType.FIAT),
    AIRWALLEX_ACH(RedeemProvider.AIRWALLEX_ACH_REDEEM, PaymentProto.UPS_AIRWALLEX, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.AIRWALLEX_ACH, ProviderIntegrationTypeSpec.AIRWALLEX_ACH, CurrencyType.FIAT),
    PAYNEARME_ACH(RedeemProvider.PAYNEARME_ACH_REDEEM, PaymentProto.UPS_PAYNEARME, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.PAYNEARME_ACH, ProviderIntegrationTypeSpec.PAYNEARME_ACH, CurrencyType.FIAT),
    AIRWALLEX_ACH_2(RedeemProvider.AIRWALLEX_ACH_2_REDEEM, PaymentProto.UPS_AIRWALLEX_2, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.AIRWALLEX_ACH, ProviderIntegrationTypeSpec.AIRWALLEX_ACH, CurrencyType.FIAT),
    AEROPAY_ACH(RedeemProvider.AEROPAY_ACH_REDEEM, PaymentProto.UPS_AEROPAY, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.AEROPAY_ACH, ProviderIntegrationTypeSpec.AEROPAY_ACH, CurrencyType.FIAT),
    AEROPAY(RedeemProvider.AEROPAY_REDEEM, PaymentProto.UPS_AEROPAY_DIRECT, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.AEROPAY, ProviderIntegrationTypeSpec.AEROPAY, CurrencyType.FIAT),
    CHECKBOOK_ACH(RedeemProvider.CHECKBOOK_ACH_REDEEM, PaymentProto.UPS_CHECKBOOK, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.CHECKBOOK_ACH, ProviderIntegrationTypeSpec.CHECKBOOK_ACH, CurrencyType.FIAT),
    NSC(RedeemProvider.NSC_REDEEM, null, PaymentMode.WIRE_TRANSFER, WithdrawMethodSpec.NSC, ProviderIntegrationTypeSpec.NSC, CurrencyType.FIAT),

    CRYPTO(RedeemProvider.CRYPTO_REDEEM, null, PaymentMode.CRYPTO, WithdrawMethodSpec.CRYPTO, ProviderIntegrationTypeSpec.CRYPTO, CurrencyType.CRYPTO),
    ORBITAL(RedeemProvider.ORBITAL_REDEEM, PaymentProto.UPS_ORBITAL_API, PaymentMode.CRYPTO, WithdrawMethodSpec.ORBITAL, ProviderIntegrationTypeSpec.ORBITAL, CurrencyType.CRYPTO);

    private static final Map<RedeemProvider, RedeemProviderSpec> SERVER_API_PROVIDER_MAPPING = Arrays.stream(values())
            .collect(Collectors.toMap(v -> v.apiType, Function.identity()));

    private static final List<RedeemProviderSpec> ACH_PROVIDERS = Arrays.stream(values())
            .filter(v -> v.methodType.isAchProvider())
            .toList();

    @Getter
    private final RedeemProvider apiType;
    private final String secret;
    private final PaymentMode paymentMode;
    @Getter
    private final WithdrawMethodSpec methodType;
    @Getter
    private final ProviderIntegrationTypeSpec integrationType;
    private final CurrencyType currencyType;


    public static RedeemProviderSpec fromServerApi(RedeemProvider provider) {
        return SERVER_API_PROVIDER_MAPPING.get(provider);
    }

    public static List<RedeemProviderSpec> getAchProviders() {
        return ACH_PROVIDERS;
    }

    @JsonCreator
    public static RedeemProviderSpec fromString(String name) {
        return fromStringOptional(name).orElseThrow(() -> new IllegalArgumentException("unknown redeem provider: " + name));
    }

    public static List<RedeemProviderSpec> forCurrencyType(CurrencyType type) {
        return Arrays.stream(values()).filter(p -> p.getCurrencyType() == type).toList();
    }

    public static List<RedeemProviderSpec> forIntegration(ProviderIntegrationTypeSpec integrationTypeSpec) {
        return Arrays.stream(values()).filter(p -> p.getIntegrationType() == integrationTypeSpec).toList();
    }

    public static Optional<RedeemProviderSpec> fromStringOptional(String name) {
        String n = name.toLowerCase();
        for (RedeemProviderSpec type : RedeemProviderSpec.values()) {
            if (type.name().equalsIgnoreCase(n)) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }

    public boolean isCrypto() {
        return getCurrencyType() == CurrencyType.CRYPTO;
    }
    public boolean isFiat() {
        return getCurrencyType() == CurrencyType.FIAT;
    }
}
