package payment.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import io.ebean.annotation.DbEnumValue;

import java.util.Arrays;

public enum BlackListTypeSpec {

    BIN,
    BIN_TYPE,
    BIN_LEVEL,
    BIN_COUNTRY_CODE;

    private final String code;

    BlackListTypeSpec() {
        this.code = name().toLowerCase().intern();
    }

    @DbEnumValue
    public String code() {
        return code;
    }

    @JsonCreator
    public static BlackListTypeSpec fromString(String code) {
        return Arrays.stream(BlackListTypeSpec.values())
                .filter(type -> type.code.equalsIgnoreCase(code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("unknown blackListType: %s".formatted(code)));
    }

    public boolean isBinFamily() {
        return this == BlackListTypeSpec.BIN || this == BlackListTypeSpec.BIN_TYPE || this == BlackListTypeSpec.BIN_LEVEL
                || this == BlackListTypeSpec.BIN_COUNTRY_CODE;
    }

}
