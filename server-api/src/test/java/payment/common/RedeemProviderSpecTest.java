package payment.common;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import payment.type.RedeemProviderSpec;
import uam.api.v1.RedeemProvider;

class RedeemProviderSpecTest {
    @ParameterizedTest
    @EnumSource(RedeemProvider.class)
    void testAllMappingsAreSet(RedeemProvider type) {
        if (type.equals(RedeemProvider.DO_NOT_CHANGE_REDEEM_PROVIDER) || type.equals(RedeemProvider.UNRECOGNIZED)) {
            return;
        }
        assertEquals(type.name(), RedeemProviderSpec.fromServerApi(type).name() + "_REDEEM");
    }
}
