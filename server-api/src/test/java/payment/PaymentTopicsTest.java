package payment;

import org.apache.kafka.clients.KafkaClient;
import org.apache.kafka.common.config.TopicConfig;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.netflix.archaius.api.Config;
import com.turbospaces.api.AcceptorsTopicRegistry;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationConfig;

class PaymentTopicsTest {
    @Test
    void test() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        cfg.setDefaultProperty("mock", KafkaClient.class.getSimpleName());

        AcceptorsTopicRegistry registry = new AcceptorsTopicRegistry(PaymentTopics.class);

        for (Topic topic : registry) {
            topic.configure(cfg);
        }
        for (Topic topic : registry) {
            if (topic.name().toString().equals(PaymentTopics.NOTIFY.name().toString())) {
                continue;
            }

            Config prefixed = cfg.getPrefixedView(topic.name().toString());
            for (String key : prefixed.keys()) {
                prefixed.getRawProperty(key);
            }
            Assertions.assertFalse(prefixed.isEmpty());
            Assertions.assertTrue(prefixed.containsKey(TopicConfig.RETENTION_MS_CONFIG));
            Assertions.assertTrue(prefixed.containsKey(TopicConfig.MAX_MESSAGE_BYTES_CONFIG));
        }
    }
}
