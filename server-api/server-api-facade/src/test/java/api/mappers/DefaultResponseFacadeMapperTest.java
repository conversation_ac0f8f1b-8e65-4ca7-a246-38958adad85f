package api.mappers;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.io.ByteSource;
import com.google.protobuf.Any;
import com.google.protobuf.util.Timestamps;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.KafkaWorkUnit;

import api.DefaultApiFactory;
import api.facade.DefaultResponseStatusFacade;
import api.facade.DefaultResponseWrapperFacade;
import api.util.ErrorDetailUtils;
import api.v1.ApiFactory;
import api.v1.Code;
import api.v1.Reason;
import io.cloudevents.kafka.KafkaMessageFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class DefaultResponseFacadeMapperTest {
    @Test
    void test() throws Exception {
        var cfg = ApplicationConfig.mock();
        var props = new ApplicationProperties(cfg.factory());
        var mapper = new CommonObjectMapper();
        var apiFactory = new DefaultApiFactory(props, mapper);

        var headers = api.v1.Headers.newBuilder();
        headers.setBrandName("bluedream");
        headers.setMessageId(ApiFactory.UUID.generate().toString());
        headers.setTimeout(30);
        headers.setReplyTo("reply" + System.currentTimeMillis());

        var status = api.v1.Status.newBuilder();
        status.setErrorCode(Code.ERR_NOT_FOUND);
        status.setErrorText("Unexpected error occured");
        status.setReason(Reason.BRAND_NOT_FOUND);
        status.addErrorDetails(ErrorDetailUtils.detail("k1", "v1"));
        status.addErrorDetails(ErrorDetailUtils.detail("k2", "v2"));

        ObjectNode node = mapper.createObjectNode();
        node.put("k1", "v1");
        node.put("k2", "v2");

        var cacheControl = api.v1.CacheControl.newBuilder();
        cacheControl.setMaxAge(30);
        cacheControl.setNoCache(true);
        cacheControl.setMustRevalidate(true);
        cacheControl.setSMaxAge(60);

        var body = Any.pack(Timestamps.now());

        var facade = new DefaultResponseWrapperFacade(
                apiFactory.eventTemplate(),
                apiFactory.objectMapper(),
                headers.build(),
                status.build(), body, cacheControl.build(), props);

        var messageWriter = KafkaMessageFactory.createWriter("bluedream", null, System.currentTimeMillis(), null);
        var record = messageWriter.writeBinary(facade);

        record.headers().forEach(new Consumer<>() {
            @Override
            public void accept(Header header) {
                log.info(header.key() + " : " + new String(header.value()));
            }
        });

        Assertions.assertEquals("1.0", new String(record.headers().lastHeader("ce_specversion").value()));
        Assertions.assertEquals(headers.getMessageId(), new String(record.headers().lastHeader("ce_id").value()));
        Assertions.assertNotNull(record.headers().lastHeader("ce_source").value());
        Assertions.assertEquals(body.getTypeUrl(), new String(record.headers().lastHeader("ce_type").value()));
        Assertions.assertEquals(Long.toString(Reason.BRAND_NOT_FOUND.getNumber()), new String(record.headers().lastHeader("ce_reason").value()));
        Assertions.assertEquals(status.getErrorText(), new String(record.headers().lastHeader("ce_errortext").value()));
        Assertions.assertEquals(headers.getMessageId(), new String(record.headers().lastHeader("ce_messageid").value()));
        Assertions.assertEquals(headers.getBrandName(), new String(record.headers().lastHeader("ce_brandname").value()));
        Assertions.assertEquals(Integer.toString(headers.getTimeout()), new String(record.headers().lastHeader("ce_timeout").value()));
        Assertions.assertEquals(Boolean.toString(cacheControl.getMustRevalidate()), new String(record.headers().lastHeader("ce_mustrevalidate").value()));
        Assertions.assertEquals(Integer.toString(cacheControl.getMaxAge()), new String(record.headers().lastHeader("ce_maxage").value()));
        Assertions.assertEquals(Integer.toString(cacheControl.getSMaxAge()), new String(record.headers().lastHeader("ce_smaxage").value()));
        Assertions.assertEquals(Boolean.toString(cacheControl.getNoCache()), new String(record.headers().lastHeader("ce_nocache").value()));
        Assertions.assertEquals(node.toString(), new String(record.headers().lastHeader("ce_errordetails").value()));
        Assertions.assertEquals(headers.getReplyTo(), new String(record.headers().lastHeader("ce_replyto").value()));
        Assertions.assertEquals(Boolean.TRUE.toString(), new String(record.headers().lastHeader("ce_nativeformat").value()));
        Assertions.assertEquals(Integer.toString(Code.ERR_NOT_FOUND.getNumber()), new String(record.headers().lastHeader("ce_errorcode").value()));

        var unpack = apiFactory.responseMapper().unpack(new KafkaWorkUnit() {
            @Override
            public ByteSource value() {
                return ByteSource.wrap(record.value());
            }
            @Override
            public String topic() {
                return record.topic();
            }
            @Override
            public long timestamp() {
                return record.timestamp();
            }
            @Override
            public byte[] key() {
                return null;
            }
            @Override
            public int partition() {
                return record.partition();
            }
            @Override
            public long offset() {
                return System.currentTimeMillis();
            }
            @Override
            public Headers headers() {
                return record.headers();
            }
            @Override
            public Optional<String> lastHeader(String key) {
                return Optional.ofNullable(headers().lastHeader(key)).map(new Function<>() {
                    @Override
                    public String apply(Header header) {
                        return new String(header.value());
                    }
                });
            }
        });

        Assertions.assertEquals(cacheControl.build(), unpack.cacheControl());
        Assertions.assertEquals(headers.build(), unpack.headers());
        Assertions.assertEquals(new DefaultResponseStatusFacade(status.build()), unpack.status());
        Assertions.assertEquals(facade, unpack);
    }
}
