package api.mappers;

import java.io.ByteArrayInputStream;
import java.io.ObjectInputStream;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Any;
import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.turbospaces.api.Topic;
import com.turbospaces.api.facade.DefaultNotificationWrapperFacade;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.dispatch.NotifyQueuePostSpec;
import com.turbospaces.executor.CloudEventWithRoutingKeyWorkUnit;

import api.DefaultApiFactory;
import api.v1.CloudEventWithRoutingKey;
import io.netty.util.AsciiString;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class DefaultNotificationFacadeMapperTest {
    long now = System.currentTimeMillis();
    Topic topic = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("topic");
        }
        @Override
        public void configure(ApplicationConfig config) {

        }
    };

    @Test
    void withRoutingKey() throws Exception {
        var cfg = ApplicationConfig.mock();
        var props = new ApplicationProperties(cfg.factory());
        var apiFactory = new DefaultApiFactory(props, new ObjectMapper());
        var messageId = PlatformUtil.randomUUID();
        Timestamp timestamp = Timestamps.now();
        var body = Any.pack(timestamp);
        var routingKey = AsciiString.cached(getClass().getSimpleName());
        var facade = new DefaultNotificationWrapperFacade(apiFactory.eventTemplate(), messageId, body);

        byte[] data = PlatformUtil.serialize(new CloudEventWithRoutingKey(now, routingKey, NotifyQueuePostSpec.newBuilder(facade).setTopic(topic).build()));
        ByteArrayInputStream io = new ByteArrayInputStream(data);
        try (ObjectInputStream inputStream = new ObjectInputStream(io)) {
            CloudEventWithRoutingKey event = new CloudEventWithRoutingKey();
            event.readExternal(inputStream);

            var unpack = apiFactory.notificationMapper().unpack(new CloudEventWithRoutingKeyWorkUnit("topic", event));
            Assertions.assertEquals(facade, unpack);
            Assertions.assertEquals(timestamp.getNanos(), unpack.body().unpack(Timestamp.class).getNanos());
            Assertions.assertEquals(timestamp.getSeconds(), unpack.body().unpack(Timestamp.class).getSeconds());
        }
    }
    @Test
    void withoutRoutingKey() throws Exception {
        var cfg = ApplicationConfig.mock();
        var props = new ApplicationProperties(cfg.factory());
        var apiFactory = new DefaultApiFactory(props, new ObjectMapper());
        var messageId = PlatformUtil.randomUUID();
        Timestamp timestamp = Timestamps.now();
        var body = Any.pack(timestamp);
        var facade = new DefaultNotificationWrapperFacade(apiFactory.eventTemplate(), messageId, body);

        byte[] data = PlatformUtil.serialize(new CloudEventWithRoutingKey(now, null, NotifyQueuePostSpec.newBuilder(facade).setTopic(topic).build()));
        ByteArrayInputStream io = new ByteArrayInputStream(data);
        try (ObjectInputStream inputStream = new ObjectInputStream(io)) {
            CloudEventWithRoutingKey event = new CloudEventWithRoutingKey();
            event.readExternal(inputStream);

            var unpack = apiFactory.notificationMapper().unpack(new CloudEventWithRoutingKeyWorkUnit("topic", event));

            Assertions.assertEquals(facade, unpack);
            Assertions.assertEquals(timestamp.getNanos(), unpack.body().unpack(Timestamp.class).getNanos());
            Assertions.assertEquals(timestamp.getSeconds(), unpack.body().unpack(Timestamp.class).getSeconds());
        }
    }
}
