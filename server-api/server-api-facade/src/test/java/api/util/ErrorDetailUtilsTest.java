package api.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;

import api.v1.ErrorDetail;

class ErrorDetailUtilsTest {

    @Test
    void detail_shouldThrowNPE_whenKeyIsNull() {
        assertThrows(NullPointerException.class, () -> ErrorDetailUtils.detail(null, null));
    }

    @Test
    void detail_shouldSetValueToEmptyString_whenValueIsNull() {
        ErrorDetail detail = ErrorDetailUtils.detail("key1", null);
        assertDetail(detail, "key1", "");
    }

    @Test
    void detail() {
        ErrorDetail detail = ErrorDetailUtils.detail("key2", "value");
        assertDetail(detail, "key2", "value");
    }

    private static void assertDetail(ErrorDetail detail, String key, String value) {
        assertEquals(key, detail.getKey());
        assertEquals(value, detail.getValue());
    }
}