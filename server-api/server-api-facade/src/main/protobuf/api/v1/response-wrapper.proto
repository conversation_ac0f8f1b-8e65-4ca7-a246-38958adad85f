syntax = "proto3";
package api.v1;

import "google/protobuf/any.proto";
import "api/v1/caching.proto";
import "api/v1/headers.proto";

option java_multiple_files = true;
option optimize_for = CODE_SIZE;

message ResponseWrapper {
  Headers headers = 1;
  google.protobuf.Any body = 2;
  Status status = 3;
  .api.v1.CacheControl cacheControl = 4;
}

message ErrorDetail {
  string key = 1;
  string value = 2;
}

message Status {
  Code errorCode = 1;
  string errorText = 2;
  string stackTrace = 3;
  google.protobuf.Any violation = 4;
  Reason reason = 5;
  repeated ErrorDetail errorDetails = 6;
}

enum Code {
  ERR_OK = 0;
  ERR_OK_NO_CONTENT = 113;

  ERR_SYSTEM = 1;
  ERR_AUTH = 2;
  ERR_BAD_REQUEST = 3;
  ERR_DENIED = 4;
  ERR_FROZEN = 5;
  ERR_NOT_FOUND = 6;
  ERR_TIMEOUT = 7;
  ERR_DUPLICATE = 8;
  ERR_INSUFFICIENT_FUNDS = 9;
  ERR_PAYMENT = 10;
  ERR_PAYMENT_ROUTING = 11;
  ERR_PAYMENT_INPUT_CVV = 12;
  ERR_PAYMENT_RETURN = 13;
  ERR_FRAUD_DECLINED = 14;
  ERR_KYC_REQUIRED = 15;
  ERR_KYC_REQUIRED_LOW_RISK = 22;
  ERR_KYC_REQUIRED_MID_RISK = 23;
  ERR_KYC_REQUIRED_HIGH_RISK = 24;
  ERR_PAYMENT_METHOD_PAUSED = 21;
  ERR_PAYMENT_3DS_REQUIRED = 25;
  ERR_PAYMENT_IN_PROGRESS = 115;
  ERR_3DS_FAILED = 26;
  ERR_3DS_PAYMENT_ROUTING = 27;
  ERR_NETWORK = 30;
  ERR_CARDHOLDER_NAME_MISMATCH = 105;
  ERR_CARD_BLOCKED = 106;
  ERR_CARD_NOT_VERIFIED = 107;
  ERR_PAYMENT_ROUTING_EMPTY_CHAIN = 108;
  ERR_PAYMENT_INACTIVE_PROVIDER = 109;
  ERR_PAYMENT_PURCHASE_LIMIT = 110;
  ERR_PAYMENT_EMAIL_NOT_VERIFIED = 111;
  ERR_PAYMENT_METHOD_BLOCKED = 112;

  ERR_TC_REQUIRED = 16;
  ERR_SR_REQUIRED = 17;
  ERR_PP_REQUIRED = 18;
  ERR_PT_REQUIRED = 19;

  ERR_PASSWORD_WEAK = 101;
  ERR_PASSWORD_SAME = 102;
  ERR_PASSWORD_INCORRECT = 103;
  ERR_PASSWORD_REPEATS = 104;
  ERR_OTP_LIMIT = 114;
  ERR_OTP_REQUIRED = 116;
  ERR_PASSWORD_SET_ON_EXISTING_PASSWORD = 123;

  ERR_RESTRICTED_LOCATION = 117;
  ERR_PICKEM_LIMIT = 118;
  ERR_3DS_NOT_SUPPORTED = 119;
  ERR_SOFT_REQUIRED_GPS = 120;
  ERR_SOFT_RESTRICTED_LOCATION = 121;
  ERR_PROMO_SMS_CONSENT_OTP_REQUIRED = 122;
}

enum Reason {
  UNSPECIFIED = 0;
  SERVER_ERROR = 1;
  TIMEOUT_ERROR = 2;
  BAD_REQUEST = 3;
  INSUFFICIENT_FUNDS = 4;
  WEAK_PASSWORD = 5;
  INCORRECT_PASSWORD = 6;
  TC_REQUIRED = 7;
  SR_REQUIRED = 8;
  PP_REQUIRED = 9;
  SAME_PASSWORD = 10;
  MISSING_CODE_OR_ROUTE = 11;
  UNKNOWN_BRAND = 12;
  WRONG_PUBLIC_KEY = 13;
  APPLE_PRIVATE_KEY_NOT_FOUND = 14;
  ACCESS_TOKEN_OR_CODE_NOT_VALID = 15;
  VIRTUAL_NUMBERS_NOT_ALLOWED = 16;
  LANDLINE_NUMBERS_NOT_ALLOWED = 17;
  OTP_CODE_EXPIRED = 18;
  FREE_GOLD_COINS_NOT_AVAILABLE = 19;
  PLAYER_CANNOT_ACCEPT_FREE_GOLD_COINS = 20;
  ACCOUNT_COMMENT_TOO_LONG = 21;
  TRANSACTION_TYPE_NOT_SUPPORTED = 22;
  VERIFICATION_REQUEST_NOT_FOUND = 23;
  ACCOUNT_DUPLICATE = 24;
  STATE_NOT_ALLOWED = 25;
  INVALID_PHONE_NUMBER = 26;
  VOIP_NUMBERS_NOT_ALLOWED = 27;
  POSTAL_CODE_DUPLICATE = 28;
  KEY_NOT_FOUND = 29;
  PHONE_NUMBER_DUPLICATE = 30;
  COUNTERPARTY_PROVIDER_MISS_MATCH = 31;
  PHONE_NUMBER_BLOCKED = 32;
  OTP_VERIFICATION_BLOCKED = 33;
  SWEEPSTAKE_DENIED = 34;
  PASSWORD_RESET_TOKEN_EXPIRED = 35;
  UNKNOWN_IDENTITY = 36;
  NOT_OWNED_BY_ACCOUNT = 37;
  PRODUCT_IS_NOT_ACTIVE = 38;
  SESSION_EXPIRED = 39;
  TOKEN_NOT_ISSUED_FOR_IDENTITY = 40;
  EMAIL_NOT_VERIFIED = 41;
  ACCOUNT_LOCKED = 42;
  EMAIL_NOT_VALID = 43;
  PLATFORM_CHANGE_FOR_EXISTING_BANNER = 44;
  PRODUCT_DUPLICATE = 45;
  FREE_SPIN_CAMPAIGN_DUPLICATE = 46;
  GOOGLE_RECAPTCHA_ERROR = 47;
  VERIFICATION_ATTEMPTS_EXCEEDED = 48;
  BRAND_NOT_FOUND = 49;
  ACCOUNT_DELETED = 50;
  TRANSACTION_NOT_FOUND = 51;
  NO_UNIQUE_TRANSACTION = 52;
  NOT_ALLOWED_BY_POLICY = 53;
  CURRENCY_NOT_ALLOWED = 54;
  NEGATIVE_REDEEMABLE_VALUE = 55;
  COMMAND_NOT_ROUTABLE = 56;
  NEGATIVE_PENDING_WITHDRAW = 57;
  PHONE_NUMBER_CONFIRMED_ALREADY = 58;
  AGE_VERIFICATION_ERROR = 59;
  REGISTRATION_NOT_ALLOWED = 60;
  COUNTRY_TOO_LONG = 61;
  ID_COUNTRY_TOO_LONG = 62;
  WALLET_SESSION_NOT_FOUND = 63;
  TEMP_TOKEN_EXPIRED_OR_USED = 64;
  GUEST_LOGOUT = 65;
  VERIFICATION_PHASE_NOT_FOUND = 66;
  PRODUCT_NOT_FOUND = 67;
  ACCOUNT_RESTRICTED = 68;
  COUNTRY_NOT_SUPPORTED = 69;
  UNKNOWN_SIGN_IN_METHOD = 70;
  UNSUPPORTED_COUNTRY_CODE_FORMAT = 71;
  TOKEN_COMPROMISED = 72;
  TOKEN_NOT_VALID = 73;
  VERIFICATION_PROVIDER_NOT_SUPPORTED = 74;
  SOFT_KYC_DATA_NOT_PROVIDED = 75;
  TWILIO_VERIFICATION_FAILED = 76;
  SIGN_IN_BY_TOKEN_NOT_ALLOWED = 77;
  UNABLE_TO_AUTHENTICATE_WITH_PROVIDER = 78;
  UNABLE_TO_GET_TOKEN = 79;
  UNABLE_TO_GET_PUBLIC_KEYS = 80;
  NO_ERROR_CODE_FOR_DOC_UPLOAD = 81;
  UNABLE_TO_FIND_BET_LIMITS = 82;
  ACCOUNT_NOT_FOUND = 83;
  RESET_TOKEN_NOT_FOUND = 84;
  CONFIRM_TOKEN_NOT_FOUND = 85;
  FREE_SPIN_CAMPAIGN_NOT_FOUND = 86;
  UNABLE_TO_FIND_PRODUCT_BY_CODE = 87;
  UNABLE_TO_FIND_PRODUCT_BY_ROUTE = 88;
  UNABLE_TO_FIND_PROVIDER_BY_CODE = 89;
  REWARD_CAMPAIGN_CODE_NOT_FOUND = 90;
  REWARD_CAMPAIGN_ID_NOT_FOUND = 91;
  CREDITOR_NAME_NOT_FOUND = 92;
  CREDITOR_ID_NOT_FOUND = 93;
  UNABLE_TO_FIND_BRAND_BY_NAME = 94;
  UNABLE_TO_FIND_PRODUCT_CATEGORY_BY_BRAND_AND_CODE = 95;
  NOTIFICATION_NOT_FOUND = 96;
  REWARD_BONUS_NOT_FOUND = 97;
  UNABLE_TO_FIND_PRODUCT_CATEGORY_BY_BRAND = 98;
  UNABLE_TO_RETRIEVE_USER_INFO = 99;
  UNABLE_TO_RETRIEVE_TOKEN_INFO = 100;
  EMAIL_UNREACHABLE = 101;
  PASSWORDS_DO_NOT_MATCH = 102;
  UNABLE_TO_OBTAIN_USER_INFO = 103;
  UNABLE_TO_FIND_KYC_REQUEST_BY_TRANSACTION_ID = 104;
  UNABLE_TO_FIND_KYC_REQUEST_BY_CODE = 105;
  UNABLE_TO_FIND_KYC_REQUEST_BY_SCAN_REF = 106;
  UNABLE_TO_CREATE_VERIFICATION_SESSION = 107;
  MALFORMED_PROFILE_PICTURE_URL = 108;
  MALFORMED_HOME_PAGE_URL = 109;
  MALFORMED_GATEWAY_URL = 110;
  MALFORMED_WEBHOOK_URL = 111;
  MALFORMED_LINK_URL = 112;
  MALFORMED_IMAGE_URL = 113;
  UNABLE_TO_PARSE_JWT_CLAIMS = 114;
  USE_GOOGLE_ACCOUNT = 115;
  USE_FACEBOOK_ACCOUNT = 116;
  USE_APPLE_ACCOUNT = 117;
  USE_GOOGLE_OR_FACEBOOK_ACCOUNT = 118;
  INCORRECT_EMAIL = 119;
  ACCOUNT_IDENTITY_REQUIRED = 120;
  UNABLE_TO_SEND_VERIFICATION_EMAIL = 121;
  AUTH0_IDENTIFIER_NOT_FOUND = 122;
  VERIFICATION_EMAIL_NOT_FOUND = 123;
  NOTIFICATION_CATEGORY_NOT_FOUND = 124;
  COUNTRY_NOT_FOUND = 125;
  GAME_LOBBY_FEATURE_SETTING_NOT_FOUND = 126;
  JACKPOT_ACCOUNT_FREE_CONTRIBUTION_NOT_FOUND = 127;
  INVALID_JWT_TOKEN = 128;
  CREDENTIALS_EXPIRED = 129;
  MALFORMED_REQUEST = 130;
  EXACTLY_ONE_QUERY_PARAM_EXPECTED = 131;
  NO_MORE_THAN_ONE_QUERY_PARAM_EXPECTED = 132;
  GAME_LOBBY_PRODUCT_GROUP_NOT_FOUND = 133;
  ACCOUNT_TEMPORARILY_LOCKED = 134;
  ACCOUNT_DUPLICATE_BY_PROVIDER = 135;
  ACCOUNT_DUPLICATE_USE_ANOTHER_PROVIDER = 136;
  ACCOUNT_NOT_FOUND_BY_EMAIL = 137;
  ACCOUNT_NOT_FOUND_BY_METHOD = 138;
  LEGAL_RULE_SETTING_UNRECOGNIZED = 139;
  LEGAL_RULE_TYPE_UNRECOGNIZED = 140;
  LEGAL_RULE_NOT_VALID = 141;
  ACCOUNT_NOT_INTERNAL = 142;
  INVALID_UUID_FORMAT = 143;
  RND_PICKEM_PROJECTION_MISMATCH = 144;
  RND_INVALID_ENTRY_AMOUNT = 145;
  RND_INVALID_MATCH = 146;
  RND_PICKEM_INVALID_PICKS_NUMBER = 147;
  RND_PICKEM_MIN_UNQIUE_PLAYERS = 148;
  RND_PICKEM_NON_UNIQUE_PLAYERS = 149;
  RND_PICKEM_MIN_UNQIUE_TEAMS = 150;
  RND_SHUFFLE_INVALID_TICKETS_NUMBER = 151;
  NO_PRIZEOUT_REDEEM_POLICY = 152;
  ACCOUNT_TAGGED_REWARDING_NOT_ALLOWED = 153;
  NO_PRIZEOUT_NON_MONETARY_REDEEM_POLICY = 154;
  ACTIVE_WHEEL_OF_WINNER_NOT_FOUND = 155;
  COUNTRY_NOT_FOUND_BY_CODE = 156;
  STATE_NOT_VALID = 157;
  PT_REQUIRED = 158;
  RND_PICKEM_MULTIPLE_PLAYERS_FROM_SAME_TEAM = 159;
  RESTRICTED_LOCATION = 160;
  RND_PICKEM_MULTIPLE_PLAYERS_FROM_SAME_MATCH = 161;
  ACCOUNT_SELF_EXCLUDED = 162;
  RND_NOT_ENOUGH_REWARDS = 163;
  RND_NOT_ELIGIBLE_METRIC = 164;
  ACCOUNT_RESTRICTED_BY_SESSION_RESTRICTION = 165;
  SWEEPSTAKE_CODE_IS_NOT_ALLOWED = 166;
  ACCOUNT_ACTIVE_SESSION_LIMIT_NOT_FOUND = 167;
  RANDOM_REWARD_INSTANCE_NOT_FOUND = 168;
  RANDOM_REWARD_INSTANCE_IS_NOT_ASSIGNED = 169;
  ACCOUNT_SESSION_LIMIT_DUPLICATE = 170;
  UNABLE_TO_CREATE_FREE_SPIN_CAMPAIGN = 171;
  RND_PICKEM_INSUFFICIENT_DAILY_LIMIT = 172;
  RND_PICKEM_INSUFFICIENT_WEEKLY_LIMIT = 173;
  SESSION_LIMIT_REACHED = 174;
  PLAYERS_LIMIT_REACHED = 175;
  ALREADY_PLAYING = 176;
  CONCURRENT_PLAY_TOGETHER_SESSIONS_LIMIT_REACHED = 177;
  PLAY_TOGETHER_SESSION_NOT_FOUND = 178;
  CREATOR_NOT_FOUND = 179;
  IMAGE_UPLOAD_FAILED = 180;
  IMAGE_REMOVE_FAILED = 181;
  ACCOUNT_SESSION_LIMIT_NOT_FOUND = 182;
  ACCOUNT_SESSION_RESTRICTION_NOT_FOUND = 183;
  SOFT_REQUIRED_GPS = 184;
  ACCOUNT_CLOSED = 185;
  SOFT_RESTRICTED_LOCATION = 186;
  PROMO_SMS_CONSENT_NEEDED = 187;
}
