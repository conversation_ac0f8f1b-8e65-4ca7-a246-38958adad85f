package api.util;

import static org.apache.commons.lang3.StringUtils.EMPTY;

import java.util.Objects;
import java.util.Optional;

import api.v1.ErrorDetail;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ErrorDetailUtils {
    public static ErrorDetail detail(String key, String value) {
        return ErrorDetail.newBuilder()
                .setKey(Objects.requireNonNull(key))
                .setValue(Optional.ofNullable(value).orElse(EMPTY))
                .build();
    }
}
