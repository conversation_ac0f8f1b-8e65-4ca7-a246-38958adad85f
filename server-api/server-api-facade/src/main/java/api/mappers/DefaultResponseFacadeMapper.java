package api.mappers;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import com.turbospaces.cfg.ApplicationProperties;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.google.protobuf.Any;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.Message;
import com.google.protobuf.TextFormat;
import com.turbospaces.api.facade.RequestWrapperFacade;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;
import com.turbospaces.api.mappers.ResponseFacadeMapper;
import com.turbospaces.executor.CloudNativeWorkUnit;
import com.turbospaces.executor.WorkUnit;

import api.facade.DefaultResponseWrapperFacade;
import api.util.ErrorDetailUtils;
import api.v1.ApiFactory;
import api.v1.Code;
import api.v1.Reason;
import api.v1.Status;
import io.vavr.CheckedFunction3;
import io.vavr.CheckedRunnable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
@RequiredArgsConstructor
public class DefaultResponseFacadeMapper implements ResponseFacadeMapper, CheckedFunction3<WorkUnit, byte[], String, ResponseWrapperFacade> {
    private final ApiFactory apiFactory;
    private final ApplicationProperties props;

    @Override
    public ResponseWrapperFacade unpack(WorkUnit workUnit) throws Exception {
        if (workUnit instanceof CloudNativeWorkUnit cnwu) {
            return apply(workUnit, cnwu.getData().toBytes(), StringUtils.EMPTY);
        }

        for (String alias : new String[] { "-", "_" }) {
            var prefix = "ce" + alias;
            var isNative = workUnit.lastHeader(prefix + ApiFactory.CLOUD_EVENT_NATIVE_FORMAT).map(Boolean::parseBoolean).orElse(false);
            if (isNative) {
                return apply(workUnit, workUnit.value().read(), prefix);
            }
        }

        throw new IllegalArgumentException("message is not coded in cloud events native format: " + workUnit);
    }
    @Override
    public ResponseWrapperFacade toReply(RequestWrapperFacade reqw, Message message, api.v1.CacheControl cacheControl) {
        var status = Status.newBuilder();
        status.setErrorCode(Code.ERR_OK);
        status.setReason(Reason.UNSPECIFIED);

        if (reqw.isNative()) {
            return new DefaultResponseWrapperFacade(
                    apiFactory.eventTemplate(),
                    apiFactory.objectMapper(),
                    reqw.headers(),
                    status.build(),
                    Any.pack(message),
                    cacheControl,
                    props);
        }

        throw new IllegalArgumentException("message is not coded in cloud events native format: " + TextFormat.printer().printToString(reqw.body()));
    }
    @Override
    public ResponseWrapperFacade toReply(RequestWrapperFacade reqw, Message message, ResponseStatusFacade wrapper) {
        var status = api.v1.Status.newBuilder();
        status.setErrorCodeValue(wrapper.errorCode().getNumber());
        status.setReasonValue(wrapper.errorReason().getNumber());
        if (StringUtils.isNotEmpty(wrapper.errorText())) {
            status.setErrorText(wrapper.errorText());
        }
        Optional.ofNullable(wrapper.errorDetails()).ifPresent(new Consumer<>() {
            @Override
            public void accept(Map<String, String> map) {
                map.forEach(new BiConsumer<>() {
                    @Override
                    public void accept(String k, String v) {
                        status.addErrorDetails(ErrorDetailUtils.detail(k, v));
                    }
                });

            }
        });

        if (reqw.isNative()) {
            return new DefaultResponseWrapperFacade(
                    apiFactory.eventTemplate(),
                    apiFactory.objectMapper(),
                    reqw.headers(),
                    status.build(),
                    Any.pack(message),
                    api.v1.CacheControl.getDefaultInstance(),
                    props);
        }

        throw new IllegalArgumentException("message is not coded in cloud events native format: " + TextFormat.printer().printToString(reqw.body()));
    }
    @Override
    public ResponseWrapperFacade apply(WorkUnit workUnit, byte[] data, String prefix) throws Exception {
        var headers = api.v1.Headers.newBuilder();
        var status = api.v1.Status.newBuilder();
        var cacheControl = api.v1.CacheControl.newBuilder();
        var body = Any.getDefaultInstance();

        //
        // ~ could be so that the message is actually empty in corner case scenarios
        //
        if (ArrayUtils.isEmpty(data)) {

        } else {
            body = Any.newBuilder().mergeFrom(data).build();
        }

        //
        // ~ read all headers as plain headers (any MessageMQ provider)
        //
        api.v1.Headers.getDescriptor().getFields().forEach(new Consumer<>() {
            @Override
            public void accept(FieldDescriptor field) {
                workUnit.lastHeader(prefix + field.getJsonName().toLowerCase().intern()).ifPresent(new Consumer<>() {
                    @Override
                    public void accept(String value) {
                        headers.setField(field, ApiFactory.parse(field, value));
                    }
                });
            }
        });
        api.v1.CacheControl.getDescriptor().getFields().forEach(new Consumer<>() {
            @Override
            public void accept(FieldDescriptor field) {
                workUnit.lastHeader(prefix + field.getJsonName().toLowerCase().intern()).ifPresent(new Consumer<>() {
                    @Override
                    public void accept(String value) {
                        cacheControl.setField(field, ApiFactory.parse(field, value));
                    }
                });
            }
        });

        api.v1.Status.getDescriptor().getFields().forEach(new Consumer<>() {
            @Override
            public void accept(FieldDescriptor field) {
                workUnit.lastHeader(prefix + field.getJsonName().toLowerCase().intern()).ifPresent(new Consumer<>() {
                    @Override
                    @SuppressWarnings("unchecked")
                    public void accept(String value) {
                        switch (field.getNumber()) {
                            case Status.ERRORCODE_FIELD_NUMBER:
                                status.setErrorCodeValue(Integer.parseInt(value));
                                break;
                            case Status.ERRORTEXT_FIELD_NUMBER:
                                status.setErrorText(value);
                                break;
                            case Status.STACKTRACE_FIELD_NUMBER:
                                status.setStackTrace(value);
                                break;
                            case Status.REASON_FIELD_NUMBER:
                                status.setReasonValue(Integer.parseInt(value));
                                break;
                            case Status.ERRORDETAILS_FIELD_NUMBER: {
                                try {
                                    DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedRunnable() {
                                        @Override
                                        public void run() throws Throwable {
                                            Map<String, String> asMap = apiFactory.objectMapper().readValue(value, Map.class);
                                            asMap.entrySet().stream().forEach(new Consumer<>() {
                                                @Override
                                                public void accept(Entry<String, String> entry) {
                                                    status.addErrorDetails(ErrorDetailUtils.detail(entry.getKey(), entry.getValue()));
                                                }
                                            });
                                        }
                                    });
                                } catch (Throwable err) {
                                    ExceptionUtils.wrapAndThrow(err);
                                }
                                break;
                            }
                            default:
                                throw new IllegalArgumentException("Unexpected field: " + field);
                        }
                    }
                });
            }
        });

        return new DefaultResponseWrapperFacade(
                apiFactory.eventTemplate(),
                apiFactory.objectMapper(),
                headers.build(),
                status.build(),
                body,
                cacheControl.build(),
                props);
    }
}
