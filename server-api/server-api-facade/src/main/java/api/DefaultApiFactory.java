package api;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.mappers.DefaultRequestFacadeMapper;
import com.turbospaces.api.mappers.RequestFacadeMapper;
import com.turbospaces.api.mappers.ResponseFacadeMapper;
import com.turbospaces.cfg.ApplicationProperties;

import api.facade.DefaultResponseStatusFacade;
import api.mappers.DefaultResponseFacadeMapper;
import api.util.ErrorDetailUtils;
import api.v1.AbstractApiFactory;
import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.ErrorDetail;
import api.v1.Reason;
import api.v1.ReplyUtil;
import api.v1.Status;

public class DefaultApiFactory extends AbstractApiFactory {
    private final ApplicationProperties props;

    public DefaultApiFactory(ApplicationProperties props, ObjectMapper mapper) {
        super(props, mapper);
        this.props = Objects.requireNonNull(props);
    }
    @Override
    public RequestFacadeMapper requestMapper() {
        return new DefaultRequestFacadeMapper(eventTemplate());
    }
    @Override
    public ResponseFacadeMapper responseMapper() {
        return new DefaultResponseFacadeMapper(this, props);
    }
    @Override
    public ResponseStatusFacade status(String code, int reason, String error, Map<String, String> details) {
        var status = Status.newBuilder();
        //
        // ~ conventional client facing ERR status mapping
        //
        for (Code it : api.v1.Code.values()) {
            if (Strings.CI.equals(code, it.name())) {
                status.setErrorCode(it);
                break;
            }
        }
        status.setReasonValue(reason);
        if (StringUtils.isNotEmpty(error)) {
            status.setErrorText(error);
        }
        Optional.ofNullable(details).ifPresent(new Consumer<>() {
            @Override
            public void accept(Map<String, String> map) {
                map.entrySet().forEach(new Consumer<>() {
                    @Override
                    public void accept(Entry<String, String> entry) {
                        status.addErrorDetails(ErrorDetailUtils.detail(entry.getKey(), entry.getValue()));
                    }
                });
            }
        });

        return new DefaultResponseStatusFacade(status.build());
    }
    @Override
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public ResponseStatusFacade toExceptional(String messageId, Throwable cause) {
        var errorCode = Code.ERR_SYSTEM.getNumber();
        var errorText = String.format(ReplyUtil.ERROR_FORMAT, messageId);
        var errorReason = Reason.SERVER_ERROR.getNumber();
        var errorDetails = Collections.<ErrorDetail> emptyList();

        if (cause instanceof ApplicationException app) {
            errorCode = app.getCode().getNumber();
            if (StringUtils.isNotEmpty(cause.getMessage())) {
                errorText = cause.getMessage();
            }
            if (app instanceof EnhancedApplicationException enhancedApp) {
                errorReason = enhancedApp.getReason().getNumber();
                errorDetails = (List) enhancedApp.getErrorDetails();
            }
        } else if (cause instanceof TimeoutException) {
            errorCode = Code.ERR_TIMEOUT.getNumber();
        }

        var status = Status.newBuilder();
        status.setStackTrace(ExceptionUtils.getStackTrace(cause));
        status.setErrorCodeValue(errorCode);
        status.setReasonValue(errorReason);
        status.addAllErrorDetails(errorDetails);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());
    }
    @Override
    public ResponseStatusFacade toExceptionalSystemReply(String errorText) {
        var status = Status.newBuilder();
        status.setReason(Reason.UNSPECIFIED);
        status.setErrorCode(Code.ERR_SYSTEM);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());
    }
    @Override
    public ResponseStatusFacade toExceptionalTimeoutReply(String errorText) {
        var status = Status.newBuilder();
        status.setErrorCode(Code.ERR_TIMEOUT);
        status.setReason(Reason.TIMEOUT_ERROR);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());
    }
    @Override
    public ResponseStatusFacade toExceptionalBadRequestReply(String errorText) {
        var status = Status.newBuilder();
        status.setErrorCode(Code.ERR_BAD_REQUEST);
        status.setReason(Reason.BAD_REQUEST);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());
    }
    @Override
    public ResponseStatusFacade toExceptionalNotFoundReply(String errorText) {
        var status = Status.newBuilder();
        status.setErrorCode(Code.ERR_NOT_FOUND);
        status.setReason(Reason.UNSPECIFIED);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());
    }
    @Override
    public ResponseStatusFacade toExceptionalAuthReply(String errorText) {
        var status = Status.newBuilder();
        status.setErrorCode(Code.ERR_AUTH);
        status.setReason(Reason.UNSPECIFIED);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());
    }
    @Override
    public ResponseStatusFacade toExceptionalDeniedReply(String errorText) {
        var status = Status.newBuilder();
        status.setErrorCode(Code.ERR_DENIED);
        status.setReason(Reason.UNSPECIFIED);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());
    }

    @Override
    public ResponseStatusFacade toExceptionalOtpRequiredReply(String errorText) {
        var status = Status.newBuilder();
        status.setErrorCode(Code.ERR_OTP_REQUIRED);
        status.setReason(Reason.UNSPECIFIED);
        if (StringUtils.isNotEmpty(errorText)) {
            status.setErrorText(errorText);
        }

        return new DefaultResponseStatusFacade(status.build());

    }
}
