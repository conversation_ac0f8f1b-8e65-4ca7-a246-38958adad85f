package api.facade;

import java.util.EnumSet;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

import com.google.common.base.Supplier;
import com.google.common.collect.ImmutableMap;
import com.google.protobuf.ProtocolMessageEnum;
import com.turbospaces.api.facade.ResponseStatusFacade;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.ErrorDetail;
import api.v1.Status;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class DefaultResponseStatusFacade implements ResponseStatusFacade, Supplier<Status> {
    private final Status delegate;

    public DefaultResponseStatusFacade(Status delegate) {
        this.delegate = Objects.requireNonNull(delegate);
    }
    @Override
    public ApplicationException toException() {
        return EnhancedApplicationException.of(delegate.getErrorText(), delegate.getErrorCode(), delegate.getReason());
    }
    @Override
    public ProtocolMessageEnum errorCode() {
        return delegate.getErrorCode();
    }
    @Override
    public ProtocolMessageEnum errorReason() {
        return delegate.getReason();
    }
    @Override
    public Map<String, String> errorDetails() {
        ImmutableMap.Builder<String, String> map = ImmutableMap.builder();
        delegate.getErrorDetailsList().forEach(new Consumer<>() {
            @Override
            public void accept(ErrorDetail it) {
                map.put(it.getKey(), it.getValue());
            }
        });
        return map.build();
    }
    @Override
    public String errorText() {
        return delegate.getErrorText();
    }
    @Override
    public boolean isOK() {
        return Code.ERR_OK.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isSystem() {
        return Code.ERR_SYSTEM.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isTimeout() {
        return Code.ERR_TIMEOUT.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isAuth() {
        return Code.ERR_AUTH.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isDuplicate() {
        return Code.ERR_DUPLICATE.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isNotFound() {
        return Code.ERR_NOT_FOUND.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isBadRequest() {
        return EnumSet.of(
                Code.ERR_BAD_REQUEST,
                Code.ERR_PASSWORD_WEAK,
                Code.ERR_PASSWORD_SAME,
                Code.ERR_PASSWORD_REPEATS,
                Code.ERR_PASSWORD_INCORRECT).contains(delegate.getErrorCode());
    }
    @Override
    public boolean isDenied() {
        return EnumSet.of(
                Code.ERR_DENIED,
                Code.ERR_TC_REQUIRED,
                Code.ERR_SR_REQUIRED,
                Code.ERR_PP_REQUIRED,
                Code.ERR_PT_REQUIRED,
                Code.ERR_PROMO_SMS_CONSENT_OTP_REQUIRED,
                Code.ERR_OTP_REQUIRED,
                Code.ERR_OTP_LIMIT,
                Code.ERR_PICKEM_LIMIT,
                Code.ERR_3DS_FAILED,
                Code.ERR_3DS_PAYMENT_ROUTING,
                Code.ERR_3DS_NOT_SUPPORTED,
                Code.ERR_CARDHOLDER_NAME_MISMATCH,
                Code.ERR_CARD_BLOCKED,
                Code.ERR_CARD_NOT_VERIFIED,
                Code.ERR_FRAUD_DECLINED,
                Code.ERR_KYC_REQUIRED,
                Code.ERR_KYC_REQUIRED_HIGH_RISK,
                Code.ERR_KYC_REQUIRED_LOW_RISK,
                Code.ERR_KYC_REQUIRED_MID_RISK,
                Code.ERR_PAYMENT,
                Code.ERR_PAYMENT_3DS_REQUIRED,
                Code.ERR_PAYMENT_EMAIL_NOT_VERIFIED,
                Code.ERR_PAYMENT_INACTIVE_PROVIDER,
                Code.ERR_PAYMENT_INPUT_CVV,
                Code.ERR_PAYMENT_IN_PROGRESS,
                Code.ERR_PAYMENT_METHOD_BLOCKED,
                Code.ERR_PAYMENT_METHOD_PAUSED,
                Code.ERR_PAYMENT_PURCHASE_LIMIT,
                Code.ERR_PAYMENT_RETURN,
                Code.ERR_PAYMENT_ROUTING,
                Code.ERR_PAYMENT_ROUTING_EMPTY_CHAIN,
                Code.ERR_RESTRICTED_LOCATION,
                Code.ERR_SOFT_REQUIRED_GPS,
                Code.ERR_SOFT_RESTRICTED_LOCATION).contains(delegate.getErrorCode());
    }
    @Override
    public boolean isInsufficientFunds() {
        return Code.ERR_INSUFFICIENT_FUNDS.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isFrozen() {
        return Code.ERR_FROZEN.equals(delegate.getErrorCode());
    }
    @Override
    public boolean isNoContent() {
        return Code.ERR_OK_NO_CONTENT.equals(delegate.getErrorCode());
    }
    @Override
    public Status get() {
        return delegate;
    }
}
