package api.facade;

import java.io.IOException;
import java.lang.reflect.UndeclaredThrowableException;
import java.net.URI;
import java.time.OffsetDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;

import api.v1.ObfuscatePrinter;
import com.turbospaces.cfg.ApplicationProperties;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.commons.lang3.exception.ExceptionUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Suppliers;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.protobuf.Any;
import com.google.protobuf.Descriptors.FieldDescriptor;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.turbospaces.api.facade.ResponseStatusFacade;
import com.turbospaces.api.facade.ResponseWrapperFacade;

import api.v1.ApiFactory;
import api.v1.CacheControl;
import api.v1.Status;
import io.cloudevents.CloudEvent;
import io.cloudevents.CloudEventData;
import io.cloudevents.SpecVersion;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.cloudevents.protobuf.ProtoCloudEventData;
import io.vavr.CheckedFunction0;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import reactor.blockhound.integration.DefaultBlockHoundIntegration;

@Slf4j
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class DefaultResponseWrapperFacade implements ResponseWrapperFacade {
    private final ThreadLocal<com.google.common.base.Supplier<Message>> cache = new ThreadLocal<>();
    private final com.google.common.base.Supplier<CloudEvent> event;

    @EqualsAndHashCode.Include
    private final api.v1.Headers headers;
    @EqualsAndHashCode.Include
    private final api.v1.CacheControl cacheControl;
    @EqualsAndHashCode.Include
    private final Any body;
    private final ApplicationProperties props;
    @EqualsAndHashCode.Include
    private final ResponseStatusFacade status;

    public DefaultResponseWrapperFacade(
            CloudEventBuilder eventTemplate,
            ObjectMapper mapper,
            api.v1.Headers headers,
            api.v1.Status status,
            Any body,
            api.v1.CacheControl cacheControl,
            ApplicationProperties props) {
        this.headers = Objects.requireNonNull(headers);
        this.status = new DefaultResponseStatusFacade(status);
        this.cacheControl = Objects.requireNonNull(cacheControl);
        this.body = Objects.requireNonNull(body);
        this.props = props;
        this.event = Suppliers.memoize(new com.google.common.base.Supplier<>() {
            @Override
            public CloudEvent get() {
                CloudEventBuilder builder = eventTemplate
                        .withId(headers.getMessageId())
                        .withType(body().getTypeUrl())
                        .withExtension(ApiFactory.CLOUD_EVENT_NATIVE_FORMAT, true)
                        .withData(ProtoCloudEventData.wrap(body));

                //
                // ~ write all headers as plain headers (any MessageMQ provider)
                //
                ImmutableMap.Builder<String, String> map = ImmutableMap.builder();
                headers.getAllFields().forEach(new BiConsumer<>() {
                    @Override
                    public void accept(FieldDescriptor field, Object value) {
                        if (Objects.nonNull(value)) {
                            map.put(field.getJsonName().toLowerCase().intern(), ApiFactory.toString(field, value));
                        }
                    }
                });
                status.getAllFields().forEach(new BiConsumer<>() {
                    @Override
                    public void accept(FieldDescriptor field, Object value) {
                        if (Objects.nonNull(value)) {
                            map.put(field.getName().toLowerCase().intern(),
                                    switch (field.getNumber()) {
                                        case Status.ERRORCODE_FIELD_NUMBER:
                                            yield ApiFactory.toString(field, value);
                                        case Status.ERRORTEXT_FIELD_NUMBER:
                                            yield ApiFactory.toString(field, value);
                                        case Status.STACKTRACE_FIELD_NUMBER:
                                            yield ApiFactory.toString(field, value);
                                        case Status.REASON_FIELD_NUMBER:
                                            yield ApiFactory.toString(field, value);
                                        case Status.ERRORDETAILS_FIELD_NUMBER: {
                                            try {
                                                yield DefaultBlockHoundIntegration.allowBlockingUnchecked(new CheckedFunction0<>() {
                                                    @Override
                                                    public String apply() throws Throwable {
                                                        Map<String, String> asMap = Maps.newLinkedHashMap();
                                                        for (var entry : status.getErrorDetailsList()) {
                                                            asMap.put(entry.getKey(), entry.getValue());
                                                        }
                                                        return mapper.writeValueAsString(asMap);
                                                    }
                                                });
                                            } catch (Throwable err) {
                                                throw new UndeclaredThrowableException(err);
                                            }
                                        }
                                        default:
                                            throw new IllegalArgumentException("Unexpected field: " + field);
                                    });
                        }
                    }
                });
                cacheControl.getAllFields().forEach(new BiConsumer<>() {
                    @Override
                    public void accept(FieldDescriptor field, Object value) {
                        if (Objects.nonNull(value)) {
                            map.put(field.getJsonName().toLowerCase().intern(), ApiFactory.toString(field, value));
                        }
                    }
                });

                //
                // ~ we want to avoid naming collisions, map builder prevents it
                //
                map.build().forEach(new BiConsumer<>() {
                    @Override
                    public void accept(String key, String value) {
                        builder.withContextAttribute(key, value);
                    }
                });

                return builder.build();
            }
        });
    }
    @Override
    public api.v1.Headers headers() {
        return headers;
    }
    @Override
    public Any body() {
        return body;
    }
    @Override
    public CacheControl cacheControl() {
        return cacheControl;
    }
    @Override
    @SuppressWarnings("unchecked")
    public <T extends Message> T unpack(Class<T> type) throws IOException {
        if (Objects.isNull(cache.get())) {
            cache.set(Suppliers.memoize(new com.google.common.base.Supplier<Message>() {
                @Override
                public Message get() {
                    Any any = body();
                    try {
                        var unpack = any.unpack(type);
                        Level level = Level.valueOf(props.LOGGER_LEVEL_DEFAULT_RESPONSE_WRAPPER_FACADE.get());
                        if (level.toInt() >= Level.DEBUG.toInt()) {
                            log.atLevel(level).log("unpack: {}", ObfuscatePrinter.shortDebugString(unpack.toBuilder()));
                        }
                        return unpack;
                    } catch (InvalidProtocolBufferException err) {
                        log.warn("unable to decode Any of type: {} to {}", any.getTypeUrl(), type.getSimpleName());
                        throw new UndeclaredThrowableException(err);
                    }
                }
            }));
        }

        try {
            var supplier = cache.get();
            return (T) supplier.get();
        } catch (Exception err) {
            Throwable rootCause = ExceptionUtils.getRootCause(err);
            if (Objects.isNull(rootCause)) {
                rootCause = err;
            }
            throw new IOException(rootCause);
        }
    }

    @Override
    public ResponseStatusFacade status() {
        return status;
    }
    @Override
    public CloudEventData getData() {
        return event.get().getData();
    }
    @Override
    public Object getExtension(String extensionName) {
        return event.get().getExtension(extensionName);
    }
    @Override
    public Set<String> getExtensionNames() {
        return event.get().getExtensionNames();
    }
    @Override
    public SpecVersion getSpecVersion() {
        return event.get().getSpecVersion();
    }
    @Override
    public String getId() {
        return event.get().getId();
    }
    @Override
    public String getType() {
        return event.get().getType();
    }
    @Override
    public URI getSource() {
        return event.get().getSource();
    }
    @Override
    public String getDataContentType() {
        return event.get().getDataContentType();
    }
    @Override
    public URI getDataSchema() {
        return event.get().getDataSchema();
    }
    @Override
    public String getSubject() {
        return event.get().getSubject();
    }
    @Override
    public OffsetDateTime getTime() {
        return event.get().getTime();
    }
    @Override
    public Object getAttribute(String attributeName) {
        return event.get().getAttribute(attributeName);
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE)
                .append("headers", headers())
                .append("status", status())
                .append("body", body())
                .append("cacheControl", cacheControl())
                .build();
    }
}
