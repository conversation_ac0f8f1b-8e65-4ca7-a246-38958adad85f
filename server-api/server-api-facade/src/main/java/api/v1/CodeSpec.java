package api.v1;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.ebean.annotation.DbEnumValue;
import lombok.Getter;

public enum CodeSpec {
    ERR_OK(Code.ERR_OK),
    ERR_OK_NO_CONTENT(Code.ERR_OK_NO_CONTENT),
    ERR_SYSTEM(Code.ERR_SYSTEM),
    ERR_AUTH(Code.ERR_AUTH),
    ERR_BAD_REQUEST(Code.ERR_BAD_REQUEST),
    ERR_DENIED(Code.ERR_DENIED),
    ERR_FROZEN(Code.ERR_FROZEN),
    ERR_NOT_FOUND(Code.ERR_NOT_FOUND),
    ERR_TIMEOUT(Code.ERR_TIMEOUT),
    ERR_DUPLICATE(Code.ERR_DUPLICATE),
    ERR_INSUFFICIENT_FUNDS(Code.ERR_INSUFFICIENT_FUNDS),
    ERR_PAYMENT(Code.ERR_PAYMENT),
    ERR_PAYMENT_ROUTING(Code.ERR_PAYMENT_ROUTING),
    ERR_PAYMENT_INPUT_CVV(Code.ERR_PAYMENT_INPUT_CVV),
    ERR_PAYMENT_RETURN(Code.ERR_PAYMENT_RETURN),
    ERR_FRAUD_DECLINED(Code.ERR_FRAUD_DECLINED),
    ERR_PAYMENT_METHOD_PAUSED(Code.ERR_PAYMENT_METHOD_PAUSED),
    ERR_KYC_REQUIRED(Code.ERR_KYC_REQUIRED),
    ERR_KYC_REQUIRED_LOW_RISK(Code.ERR_KYC_REQUIRED_LOW_RISK),
    ERR_KYC_REQUIRED_MID_RISK(Code.ERR_KYC_REQUIRED_MID_RISK),
    ERR_KYC_REQUIRED_HIGH_RISK(Code.ERR_KYC_REQUIRED_HIGH_RISK),
    ERR_TC_REQUIRED(Code.ERR_TC_REQUIRED),
    ERR_SR_REQUIRED(Code.ERR_SR_REQUIRED),
    ERR_PP_REQUIRED(Code.ERR_PP_REQUIRED),
    ERR_PT_REQUIRED(Code.ERR_PT_REQUIRED),
    ERR_PASSWORD_WEAK(Code.ERR_PASSWORD_WEAK),
    ERR_PASSWORD_SAME(Code.ERR_PASSWORD_SAME),
    ERR_PASSWORD_INCORRECT(Code.ERR_PASSWORD_INCORRECT),
    ERR_PASSWORD_REPEATS(Code.ERR_PASSWORD_REPEATS),
    ERR_PASSWORD_SET_ON_EXISTING_PASSWORD(Code.ERR_PASSWORD_SET_ON_EXISTING_PASSWORD),
    ERR_CARDHOLDER_NAME_MISMATCH(Code.ERR_CARDHOLDER_NAME_MISMATCH),
    UNRECOGNIZED(Code.UNRECOGNIZED),
    ERR_CARD_BLOCKED(Code.ERR_CARD_BLOCKED),
    ERR_CARD_NOT_VERIFIED(Code.ERR_CARD_NOT_VERIFIED),
    ERR_PAYMENT_ROUTING_EMPTY_CHAIN(Code.ERR_PAYMENT_ROUTING_EMPTY_CHAIN),
    ERR_3DS_PAYMENT_ROUTING(Code.ERR_3DS_PAYMENT_ROUTING),
    ERR_NETWORK(Code.ERR_NETWORK),
    ERR_PAYMENT_INACTIVE_PROVIDER(Code.ERR_PAYMENT_INACTIVE_PROVIDER),
    ERR_PAYMENT_EMAIL_NOT_VERIFIED(Code.ERR_PAYMENT_EMAIL_NOT_VERIFIED),
    ERR_PAYMENT_PURCHASE_LIMIT(Code.ERR_PAYMENT_PURCHASE_LIMIT),
    ERR_PAYMENT_METHOD_BLOCKED(Code.ERR_PAYMENT_METHOD_BLOCKED),
    ERR_PAYMENT_3DS_REQUIRED(Code.ERR_PAYMENT_3DS_REQUIRED),
    ERR_PAYMENT_IN_PROGRESS(Code.ERR_PAYMENT_IN_PROGRESS),
    ERR_3DS_FAILED(Code.ERR_3DS_FAILED),
    ERR_OTP_LIMIT(Code.ERR_OTP_LIMIT),
    ERR_OTP_REQUIRED(Code.ERR_OTP_REQUIRED),
    ERR_PROMO_SMS_CONSENT_OTP_REQUIRED(Code.ERR_PROMO_SMS_CONSENT_OTP_REQUIRED),
    ERR_RESTRICTED_LOCATION(Code.ERR_RESTRICTED_LOCATION),
    ERR_PICKEM_LIMIT(Code.ERR_PICKEM_LIMIT),
    ERR_3DS_NOT_SUPPORTED(Code.ERR_3DS_NOT_SUPPORTED),
    ERR_SOFT_REQUIRED_GPS(Code.ERR_SOFT_REQUIRED_GPS),
    ERR_SOFT_RESTRICTED_LOCATION(Code.ERR_SOFT_RESTRICTED_LOCATION);

    @Getter
    private final Code codeApi;
    private static final Map<Code, CodeSpec> SEVER_API_PROVIDER_MAPPING = Arrays.stream(values()).collect(Collectors.toMap(v -> v.codeApi, Function.identity()));

    private CodeSpec(Code codeApi) {
        this.codeApi = Objects.requireNonNull(codeApi);
    }
    public static CodeSpec fromServerApi(Code code) {
        return SEVER_API_PROVIDER_MAPPING.get(code);
    }
    @DbEnumValue
    public String getValue() {
        return this.codeApi.name().toLowerCase();
    }
}
