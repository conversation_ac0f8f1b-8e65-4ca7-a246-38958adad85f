<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="account_id" tableName="fraud.aml_check" withHistory="true"
                     uniqueOneToOne="uq_aml_check_account_id" references="uam.accounts.id"
                     foreignKeyName="fk_aml_check_account_id"/>
        <addUniqueConstraint constraintName="uq_aml_check_account_id" tableName="fraud.aml_check"
                             columnNames="DROP CONSTRAINT" nullableColumns=""/>
    </changeSet>
</migration>