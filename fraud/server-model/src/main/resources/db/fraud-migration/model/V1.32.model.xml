<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules"
                     checkConstraint="check ( decline_code in ('err_ok','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_kyc_required_low_risk','err_kyc_required_mid_risk','err_kyc_required_high_risk','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_cardholder_name_mismatch','unrecognized','err_card_blocked','err_card_not_verified','err_payment_routing_empty_chain','err_payment_inactive_provider','err_payment_email_not_verified','err_payment_purchase_limit','err_payment_method_blocked'))"
                     checkConstraintName="ck_fraud_decline_rules_decline_code"/>
        <createTable name="fraud.kyc_risk_spend_policy" pkName="pk_kyc_risk_spend_policy">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" references="core.brands.id"
                    foreignKeyName="fk_kyc_risk_spend_policy_brand_id"
                    foreignKeyIndex="ix_kyc_risk_spend_policy_brand_id"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="low_risk_spend_threshold" type="decimal(16,2)" notnull="true"/>
            <column name="mid_risk_spend_threshold" type="decimal(16,2)" notnull="true"/>
            <column name="high_risk_spend_threshold" type="decimal(16,2)" notnull="true"/>
        </createTable>
        <createIndex indexName="ix_kyc_risk_spend_policy_currency" tableName="fraud.kyc_risk_spend_policy" columns=""
                     unique="true"
                     definition="CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_currency ON fraud.kyc_risk_spend_policy (currency) WHERE brand_id is null"
                     platforms="POSTGRES"/>
        <createIndex indexName="ix_kyc_risk_spend_policy_brand_id_currency" tableName="fraud.kyc_risk_spend_policy"
                     columns="" unique="true"
                     definition="CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_brand_id_currency ON fraud.kyc_risk_spend_policy (brand_id, currency) WHERE brand_id is not null"
                     platforms="POSTGRES"/>
    </changeSet>
</migration>