<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.aml_check" withHistory="true" pkName="pk_aml_check">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true"/>
            <column name="crime_list_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="pep_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="watchlist_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="sanction_match" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createIndex indexName="ix_aml_check_account_id" tableName="fraud.aml_check" columns="account_id"/>
    </changeSet>
</migration>