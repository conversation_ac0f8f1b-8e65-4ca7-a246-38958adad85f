<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="fraud"/>
        <createTable name="fraud.idology_check" withHistory="true" pkName="pk_idology_check">
            <column name="account_id" type="bigint" primaryKey="true"/>
            <column name="restriction_key" type="varchar"/>
            <column name="restriction_message" type="varchar"/>
            <column name="id_number" type="bigint"/>
            <column name="failed" type="varchar(500)"/>
            <column name="error" type="varchar(500)"/>
            <column name="at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.idology_patriot_act" pkName="pk_idology_patriot_act">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="list" type="varchar"/>
            <column name="score" type="integer"/>
            <column name="check_account_id" type="bigint" references="fraud.idology_check.account_id"
                    foreignKeyName="fk_idology_patriot_act_check_account_id"
                    foreignKeyIndex="ix_idology_patriot_act_check_account_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createIndex indexName="ix_idology_check_at" tableName="fraud.idology_check" columns="at"/>
    </changeSet>
</migration>
