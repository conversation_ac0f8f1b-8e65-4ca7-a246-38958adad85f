<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.accounts">
            <column name="email_hash" type="varchar" notnull="true"/>
        </addColumn>
        <addColumn tableName="core.brands">
            <column name="active" type="boolean" defaultValue="false" notnull="true"/>
            <column name="domain" type="varchar"/>
        </addColumn>
    </changeSet>
</migration>