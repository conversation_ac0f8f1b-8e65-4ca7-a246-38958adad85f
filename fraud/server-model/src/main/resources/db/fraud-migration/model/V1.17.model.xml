<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules" type="varchar(28)" currentType="varchar(25)" currentNotnull="true" checkConstraint="check ( decline_code in ('err_ok','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_cardholder_name_mismatch','unrecognized','err_card_blocked'))" checkConstraintName="ck_fraud_decline_rules_decline_code"/>
        <addColumn tableName="uam.fraud_response">
            <column name="action_type" type="varchar(16)" checkConstraint="check ( action_type in ('account_login','account_register','withdrawal','purchase','aml_check','verification'))" checkConstraintName="ck_fraud_response_action_type"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_fraud_response_account_id" foreignKeyIndex="ix_fraud_response_account_id"/>
        </addColumn>
        <alterColumn columnName="status" tableName="uam.accounts" checkConstraint="check ( status in ('default','requires_jumio_kyc','restricted_by_geo_policy','under_investigation','pending_redeem_review','bw_pending_redeem_review','pending_verification','pending_pm_verification','pending_pm_bw_verification','for_payment_processing'))" checkConstraintName="ck_accounts_status"/>
    </changeSet>
</migration>