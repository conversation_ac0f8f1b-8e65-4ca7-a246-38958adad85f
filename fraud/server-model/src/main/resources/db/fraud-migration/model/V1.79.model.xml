<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.zendesk_export_info" pkName="pk_zendesk_export_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" uniqueOneToOne="uq_zendesk_export_info_account_id" references="uam.accounts.id" foreignKeyName="fk_zendesk_export_info_account_id"/>
            <column name="total_purchase" type="decimal(16,2)"/>
            <column name="total_withdraw" type="decimal(16,2)"/>
            <column name="is_updated" type="boolean" defaultValue="false" notnull="true"/>
            <column name="email" type="varchar"/>
            <column name="last_updated_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <dropIndex indexName="ix_account_auth_info_last_activity" tableName="uam.account_auth_info"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="last_activity" tableName="uam.account_auth_info"/>
    </changeSet>
</migration>