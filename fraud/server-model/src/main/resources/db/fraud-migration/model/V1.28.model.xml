<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.ip_details">
            <column name="created_at" type="timestamp" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_ip_details_ip" tableName="uam.ip_details" columnNames="ip" oneToOne="false" nullableColumns="ip"/>
        <dropIndex indexName="ix_ip_details_ip" tableName="uam.ip_details"/>
    </changeSet>
</migration>