<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="doc_country" tableName="uam.kyc_verification_requests" type="varchar(2)"
                     currentType="varchar" currentNotnull="false"/>
        <addColumn tableName="uam.kyc_verification_requests">
            <column name="transaction_id" type="uuid" notnull="true"
                    unique="uq_kyc_verification_requests_transaction_id"/>
            <column name="scan_reference" type="varchar"/>
            <column name="client_redirect_url" type="varchar(4000)"/>
            <column name="sweepstake" type="boolean" defaultValue="false" notnull="true"/>
            <column name="api_error" type="jsonb"/>
            <column name="id_country" type="varchar(2)"/>
            <column name="id_number_hash" type="varchar"/>
            <column name="id_state" type="varchar"/>
            <column name="similarity" type="varchar"/>
            <column name="validity" type="boolean" defaultValue="false" notnull="true"/>
            <column name="doc_address_mask" type="varbinary"/>
            <column name="doc_name" type="varchar"/>
            <column name="doc_issue_date" type="date"/>
            <column name="expire_at" type="date"/>
            <column name="platform" type="varchar(7)" notnull="true"
                    checkConstraint="check ( platform in ('web','android','ios'))"
                    checkConstraintName="ck_kyc_verification_requests_platform"/>
            <column name="reason" type="varchar(4000)"/>
            <column name="reason_desc" type="varchar(4000)"/>
            <column name="reason_code" type="integer"/>
            <column name="at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <createIndex indexName="ix_kyc_verification_requests_scan_reference" tableName="uam.kyc_verification_requests"
                     columns="scan_reference"/>
        <createIndex indexName="ix_kyc_verification_requests_id_number_hash" tableName="uam.kyc_verification_requests"
                     columns="id_number_hash"/>
        <createIndex indexName="ix_kyc_verification_requests_at" tableName="uam.kyc_verification_requests"
                     columns="at"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="doc_address" tableName="uam.kyc_verification_requests"/>
    </changeSet>
</migration>