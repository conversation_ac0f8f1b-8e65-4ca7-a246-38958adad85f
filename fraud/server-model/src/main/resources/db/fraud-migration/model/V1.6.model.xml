<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.account_balances">
            <column name="id" type="bigint" primaryKey="true"/>
        </addColumn>
        <addColumn tableName="uam.account_category_tags">
            <column name="id" type="bigint" primaryKey="true"/>
        </addColumn>
        <addColumn tableName="uam.account_oauths">
            <column name="id" type="bigint" primaryKey="true"/>
        </addColumn>
        <addColumn tableName="uam.accounts">
            <column name="deleted" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <createIndex indexName="ix_accounts_deleted" tableName="uam.accounts" columns="deleted"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="brand_id" tableName="uam.account_oauths"/>
    </changeSet>
</migration>