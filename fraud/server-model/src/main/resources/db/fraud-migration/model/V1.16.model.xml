<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="payment"/>
        <createTable name="payment.account_meta_info" pkName="pk_account_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" uniqueOneToOne="uq_account_meta_info_account_id" references="uam.accounts.id" foreignKeyName="fk_account_meta_info_account_id"/>
            <column name="deposit_count" type="integer" notnull="true"/>
            <column name="total_deposit_amount" type="decimal(16,2)"/>
        </createTable>
        <alterColumn columnName="status" tableName="uam.accounts" type="varchar(26)" currentType="varchar(24)" currentNotnull="true" checkConstraint="check ( status in ('default','requires_jumio_kyc','under_investigation','pending_redeem_review','bw_pending_redeem_review','pending_verification','pending_pm_verification','pending_pm_bw_verification'))" checkConstraintName="ck_accounts_status"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="deposit_count" tableName="uam.account_meta_info"/>
        <dropColumn columnName="total_deposit_amount" tableName="uam.account_meta_info"/>
    </changeSet>
</migration>