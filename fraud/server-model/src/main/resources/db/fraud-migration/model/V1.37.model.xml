<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.otp_trigger_rules" pkName="pk_otp_trigger_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id"
                    foreignKeyName="fk_otp_trigger_rules_brand_id"/>
            <column name="country" type="varchar(2)"/>
            <column name="bonus_trigger_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="sign_up_fraud_score_trigger_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="daily_bonus_accepted_threshold" type="integer" notnull="true"/>
            <column name="sign_up_fraud_score_threshold" type="integer" notnull="true"/>
        </createTable>
        <createIndex indexName="ix_otp_trigger_rules_brand_id" tableName="fraud.otp_trigger_rules" columns=""
                     unique="true"
                     definition="CREATE UNIQUE INDEX ix_otp_trigger_rules_brand_id ON fraud.otp_trigger_rules (brand_id) WHERE country is null"
                     platforms="POSTGRES"/>
        <createIndex indexName="ix_otp_trigger_rules_brand_id_country" tableName="fraud.otp_trigger_rules" columns=""
                     unique="true"
                     definition="CREATE UNIQUE INDEX ix_otp_trigger_rules_brand_id_country ON fraud.otp_trigger_rules (brand_id, country) WHERE country is not null"
                     platforms="POSTGRES"/>
    </changeSet>
</migration>