<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.card_verification_meta_info" withHistory="true"
                     pkName="pk_card_verification_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id"
                    foreignKeyName="fk_card_verification_meta_info_account_id"
                    foreignKeyIndex="ix_card_verification_meta_info_account_id"/>
            <column name="fingerprint" type="varchar" notnull="true"/>
            <column name="card_bin" type="varchar"/>
            <column name="last_four" type="varchar"/>
            <column name="status" type="varchar(21)" notnull="true"
                    checkConstraint="check ( status in ('unverified','require_verification','in_progress','verified','attempt_limit_reached','verification_failed','unverifiable'))"
                    checkConstraintName="ck_card_verification_meta_info_status"/>
            <column name="status_updated_at" type="timestamp"/>
            <column name="manually_updated" type="boolean"/>
            <column name="agent_name" type="varchar"/>
            <column name="manually_verified" type="boolean"/>
            <column name="verification_attempts" type="integer" notnull="true"/>
            <column name="require_verification_at" type="timestamp"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_card_verification_meta_info_account_id_fingerprint"
                              columnNames="account_id,fingerprint" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="fraud.card_verification_request" withHistory="true" pkName="pk_card_verification_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="scan_reference" type="varchar"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id"
                    foreignKeyName="fk_card_verification_request_account_id"
                    foreignKeyIndex="ix_card_verification_request_account_id"/>
            <column name="meta_info_id" type="bigint" notnull="true" references="fraud.card_verification_meta_info.id"
                    foreignKeyName="fk_card_verification_request_meta_info_id"
                    foreignKeyIndex="ix_card_verification_request_meta_info_id"/>
            <column name="status" type="varchar(9)" notnull="true"
                    checkConstraint="check ( status in ('initiated','verified','failed','expired'))"
                    checkConstraintName="ck_card_verification_request_status"/>
            <column name="redirect_url" type="varchar(4000)"/>
            <column name="client_ip" type="varchar"/>
            <column name="timestamp" type="varchar"/>
            <column name="doc_type" type="varchar"/>
            <column name="country" type="varchar"/>
            <column name="images" type="varchar[]"/>
            <column name="extracted_name" type="varchar"/>
            <column name="extracted_pan" type="varchar"/>
            <column name="extracted_issue_date" type="varchar"/>
            <column name="extracted_expiry_date" type="varchar"/>
            <column name="api_error" type="varchar"/>
            <column name="fail_reason" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_card_verification_request_provider_scan_reference"
                              columnNames="provider,scan_reference" oneToOne="false" nullableColumns="scan_reference"/>
        </createTable>
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules"
                     checkConstraint="check ( decline_code in ('err_ok','err_ok_no_content','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_kyc_required_low_risk','err_kyc_required_mid_risk','err_kyc_required_high_risk','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_cardholder_name_mismatch','unrecognized','err_card_blocked','err_card_not_verified','err_payment_routing_empty_chain','err_payment_inactive_provider','err_payment_email_not_verified','err_payment_purchase_limit','err_payment_method_blocked','err_payment_3ds_required','err_3ds_failed','err_otp_limit'))"
                     checkConstraintName="ck_fraud_decline_rules_decline_code"/>
        <alterForeignKey name="fk_payment_method_verification_meta_info_account_id" columnNames="DROP FOREIGN KEY"
                         indexName="ix_payment_method_verification_meta_info_account_id"
                         tableName="fraud.payment_method_verification_meta_info"/>
        <alterForeignKey name="fk_payment_method_verification_request_account_id" columnNames="DROP FOREIGN KEY"
                         indexName="ix_payment_method_verification_request_account_id"
                         tableName="fraud.payment_method_verification_request"/>
        <alterForeignKey name="fk_payment_method_verification_request_meta_info_id" columnNames="DROP FOREIGN KEY"
                         indexName="ix_payment_method_verification_request_meta_info_id"
                         tableName="fraud.payment_method_verification_request"/>
        <dropIndex indexName="ix_payment_method_verification_meta_info_account_id_finge_1"
                   tableName="fraud.payment_method_verification_meta_info"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropTable name="fraud.payment_method_verification_meta_info" sequenceCol="id"/>
        <dropTable name="fraud.payment_method_verification_request" sequenceCol="id"/>
    </changeSet>
</migration>