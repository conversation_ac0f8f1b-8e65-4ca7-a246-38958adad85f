<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.kyc_risk_spend_policy_history" identityType="identity" pkName="pk_kyc_risk_spend_policy_history">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_kyc_risk_spend_policy_history_brand_id" foreignKeyIndex="ix_kyc_risk_spend_policy_history_brand_id"/>
            <column name="limit_type" type="varchar(10)" notnull="true" checkConstraint="check ( limit_type in ('LOW','MID','HIGH'))" checkConstraintName="ck_kyc_risk_spend_policy_history_limit_type"/>
            <column name="old_value" type="decimal(20,6)"/>
            <column name="new_value" type="decimal(20,6)" notnull="true"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="updated_by" type="varchar" notnull="true"/>
            <column name="updated_at" type="timestamp" notnull="true"/>
        </createTable>
    </changeSet>
</migration>