<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.fraud_response">
            <column name="ip_details_id" type="bigint" references="uam.ip_details.id" foreignKeyName="fk_fraud_response_ip_details_id" foreignKeyIndex="ix_fraud_response_ip_details_id"/>
        </addColumn>
        <createTable name="uam.ip_details" pkName="pk_ip_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="ip" type="varchar"/>
            <column name="type" type="varchar"/>
        </createTable>
        <createIndex indexName="ix_ip_details_ip" tableName="uam.ip_details" columns="ip"/>
    </changeSet>
</migration>