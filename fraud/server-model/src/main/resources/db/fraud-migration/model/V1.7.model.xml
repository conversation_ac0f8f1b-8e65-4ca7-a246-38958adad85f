<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createSchema name="core"/>
        <createSchema name="uam"/>
        <addColumn tableName="uam.citizens">
            <column name="id" type="bigint" primaryKey="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_citizens_person_id_country" tableName="uam.citizens" columnNames="DROP CONSTRAINT" nullableColumns=""/>
        <addColumn tableName="uam.fraud_applied_rules">
            <column name="at" type="date" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.fraud_response">
            <column name="created_at" type="timestamp" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_fraud_response_response_id" tableName="uam.fraud_response" columnNames="response_id" oneToOne="false"
                             nullableColumns=""/>
    </changeSet>
</migration>