<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.accounts">
            <column name="vip_level_ggr" type="varchar(7)"
                    checkConstraint="check ( vip_level_ggr in ('class_a','class_b','class_c','class_d','class_e','class_f','whale'))"
                    checkConstraintName="ck_accounts_vip_level_ggr"/>
        </addColumn>
    </changeSet>
</migration>