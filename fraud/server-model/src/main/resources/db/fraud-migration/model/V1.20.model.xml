<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules" type="varchar(31)" currentType="varchar(28)" currentNotnull="true" checkConstraint="check ( decline_code in ('err_ok','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_cardholder_name_mismatch','unrecognized','err_card_blocked','err_card_not_verified','err_payment_routing_empty_chain'))" checkConstraintName="ck_fraud_decline_rules_decline_code"/>
        <addUniqueConstraint constraintName="uq_fraud_response_response_id" tableName="uam.fraud_response" columnNames="DROP CONSTRAINT" nullableColumns=""/>
    </changeSet>
</migration>