<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.account_card_verification_info" pkName="pk_account_card_verification_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true"
                    uniqueOneToOne="uq_account_card_verification_info_account_id" references="uam.accounts.id"
                    foreignKeyName="fk_account_card_verification_info_account_id"/>
            <column name="email_sent_at" type="date" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules"
                     checkConstraint="check ( decline_code in ('err_ok','err_ok_no_content','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_kyc_required_low_risk','err_kyc_required_mid_risk','err_kyc_required_high_risk','err_tc_required','err_sr_required','err_pp_required','err_pt_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_cardholder_name_mismatch','unrecognized','err_card_blocked','err_card_not_verified','err_payment_routing_empty_chain','err_3ds_payment_routing','err_network','err_payment_inactive_provider','err_payment_email_not_verified','err_payment_purchase_limit','err_payment_method_blocked','err_payment_3ds_required','err_payment_in_progress','err_3ds_failed','err_otp_limit','err_otp_required'))"
                     checkConstraintName="ck_fraud_decline_rules_decline_code"/>
    </changeSet>
</migration>