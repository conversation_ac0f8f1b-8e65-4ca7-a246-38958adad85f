<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="payment.payment_method_meta_info" pkName="pk_payment_method_meta_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="type" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id"
                    foreignKeyName="fk_payment_method_meta_info_account_id"
                    foreignKeyIndex="ix_payment_method_meta_info_account_id"/>
            <column name="fingerprint" type="varchar" notnull="true"/>
            <column name="card_bin" type="varchar"/>
            <column name="last_four" type="varchar"/>
            <column name="success_purchase_count" type="bigint" notnull="true"/>
            <column name="total_purchase_count" type="bigint" notnull="true"/>
            <column name="total_purchase_amount" type="decimal(16,2)" notnull="true"/>
            <column name="verification_status" type="varchar(21)" notnull="true"
                    checkConstraint="check ( verification_status in ('unverified','require_verification','in_progress','verified','attempt_limit_reached','verification_failed','unverifiable'))"
                    checkConstraintName="ck_payment_method_meta_info_verification_status"/>
            <column name="manually_verified" type="boolean"/>
            <column name="verification_attempts" type="integer" notnull="true"/>
            <column name="require_verification_at" type="timestamp"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <uniqueConstraint name="uq_payment_method_meta_info_account_id_fingerprint"
                              columnNames="account_id,fingerprint" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="payment.payment_method_verification_request" pkName="pk_payment_method_verification_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="scan_reference" type="varchar"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id"
                    foreignKeyName="fk_payment_method_verification_request_account_id"
                    foreignKeyIndex="ix_payment_method_verification_request_account_id"/>
            <column name="meta_info_id" type="bigint" notnull="true" references="payment.payment_method_meta_info.id"
                    foreignKeyName="fk_payment_method_verification_request_meta_info_id"
                    foreignKeyIndex="ix_payment_method_verification_request_meta_info_id"/>
            <column name="status" type="varchar(9)" notnull="true"
                    checkConstraint="check ( status in ('initiated','verified','failed','expired'))"
                    checkConstraintName="ck_payment_method_verification_request_status"/>
            <column name="redirect_url" type="varchar(4000)"/>
            <column name="client_ip" type="varchar"/>
            <column name="timestamp" type="varchar"/>
            <column name="doc_type" type="varchar"/>
            <column name="country" type="varchar"/>
            <column name="images" type="varchar[]"/>
            <column name="extracted_name" type="varchar"/>
            <column name="extracted_pan" type="varchar"/>
            <column name="extracted_issue_date" type="varchar"/>
            <column name="extracted_expiry_date" type="varchar"/>
            <column name="api_error" type="varchar"/>
            <column name="fail_reason" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_payment_method_verification_request_provider_scan_refe_1"
                              columnNames="provider,scan_reference" oneToOne="false" nullableColumns="scan_reference"/>
            <uniqueConstraint name="uq_payment_method_verification_request_provider_scan_refe_2"
                              columnNames="provider,scan_reference" oneToOne="false" nullableColumns="scan_reference"/>
        </createTable>
        <createIndex indexName="ix_payment_method_meta_info_account_id_fingerprint"
                     tableName="payment.payment_method_meta_info" columns="account_id,fingerprint"/>
    </changeSet>
</migration>