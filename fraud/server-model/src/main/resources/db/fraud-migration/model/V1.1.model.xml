<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.fraud_response_info" pkName="pk_fraud_response_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="response_id" type="varchar" unique="uq_fraud_response_info_response_id"/>
            <column name="cookie_hash" type="varchar"/>
            <column name="fraud_score" type="integer"/>
        </createTable>
    </changeSet>
</migration>