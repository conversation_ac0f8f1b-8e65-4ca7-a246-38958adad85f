<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.fraud_transaction_labels" pkName="pk_fraud_transaction_labels">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="fraud_request_id" type="varchar" notnull="true"/>
            <column name="label" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createIndex indexName="ix_fraud_transaction_labels_fraud_request_id" tableName="fraud.fraud_transaction_labels"
                     columns="fraud_request_id"/>
    </changeSet>
</migration>