<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules" checkConstraint="check ( decline_code in ('err_ok','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_cardholder_name_mismatch','unrecognized','err_card_blocked','err_card_not_verified','err_payment_routing_empty_chain','err_payment_inactive_provider','err_payment_email_not_verified','err_payment_purchase_limit'))" checkConstraintName="ck_fraud_decline_rules_decline_code"/>
        <alterColumn columnName="status" tableName="uam.accounts" checkConstraint="check ( status in ('default','requires_jumio_kyc','restricted_by_geo_policy','under_investigation','pending_redeem_review','bw_pending_redeem_review','pending_verification','pending_pm_verification','pending_pm_bw_verification','for_payment_processing','for_nsc_processing'))" checkConstraintName="ck_accounts_status"/>
        <alterForeignKey name="fk_idology_patriot_act_check_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_idology_patriot_act_check_account_id" tableName="fraud.idology_patriot_act"/>
        <dropIndex indexName="ix_idology_check_at" tableName="fraud.idology_check"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropTable name="fraud.idology_check" sequenceCol="account_id"/>
        <dropTable name="fraud.idology_patriot_act" sequenceCol="id"/>
    </changeSet>
</migration>