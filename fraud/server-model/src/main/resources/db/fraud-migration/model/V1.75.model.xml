<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="uam.account_auth_info" pkName="pk_account_auth_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_auth_info_id"/>
            <column name="last_activity" type="timestamp"/>
            <column name="last_sign_in_ip" type="varchar"/>
            <column name="last_sign_in_country" type="varchar"/>
            <column name="last_sign_in_city" type="varchar"/>
            <column name="last_sign_in_state" type="varchar"/>
            <column name="sign_up_ip" type="varchar(45)" notnull="true"/>
            <column name="sign_up_country" type="varchar"/>
            <column name="sign_up_city" type="varchar"/>
            <column name="sign_up_state" type="varchar"/>
            <column name="sign_up_method" type="varchar" notnull="true"/>
            <column name="first_utm_id" type="bigint" references="uam.utm_templates.id" foreignKeyName="fk_account_auth_info_first_utm_id" foreignKeyIndex="ix_account_auth_info_first_utm_id"/>
        </createTable>
        <createIndex indexName="ix_account_auth_info_last_activity" tableName="uam.account_auth_info" columns="last_activity"/>
        <createIndex indexName="ix_account_auth_info_last_sign_in_ip" tableName="uam.account_auth_info" columns="last_sign_in_ip"/>
        <createIndex indexName="ix_account_auth_info_sign_up_ip" tableName="uam.account_auth_info" columns="sign_up_ip"/>
        <dropIndex indexName="ix_account_meta_info_last_sign_in_ip" tableName="uam.account_meta_info"/>
        <dropIndex indexName="ix_account_meta_info_sign_up_ip" tableName="uam.account_meta_info"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="last_sign_in_ip" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_country" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_city" tableName="uam.account_meta_info"/>
        <dropColumn columnName="last_sign_in_state" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_country" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_city" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_state" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_method" tableName="uam.account_meta_info"/>
        <dropColumn columnName="first_utm_id" tableName="uam.account_meta_info"/>
        <dropColumn columnName="sign_up_ip" tableName="uam.account_meta_info"/>
    </changeSet>
</migration>