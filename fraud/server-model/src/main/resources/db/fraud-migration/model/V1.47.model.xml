<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.chat_events" pkName="pk_chat_events">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id"
                    foreignKeyName="fk_chat_events_account_id" foreignKeyIndex="ix_chat_events_account_id"/>
            <column name="action" type="varchar(11)" notnull="true"
                    checkConstraint="check ( action in ('chat_shown','chat_opened'))"
                    checkConstraintName="ck_chat_events_action"/>
            <column name="flow" type="varchar(13)" notnull="true"
                    checkConstraint="check ( flow in ('purchase_flow','redeem_flow'))"
                    checkConstraintName="ck_chat_events_flow"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
    </changeSet>
</migration>