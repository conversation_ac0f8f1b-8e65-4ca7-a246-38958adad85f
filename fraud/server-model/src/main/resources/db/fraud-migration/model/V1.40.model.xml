<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="vip_level_purchase" tableName="uam.accounts" checkConstraint="check ( vip_level_purchase in ('class_a','class_b','class_c','class_d','class_e','class_f','whale'))" checkConstraintName="ck_accounts_vip_level_purchase"/>
        <alterColumn columnName="vip_level_override_purchase" tableName="uam.accounts" checkConstraint="check ( vip_level_override_purchase in ('class_a','class_b','class_c','class_d','class_e','class_f','whale'))" checkConstraintName="ck_accounts_vip_level_override_purchase"/>
        <createTable name="fraud.otp_trigger_audit" pkName="pk_otp_trigger_audit">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" uniqueOneToOne="uq_otp_trigger_audit_account_id" references="uam.accounts.id" foreignKeyName="fk_otp_trigger_audit_account_id"/>
            <column name="triggered_at" type="timestamp" notnull="true"/>
            <column name="otp_trigger_rule" type="varchar(17)" checkConstraint="check ( otp_trigger_rule in ('signed_up_trigger','bonus_trigger'))" checkConstraintName="ck_otp_trigger_audit_otp_trigger_rule"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
        </createTable>
    </changeSet>
</migration>