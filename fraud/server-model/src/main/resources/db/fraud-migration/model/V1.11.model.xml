<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="fraud.fraud_decline_rules" pkName="pk_fraud_decline_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="rule_id" type="varchar" notnull="true"/>
            <column name="decline_reason" type="varchar" notnull="true"/>
            <column name="decline_code" type="varchar(25)" notnull="true"
                    checkConstraint="check ( decline_code in ('err_ok','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','unrecognized'))"
                    checkConstraintName="ck_fraud_decline_rules_decline_code"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
    </changeSet>
</migration>