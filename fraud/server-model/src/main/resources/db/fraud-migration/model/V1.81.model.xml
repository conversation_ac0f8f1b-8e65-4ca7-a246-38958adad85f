<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="fraud.inbox_notifications" withHistory="true">
            <column name="token" type="uuid" notnull="true" unique="uq_inbox_notifications_token"/>
            <column name="status" type="varchar(7)" notnull="true" checkConstraint="check ( status in ('read','removed','unread'))" checkConstraintName="ck_inbox_notifications_status"/>
        </addColumn>
        <createIndex indexName="ix_inbox_notifications_account_id_status_type" tableName="fraud.inbox_notifications" columns="account_id,status,type"/>
        <createIndex indexName="ix_inbox_notifications_token" tableName="fraud.inbox_notifications" columns="token"/>
        <dropIndex indexName="ix_inbox_notifications_account_id_deleted_type" tableName="fraud.inbox_notifications"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="deleted" tableName="fraud.inbox_notifications" withHistory="true"/>
    </changeSet>
</migration>