<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="id" tableName="uam.account_fraud_info" dropForeignKey="fk_account_fraud_info_id"/>
        <addColumn tableName="uam.account_fraud_info">
            <column name="account_id" type="bigint" uniqueOneToOne="uq_account_fraud_info_account_id"
                    references="uam.accounts.id" foreignKeyName="fk_account_fraud_info_account_id"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
    </changeSet>
</migration>