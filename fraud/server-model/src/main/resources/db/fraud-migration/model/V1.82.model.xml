<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.accounts">
            <column name="auth_email" type="varchar" notnull="true"/>
        </addColumn>
        <alterColumn columnName="brand_id" tableName="fraud.kyc_risk_spend_policy_history" currentType="integer" notnull="false" currentNotnull="true"/>
        <alterColumn columnName="report_type" tableName="fraud.report_log" type="varchar(23)" currentType="varchar(20)" currentNotnull="true" checkConstraint="check ( report_type in ('worldpay_chargebacks','worldpay_reconciliation'))" checkConstraintName="ck_report_log_report_type"/>
    </changeSet>
</migration>