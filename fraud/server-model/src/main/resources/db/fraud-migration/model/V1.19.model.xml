<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="currency" tableName="uam.account_balances" type="varchar" currentType="varchar(3)" currentNotnull="true"/>
        <alterColumn columnName="amount" tableName="uam.account_balances" type="decimal" currentType="decimal(16,2)" currentNotnull="true"/>
        <alterColumn columnName="total_sc_wager_amount" tableName="uam.account_gameplay_info" type="decimal" currentType="decimal(16,2)" currentNotnull="false"/>
        <alterColumn columnName="total_deposit_amount" tableName="payment.account_meta_info" type="decimal" currentType="decimal(16,2)" currentNotnull="false"/>
        <alterColumn columnName="country" tableName="uam.citizens" type="varchar" currentType="varchar(2)" currentNotnull="true"/>
        <alterColumn columnName="first_name" tableName="uam.citizens" type="varchar" currentType="varchar(100)" currentNotnull="false"/>
        <alterColumn columnName="last_name" tableName="uam.citizens" type="varchar" currentType="varchar(250)" currentNotnull="false"/>
        <alterColumn columnName="address" tableName="uam.citizens" type="varchar" currentType="varchar(4000)" currentNotnull="false"/>
        <alterColumn columnName="address2" tableName="uam.citizens" type="varchar" currentType="varchar(4000)" currentNotnull="false"/>
        <alterColumn columnName="username" tableName="uam.accounts" type="varchar" currentType="varchar(50)" currentNotnull="true"/>
        <alterColumn columnName="name" tableName="uam.accounts" type="varchar" currentType="varchar(4000)" currentNotnull="false"/>
        <alterColumn columnName="phone_number" tableName="uam.accounts" type="varchar" currentType="varchar(25)" currentNotnull="false"/>
        <addColumn tableName="uam.accounts">
            <column name="country" type="varchar(2) DEFAULT 'US'" notnull="true"/>
        </addColumn>
        <alterColumn columnName="last_sign_in_ip" tableName="uam.account_meta_info" type="varchar" currentType="varchar(45)" currentNotnull="false"/>
        <alterColumn columnName="last_sign_in_country" tableName="uam.account_meta_info" type="varchar" currentType="varchar(2)" currentNotnull="false"/>
        <alterColumn columnName="last_sign_in_city" tableName="uam.account_meta_info" type="varchar" currentType="varchar(4000)" currentNotnull="false"/>
        <alterColumn columnName="sign_up_country" tableName="uam.account_meta_info" type="varchar" currentType="varchar(2)" currentNotnull="false"/>
        <alterColumn columnName="sign_up_city" tableName="uam.account_meta_info" type="varchar" currentType="varchar(4000)" currentNotnull="false"/>
        <alterColumn columnName="sweepstake_currency" tableName="core.brands" type="varchar" currentType="varchar(3)" currentNotnull="false"/>
        <alterColumn columnName="first_name" tableName="uam.persons" type="varchar" currentType="varchar(100)" currentNotnull="false"/>
        <alterColumn columnName="last_name" tableName="uam.persons" type="varchar" currentType="varchar(250)" currentNotnull="false"/>
        <alterColumn columnName="doc_country" tableName="uam.kyc_verification_requests" type="varchar" currentType="varchar(2)" currentNotnull="false"/>
        <alterColumn columnName="phone_number" tableName="uam.phone_number_request" type="varchar" currentType="varchar(25)" notnull="false" currentNotnull="true"/>
        <dropIndex indexName="ix_accounts_bot" tableName="uam.accounts"/>
        <dropIndex indexName="ix_accounts_at" tableName="uam.accounts"/>
        <dropIndex indexName="ix_accounts_deleted" tableName="uam.accounts"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="bot" tableName="uam.accounts"/>
        <dropColumn columnName="at" tableName="uam.accounts"/>
        <dropColumn columnName="deleted" tableName="uam.accounts"/>
        <dropColumn columnName="title" tableName="core.brands"/>
        <dropColumn columnName="fiat_currency" tableName="core.brands"/>
        <dropColumn columnName="gold_currency" tableName="core.brands"/>
        <dropColumn columnName="type" tableName="core.products"/>
        <dropColumn columnName="title" tableName="core.products"/>
        <dropColumn columnName="name" tableName="core.products"/>
        <dropColumn columnName="route" tableName="core.products"/>
        <dropColumn columnName="mode" tableName="core.products"/>
        <dropColumn columnName="orientation" tableName="core.products"/>
        <dropColumn columnName="inactive" tableName="core.products"/>
        <dropColumn columnName="max_lines" tableName="core.products"/>
    </changeSet>
</migration>