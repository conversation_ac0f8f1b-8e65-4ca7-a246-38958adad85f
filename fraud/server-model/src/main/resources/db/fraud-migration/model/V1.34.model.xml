<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="decline_code" tableName="fraud.fraud_decline_rules"
                     checkConstraint="check ( decline_code in ('err_ok','err_ok_no_content','err_system','err_auth','err_bad_request','err_denied','err_frozen','err_not_found','err_timeout','err_duplicate','err_insufficient_funds','err_payment','err_payment_routing','err_payment_input_cvv','err_payment_return','err_fraud_declined','err_payment_method_paused','err_kyc_required','err_kyc_required_low_risk','err_kyc_required_mid_risk','err_kyc_required_high_risk','err_tc_required','err_sr_required','err_pp_required','err_password_weak','err_password_same','err_password_incorrect','err_password_repeats','err_cardholder_name_mismatch','unrecognized','err_card_blocked','err_card_not_verified','err_payment_routing_empty_chain','err_payment_inactive_provider','err_payment_email_not_verified','err_payment_purchase_limit','err_payment_method_blocked'))"
                     checkConstraintName="ck_fraud_decline_rules_decline_code"/>
        <addColumn tableName="uam.accounts">
            <column name="phone_number_hash" type="varchar"/>
            <column name="admin" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <addColumn tableName="core.brands">
            <column name="phone_expiration" type="integer"/>
        </addColumn>
        <alterColumn columnName="phone_number" tableName="uam.phone_number_request" type="varchar(25)"
                     currentType="varchar" notnull="true" currentNotnull="false"/>
        <addColumn tableName="uam.phone_number_request">
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </addColumn>
        <addUniqueConstraint constraintName="uq_phone_number_request_account_id_sid"
                             tableName="uam.phone_number_request" columnNames="DROP CONSTRAINT"
                             nullableColumns="account_id"/>
        <addUniqueConstraint constraintName="uq_phone_number_request_account_id_sid_phone_number"
                             tableName="uam.phone_number_request" columnNames="account_id,sid,phone_number"
                             oneToOne="false" nullableColumns="account_id"/>
        <createIndex indexName="ix_accounts_admin" tableName="uam.accounts" columns="admin"/>
    </changeSet>
</migration>