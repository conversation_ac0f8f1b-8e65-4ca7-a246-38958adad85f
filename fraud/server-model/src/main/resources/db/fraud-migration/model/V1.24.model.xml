<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="uam.account_kyc_info" pkName="pk_account_kyc_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_kyc_info_id"/>
            <column name="attempts" type="integer" notnull="true"/>
            <column name="first_kyc" type="timestamp"/>
            <column name="last_kyc" type="timestamp"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropColumn columnName="last_kyc" tableName="uam.account_meta_info"/>
    </changeSet>
</migration>