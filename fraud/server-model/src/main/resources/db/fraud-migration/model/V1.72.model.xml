<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="payment.account_meta_info">
            <column name="total_withdraw_amount" type="decimal"/>
            <column name="last_payment_order_id" type="bigint"
                    uniqueOneToOne="uq_account_meta_info_last_payment_order_id" references="payment.payment_orders.id"
                    foreignKeyName="fk_account_meta_info_last_payment_order_id"/>
            <column name="last_deposit_order_id" type="bigint"
                    uniqueOneToOne="uq_account_meta_info_last_deposit_order_id" references="payment.payment_orders.id"
                    foreignKeyName="fk_account_meta_info_last_deposit_order_id"/>
            <column name="first_withdraw_id" type="bigint" uniqueOneToOne="uq_account_meta_info_first_withdraw_id"
                    references="payment.withdraw_money_requests.id"
                    foreignKeyName="fk_account_meta_info_first_withdraw_id"/>
            <column name="last_withdraw_id" type="bigint" uniqueOneToOne="uq_account_meta_info_last_withdraw_id"
                    references="payment.withdraw_money_requests.id"
                    foreignKeyName="fk_account_meta_info_last_withdraw_id"/>
        </addColumn>
        <createTable name="payment.payment_orders" pkName="pk_payment_orders">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true" unique="uq_payment_orders_transaction_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id"
                    foreignKeyName="fk_payment_orders_account_id" foreignKeyIndex="ix_payment_orders_account_id"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="at" type="date" notnull="true"/>
            <column name="fraud_score" type="integer"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
        </createTable>
        <createTable name="payment.withdraw_money_requests" withHistory="true" pkName="pk_withdraw_money_requests">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="transaction_id" type="uuid" notnull="true"
                    unique="uq_withdraw_money_requests_transaction_id"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id"
                    foreignKeyName="fk_withdraw_money_requests_account_id"
                    foreignKeyIndex="ix_withdraw_money_requests_account_id"/>
            <column name="ext_code" type="varchar"/>
            <column name="currency" type="varchar(3)" notnull="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <column name="local_currency" type="varchar(3)"/>
            <column name="base_amount" type="decimal(16,2)"/>
            <column name="email" type="varchar(4000)"/>
            <column name="fraud_score" type="integer"/>
            <column name="confirm_score" type="integer"/>
            <column name="decline_score" type="integer"/>
            <column name="fraud_response_id" type="bigint"/>
            <column name="payout_type" type="varchar"/>
            <column name="at" type="date" notnull="true"/>
            <column name="locked_at" type="timestamp"/>
            <column name="locked_by_agent" type="varchar"/>
            <column name="confirmed_at" type="timestamp"/>
            <column name="declined_at" type="timestamp"/>
            <column name="pre_confirmed_at" type="timestamp"/>
            <column name="cancelled_at" type="timestamp"/>
            <column name="remote_ip" type="varchar(45)"/>
            <column name="status" type="varchar(14)" notnull="true"
                    checkConstraint="check ( status in ('failed','new','pre_authorized','confirmed','declined','cancelled','locked'))"
                    checkConstraintName="ck_withdraw_money_requests_status"/>
            <column name="modified_at_date" type="date"/>
            <column name="requested_at" type="timestamp"/>
            <column name="risk_status_approved_at" type="timestamp"/>
            <column name="comments" type="varchar"/>
            <column name="rtp_flag" type="boolean" defaultValue="false" notnull="true"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <alterForeignKey name="fk_payment_method_meta_info_account_id" columnNames="DROP FOREIGN KEY"
                         indexName="ix_payment_method_meta_info_account_id"
                         tableName="payment.payment_method_meta_info"/>
        <alterForeignKey name="fk_payment_method_verification_request_account_id" columnNames="DROP FOREIGN KEY"
                         indexName="ix_payment_method_verification_request_account_id"
                         tableName="payment.payment_method_verification_request"/>
        <alterForeignKey name="fk_payment_method_verification_request_meta_info_id" columnNames="DROP FOREIGN KEY"
                         indexName="ix_payment_method_verification_request_meta_info_id"
                         tableName="payment.payment_method_verification_request"/>
        <createIndex indexName="ix_payment_orders_at" tableName="payment.payment_orders" columns="at"/>
        <createIndex indexName="ix_withdraw_money_requests_transaction_id" tableName="payment.withdraw_money_requests"
                     columns="transaction_id"/>
        <createIndex indexName="ix_withdraw_money_requests_at" tableName="payment.withdraw_money_requests"
                     columns="at"/>
        <createIndex indexName="ix_withdraw_money_requests_modified_at_date" tableName="payment.withdraw_money_requests"
                     columns="modified_at_date"/>
        <createIndex indexName="ix_withdraw_money_requests_modified_at" tableName="payment.withdraw_money_requests"
                     columns="modified_at"
                     definition="create index ix_withdraw_money_requests_modified_at on payment.withdraw_money_requests (date(timezone('UTC',modified_at)))"
                     platforms="POSTGRES"/>
        <dropIndex indexName="ix_payment_method_meta_info_account_id_fingerprint"
                   tableName="payment.payment_method_meta_info"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropTable name="payment.payment_method_meta_info" sequenceCol="id"/>
        <dropTable name="payment.payment_method_verification_request" sequenceCol="id"/>
    </changeSet>
</migration>