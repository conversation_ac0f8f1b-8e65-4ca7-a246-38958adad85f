<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="uam.fraud_rules" identityType="external" pkName="pk_fraud_rules">
            <column name="id" type="varchar" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
    </changeSet>
</migration>