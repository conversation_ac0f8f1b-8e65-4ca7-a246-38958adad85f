<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="uam.account_balances" pkName="pk_account_balances">
            <column name="account_id" type="bigint" notnull="true" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_balances_account_id" foreignKeyIndex="ix_account_balances_account_id"/>
            <column name="currency" type="varchar(3)" notnull="true" primaryKey="true"/>
            <column name="amount" type="decimal(16,2)" notnull="true"/>
            <uniqueConstraint name="uq_account_balances_account_id_currency" columnNames="account_id,currency" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.account_category_tags" pkName="pk_account_category_tags">
            <column name="account_id" type="bigint" notnull="true" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_category_tags_account_id" foreignKeyIndex="ix_account_category_tags_account_id"/>
            <column name="category" type="varchar" notnull="true" primaryKey="true"/>
            <column name="tags" type="varchar[]"/>
        </createTable>
        <createTable name="uam.account_fraud_info" pkName="pk_account_fraud_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_fraud_info_id"/>
            <column name="last_sign_in_fraud_score" type="integer"/>
            <column name="sign_up_fraud_score" type="integer"/>
            <column name="last_fraud_score" type="integer"/>
            <column name="sign_up_cookie_hash" type="varchar"/>
            <column name="last_sign_in_cookie_hash" type="varchar"/>
            <column name="last_withdraw_cookie_hash" type="varchar"/>
        </createTable>
        <createTable name="uam.account_gameplay_info" pkName="pk_account_gameplay_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_gameplay_info_id"/>
            <column name="total_sc_game_round_count" type="integer"/>
            <column name="first_sc_casino_product_id" type="integer" references="core.products.id" foreignKeyName="fk_account_gameplay_info_first_sc_casino_product_id" foreignKeyIndex="ix_account_gameplay_info_first_sc_casino_product_id"/>
            <column name="total_sc_wager_amount" type="decimal(16,2)"/>
        </createTable>
        <createTable name="uam.account_oauths" pkName="pk_account_oauths">
            <column name="account_id" type="bigint" notnull="true" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_oauths_account_id" foreignKeyIndex="ix_account_oauths_account_id"/>
            <column name="provider" type="varchar" notnull="true" primaryKey="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_account_oauths_brand_id" foreignKeyIndex="ix_account_oauths_brand_id"/>
        </createTable>
        <createTable name="uam.account_preferences" pkName="pk_account_preferences">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_preferences_id"/>
            <column name="lang" type="varchar(2)" notnull="true"/>
            <column name="timezone" type="varchar(256)"/>
            <column name="do_not_send_emails" type="boolean" defaultValue="false" notnull="true"/>
            <column name="do_not_send_pushes" type="boolean" defaultValue="false" notnull="true"/>
        </createTable>
        <createTable name="uam.account_tracking_info" pkName="pk_account_tracking_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_account_tracking_info_account_id" foreignKeyIndex="ix_account_tracking_info_account_id"/>
            <column name="tracking_id" type="varchar" notnull="true"/>
        </createTable>
        <createTable name="uam.citizens" pkName="pk_citizens">
            <column name="person_id" type="bigint" notnull="true" primaryKey="true" references="uam.persons.id" foreignKeyName="fk_citizens_person_id" foreignKeyIndex="ix_citizens_person_id"/>
            <column name="country" type="varchar(2)" notnull="true" primaryKey="true"/>
            <column name="first_name" type="varchar(100)"/>
            <column name="last_name" type="varchar(250)"/>
            <column name="state" type="varchar"/>
            <column name="city" type="varchar"/>
            <column name="address" type="varchar(4000)"/>
            <column name="address2" type="varchar(4000)"/>
            <column name="postal" type="varchar(50)"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_citizens_person_id_country" columnNames="person_id,country" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.accounts" pkName="pk_accounts">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="hash" type="varchar" notnull="true"/>
            <column name="bot" type="boolean" defaultValue="false" notnull="true"/>
            <column name="at" type="date" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_accounts_brand_id" foreignKeyIndex="ix_accounts_brand_id"/>
            <column name="person_id" type="bigint" notnull="true" references="uam.persons.id" foreignKeyName="fk_accounts_person_id" foreignKeyIndex="ix_accounts_person_id"/>
            <column name="username" type="varchar(50)" notnull="true"/>
            <column name="email" type="varchar" notnull="true"/>
            <column name="real_email" type="varchar" notnull="true"/>
            <column name="name" type="varchar(4000)"/>
            <column name="phone_number" type="varchar(25)"/>
            <column name="mode" type="varchar(22)" notnull="true" checkConstraint="check ( mode in ('default','gold','gold_free','sweepstake','sweepstake_preview','sweepstake_preview_web'))" checkConstraintName="ck_accounts_mode"/>
            <column name="vip_level_purchase" type="varchar(7)" checkConstraint="check ( vip_level_purchase in ('class_a','class_b','class_c','class_d'))" checkConstraintName="ck_accounts_vip_level_purchase"/>
            <column name="kyc" type="varchar(15)" notnull="true" checkConstraint="check ( kyc in ('initial','in_review','confirmed','id_confirmed','doc_review','doc_declined','session_expired','declined','blocked'))" checkConstraintName="ck_accounts_kyc"/>
            <column name="status" type="varchar(21)" notnull="true" checkConstraint="check ( status in ('default','requires_jumio_kyc','under_investigation','pending_redeem_review','pending_verification'))" checkConstraintName="ck_accounts_status"/>
            <column name="vip_level_override_purchase" type="varchar(7)" checkConstraint="check ( vip_level_override_purchase in ('class_a','class_b','class_c','class_d'))" checkConstraintName="ck_accounts_vip_level_override_purchase"/>
            <column name="invited_by_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_accounts_invited_by_id" foreignKeyIndex="ix_accounts_invited_by_id"/>
            <column name="guest" type="boolean" defaultValue="false" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="uam.account_meta_info" pkName="pk_account_meta_info">
            <column name="id" type="bigint" primaryKey="true" references="uam.accounts.id" foreignKeyName="fk_account_meta_info_id"/>
            <column name="last_sign_in_ip" type="varchar(45)"/>
            <column name="last_sign_in_country" type="varchar(2)"/>
            <column name="last_sign_in_city" type="varchar(4000)"/>
            <column name="last_sign_in_state" type="varchar"/>
            <column name="sign_up_country" type="varchar(2)"/>
            <column name="sign_up_city" type="varchar(4000)"/>
            <column name="sign_up_state" type="varchar"/>
            <column name="sign_up_method" type="varchar" notnull="true"/>
            <column name="last_deposit" type="timestamp"/>
            <column name="last_kyc" type="timestamp"/>
            <column name="deposit_count" type="integer" notnull="true"/>
            <column name="first_utm_id" type="bigint" references="uam.utm_templates.id" foreignKeyName="fk_account_meta_info_first_utm_id" foreignKeyIndex="ix_account_meta_info_first_utm_id"/>
            <column name="total_deposit_amount" type="decimal(16,2)"/>
        </createTable>
        <createTable name="core.brands" pkName="pk_brands">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true" unique="uq_brands_name"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="fiat_currency" type="varchar(3)" notnull="true"/>
            <column name="gold_currency" type="varchar(3)" notnull="true"/>
            <column name="sweepstake_currency" type="varchar(3)"/>
            <column name="secret" type="varchar"/>
            <column name="fraud_lock_threshold" type="integer"/>
            <column name="fraud_suspect_threshold" type="integer"/>
        </createTable>
        <createTable name="core.products" pkName="pk_products">
            <column name="id" type="integer" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="type" type="varchar" notnull="true"/>
            <column name="title" type="varchar" notnull="true"/>
            <column name="name" type="varchar"/>
            <column name="route" type="varchar" notnull="true"/>
            <column name="mode" type="varchar(15)" notnull="true" checkConstraint="check ( mode in ('gold','sweepstake','fiat','gold_sweepstake','gold_fiat','free'))" checkConstraintName="ck_products_mode"/>
            <column name="orientation" type="varchar(9)" notnull="true" checkConstraint="check ( orientation in ('landscape','portrait','both'))" checkConstraintName="ck_products_orientation"/>
            <column name="inactive" type="boolean" defaultValue="false" notnull="true"/>
            <column name="max_lines" type="integer" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_products_brand_id" foreignKeyIndex="ix_products_brand_id"/>
        </createTable>
        <createTable name="uam.kyc_verification_requests" pkName="pk_kyc_verification_requests">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="code" type="varchar" notnull="true"/>
            <column name="provider" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_kyc_verification_requests_account_id" foreignKeyIndex="ix_kyc_verification_requests_account_id"/>
            <column name="status" type="varchar(15)" notnull="true" checkConstraint="check ( status in ('initial','in_review','confirmed','id_confirmed','doc_review','doc_declined','session_expired','declined','blocked'))" checkConstraintName="ck_kyc_verification_requests_status"/>
            <column name="id_first_name" type="varchar"/>
            <column name="id_last_name" type="varchar"/>
            <column name="id_birth_date" type="date"/>
            <column name="doc_status" type="varchar(15)" notnull="true" checkConstraint="check ( doc_status in ('initial','in_review','confirmed','id_confirmed','doc_review','doc_declined','session_expired','declined','blocked'))" checkConstraintName="ck_kyc_verification_requests_doc_status"/>
            <column name="doc_country" type="varchar(2)"/>
            <column name="doc_state" type="varchar"/>
            <column name="doc_city" type="varchar"/>
            <column name="doc_address" type="varchar"/>
            <column name="doc_postal" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_kyc_verification_requests_provider_code" columnNames="provider,code" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createTable name="uam.persons" pkName="pk_persons">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="first_name" type="varchar(100)"/>
            <column name="last_name" type="varchar(250)"/>
            <column name="birth_date" type="date"/>
        </createTable>
        <createTable name="uam.phone_number_request" pkName="pk_phone_number_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="phone_number" type="varchar(25)" notnull="true"/>
            <column name="sid" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_phone_number_request_account_id" foreignKeyIndex="ix_phone_number_request_account_id"/>
            <column name="last_attempt" type="timestamp" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_phone_number_request_account_id_sid" columnNames="account_id,sid" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <createTable name="uam.utm_templates" pkName="pk_utm_templates">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="campaign" type="varchar" notnull="true"/>
            <column name="brand_id" type="integer" notnull="true" references="core.brands.id" foreignKeyName="fk_utm_templates_brand_id" foreignKeyIndex="ix_utm_templates_brand_id"/>
            <uniqueConstraint name="uq_utm_templates_brand_id_campaign" columnNames="brand_id,campaign" oneToOne="false" nullableColumns=""/>
        </createTable>
        <createIndex indexName="ix_accounts_hash" tableName="uam.accounts" columns="hash"/>
        <createIndex indexName="ix_accounts_bot" tableName="uam.accounts" columns="bot"/>
        <createIndex indexName="ix_accounts_at" tableName="uam.accounts" columns="at"/>
        <createIndex indexName="ix_account_meta_info_last_sign_in_ip" tableName="uam.account_meta_info" columns="last_sign_in_ip"/>
        <createIndex indexName="ix_phone_number_request_phone_number" tableName="uam.phone_number_request" columns="phone_number"/>
    </changeSet>
</migration>
