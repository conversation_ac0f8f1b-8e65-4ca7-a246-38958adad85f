<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="fraud_response_id" tableName="uam.fraud_applied_rules" references="fraud.fraud_response.id" foreignKeyName="fk_fraud_applied_rules_fraud_response_id" foreignKeyIndex="ix_fraud_applied_rules_fraud_response_id" dropForeignKey="fk_fraud_applied_rules_fraud_response_id" dropForeignKeyIndex="ix_fraud_applied_rules_fraud_response_id"/>
        <createTable name="fraud.fraud_response" pkName="pk_fraud_response">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="response_id" type="varchar" notnull="true"/>
            <column name="cookie_hash" type="varchar"/>
            <column name="action_type" type="varchar(16)" checkConstraint="check ( action_type in ('account_login','account_register','withdrawal','purchase','aml_check','verification'))" checkConstraintName="ck_fraud_response_action_type"/>
            <column name="score" type="integer"/>
            <column name="account_id" type="bigint" notnull="true" references="uam.accounts.id" foreignKeyName="fk_fraud_response_account_id" foreignKeyIndex="ix_fraud_response_account_id"/>
            <column name="ip_details_id" type="bigint" references="uam.ip_details.id" foreignKeyName="fk_fraud_response_ip_details_id" foreignKeyIndex="ix_fraud_response_ip_details_id"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.phone_number_request" withHistory="true" pkName="pk_phone_number_request">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="phone_number" type="varchar(25)" notnull="true"/>
            <column name="sid" type="varchar" notnull="true"/>
            <column name="account_id" type="bigint" references="uam.accounts.id" foreignKeyName="fk_phone_number_request_account_id" foreignKeyIndex="ix_phone_number_request_account_id"/>
            <column name="verified" type="boolean" defaultValue="false" notnull="true"/>
            <column name="last_attempt" type="timestamp" notnull="true"/>
            <column name="carrier_name" type="varchar"/>
            <column name="country_code" type="varchar"/>
            <column name="network_code" type="varchar"/>
            <column name="type" type="varchar(12)" checkConstraint="check ( type in ('landline','mobile','fixedVoip','nonFixedVoip','personal','tollFree','premium','sharedCost','uan','voicemail','pager','unknown'))" checkConstraintName="ck_phone_number_request_type"/>
            <column name="error_code" type="varchar"/>
            <column name="error_reason" type="varchar(28)" checkConstraint="check ( error_reason in ('duplicate_phone_number_error','otp_not_entered_error','invalid_phone_number_error','country_is_not_matched_error','incorrect_otp_error'))" checkConstraintName="ck_phone_number_request_error_reason"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_phone_number_request_account_id_sid_phone_number" columnNames="account_id,sid,phone_number" oneToOne="false" nullableColumns="account_id"/>
        </createTable>
        <alterForeignKey name="fk_phone_number_request_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_phone_number_request_account_id" tableName="uam.phone_number_request"/>
        <alterForeignKey name="fk_fraud_response_account_id" columnNames="DROP FOREIGN KEY" indexName="ix_fraud_response_account_id" tableName="uam.fraud_response"/>
        <alterForeignKey name="fk_fraud_response_ip_details_id" columnNames="DROP FOREIGN KEY" indexName="ix_fraud_response_ip_details_id" tableName="uam.fraud_response"/>
        <dropIndex indexName="ix_fraud_response_response_id" tableName="uam.fraud_response"/>
        <createIndex indexName="ix_fraud_response_response_id" tableName="fraud.fraud_response" columns="response_id"/>
        <dropIndex indexName="ix_phone_number_request_phone_number" tableName="uam.phone_number_request"/>
        <createIndex indexName="ix_phone_number_request_phone_number" tableName="fraud.phone_number_request" columns="phone_number"/>
        <createTable name="fraud.account_fraud_info" pkName="pk_account_fraud_info">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="account_id" type="bigint" uniqueOneToOne="uq_account_fraud_info_account_id" references="uam.accounts.id" foreignKeyName="fk_account_fraud_info_account_id"/>
            <column name="last_sign_in_fraud_score" type="integer"/>
            <column name="sign_up_fraud_score" type="integer"/>
            <column name="last_fraud_score" type="integer"/>
            <column name="sign_up_cookie_hash" type="varchar"/>
            <column name="last_sign_in_cookie_hash" type="varchar"/>
            <column name="last_withdraw_cookie_hash" type="varchar"/>
            <column name="seon_session" type="varchar"/>
            <column name="version" type="integer" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <column name="modified_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.fraud_applied_rules" pkName="pk_fraud_applied_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="rule_id" type="varchar" notnull="true"/>
            <column name="score" type="integer" notnull="true"/>
            <column name="operation" type="varchar"/>
            <column name="fraud_response_id" type="bigint" notnull="true" references="fraud.fraud_response.id" foreignKeyName="fk_fraud_applied_rules_fraud_response_id" foreignKeyIndex="ix_fraud_applied_rules_fraud_response_id"/>
            <column name="at" type="date" notnull="true"/>
        </createTable>
        <alterColumn columnName="ip_details_id" tableName="fraud.fraud_response" references="fraud.ip_details.id" foreignKeyName="fk_fraud_response_ip_details_id" foreignKeyIndex="ix_fraud_response_ip_details_id" dropForeignKey="fk_fraud_response_ip_details_id" dropForeignKeyIndex="ix_fraud_response_ip_details_id"/>
        <createTable name="fraud.fraud_rules" identityType="external" pkName="pk_fraud_rules">
            <column name="id" type="varchar" primaryKey="true"/>
            <column name="name" type="varchar" notnull="true"/>
            <column name="created_at" type="timestamp" notnull="true"/>
        </createTable>
        <createTable name="fraud.ip_details" pkName="pk_ip_details">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="ip" type="varchar"/>
            <column name="type" type="varchar"/>
            <column name="created_at" type="timestamp" notnull="true"/>
            <uniqueConstraint name="uq_ip_details_ip" columnNames="ip" oneToOne="false" nullableColumns="ip"/>
        </createTable>
        <alterForeignKey name="fk_account_fraud_info_account_id" columnNames="DROP FOREIGN KEY" tableName="uam.account_fraud_info"/>
        <alterForeignKey name="fk_fraud_applied_rules_fraud_response_id" columnNames="DROP FOREIGN KEY" indexName="ix_fraud_applied_rules_fraud_response_id" tableName="uam.fraud_applied_rules"/>
        <dropIndex indexName="ix_account_fraud_info_sign_up_fraud_score" tableName="uam.account_fraud_info"/>
        <createIndex indexName="ix_account_fraud_info_sign_up_fraud_score" tableName="fraud.account_fraud_info" columns="sign_up_fraud_score"/>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropTable name="uam.fraud_response" sequenceCol="id"/>
        <dropTable name="uam.ip_details" sequenceCol="id"/>
        <dropTable name="uam.fraud_rules"/>
        <dropTable name="uam.phone_number_request" sequenceCol="id"/>
        <dropTable name="uam.account_fraud_info" sequenceCol="id"/>
        <dropTable name="uam.fraud_applied_rules" sequenceCol="id"/>
    </changeSet>
</migration>