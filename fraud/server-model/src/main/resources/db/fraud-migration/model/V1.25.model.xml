<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.account_gameplay_info">
            <column name="last_gameplay" type="timestamp"/>
        </addColumn>
        <addColumn tableName="payment.account_meta_info">
            <column name="withdraw_count" type="integer" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.accounts">
            <column name="at" type="date" notnull="true"/>
            <column name="locked" type="boolean" defaultValue="false" notnull="true"/>
        </addColumn>
        <addColumn tableName="uam.account_meta_info">
            <column name="last_activity" type="timestamp"/>
        </addColumn>
        <createIndex indexName="ix_account_gameplay_info_last_gameplay" tableName="uam.account_gameplay_info" columns="last_gameplay"/>
        <createIndex indexName="ix_accounts_at" tableName="uam.accounts" columns="at"/>
        <createIndex indexName="ix_account_meta_info_last_activity" tableName="uam.account_meta_info" columns="last_activity"/>
    </changeSet>
</migration>