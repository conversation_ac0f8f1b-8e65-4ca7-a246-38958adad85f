<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <addColumn tableName="uam.phone_number_request">
            <column name="carrier_name" type="varchar"/>
            <column name="country_code" type="varchar"/>
            <column name="network_code" type="varchar"/>
            <column name="type" type="varchar(12)"
                    checkConstraint="check ( type in ('landline','mobile','fixedVoip','nonFixedVoip','personal','tollFree','premium','sharedCost','uan','voicemail','pager','unknown'))"
                    checkConstraintName="ck_phone_number_request_type"/>
            <column name="error_code" type="varchar"/>
            <column name="error_reason" type="varchar(28)"
                    checkConstraint="check ( error_reason in ('duplicate_phone_number_error','otp_not_entered_error','invalid_phone_number_error','country_is_not_matched_error','incorrect_otp_error'))"
                    checkConstraintName="ck_phone_number_request_error_reason"/>
        </addColumn>
    </changeSet>
</migration>