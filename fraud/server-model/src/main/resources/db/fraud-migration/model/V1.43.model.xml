<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <alterColumn columnName="otp_trigger_rule" tableName="fraud.otp_trigger_audit" type="varchar(18)" currentType="varchar(17)" currentNotnull="false" checkConstraint="check ( otp_trigger_rule in ('signed_up_trigger','bonus_trigger','utm_source_trigger'))" checkConstraintName="ck_otp_trigger_audit_otp_trigger_rule"/>
        <addColumn tableName="fraud.otp_trigger_rules">
            <column name="utm_source_trigger_enabled" type="boolean" defaultValue="false" notnull="true"/>
            <column name="utm_sources" type="varchar[]" notnull="true"/>
        </addColumn>
    </changeSet>
</migration>