<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<migration xmlns="http://ebean-orm.github.io/xml/ns/dbmigration">
    <changeSet type="apply">
        <createTable name="uam.fraud_applied_rules" pkName="pk_fraud_applied_rules">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="rule_id" type="varchar" notnull="true"/>
            <column name="score" type="integer" notnull="true"/>
            <column name="fraud_response_id" type="bigint" notnull="true" references="uam.fraud_response.id" foreignKeyName="fk_fraud_applied_rules_fraud_response_id" foreignKeyIndex="ix_fraud_applied_rules_fraud_response_id"/>
        </createTable>
        <createTable name="uam.fraud_response" pkName="pk_fraud_response">
            <column name="id" type="bigint" primaryKey="true"/>
            <column name="response_id" type="varchar" notnull="true"/>
            <column name="cookie_hash" type="varchar"/>
        </createTable>
        <createTable name="uam.fraud_response_info" pkName="pk_fraud_response_info">
            <column name="response_id" type="varchar"/>
            <column name="cookie_hash" type="varchar"/>
            <column name="fraud_score" type="integer"/>
        </createTable>
    </changeSet>
    <changeSet type="pendingDrops">
        <dropTable name="fraud.fraud_response_info" sequenceCol="id"/>
    </changeSet>
</migration>