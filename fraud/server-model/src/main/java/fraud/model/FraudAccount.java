package fraud.model;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.KYCStatusSpec;
import core.model.CoreAccount;
import fraud.model.chat.ChatEvents;
import fraud.model.fraud.AccountFraudInfo;
import fraud.model.fraud.AmlCheck;
import fraud.model.fraud.FraudResponse;
import fraud.model.kyc.AccountKycInfo;
import fraud.model.kyc.KYCVerificationRequest;
import fraud.model.otp.PhoneNumberRequest;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@Setter
@Entity
@Table(name = "accounts", schema = Schemas.FRAUD)
public class FraudAccount extends CoreAccount {
    @ManyToOne(optional = false)
    private FraudBrand brand;

    @OneToOne(mappedBy = "account")
    private AccountKycInfo kycInfo;

    @OneToOne(mappedBy = "account")
    private AccountFraudInfo fraudInfo;

    @OneToOne(mappedBy = "account", fetch = FetchType.LAZY)
    private AmlCheck amlCheck;

    @OneToMany(mappedBy = "account")
    private List<PhoneNumberRequest> phoneNumberVerifications;

    @OneToMany(mappedBy = "account")
    private List<KYCVerificationRequest> kycVerifications;

    @OneToMany(mappedBy = "account")
    private List<FraudResponse> fraudResponses;

    @OneToMany(mappedBy = "account")
    private List<ChatEvents> chatEvents;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public FraudAccount() {
    }

    public FraudAccount(FraudBrand brand) {
        this.brand = brand;
    }

    public Optional<AccountFraudInfo> fraudInfo() {
        return Optional.ofNullable(fraudInfo);
    }

    public Optional<AccountKycInfo> kycInfo() {
        return Optional.ofNullable(kycInfo);
    }

    public Optional<AmlCheck> amlCheck() {
        return Optional.ofNullable(amlCheck);
    }

    public Optional<KYCVerificationRequest> confirmedKycId() {
        return getKycVerifications().stream().filter(
                kvr -> kvr.getStatus().isIdConfirmed()).sorted(Comparator.reverseOrder()).findFirst();
    }

    public String jumioCity() {
        return latestKycDoc().map(KYCVerificationRequest::getDocCity).orElse(null);
    }

    public String jumioState() {
        return latestKycDoc().map(KYCVerificationRequest::getDocState).orElse(null);
    }

    public String jumioCountry() {
        return latestKycDoc().map(KYCVerificationRequest::getDocCountry).orElse(null);
    }

    public String jumioPostal() {
        return latestKycDoc().map(KYCVerificationRequest::getDocPostal).orElse(null);
    }

    public String jumioAddress() {
        return latestKycDoc().map(KYCVerificationRequest::getDocAddress).orElse(null);
    }

    public Optional<LocalDate> jumioBirthDate() {
        return confirmedKycId().map(KYCVerificationRequest::getIdBirthDate);
    }

    public Optional<String> jumioFirstName() {
        return confirmedKycId().map(KYCVerificationRequest::getIdFirstName);
    }

    public Optional<String> jumioLastName() {
        return confirmedKycId().map(KYCVerificationRequest::getIdLastName);
    }

    private Optional<KYCVerificationRequest> latestKycId() {
        return resolveIdKYCState(getKycVerificationSorted());
    }

    public Optional<KYCVerificationRequest> latestKycDoc() {
        return resolveDocKYCState(getKycVerificationSorted());
    }

    private Collection<KYCVerificationRequest> getKycVerificationSorted() {
        return getKycVerifications().stream().sorted(Comparator.reverseOrder()).collect(Collectors.toList());
    }

    public Optional<KYCVerificationRequest> resolveIdKYCState(Collection<KYCVerificationRequest> kvrs) {
        return getLastIdConfirmedKvr(kvrs).or(() -> getLastNotInitialKvr(kvrs));
    }

    public Optional<KYCVerificationRequest> resolveDocKYCState(Collection<KYCVerificationRequest> kvrs) {
        return kvrs.stream().filter(k -> !k.getDocStatus().isInitial()).findFirst();
    }

    private static Optional<KYCVerificationRequest> getLastNotInitialKvr(Collection<KYCVerificationRequest> kvrs) {
        return kvrs.stream().filter(k -> !k.getStatus().isInitial()).findFirst();
    }

    private static Optional<KYCVerificationRequest> getLastIdConfirmedKvr(Collection<KYCVerificationRequest> kvrs) {
        return kvrs.stream().filter(k -> k.getStatus().isIdConfirmed()).findFirst();
    }

    public Optional<KYCVerificationRequest> confirmedKycDoc() {
        return getKycVerifications().stream()
                .filter(kvr -> kvr.getDocStatus().isConfirmed())
                .max(Comparator.naturalOrder());
    }

    public String jumioDocStatus() {
        var kycStatus = fraudInfo().map(AccountFraudInfo::getKyc).orElse(KYCStatusSpec.INITIAL);
        switch (kycStatus) {
            case CONFIRMED, DOC_REVIEW, DOC_DECLINED:
                return kycStatus.code();
            default: {
                Optional<KYCVerificationRequest> opt = latestKycDoc();
                if (opt.isPresent()) {
                    KYCVerificationRequest req = opt.get();
                    return req.getDocStatus().code();
                }
                return null;
            }
        }
    }

    public String jumioIdStatus() {
        var kycStatus = fraudInfo().map(AccountFraudInfo::getKyc).orElse(KYCStatusSpec.INITIAL);
        switch (kycStatus) {
            case ID_CONFIRMED, IN_REVIEW, DECLINED:
                return kycStatus.code();
            default: {
                Optional<KYCVerificationRequest> opt = latestKycId();
                if (opt.isPresent()) {
                    KYCVerificationRequest req = opt.get();
                    return req.getStatus().code();
                }
                return null;
            }
        }
    }

    public void assertHash(String expected) throws ApplicationException {
        if (StringUtils.equals(getHash(), expected)) {

        } else {
            throw ApplicationException.of("Something went wrong, command is unroutable", Code.ERR_AUTH);
        }
    }
}
