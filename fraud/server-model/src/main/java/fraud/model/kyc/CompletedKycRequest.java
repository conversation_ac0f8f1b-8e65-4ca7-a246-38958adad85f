package fraud.model.kyc;

import java.util.Date;

import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(schema = Schemas.UAM,
        name = "completed_kyc_verifications_requests")
public class CompletedKycRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @OneToOne(optional = false)
    private KYCVerificationRequest kycVerificationRequest;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @Index
    private boolean processedToBox;
}
