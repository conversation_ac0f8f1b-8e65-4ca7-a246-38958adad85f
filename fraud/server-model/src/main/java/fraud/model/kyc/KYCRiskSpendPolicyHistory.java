package fraud.model.kyc;

import java.math.BigDecimal;
import java.time.Instant;

import fraud.model.FraudBrand;
import jakarta.persistence.*;

import io.ebean.annotation.WhenCreated;
import lombok.Data;
import model.Schemas;

@Data
@Entity
@Table(name = "kyc_risk_spend_policy_history", schema = Schemas.FRAUD)
public class KYCRiskSpendPolicyHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    private FraudBrand fraudBrand;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private LimitType limitType;

    @Column(precision = 20, scale = 6)
    private BigDecimal oldValue;

    @Column(precision = 20, scale = 6, nullable = false)
    private BigDecimal newValue;

    @Column(length = 3, nullable = false)
    private String currency;

    @Column(nullable = false)
    private String updatedBy;

    @WhenCreated
    @Column(nullable = false)
    private Instant updatedAt;

    public enum LimitType {
        LOW,
        MID,
        HIGH
    }
}
