package fraud.model.kyc;

import java.math.BigDecimal;

import common.CoreConstraints;
import fraud.model.FraudBrand;
import io.ebean.annotation.Cache;
import io.ebean.annotation.Index;
import io.ebean.annotation.Platform;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "kyc_risk_spend_policy", schema = Schemas.FRAUD)
@Index(platforms = Platform.POSTGRES,
        unique = true,
        name = "ix_kyc_risk_spend_policy_currency",
        definition = "CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_currency " +
                "ON fraud.kyc_risk_spend_policy (currency) " +
                "WHERE fraud_brand_id is null")
@Index(platforms = Platform.POSTGRES,
        unique = true,
        name = "ix_kyc_risk_spend_policy_fraud_brand_id_currency",
        definition = "CREATE UNIQUE INDEX ix_kyc_risk_spend_policy_brand_id_currency " +
                "ON fraud.kyc_risk_spend_policy (fraud_brand_id, currency) " +
                "WHERE fraud_brand_id is not null")
@Getter
@Setter
@Cache(enableQueryCache = true)
public class KYCRiskSpendPolicy {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    @ManyToOne
    private FraudBrand fraudBrand;
    @Column(nullable = false, length = CoreConstraints.CURRENCY_CODE)
    private String currency;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE, nullable = false)
    private BigDecimal lowRiskSpendThreshold;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE, nullable = false)
    private BigDecimal midRiskSpendThreshold;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE, nullable = false)
    private BigDecimal highRiskSpendThreshold;

    @Version
    private int version;

    public KYCRiskSpendPolicy(String currency) {
        setCurrency(currency);
    }

    public KYCRiskSpendPolicy(FraudBrand brand, String currency) {
        setFraudBrand(brand);
        setCurrency(currency);
    }
}
