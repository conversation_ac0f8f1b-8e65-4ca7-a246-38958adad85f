package fraud.model.kyc;

import java.util.Date;

import fraud.model.FraudAccount;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;

import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "account_kyc_info", schema = Schemas.UAM)
@Getter
@Setter
public class AccountKycInfo {
    @Id
    private Long id;

    @OneToOne(optional = true)
    private FraudAccount account;

    @Column
    private int attempts;

    @Column
    private Date firstKYC;

    @Column
    private Date lastKYC;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public AccountKycInfo(long id) {
        setId(id);
    }

    public AccountKycInfo(FraudAccount account) {
        setId(account.getId());
        this.account = account;
        account.setKycInfo(this);
    }

    public void lastKyc(Date when) {
        if (getFirstKYC() == null) {
            setFirstKYC(when);
        }
        setLastKYC(when);
    }

    public void clearLastKYC() {
        setLastKYC(null);
    }

    public int increaseAttempts() {
        int inc = getAttempts() + 1;
        setAttempts(inc);

        return inc;
    }
}
