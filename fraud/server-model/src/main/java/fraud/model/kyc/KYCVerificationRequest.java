package fraud.model.kyc;

import java.time.LocalDate;
import java.util.Date;
import java.util.UUID;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.Reason;
import fraud.model.FraudAccount;
import fraud.model.IdTypeSpec;
import fraud.model.ValidityReasonSpec;
import lombok.Setter;
import model.HashingUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import com.turbospaces.api.CommonModelContraints;

import api.v1.KYCStatusSpec;
import api.v1.PlatformSpec;
import common.CoreConstraints;
import io.ebean.annotation.DbJsonB;
import io.ebean.annotation.Encrypted;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;
import lombok.Getter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(
        name = "kyc_verification_requests",
        schema = Schemas.UAM,
        uniqueConstraints = { //
                @UniqueConstraint(columnNames = { "provider", "code" }) //
        })
public class KYCVerificationRequest implements Comparable<KYCVerificationRequest> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false, unique = true)
    private UUID transactionId;

    @Column(nullable = false)
    private String code;

    @ManyToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private String provider;

    @Column
    private PhaseSpec phase;

    @Column(nullable = false)
    private KYCStatusSpec status;

    @Column
    @Index
    private String scanReference;

    @Column(length = CommonModelContraints.MAX)
    private String clientRedirectUrl;

    @DbJsonB
    private Object apiError;

    @Column(length = CoreConstraints.COUNTRY_CODE)
    private String idCountry;

    @Column
    private String idFirstName;

    @Column
    private String idLastName;

    @Column
    private LocalDate idBirthDate;

    @Encrypted(dbEncryption = false)
    @Column(name = "id_number_mask")
    private String idNumber;

    @Index
    @Column
    private String idNumberHash;

    @Column
    private IdTypeSpec idType;

    @Column
    private String idState;

    @Column
    private String similarity;

    @Column
    private boolean validity;

    @Column
    private ValidityReasonSpec validityReason;

    @Column(nullable = false)
    private KYCStatusSpec docStatus;

    @Column(length = CoreConstraints.COUNTRY_CODE)
    private String docCountry;

    @Column
    private String docState;

    @Column
    private String docCity;

    @Encrypted(dbEncryption = false)
    @Column(name = "doc_address_mask")
    private String docAddress;

    @Column
    private String docPostal;

    @Column
    private String docName;

    @Column
    private LocalDate docIssueDate;

    @Column
    private LocalDate expireAt;

    @Column(nullable = false)
    private PlatformSpec platform;

    @Column(length = CommonModelContraints.MAX)
    private String reason;

    @Column(length = CommonModelContraints.MAX)
    private String reasonDesc;

    @Column
    private Integer reasonCode;

    @Column(nullable = false)
    @Index()
    private LocalDate at;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public KYCVerificationRequest() {}
    public KYCVerificationRequest(FraudAccount account, LocalDate at, UUID transactionId, PlatformSpec platform) {
        this();
        setAccount(account);
        setAt(at);
        setTransactionId(transactionId);
        setPlatform(platform);
    }

    public void idNumber(String value) {
        setIdNumber(value);
        setIdNumberHash(HashingUtils.hash(value));
    }

    public void assertOwned(FraudAccount owner) throws ApplicationException {
        if (!getAccount().equals(owner)) {
            throw EnhancedApplicationException.of("Not owned by account", Code.ERR_DENIED, Reason.NOT_OWNED_BY_ACCOUNT);
        }
    }
    @Override
    public int compareTo(KYCVerificationRequest o) {
        return getCreatedAt().compareTo(o.getCreatedAt());
    }
    public boolean isApiError() {
        return getApiError() != null;
    }
    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getTransactionId()).toHashCode();
    }
    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        KYCVerificationRequest other = (KYCVerificationRequest) obj;
        return new EqualsBuilder().append(getTransactionId(), other.getTransactionId()).isEquals();
    }
}