package fraud.model.kyc;

import com.fasterxml.jackson.annotation.JsonCreator;

import io.ebean.annotation.DbEnumValue;

public enum SimilaritySpec {
    MATCH,
    NO_MATCH,
    NOT_POSSIBLE;

    private String code;

    SimilaritySpec() {
        this.code = name().toLowerCase().intern();
    }

    @DbEnumValue
    public String code() {
        return code;
    }

    @JsonCreator
    public static SimilaritySpec fromString(String name) {
        String n = name.toLowerCase();
        for (SimilaritySpec type : SimilaritySpec.values()) {
            if (type.code.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown similarity value: " + n);
    }

    public boolean isMatch() {
        return this == MATCH;
    }
    public boolean isNoMatch() {
        return this == NO_MATCH;
    }
    public boolean isNotPossible() {
        return this == NOT_POSSIBLE;
    }
}
