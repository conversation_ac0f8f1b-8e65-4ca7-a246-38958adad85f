package fraud.model.doc.upload;

import com.fasterxml.jackson.annotation.JsonValue;
import io.ebean.annotation.DbEnumValue;

import java.util.Arrays;

public enum DocUploadStatus {
    INITIATED, SESSION_EXPIRED, FAILED, PENDING_REVIEW, APPROVED, REJECTED, IN_REVIEW;

    private final String dbValue;

    DocUploadStatus() {
        this.dbValue = name().toLowerCase().intern();
    }

    @JsonValue
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

    public static DocUploadStatus fromString(String type) {
        if ("doc_in_review".equalsIgnoreCase(type)) {
            return IN_REVIEW;
        }
        return Arrays.asList(values()).stream()
                .filter(t -> t.name().equalsIgnoreCase(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown status: " + type));
    }
}
