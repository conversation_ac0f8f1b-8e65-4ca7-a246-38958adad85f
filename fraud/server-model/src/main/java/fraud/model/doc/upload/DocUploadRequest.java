package fraud.model.doc.upload;

import fraud.model.FraudAccount;
import io.ebean.annotation.History;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.util.Date;
import java.util.UUID;

@Getter
@Setter
@Entity
@History
@Table(
        name = "doc_upload_requests",
        schema = Schemas.FRAUD,
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"account_id", "code"})
        })
public class DocUploadRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false, unique = true)
    private UUID transactionId;

    @Column(nullable = false)
    private String code;

    @ManyToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private DocType docType;

    @Column(nullable = false)
    private DocUploadStatus status;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Column
    private Date uploadedAt;

    @Column
    private Date inReviewAt;

    @Column
    private Date finalActionedAt;

    @Column
    private String agentName;

    @Column
    private String comment;

    @Column
    private String customDocType;

    public DocUploadRequest(FraudAccount account, DocType docType) {
        this.account = account;
        this.docType = docType;
    }
}
