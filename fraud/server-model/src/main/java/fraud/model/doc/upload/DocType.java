package fraud.model.doc.upload;

import com.fasterxml.jackson.annotation.JsonValue;
import io.ebean.annotation.DbEnumValue;
import lombok.Getter;

import java.util.Arrays;

public enum DocType {
    BANK_STATEMENT("BS", "Bank Statement"),
    TAX_RETURN("TR", "Tax Return"),
    PURCHASE_AGREEMENT("PA", "Purchase Agreement"),
    SALE_OF_AN_ASSET("SA", "Sale of an Asset"),
    PAYSLIPS("PSL", "Payslips"),
    OTHERS("OTH", "Others");

    private final String dbValue;
    @Getter
    private final String apiValue;
    @Getter
    private final String title;

    DocType(String apiValue, String title) {
        this.apiValue = apiValue;
        this.title = title;
        dbValue = name().toLowerCase().intern();
    }

    @JsonValue
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

    public static DocType fromString(String type) {
        return Arrays.asList(values()).stream()
                .filter(t -> t.apiValue.equalsIgnoreCase(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unknown doc type: " + type));
    }
}
