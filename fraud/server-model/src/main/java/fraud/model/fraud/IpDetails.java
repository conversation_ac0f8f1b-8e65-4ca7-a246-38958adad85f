package fraud.model.fraud;

import java.util.Date;
import java.util.List;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "ip_details", schema = Schemas.FRAUD)
@Getter
@Setter
public class IpDetails {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    @Index(unique = true)
    @Column
    private String ip;
    @Column
    private String type;
    @OneToMany(mappedBy = "ipDetails", cascade = CascadeType.ALL)
    private List<FraudResponse> fraudResponses;
    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    public IpDetails(String ip) {
        this.ip = ip;
    }
}
