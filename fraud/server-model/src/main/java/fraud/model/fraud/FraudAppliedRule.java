package fraud.model.fraud;

import java.time.LocalDate;

import jakarta.persistence.Column;
import jakarta.persistence.ConstraintMode;
import jakarta.persistence.Entity;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "fraud_applied_rules", schema = Schemas.FRAUD)
@Getter
@Setter
public class FraudAppliedRule {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String ruleId;

    @Column(nullable = false)
    private int score;

    @Column
    private String operation;

    @ManyToOne(optional = false)
    private FraudResponse fraudResponse;

    @ManyToOne
    @JoinColumn(name = "rule_id", referencedColumnName = "id", insertable = false, updatable = false, foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private FraudRule fraudRule;

    @Column(nullable = false)
    private LocalDate at;

    @Version
    private int version;

    public FraudAppliedRule(String ruleId, Integer score, String operation, FraudResponse fraudResponse, LocalDate at) {
        setRuleId(ruleId);
        setScore(score);
        setOperation(operation);
        setFraudResponse(fraudResponse);
        setAt(at);
    }
}
