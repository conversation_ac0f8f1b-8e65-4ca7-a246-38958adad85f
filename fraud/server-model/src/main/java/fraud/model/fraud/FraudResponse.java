package fraud.model.fraud;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import fraud.model.ActionTypeSpec;
import fraud.model.FraudAccount;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "fraud_response", schema = Schemas.FRAUD)
@Getter
@Setter
@Index(columnNames = {"response_id"})
public class FraudResponse {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String responseId;

    @Column
    private String cookieHash;

    @Column(name = "action_type")
    @Enumerated(EnumType.STRING)
    private ActionTypeSpec actionType;

    @Column
    private Integer score;

    @ManyToOne(optional = false)
    private FraudAccount account;

    @OneToMany(cascade = CascadeType.ALL)
    private List<FraudAppliedRule> appliedRules = new ArrayList<>();

    @ManyToOne
    private IpDetails ipDetails;

    @OneToMany(cascade = CascadeType.ALL)
    private List<FraudRuleCategoryDetails> fraudRuleCategoryDetails = new ArrayList<>();

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    public FraudResponse(String responseId, String cookieHash) {
        setResponseId(responseId);
        setCookieHash(cookieHash);
    }
    public void addAppliedRule(FraudAppliedRule rule) {
        getAppliedRules().add(rule);
    }
}
