package fraud.model.fraud;

import java.util.Date;

import fraud.model.FraudAccount;
import io.ebean.annotation.History;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Data;
import model.Schemas;

@Data
@Entity
@History
@Table(name = "aml_check", schema = Schemas.FRAUD)
public class AmlCheck {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Index(unique = true)
    @OneToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private boolean crimeListMatch;

    @Column(nullable = false)
    private boolean pepMatch;

    @Column(nullable = false)
    private boolean watchlistMatch;

    @Column(nullable = false)
    private boolean sanctionMatch;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;
}
