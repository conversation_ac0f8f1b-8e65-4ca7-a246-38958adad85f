package fraud.model.fraud;

import java.util.Date;

import io.ebean.annotation.Cache;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import io.ebean.annotation.WhenCreated;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "fraud_rules", schema = Schemas.FRAUD)
@Getter
@Setter
@Cache
public class FraudRule {
    @Id
    private String id;

    @Column(nullable = false)
    private String name;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @Version
    private int version;

    public FraudRule(String ruleId) {
        this.id = ruleId;
    }
}
