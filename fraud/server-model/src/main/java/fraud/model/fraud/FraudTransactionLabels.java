package fraud.model.fraud;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(schema = Schemas.FRAUD, name = "fraud_transaction_labels")
@Getter
@Setter
public class FraudTransactionLabels {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    @Index
    @Column(nullable = false)
    private String fraudRequestId;
    @Column(nullable = false)
    private String label;
    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;
}
