package fraud.model.fraud;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import api.v1.CodeSpec;
import io.ebean.annotation.WhenCreated;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "fraud_decline_rules", schema = Schemas.FRAUD)
@Getter
@Setter
public class FraudDeclineRule {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private String ruleId;

    @Column(nullable = false)
    private String declineReason;

    @Column(nullable = false)
    private CodeSpec declineCode;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;
}
