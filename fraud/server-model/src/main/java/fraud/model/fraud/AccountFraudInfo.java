package fraud.model.fraud;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import api.v1.KYCStatusSpec;
import fraud.model.FraudAccount;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "account_fraud_info", schema = Schemas.FRAUD)
public class AccountFraudInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @OneToOne(optional = true)
    private FraudAccount account;

    @Column
    private Integer lastSignInFraudScore;

    @Column
    private String signUpDeviceSession;

    @Index
    @Column
    private Integer signUpFraudScore;

    @Column
    private Integer lastFraudScore;

    @Column
    private String signUpCookieHash;

    @Column
    private String lastSignInCookieHash;

    @Column
    private String lastWithdrawCookieHash;

    @Column
    private String seonSession;

    @Column
    private long offerPurchaseTotalCount;

    @Column
    private BigDecimal offerPurchaseTotalAmount;

    @Column
    private Date firstOtpShow, lastOtpShow;

    @Column(nullable = false)
    private KYCStatusSpec kyc;

    @Column(nullable = false)
    private boolean kycEmailSent;

    @Column(unique = true)
    private String jumioAccountReference;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public AccountFraudInfo(FraudAccount account) {
        this.account = account;
        this.kyc = KYCStatusSpec.INITIAL;
    }

    public Optional<Integer> signUpFraudScore() {
        return Optional.ofNullable(getSignUpFraudScore());
    }

    public Optional<Integer> lastFraudScore() {
        return Optional.ofNullable(getLastFraudScore());
    }

    public Optional<Integer> lastSignInFraudScore() {
        return Optional.ofNullable(getLastSignInFraudScore());
    }

    public Optional<String> jumioAccountReference() {
        return Optional.ofNullable(jumioAccountReference);
    }

    public void markLastScore(int score, boolean success) {
        if (success) {
            setLastFraudScore(score);
        } else {
            if (Objects.isNull(getLastFraudScore())) {
                setLastFraudScore(0);
            }
        }
    }

    public void markLastWithdrawCookieHash(String cookieHash, boolean success) {
        if (success) {
            setLastWithdrawCookieHash(cookieHash);
        }
    }
}
