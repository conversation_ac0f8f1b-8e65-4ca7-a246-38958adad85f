package fraud.model.fraud;

import io.ebean.annotation.Index;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "fraud_rule_category_details", schema = Schemas.FRAUD)
@Getter
@Setter
public class FraudRuleCategoryDetails {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    @Index
    private Long categoryId;

    @Column
    private String categoryName;

    @Column
    private Integer categoryScoreTestScore;

    @Column
    private String categoryScoreTestState;

    @ManyToOne(optional = false)
    private FraudResponse fraudResponse;

    public FraudRuleCategoryDetails(Long categoryId, FraudResponse fraudResponse) {
        setCategoryId(categoryId);
        setFraudResponse(fraudResponse);
    }
}
