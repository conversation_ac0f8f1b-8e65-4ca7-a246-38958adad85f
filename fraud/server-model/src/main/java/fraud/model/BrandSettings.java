package fraud.model;

import java.net.URL;

import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(
        name = "brand_settings",
        schema = Schemas.FRAUD
)
@Getter
@Setter
@EqualsAndHashCode(of = {"fraudBrand"})
public class BrandSettings {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer id;

    @Column(nullable = true)
    private Integer brandId;

    @OneToOne(optional = false)
    private FraudBrand fraudBrand;

    @Column
    private URL fraudWebhookUrl;

    @Column
    private String cardVerificationSuccessPage;

    @Column
    private String cardVerificationFailedPage;

    @Column
    private String kycSuccessPage;

    @Column
    private String kycErrorPage;

    @Embedded(prefix = "doc_upload_")
    private DocUploadSettings docUploadSettings;


    @Embedded(prefix = "ethoca_")
    private EthocaSettings ethocaSettings;
}
