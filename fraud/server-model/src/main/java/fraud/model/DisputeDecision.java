package fraud.model;

import common.CoreConstraints;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "dispute_decisions", schema = Schemas.FRAUD)
public class DisputeDecision {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(name = "decision_id")
    @Index(unique = true)
    private String decisionId;

    @Column(name = "arn", nullable = false)
    @Index(unique = true)
    private String arn;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "card_bin")
    private String cardBin;

    @Column(name = "card_last4")
    private String cardLast4;

    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE, nullable = false)
    private BigDecimal amount;

    @Column(name = "currency", nullable = false)
    private String currency;

    @Column(name = "case_date", nullable = false)
    private LocalDateTime caseDate;

    @Column(name = "reason_code", nullable = false)
    private String reasonCode;

    @Column(name = "outcome", nullable = false)
    private String outcome;

    @Column(name = "status_code", nullable = false)
    private Integer statusCode;

    @Column(name = "reason", nullable = false)
    private String reason;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public void setDecisionInfo(DecisionStatusCodeSpec decisionInfo) {
        setOutcome(decisionInfo.getStatus());
        setStatusCode(decisionInfo.getCode());
        setReason(decisionInfo.getDescription());
    }
}
