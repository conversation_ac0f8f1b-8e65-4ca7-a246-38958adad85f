package fraud.model;

import core.model.CoreBrand;
import io.ebean.annotation.Cache;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.Optional;

@Setter
@Getter
@Entity
@Table(name = "brands", schema = Schemas.FRAUD)
@Cache(naturalKey = { "name" }, enableQueryCache = true)
public class FraudBrand extends CoreBrand {
    @Embedded(prefix = "fraud_")
    private FraudPolicy fraudPolicy;

    @Embedded(prefix = "phone_")
    private PhonePolicy phonePolicy;

    @Version
    private int version;

    public FraudBrand(String name) {
        super(name);
    }

    public Optional<FraudPolicy> fraudPolicy() {
        return Optional.ofNullable(fraudPolicy);
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getName()).build();
    }
    @Override
    public boolean equals(Object obj) {
        FraudBrand other = (FraudBrand) obj;
        return new EqualsBuilder().append(getName(), other.getName()).isEquals();
    }
}
