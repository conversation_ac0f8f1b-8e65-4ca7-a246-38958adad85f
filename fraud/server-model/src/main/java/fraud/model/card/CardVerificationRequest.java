package fraud.model.card;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import com.turbospaces.api.CommonModelContraints;

import fraud.model.CardVerificationProviderStatusSpec;
import fraud.model.FraudAccount;
import io.ebean.annotation.DbArray;
import io.ebean.annotation.History;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(
        name = "card_verification_request",
        schema = Schemas.FRAUD,
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"provider", "scan_reference"})
        })
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(of = {"transactionId"})
@History
public class CardVerificationRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private UUID transactionId;

    @Column(nullable = false)
    private String provider;

    @Column
    private String scanReference;

    @ManyToOne(optional = false)
    private FraudAccount account;

    @ManyToOne(optional = false)
    @Column(nullable = false)
    private CardVerificationMetaInfo metaInfo;

    @Column(nullable = false)
    private CardVerificationProviderStatusSpec status;

    @Column(length = CommonModelContraints.MAX)
    private String redirectUrl;

    @Column
    private String clientIp;

    @Column
    private String timestamp;

    @Column
    private String docType;

    @Column
    private String country;

    @DbArray
    @Builder.Default
    private List<String> images = new ArrayList<>();

    @Column
    private String extractedName;

    @Column
    private String extractedPan;

    @Column
    private String extractedIssueDate;

    @Column
    private String extractedExpiryDate;

    @Column
    private String apiError;

    @Column
    private String failReason;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Version
    private int version;
}
