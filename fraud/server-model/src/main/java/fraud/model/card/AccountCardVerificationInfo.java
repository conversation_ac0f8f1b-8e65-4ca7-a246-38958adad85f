package fraud.model.card;

import java.time.LocalDate;
import java.util.Date;

import fraud.model.FraudAccount;
import io.ebean.annotation.WhenCreated;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "account_card_verification_info", schema = Schemas.FRAUD)
public class AccountCardVerificationInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @OneToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private LocalDate emailSentAt;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    public AccountCardVerificationInfo(FraudAccount account) {
        this.account = account;
    }
}
