package fraud.model.card;

import java.util.Date;

import fraud.model.CardVerificationStatusSpec;
import fraud.model.FraudAccount;
import org.apache.commons.lang3.BooleanUtils;

import io.ebean.annotation.History;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(
        name = "card_verification_meta_info",
        schema = Schemas.FRAUD,
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"account_id", "fingerprint"})
        })
@EqualsAndHashCode(of = {"account", "fingerprint"})
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@History
public class CardVerificationMetaInfo {
    public static final String SYSTEM_USER = "system";
    public static final String SYSTEM_AUTO_PMV = "system_auto_pmv";

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @ManyToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private String fingerprint;

    private String cardBin;

    private String lastFour;

    @Column(nullable = false)
    private CardVerificationStatusSpec status;

    @Column
    private Date statusUpdatedAt;

    @Column
    private Boolean manuallyUpdated;

    @Column
    private String agentName;

    @Column
    private Boolean manuallyVerified;

    @Column(nullable = false)
    private int verificationAttempts;

    @Column
    private Date requireVerificationAt;

    @Column(nullable = false)
    private boolean emailSent;

    @Column
    private Date automatedTriggerAt;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Version
    private int version;

    public boolean isFinalStatus() {
        return status.isFinal() || BooleanUtils.isTrue(manuallyVerified);
    }

    public void updateStatusBySystem(CardVerificationStatusSpec status, Date timestamp) {
        updateStatusBy(status, SYSTEM_USER, timestamp);
    }

    public void updateStatusBy(CardVerificationStatusSpec status, String agentName, Date timestamp) {
        setStatus(status);
        setAgentName(agentName);
        setStatusUpdatedAt(timestamp);
        setManuallyUpdated(BooleanUtils.negate(SYSTEM_USER.equals(agentName) || SYSTEM_AUTO_PMV.equals(agentName)));
        if (automatedTriggerAt == null && SYSTEM_AUTO_PMV.equals(agentName)) {
            setAutomatedTriggerAt(timestamp);
        }
        if (requireVerificationAt == null && status.equals(CardVerificationStatusSpec.REQUIRE_VERIFICATION)) {
            setRequireVerificationAt(timestamp);
        }
    }
}
