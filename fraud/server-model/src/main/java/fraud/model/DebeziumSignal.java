package fraud.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Table(name = "debezium_signal", schema = Schemas.FRAUD)
public class DebeziumSignal {
    @Id
    @Column(length = 42)
    // according to official doc
    public String id;

    @Column(length = 42, nullable = false)
    // according to official doc
    public String type;

    @Column(length = 2048)
    // according to official doc
    public String data;
}
