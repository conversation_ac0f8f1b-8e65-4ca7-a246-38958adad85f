package fraud.model;

import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.util.Date;

@Getter
@Setter
@Entity
@Index(columnNames = {"source", "identifier"}, unique = true)
@Table(name = "email_log", schema = Schemas.FRAUD)
public class EmailLog {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false)
    private FraudAccount fraudAccount;

    @Column(nullable = false)
    private String identifier;

    @Column(nullable = false)
    private EmailSourceSpec source;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;
}
