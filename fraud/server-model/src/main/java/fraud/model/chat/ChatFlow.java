package fraud.model.chat;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import io.ebean.annotation.DbEnumValue;

public enum ChatFlow {
    PURCHASE_FLOW, REDEEM_FLOW;

    private final String dbValue;

    ChatFlow() {
        this.dbValue = name().toLowerCase().intern();
    }

    @JsonValue
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

    @JsonCreator
    public static ChatFlow fromString(String name) {
        String n = name.toLowerCase();
        for (ChatFlow type : ChatFlow.values()) {
            if (type.dbValue.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown ChatFlow: " + n);
    }
}
