package fraud.model.chat;

import common.CoreConstraints;
import fraud.model.FraudAccount;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "zendesk_export_info", schema = Schemas.FRAUD)
@Getter
@Setter
public class ZendeskExportInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private long id;
    @OneToOne(optional = false)
    @Index(unique = true)
    private FraudAccount account;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal totalPurchase;
    @Column(precision = CoreConstraints.MONEY_PRECISION, scale = CoreConstraints.MONEY_SCALE)
    private BigDecimal totalWithdraw;
    @Column(nullable = false)
    private boolean isUpdated;
    @Column
    private String email;
    @Version
    private int version;
    @Column(nullable = false)
    private Date lastUpdatedAt;
    @WhenModified
    @Column
    private Date modifiedAt;
    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;
}
