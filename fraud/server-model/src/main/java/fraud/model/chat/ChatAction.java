package fraud.model.chat;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import io.ebean.annotation.DbEnumValue;

public enum ChatAction {
    CHAT_SHOWN, CHAT_OPENED;

    private final String dbValue;

    ChatAction() {
        this.dbValue = name().toLowerCase().intern();
    }

    @JsonValue
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

    @JsonCreator
    public static ChatAction fromString(String name) {
        String n = name.toLowerCase();
        for (ChatAction type : ChatAction.values()) {
            if (type.dbValue.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown ChatAction: " + n);
    }
}
