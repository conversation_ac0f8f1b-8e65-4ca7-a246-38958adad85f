package fraud.model.chat;

import java.util.Date;

import fraud.model.FraudAccount;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "chat_events", schema = Schemas.FRAUD)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatEvents {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @ManyToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private ChatAction action;

    @Column(nullable = false)
    private ChatFlow flow;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;
}
