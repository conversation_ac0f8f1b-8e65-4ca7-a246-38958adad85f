package fraud.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import io.ebean.annotation.DbEnumValue;

public enum RegistrationOperationModeSpec {
    PROHIBITED,
    FULL;

    private final String code;

    RegistrationOperationModeSpec() {
        this.code = name().toLowerCase().intern();
    }
    @JsonValue
    @DbEnumValue
    public String code() {
        return code;
    }
    public boolean isProhibited() {
        return this == PROHIBITED;
    }
    public boolean isFull() {
        return this == FULL;
    }
    @JsonCreator
    public static RegistrationOperationModeSpec fromString(String name) {
        String opname = name.toLowerCase();
        for (RegistrationOperationModeSpec type : RegistrationOperationModeSpec.values()) {
            if (type.code.equalsIgnoreCase(opname)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown operational mode");
    }
}
