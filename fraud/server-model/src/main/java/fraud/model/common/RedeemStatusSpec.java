package fraud.model.common;

import java.util.Optional;

import com.fasterxml.jackson.annotation.JsonCreator;

import io.ebean.annotation.DbEnumValue;

public enum RedeemStatusSpec {
    FAILED(RedeemUserStatusSpec.FAILED, false, false),
    NEW(RedeemUserStatusSpec.PENDING, false, false),
    PRE_AUTHORIZED(RedeemUserStatusSpec.PENDING, true, true),
    CONFIRMED(RedeemUserStatusSpec.PROCESSED, false, false),
    DECLINED(RedeemUserStatusSpec.FAILED, false, false),
    CANCELLED(RedeemUserStatusSpec.CANCELED, false, false),
    LOCKED(RedeemUserStatusSpec.PENDING, false, false);

    private final String code;
    private final RedeemUserStatusSpec userStatus;
    private final boolean isCancellable;
    private final boolean isLockable;

    RedeemStatusSpec(RedeemUserStatusSpec userStatus, boolean isCancellable, boolean isLockable) {
        this.userStatus = userStatus;
        this.code = name().toLowerCase().intern();
        this.isCancellable = isCancellable;
        this.isLockable = isLockable;
    }

    public Optional<RedeemUserStatusSpec> getUserStatus() {
        return Optional.ofNullable(userStatus);
    }

    public boolean isFailed() {
        return userStatus == RedeemUserStatusSpec.FAILED;
    }

    @DbEnumValue
    public String code() {
        return code;
    }
    @JsonCreator
    public static RedeemStatusSpec fromString(String name) {
        String n = name.toLowerCase();
        for (RedeemStatusSpec type : RedeemStatusSpec.values()) {
            if (type.code.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown redeem status: " + n);
    }
}