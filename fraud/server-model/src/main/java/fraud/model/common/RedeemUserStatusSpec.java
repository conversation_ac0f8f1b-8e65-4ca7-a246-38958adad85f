package fraud.model.common;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonCreator;

import io.ebean.annotation.DbEnumValue;

public enum RedeemUserStatusSpec {
    PENDING,
    PROCESSED,
    CANCELED,
    FAILED;

    private String code;

    RedeemUserStatusSpec() {
        this.code = name().toLowerCase().intern();
    }

    @DbEnumValue
    public String code() {
        return code;
    }

    public List<RedeemStatusSpec> toRedeemSystemStatuses() {
        return Arrays.stream(RedeemStatusSpec.values())
                .filter(s -> s.getUserStatus().map(us -> us.equals(this)).orElse(false))
                .collect(Collectors.toList());
    }
    @JsonCreator
    public static RedeemUserStatusSpec fromString(String name) {
        String n = name.toLowerCase();
        for (RedeemUserStatusSpec type : RedeemUserStatusSpec.values()) {
            if (type.code.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown redeem status: " + n);
    }
}
