package fraud.model.otp;

import fraud.model.FraudAccount;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import model.Schemas;

import java.time.LocalDate;
import java.util.Date;

@Builder
@Entity
@Table(name = "otp_trigger_events", schema = Schemas.FRAUD)
@EqualsAndHashCode(of = {"trigger", "source"})
@Getter
public class OtpTriggerEvent {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @ManyToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private OtpTriggerRule trigger;

    @Column
    private OtpSource source;

    @Index
    @Column(nullable = false)
    private LocalDate at;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @Version
    private int version;
}
