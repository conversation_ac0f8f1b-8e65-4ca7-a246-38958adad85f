package fraud.model.otp;

import java.util.ArrayList;
import java.util.List;

import fraud.model.FraudBrand;
import io.ebean.annotation.Cache;
import io.ebean.annotation.DbArray;
import io.ebean.annotation.Index;
import io.ebean.annotation.Platform;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "otp_trigger_rules", schema = Schemas.FRAUD)
@Index(platforms = Platform.POSTGRES,
        unique = true,
        name = "ix_otp_trigger_rules_fraud_brand_id",
        definition = """
            CREATE UNIQUE INDEX ix_otp_trigger_rules_fraud_brand_id \
            ON fraud.otp_trigger_rules (fraud_brand_id) \
            WHERE country is null""")
@Index(platforms = Platform.POSTGRES,
        unique = true,
        name = "ix_otp_trigger_rules_fraud_brand_id_country",
        definition = """
            CREATE UNIQUE INDEX ix_otp_trigger_rules_fraud_brand_id_country \
            ON fraud.otp_trigger_rules (fraud_brand_id, country) \
            WHERE country is not null""")
@Getter
@Setter
@Builder
@AllArgsConstructor
@Cache(enableQueryCache = true)
public class OtpTriggerRules {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = true)
    private Integer brandId;

    @ManyToOne(optional = false)
    private FraudBrand fraudBrand;

    @Column(length = 2)
    private String country;

    @Column(nullable = false)
    private boolean bonusTriggerEnabled;

    @Column(nullable = false)
    private boolean signUpFraudScoreTriggerEnabled;

    @Column(nullable = false)
    private boolean utmSourceTriggerEnabled;

    @Column(nullable = false)
    private int dailyBonusAcceptedThreshold;

    @Column(nullable = false)
    private int signUpFraudScoreThreshold;

    @DbArray(nullable = false)
    @Builder.Default
    private List<String> utmSources = new ArrayList<>();

    @Column(nullable = false)
    private int bonusAcceptanceTriggerThreshold;

    @Column(nullable = false)
    private boolean bonusAcceptanceTriggerEnabled;
    
    @Version
    private int version;

    public OtpTriggerRules(FraudBrand brand) {
        setFraudBrand(brand);
    }

    public OtpTriggerRules(FraudBrand brand, String country) {
        setFraudBrand(brand);
        setCountry(country);
    }
}
