package fraud.model.otp;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.ebean.annotation.DbEnumValue;

public enum OtpSource {
    MY_PROFILE,
    INBOX_NOTIFICATION;
    private final String dbValue;

    OtpSource() {
        this.dbValue = name().toLowerCase().intern();
    }

    @JsonValue
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

    @JsonCreator
    public static OtpSource fromString(String name) {
        String n = name.toLowerCase();
        for (OtpSource type : OtpSource.values()) {
            if (type.dbValue.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown OtpSource status: " + n);
    }
}
