package fraud.model.otp;

import java.util.Date;

import fraud.model.FraudAccount;
import io.ebean.annotation.History;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "otp_trigger_audit", schema = Schemas.FRAUD)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@History
public class OtpTriggerAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @OneToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    private Date triggeredAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @Column(name = "otp_trigger_rule")
    private OtpTriggerRule otpTriggerRule;

    @Version
    private int version;

}
