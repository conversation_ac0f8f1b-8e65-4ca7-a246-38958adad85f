package fraud.model.otp;

import java.util.Date;

import fraud.model.FraudAccount;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(name = "account_otp_limit", schema = Schemas.FRAUD)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountOtpLimit {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @OneToOne(optional = false)
    private FraudAccount account;

    @Column(nullable = false)
    @Index(unique = false)
    private boolean limitExceeded;

    @Column(nullable = false)
    private int attempts;

    @Column
    private Date lastAttemptAt;

    @Column
    private Date limitExceededAt;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;
}
