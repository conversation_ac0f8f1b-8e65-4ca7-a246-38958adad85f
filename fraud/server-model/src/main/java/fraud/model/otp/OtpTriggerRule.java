package fraud.model.otp;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.ebean.annotation.DbEnumValue;

public enum OtpTriggerRule {
    SIGNED_UP_TRIGGER,
    BONUS_TRIGGER,
    UTM_SOURCE_TRIGGER,
    SET_UP_NOTIFICATIONS_TRIGGER,
    BONUS_ACCEPTANCE_TRIGGER,
    ACCOUNT_CLOSURE_TRIGGER;
    private final String dbValue;

    OtpTriggerRule() {
        this.dbValue = name().toLowerCase().intern();
    }

    @JsonValue
    @DbEnumValue
    public String getValue() {
        return dbValue;
    }

    @JsonCreator
    public static OtpTriggerRule fromString(String name) {
        String n = name.toLowerCase();
        for (OtpTriggerRule type : OtpTriggerRule.values()) {
            if (type.dbValue.equalsIgnoreCase(n)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown OtpTriggerRule status: " + n);
    }
}
