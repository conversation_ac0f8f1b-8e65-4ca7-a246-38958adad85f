package fraud.model.otp;

import java.util.Date;

import fraud.model.FraudAccount;
import fraud.model.PhoneNumberTypeSpec;
import fraud.model.PhoneVerificationErrorReasonSpec;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import com.turbospaces.api.CommonModelContraints;

import io.ebean.annotation.History;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(
        name = "phone_number_request",
        schema = Schemas.FRAUD,
        uniqueConstraints = {
                @UniqueConstraint(columnNames = {"account_id", "sid", "phone_number"})
        })
@Getter
@Setter
@NoArgsConstructor
@History
public class PhoneNumberRequest implements Comparable<PhoneNumberRequest> {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Index()
    @Column(nullable = false, length = CommonModelContraints.PHONE_NUMBER)
    private String phoneNumber;

    @Column(nullable = false)
    private String sid;

    @ManyToOne
    private FraudAccount account;

    @Column(nullable = false)
    private boolean verified;

    @Column(nullable = false)
    private Date lastAttempt;

    @Column
    private String carrierName;

    @Column
    private String countryCode;

    @Column
    private String networkCode;

    @Column
    private PhoneNumberTypeSpec type;

    @Column
    private String errorCode;

    @Column
    private PhoneVerificationErrorReasonSpec errorReason;

    @Column
    private OtpSource source;

    @Version
    private int version;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public PhoneNumberRequest(FraudAccount account, String phoneNumber, String sid, Date at) {
        this();
        setAccount(account);
        setPhoneNumber(phoneNumber);
        setSid(sid);
        setLastAttempt(at);
    }

    public boolean isActive() {
        return !isVerified();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder().append(getPhoneNumber()).append(getAccount()).build();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }

        PhoneNumberRequest other = (PhoneNumberRequest) obj;
        return new EqualsBuilder().append(getPhoneNumber(), other.getPhoneNumber()).append(getAccount(), other.getAccount()).isEquals();
    }

    @Override
    public int compareTo(PhoneNumberRequest o) {
        return getCreatedAt().compareTo(o.getCreatedAt());
    }
}
