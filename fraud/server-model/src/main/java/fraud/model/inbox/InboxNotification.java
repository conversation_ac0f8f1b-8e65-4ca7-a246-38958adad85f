package fraud.model.inbox;

import java.util.Date;
import java.util.UUID;

import com.turbospaces.common.PlatformUtil;
import fraud.model.FraudAccount;
import io.ebean.annotation.DbForeignKey;
import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import io.ebean.annotation.History;
import io.ebean.annotation.NotNull;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Entity
@Table(
    name = "inbox_notifications",
    schema = Schemas.FRAUD)
@Getter
@Setter
@Index(columnNames = {"account_id", "status", "type"})
@Index(columnNames = {"token"})
@History
public class InboxNotification {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;

    @Column(nullable = false, unique = true)
    private UUID token = PlatformUtil.randomUUID();

    @Index(unique = true)
    @ManyToOne(optional = false)
    @DbForeignKey(noIndex=true)
    private FraudAccount account;

    @Column(nullable = false)
    private InboxNotificationTypeSpec type;

    @Column(nullable = false)
    private InboxNotificationStatusSpec status;

    @Column
    @NotNull
    private Date activeFrom;

    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;

    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    @Version
    private int version;

}
