package fraud.model;

import java.util.Date;

import io.ebean.annotation.Index;
import io.ebean.annotation.WhenCreated;
import io.ebean.annotation.WhenModified;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import model.Schemas;

@Getter
@Setter
@Entity
@Index(columnNames = {"report_type", "report_identifier"}, unique = true)
@Index(columnNames = {"report_type", "report_name"}, unique = true)
@Table(name = "report_log", schema = Schemas.FRAUD)
public class ReportLog {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Long id;
    @Column(nullable = false)
    private ReportType reportType;
    @Column(nullable = false)
    private String reportName;
    @Column(nullable = false)
    private String reportIdentifier;
    @Column
    private boolean processed;
    @WhenCreated
    @Column(nullable = false)
    private Date createdAt;
    @WhenModified
    @Column(nullable = false)
    private Date modifiedAt;

    public ReportLog(ReportType reportType, String reportName) {
        this.reportType = reportType;
        this.reportIdentifier = reportName;
    }
}
