package fraud;

import com.turbospaces.ebean.JpaManager;

import fraud.repo.AccountRepo;
import fraud.repo.BrandRepo;
import fraud.repo.CardVerificationRepo;
import fraud.repo.DocUploadRepo;
import fraud.repo.FraudAccountRepo;
import fraud.repo.FraudBrandRepo;
import fraud.repo.FraudRepo;
import fraud.repo.InboxNotificationRepo;
import fraud.repo.KYCRepo;

public interface FraudJpaManager extends JpaManager {
    BrandRepo brandRepo();
    AccountRepo accountRepo();
    FraudRepo fraudRepo();
    CardVerificationRepo cardVerificationRepo();
    InboxNotificationRepo inboxNotificationsRepo();
    KYCRepo kycRepo();
    DocUploadRepo docUploadRepo();
    FraudBrandRepo fraudBrandRepo();
    FraudAccountRepo fraudAccountRepo();
}
