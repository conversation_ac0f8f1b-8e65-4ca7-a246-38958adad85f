package fraud;

import java.util.concurrent.ScheduledExecutorService;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanJpaManager;

import fraud.repo.DefaultDocUploadRepo;
import fraud.repo.DefaultFraudAccountRepo;
import fraud.repo.DefaultFraudBrandRepo;
import fraud.repo.DefaultInboxNotificationRepo;
import fraud.repo.AccountRepo;
import fraud.repo.BrandRepo;
import fraud.repo.CardVerificationRepo;
import fraud.repo.DefaultAccountRepo;
import fraud.repo.DefaultBrandRepo;
import fraud.repo.DefaultCardVerificationRepo;
import fraud.repo.DefaultFraudRepo;
import fraud.repo.DefaultKYCRepo;
import fraud.repo.DocUploadRepo;
import fraud.repo.FraudAccountRepo;
import fraud.repo.FraudBrandRepo;
import fraud.repo.FraudRepo;
import fraud.repo.InboxNotificationRepo;
import fraud.repo.KYCRepo;
import io.ebeaninternal.api.SpiEbeanServer;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

public class FraudEbeanJpaManager extends EbeanJpaManager implements FraudJpaManager {
    private final BrandRepo brandRepo;
    private final AccountRepo accountRepo;
    private final FraudRepo fraudRepo;
    private final CardVerificationRepo cardVerificationRepo;
    private final InboxNotificationRepo inboxNotificationRepo;
    private final KYCRepo kycRepo;
    private final DocUploadRepo docUploadRepo;
    private final FraudBrandRepo fraudBrandRepo;
    private final FraudAccountRepo fraudAccountRepo;

    public FraudEbeanJpaManager(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            Tracer tracer,
            SpiEbeanServer ebean,
            ScheduledExecutorService timer) {
        super(props, meterRegistry, tracer, ebean, timer);
        this.brandRepo = new DefaultBrandRepo(ebean);
        this.accountRepo = new DefaultAccountRepo(ebean);
        this.fraudRepo = new DefaultFraudRepo(ebean);
        this.cardVerificationRepo = new DefaultCardVerificationRepo(ebean);
        this.inboxNotificationRepo = new DefaultInboxNotificationRepo(ebean);
        this.kycRepo = new DefaultKYCRepo(ebean);
        this.docUploadRepo = new DefaultDocUploadRepo(ebean);
        this.fraudBrandRepo = new DefaultFraudBrandRepo(ebean);
        this.fraudAccountRepo = new DefaultFraudAccountRepo(ebean);
    }
    @Override
    public BrandRepo brandRepo() {
        return brandRepo;
    }
    @Override
    public AccountRepo accountRepo() {
        return accountRepo;
    }
    @Override
    public FraudRepo fraudRepo() {
        return fraudRepo;
    }
    @Override
    public CardVerificationRepo cardVerificationRepo() {
        return cardVerificationRepo;
    }
    @Override
    public InboxNotificationRepo inboxNotificationsRepo() {
        return inboxNotificationRepo;
    }
    @Override
    public KYCRepo kycRepo() {
        return kycRepo;
    }
    @Override
    public DocUploadRepo docUploadRepo() {
        return docUploadRepo;
    }
    @Override
    public FraudBrandRepo fraudBrandRepo() {
        return fraudBrandRepo;
    }
    @Override
    public FraudAccountRepo fraudAccountRepo() {
        return fraudAccountRepo;
    }
}
