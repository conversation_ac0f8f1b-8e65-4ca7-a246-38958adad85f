package fraud;

import ebean.OpenPGPEncryptor;
import javax.sql.DataSource;

import fraud.model.FraudBrand;
import fraud.model.fraud.FraudRule;
import org.springframework.beans.factory.InitializingBean;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.EbeanDatabaseConfig;

import fraud.model.kyc.KYCRiskSpendPolicy;
import fraud.model.otp.OtpTriggerRules;
import io.ebean.cache.ServerCachePlugin;
import io.ebean.config.EncryptKeyManager;
import io.ebean.config.dbplatform.DatabasePlatform;
import io.ebean.platform.postgres.Postgres9Platform;

public class FraudEbeanConfiguration extends EbeanDatabaseConfig implements InitializingBean {
    public FraudEbeanConfiguration(DataSource ds, ApplicationProperties props, ServerCachePlugin cacheManager, EncryptKeyManager encryptKeyManager) {
        this(ds, props, cacheManager, encryptKeyManager, new Postgres9Platform());
    }

    public FraudEbeanConfiguration(
            DataSource ds,
            ApplicationProperties props,
            ServerCachePlugin cacheManager,
            EncryptKeyManager encryptKeyManager,
            DatabasePlatform platform) {
        super(ds, props);
        setServerCachePlugin(cacheManager);
        setEncryptKeyManager(encryptKeyManager);
        setEncryptor(new OpenPGPEncryptor());
        setDatabasePlatform(platform);
        addAll(new FraudEntities());
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        setReplicated(OtpTriggerRules.class);
        setReplicated(KYCRiskSpendPolicy.class);
        setReplicated(FraudRule.class);
        setReplicated(FraudBrand.class);
    }
}
