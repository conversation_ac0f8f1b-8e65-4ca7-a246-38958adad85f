package fraud.repo;

import java.util.Objects;
import java.util.Optional;

import fraud.model.fraud.AmlCheck;
import fraud.model.fraud.query.QAmlCheck;
import io.ebean.Database;
import io.ebean.Transaction;

public class DefaultAmlCheckRepo implements AmlCheckRepo {
    private final Database ebean;

    public DefaultAmlCheckRepo(Database ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }
    @Override
    public Optional<AmlCheck> find(long accountId, Transaction tx) {
        QAmlCheck q = new QAmlCheck(ebean).usingTransaction(tx);
        q.account.id.eq(accountId);
        return q.findOneOrEmpty();
    }
}
