package fraud.repo;

import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.FraudBrand;
import fraud.model.query.QFraudBrand;
import io.ebean.Database;
import io.ebean.Transaction;

import java.util.List;
import java.util.Optional;

public class DefaultFraudBrandRepo implements FraudBrandRepo {
    private final Database ebean;

    public DefaultFraudBrandRepo(Database ebean) {
        this.ebean = ebean;
    }

    @Override
    public FraudBrand requiredBrandByName(String name, Transaction tx) throws ApplicationException {
        Optional<FraudBrand> opt = brandByName(name, tx);
        return opt.orElseThrow(ApplicationException.orElseThrow("unable to find brand by name: " + name, Code.ERR_NOT_FOUND));
    }

    @Override
    public Optional<FraudBrand> brandByName(String name, Transaction tx) {
        QFraudBrand q = new QFraudBrand(ebean).usingTransaction(tx);
        q.name.eq(name);
        return q.findOneOrEmpty();
    }

    @Override
    public List<FraudBrand> brands(Transaction tx) {
        QFraudBrand q = new QFraudBrand(ebean).usingTransaction(tx);
        q.orderBy().name.asc();
        return q.findList();
    }

    @Override
    public FraudBrand getOrCreate(String name, Transaction tx) {
        return brandByName(name, tx).orElseGet(() -> createBrand(name, tx));
    }

    FraudBrand createBrand(String name, Transaction tx) {
        FraudBrand brand = new FraudBrand(name);
        ebean.save(brand, tx);
        return brand;
    }
}
