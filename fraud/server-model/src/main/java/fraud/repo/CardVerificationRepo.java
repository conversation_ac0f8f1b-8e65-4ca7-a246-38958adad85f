package fraud.repo;

import java.util.List;
import java.util.Optional;

import api.v1.ApplicationException;
import fraud.model.FraudAccount;
import fraud.model.card.AccountCardVerificationInfo;
import fraud.model.card.CardVerificationMetaInfo;
import fraud.model.card.CardVerificationRequest;
import fraud.model.CardVerificationStatusSpec;
import io.ebean.Transaction;

public interface CardVerificationRepo {
    Optional<CardVerificationMetaInfo> findMetaInfoBy(FraudAccount account, String fingerprint, Transaction tx);
    Optional<CardVerificationRequest> findVerificationBy(String scanReference, String provider, Transaction tx);
    CardVerificationRequest requiredVerificationBy(String scanReference, String provider, Transaction tx) throws ApplicationException;
    List<CardVerificationMetaInfo> findAllMetaInfoByAccount(FraudAccount account, Transaction tx);
    List<CardVerificationMetaInfo> findAllMetaInfoByAccount(Long accountId, Transaction tx);
    Optional<AccountCardVerificationInfo> accountCardVerificationInfo(FraudAccount account, Transaction tx);
    List<CardVerificationMetaInfo> findByStatus(FraudAccount account, List<CardVerificationStatusSpec> statusSpecs, Transaction tx);
}
