package fraud.repo;

import java.util.List;
import java.util.Optional;

import fraud.model.ActionTypeSpec;
import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.EmailOtpRequest;
import fraud.model.FraudAccount;
import fraud.model.FraudBrand;
import fraud.model.fraud.AccountFraudInfo;
import fraud.model.otp.AccountOtpLimit;
import fraud.model.fraud.FraudDeclineRule;
import fraud.model.fraud.FraudResponse;
import fraud.model.fraud.FraudRule;
import fraud.model.fraud.FraudRuleCategoryDetails;
import fraud.model.fraud.FraudTransactionLabels;
import fraud.model.fraud.IpDetails;
import fraud.model.kyc.KYCRiskSpendPolicy;
import fraud.model.otp.OtpTriggerAudit;
import fraud.model.otp.OtpTriggerEvent;
import fraud.model.otp.OtpTriggerRules;
import fraud.model.otp.PhoneNumberRequest;
import fraud.model.chat.ZendeskExportInfo;
import fraud.model.fraud.query.QAccountFraudInfo;
import fraud.model.otp.query.QAccountOtpLimit;
import fraud.model.fraud.query.QFraudDeclineRule;
import fraud.model.fraud.query.QFraudResponse;
import fraud.model.fraud.query.QFraudRule;
import fraud.model.fraud.query.QFraudRuleCategoryDetails;
import fraud.model.fraud.query.QFraudTransactionLabels;
import fraud.model.fraud.query.QIpDetails;
import fraud.model.kyc.query.QKYCRiskSpendPolicy;
import fraud.model.otp.query.QOtpTriggerAudit;
import fraud.model.otp.query.QOtpTriggerEvent;
import fraud.model.otp.query.QOtpTriggerRules;
import fraud.model.otp.query.QPhoneNumberRequest;
import fraud.model.chat.query.QZendeskExportInfo;
import fraud.model.query.QEmailOtpRequest;
import io.ebean.Database;
import io.ebean.Transaction;

public class DefaultFraudRepo implements FraudRepo {
    private final Database ebean;

    public DefaultFraudRepo(Database ebean) {
        this.ebean = ebean;
    }

    @Override
    public Optional<AccountFraudInfo> accountFraudInfo(Transaction tx, long id) {
        QAccountFraudInfo q = new QAccountFraudInfo(ebean).usingTransaction(tx);
        q.account.id.eq(id);
        return q.findOneOrEmpty();
    }

    @Override
    public List<FraudDeclineRule> declineFraudRule(Transaction tx) {
        QFraudDeclineRule q = new QFraudDeclineRule(ebean).usingTransaction(tx);
        return q.findList();
    }

    @Override
    public List<FraudTransactionLabels> transactionLabels(String fraudRequestId, Transaction tx) {
        QFraudTransactionLabels q = new QFraudTransactionLabels(ebean).usingTransaction(tx);
        q.fraudRequestId.eq(fraudRequestId);
        return q.findList();
    }

    @Override
    public Optional<IpDetails> ipDetails(String ip, Transaction tx) {
        QIpDetails query = new QIpDetails(ebean).usingTransaction(tx);
        query.ip.eq(ip);
        return query.findOneOrEmpty();
    }

    @Override
    public Optional<FraudRuleCategoryDetails> fraudRuleCategoryDetails(Long categoryId, Transaction tx) {
        QFraudRuleCategoryDetails query = new QFraudRuleCategoryDetails(ebean).usingTransaction(tx);
        query.categoryId.eq(categoryId);
        return query.findOneOrEmpty();
    }

    @Override
    public Optional<FraudRule> fraudRule(String ruleId, Transaction tx) {
        QFraudRule query = new QFraudRule(ebean).usingTransaction(tx);
        query.id.eq(ruleId);
        return query.findOneOrEmpty();
    }

    @Override
    public List<FraudResponse> fraudResponses(Long accountId, ActionTypeSpec type, Transaction tx) {
        QFraudResponse q = new QFraudResponse(ebean).usingTransaction(tx);
        q.account.id.eq(accountId);
        q.actionType.eq(type);
        return q.findList();
    }

    @Override
    public FraudResponse fraudResponse(Transaction tx, Long id) {
        QFraudResponse q = new QFraudResponse(ebean).usingTransaction(tx);
        q.id.eq(id);
        return q.findOne();
    }

    @Override
    public KYCRiskSpendPolicy requiredKYCRiskSpendPolicy(FraudBrand brand, String currency, Transaction tx) throws ApplicationException {
        QKYCRiskSpendPolicy q = new QKYCRiskSpendPolicy(ebean).usingTransaction(tx);
        q.fraudBrand.eq(brand);
        q.currency.eq(currency);
        return q.findOneOrEmpty()
                .orElseThrow(ApplicationException.orElseThrow("Unable to find KYCRiskSpendPolicy for currency "
                        + currency + " on brand " + brand.getName(), Code.ERR_NOT_FOUND));
    }

    @Override
    public Optional<KYCRiskSpendPolicy> kycRiskSpendPolicy(FraudBrand brand, String currency, Transaction tx) {
        QKYCRiskSpendPolicy q = new QKYCRiskSpendPolicy(ebean).usingTransaction(tx).setUseCache(true).setUseQueryCache(true);
        q.fraudBrand.eq(brand);
        q.currency.eq(currency);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<KYCRiskSpendPolicy> kycRiskSpendPolicy(String currency, Transaction tx) {
        QKYCRiskSpendPolicy q = new QKYCRiskSpendPolicy(ebean).usingTransaction(tx).setUseCache(true).setUseQueryCache(true);
        q.fraudBrand.isNull();
        q.currency.eq(currency);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<PhoneNumberRequest> phoneNumberRequestBySidAndNumber(FraudAccount account, String sid, String number, Transaction tx) {
        QPhoneNumberRequest q = new QPhoneNumberRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.sid.eq(sid);
        q.phoneNumber.eq(number);
        q.verified.eq(false);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<PhoneNumberRequest> lastPhoneNumberRequest(FraudAccount account, Transaction tx) {
        QPhoneNumberRequest q = new QPhoneNumberRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.orderBy().lastAttempt.desc();
        q.setMaxRows(1);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<PhoneNumberRequest> lastPhoneNumberRequest(FraudAccount account, String phoneNumber, Transaction tx) {
        QPhoneNumberRequest q = new QPhoneNumberRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.phoneNumber.eq(phoneNumber);
        q.orderBy().lastAttempt.desc();
        q.setMaxRows(1);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<AccountOtpLimit> accountOtpLimit(FraudAccount account, Transaction tx) {
        QAccountOtpLimit q = new QAccountOtpLimit(ebean).usingTransaction(tx);
        q.account.eq(account);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<OtpTriggerRules> otpTriggerRules(FraudBrand brand, String country, Transaction tx) {
        QOtpTriggerRules q = new QOtpTriggerRules(ebean).usingTransaction(tx).setUseCache(true).setUseQueryCache(true);
        q.fraudBrand.eq(brand);
        q.country.eq(country);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<OtpTriggerRules> otpTriggerRules(FraudBrand brand, Transaction tx) {
        QOtpTriggerRules q = new QOtpTriggerRules(ebean).usingTransaction(tx).setUseCache(true).setUseQueryCache(true);
        q.fraudBrand.eq(brand);
        q.country.isNull();
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<OtpTriggerAudit> otpTriggerAudit(FraudAccount account, Transaction tx) {
        QOtpTriggerAudit q = new QOtpTriggerAudit(ebean).usingTransaction(tx);
        q.account.eq(account);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<OtpTriggerEvent> lastOtpTriggerEvent(FraudAccount account, Transaction tx) {
        QOtpTriggerEvent q = new QOtpTriggerEvent(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.orderBy().id.desc();
        q.setMaxRows(1);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<ZendeskExportInfo> zendeskExportInfo(FraudAccount account, Transaction tx) {
        QZendeskExportInfo q = new QZendeskExportInfo(ebean).usingTransaction(tx);
        q.account.eq(account);
        return q.findOneOrEmpty();
    }

    @Override
    public void deletePhoneNumberVerificationRequests(Long accountId, Transaction tx) {
        QPhoneNumberRequest query = new QPhoneNumberRequest(ebean).usingTransaction(tx);
        query.account.id.eq(accountId);
        query.delete();
    }

    @Override
    public void deletePhoneNumberVerificationRequests(FraudAccount account, Transaction tx) {
        QPhoneNumberRequest query = new QPhoneNumberRequest(ebean).usingTransaction(tx);
        query.account.eq(account);
        query.delete();
    }

    @Override
    public Optional<EmailOtpRequest> findLatestEmailOtpRequest(FraudAccount account, Transaction tx) {
        QEmailOtpRequest q = new QEmailOtpRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.orderBy().createdAt.desc();
        q.setMaxRows(1);
        return q.findOneOrEmpty();
    }

}
