package fraud.repo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import fraud.model.FraudAccount;
import io.ebean.Transaction;

public interface AccountRepo {
    Optional<FraudAccount> account(Long id, Transaction tx);
    FraudAccount requiredAccount(Long id, Transaction tx);
    List<FraudAccount> accounts(List<Long> accountIds, Transaction tx);
    List<FraudAccount> accountsById(Collection<Long> ids, Transaction tx);
}
