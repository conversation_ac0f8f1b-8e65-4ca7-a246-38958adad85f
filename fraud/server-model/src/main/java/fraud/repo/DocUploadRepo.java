package fraud.repo;

import api.v1.ApplicationException;
import fraud.model.FraudAccount;
import fraud.model.doc.upload.DocUploadRequest;
import fraud.model.doc.upload.DocUploadStatus;
import io.ebean.Transaction;

import java.util.List;

public interface DocUploadRepo {
    DocUploadRequest requiredDocUploadRequest(FraudAccount account, String code, Transaction tx) throws ApplicationException;
    List<DocUploadRequest> uploadedDocRequests(FraudAccount account, Transaction tx);
    List<DocUploadRequest> getDocUploadRequests(FraudAccount account, List<DocUploadStatus> statuses, Transaction tx);
}
