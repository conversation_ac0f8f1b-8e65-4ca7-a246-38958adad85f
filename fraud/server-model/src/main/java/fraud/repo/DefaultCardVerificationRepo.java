package fraud.repo;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.FraudAccount;
import fraud.model.card.AccountCardVerificationInfo;
import fraud.model.card.CardVerificationMetaInfo;
import fraud.model.card.CardVerificationRequest;
import fraud.model.CardVerificationStatusSpec;
import fraud.model.card.query.QAccountCardVerificationInfo;
import fraud.model.card.query.QCardVerificationMetaInfo;
import fraud.model.card.query.QCardVerificationRequest;
import io.ebean.Database;
import io.ebean.Transaction;

public class DefaultCardVerificationRepo implements CardVerificationRepo {
    private final Database ebean;

    public DefaultCardVerificationRepo(Database ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public Optional<CardVerificationMetaInfo> findMetaInfoBy(FraudAccount account, String fingerprint, Transaction tx) {
        var q = new QCardVerificationMetaInfo(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.fingerprint.eq(fingerprint);
        return q.findOneOrEmpty();
    }

    @Override
    public Optional<CardVerificationRequest> findVerificationBy(String scanReference, String provider, Transaction tx) {
        var q = new QCardVerificationRequest(ebean).usingTransaction(tx);
        q.scanReference.eq(scanReference);
        q.provider.eq(provider);
        return q.findOneOrEmpty();
    }

    @Override
    public CardVerificationRequest requiredVerificationBy(String scanReference, String provider, Transaction tx) throws ApplicationException {
        var q = new QCardVerificationRequest(ebean).usingTransaction(tx);
        q.scanReference.eq(scanReference);
        q.provider.eq(provider);
        return q.findOneOrEmpty()
                .orElseThrow(ApplicationException.orElseThrow(
                        "Unable to find CardVerificationRequest for scanReference " + scanReference
                                + " and provider " + provider,
                        Code.ERR_NOT_FOUND));
    }

    @Override
    public List<CardVerificationMetaInfo> findAllMetaInfoByAccount(FraudAccount account, Transaction tx) {
        var q = new QCardVerificationMetaInfo(ebean).usingTransaction(tx);
        q.account.eq(account);
        return q.findList();
    }

    @Override
    public List<CardVerificationMetaInfo> findAllMetaInfoByAccount(Long accountId, Transaction tx) {
        var q = new QCardVerificationMetaInfo(ebean).usingTransaction(tx);
        q.account.id.eq(accountId);
        return q.findList();
    }

    @Override
    public Optional<AccountCardVerificationInfo> accountCardVerificationInfo(FraudAccount account, Transaction tx) {
        var q = new QAccountCardVerificationInfo(ebean).usingTransaction(tx);
        q.account.eq(account);
        return q.findOneOrEmpty();
    }

    @Override
    public List<CardVerificationMetaInfo> findByStatus(FraudAccount account, List<CardVerificationStatusSpec> statusSpecs, Transaction tx) {
        var q = new QCardVerificationMetaInfo(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.status.in(statusSpecs);
        return q.findList();
    }
}
