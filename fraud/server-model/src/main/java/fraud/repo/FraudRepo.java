package fraud.repo;

import java.util.List;
import java.util.Optional;

import fraud.model.ActionTypeSpec;
import api.v1.ApplicationException;
import fraud.model.EmailOtpRequest;
import fraud.model.FraudAccount;
import fraud.model.FraudBrand;
import fraud.model.fraud.AccountFraudInfo;
import fraud.model.otp.AccountOtpLimit;
import fraud.model.fraud.FraudDeclineRule;
import fraud.model.fraud.FraudResponse;
import fraud.model.fraud.FraudRule;
import fraud.model.fraud.FraudRuleCategoryDetails;
import fraud.model.fraud.FraudTransactionLabels;
import fraud.model.fraud.IpDetails;
import fraud.model.kyc.KYCRiskSpendPolicy;
import fraud.model.otp.OtpTriggerAudit;
import fraud.model.otp.OtpTriggerEvent;
import fraud.model.otp.OtpTriggerRules;
import fraud.model.otp.PhoneNumberRequest;
import fraud.model.chat.ZendeskExportInfo;
import io.ebean.Transaction;

public interface FraudRepo {
    Optional<AccountFraudInfo> accountFraudInfo(Transaction tx, long id);
    List<FraudDeclineRule> declineFraudRule(Transaction tx);
    List<FraudTransactionLabels> transactionLabels(String fraudRequestId, Transaction tx);
    Optional<IpDetails> ipDetails(String ip, Transaction tx);
    Optional<FraudRuleCategoryDetails> fraudRuleCategoryDetails(Long categoryId, Transaction tx);
    Optional<FraudRule> fraudRule(String ruleId, Transaction tx);
    List<FraudResponse> fraudResponses(Long accountId, ActionTypeSpec type, Transaction tx);
    FraudResponse fraudResponse(Transaction tx, Long id);
    KYCRiskSpendPolicy requiredKYCRiskSpendPolicy(FraudBrand brand, String currency, Transaction tx) throws ApplicationException;
    Optional<KYCRiskSpendPolicy> kycRiskSpendPolicy(FraudBrand brand, String currency, Transaction tx);
    Optional<KYCRiskSpendPolicy> kycRiskSpendPolicy(String currency, Transaction tx);
    Optional<PhoneNumberRequest> phoneNumberRequestBySidAndNumber(FraudAccount account, String sid, String number, Transaction tx);
    Optional<PhoneNumberRequest> lastPhoneNumberRequest(FraudAccount account, Transaction tx);
    Optional<PhoneNumberRequest> lastPhoneNumberRequest(FraudAccount account, String phoneNumber, Transaction tx);
    Optional<AccountOtpLimit> accountOtpLimit(FraudAccount account, Transaction tx);
    Optional<OtpTriggerRules> otpTriggerRules(FraudBrand brand, String country, Transaction tx);
    Optional<OtpTriggerRules> otpTriggerRules(FraudBrand brand, Transaction tx);
    Optional<OtpTriggerAudit> otpTriggerAudit(FraudAccount account, Transaction tx);
    Optional<OtpTriggerEvent> lastOtpTriggerEvent(FraudAccount account, Transaction tx);
    Optional<ZendeskExportInfo> zendeskExportInfo(FraudAccount account, Transaction tx);
    void deletePhoneNumberVerificationRequests(Long accountId, Transaction tx);
    void deletePhoneNumberVerificationRequests(FraudAccount immutableAccount, Transaction tx);
    Optional<EmailOtpRequest> findLatestEmailOtpRequest(FraudAccount account, Transaction tx);
}
