package fraud.repo;

import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.FraudAccount;
import fraud.model.doc.upload.DocUploadRequest;
import fraud.model.doc.upload.DocUploadStatus;
import fraud.model.doc.upload.query.QDocUploadRequest;
import io.ebean.Database;
import io.ebean.Transaction;

import java.util.List;

public class DefaultDocUploadRepo implements DocUploadRepo {
    private final Database ebean;

    public DefaultDocUploadRepo(Database ebean) {
        this.ebean = ebean;
    }

    @Override
    public DocUploadRequest requiredDocUploadRequest(FraudAccount account, String code, Transaction tx) throws ApplicationException {
        QDocUploadRequest q = new QDocUploadRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.code.eq(code);
        return q.findOneOrEmpty()
                .orElseThrow(ApplicationException.orElseThrow(
                        "Unable to find DocUploadRequest for account " + account.getId()
                                + " and code " + code,
                        Code.ERR_NOT_FOUND));
    }

    @Override
    public List<DocUploadRequest> uploadedDocRequests(FraudAccount account, Transaction tx) {
        QDocUploadRequest q = new QDocUploadRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.status.in(List.of(DocUploadStatus.PENDING_REVIEW, DocUploadStatus.IN_REVIEW, DocUploadStatus.APPROVED, DocUploadStatus.REJECTED));
        return q.findList();
    }

    @Override
    public List<DocUploadRequest> getDocUploadRequests(FraudAccount account, List<DocUploadStatus> statuses, Transaction tx) {
        QDocUploadRequest q = new QDocUploadRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.status.in(statuses);
        return q.findList();
    }
}
