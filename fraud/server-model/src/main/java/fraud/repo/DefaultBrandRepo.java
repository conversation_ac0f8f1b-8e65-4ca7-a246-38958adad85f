package fraud.repo;

import java.util.Objects;
import java.util.Optional;

import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.BrandSettings;
import fraud.model.FraudBrand;
import fraud.model.query.QBrandSettings;
import fraud.model.query.QFraudBrand;
import io.ebean.Database;
import io.ebean.Transaction;
import lombok.SneakyThrows;

public class DefaultBrandRepo implements BrandRepo {
    private final Database ebean;

    public DefaultBrandRepo(Database ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public Optional<FraudBrand> brandByName(String name, Transaction tx) {
        QFraudBrand q = new QFraudBrand(ebean).usingTransaction(tx);
        q.name.eq(name);
        return q.findOneOrEmpty();
    }

    @Override
    public FraudBrand requiredBrandByName(String name, Transaction tx) throws ApplicationException {
        Optional<FraudBrand> opt = brandByName(name, tx);
        return opt.orElseThrow(ApplicationException.orElseThrow("unable to find brand: " + name, Code.ERR_NOT_FOUND));
    }

    @Override
    @SneakyThrows
    public Optional<BrandSettings> getBrandSettings(FraudBrand brand, Transaction tx) {
        QBrandSettings q = new QBrandSettings(ebean).usingTransaction(tx);
        q.fraudBrand.eq(brand);
        return q.findOneOrEmpty();
    }
}
