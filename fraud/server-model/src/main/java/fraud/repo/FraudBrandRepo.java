package fraud.repo;

import api.v1.ApplicationException;
import fraud.model.FraudBrand;
import io.ebean.Transaction;

import java.util.List;
import java.util.Optional;

public interface FraudBrandRepo {
    FraudBrand requiredBrandByName(String name, Transaction tx) throws ApplicationException;
    Optional<FraudBrand> brandByName(String name, Transaction tx) throws ApplicationException;
    List<FraudBrand> brands(Transaction tx);
    FraudBrand getOrCreate(String name, Transaction tx);
}
