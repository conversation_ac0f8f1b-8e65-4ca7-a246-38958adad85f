package fraud.repo;

import java.util.Optional;

import api.v1.ApplicationException;
import fraud.model.BrandSettings;
import fraud.model.FraudBrand;
import io.ebean.Transaction;

public interface BrandRepo {
    Optional<FraudBrand> brandByName(String name, Transaction tx);
    FraudBrand requiredBrandByName(String name, Transaction tx) throws ApplicationException;
    Optional<BrandSettings> getBrandSettings(FraudBrand brand, Transaction tx);
}
