package fraud.repo;

import api.v1.ApplicationException;
import api.v1.KYCStatusSpec;
import fraud.model.FraudAccount;
import fraud.model.kyc.AccountKycInfo;
import fraud.model.kyc.KYCVerificationRequest;
import io.ebean.Transaction;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface KYCRepo {
    KYCVerificationRequest requiredKycVerificationByAccountAndTransactionId(FraudAccount account, UUID transactionId, Transaction tx) throws ApplicationException;
    KYCVerificationRequest requiredKycVerificationByTransactionId(UUID transactionId, Transaction tx) throws ApplicationException;
    KYCVerificationRequest requiredKycVerificationByScanReference(String provider, String scanReference, Transaction tx) throws ApplicationException;
    List<KYCVerificationRequest> kycVerificationsSorted(String provider, FraudAccount account, Transaction tx);
    KYCVerificationRequest requiredKycVerification(String provider, String code, Transaction tx) throws ApplicationException;
    List<KYCVerificationRequest> requiredKycVerification(KYCStatusSpec status, FraudAccount account, Transaction tx);
    List<KYCVerificationRequest> getAccountWithDocIdNumberExists(FraudAccount account, String idNumber, Transaction tx);
    List<KYCVerificationRequest> kycVerificationsSorted(FraudAccount account, Transaction tx);
    void deleteKycVerificationRequests(FraudAccount account, Transaction tx);
    Optional<AccountKycInfo> accountKycInfo(long accountId, Transaction tx);
}
