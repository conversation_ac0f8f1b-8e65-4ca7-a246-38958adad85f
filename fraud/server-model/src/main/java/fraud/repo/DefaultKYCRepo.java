package fraud.repo;

import api.v1.ApplicationException;
import api.v1.Code;
import api.v1.EnhancedApplicationException;
import api.v1.KYCStatusSpec;
import api.v1.Reason;
import fraud.model.FraudAccount;
import fraud.model.kyc.AccountKycInfo;
import fraud.model.kyc.KYCVerificationRequest;
import fraud.model.kyc.query.QAccountKycInfo;
import fraud.model.kyc.query.QKYCVerificationRequest;
import io.ebean.Database;
import io.ebean.Transaction;
import model.HashingUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static api.util.ErrorDetailUtils.detail;

public class DefaultKYCRepo implements KYCRepo {
    private final Database ebean;

    public DefaultKYCRepo(Database ebean) {
        this.ebean = ebean;
    }

    @Override
    public KYCVerificationRequest requiredKycVerificationByAccountAndTransactionId(FraudAccount account, UUID transactionId, Transaction tx)
            throws ApplicationException {
        QKYCVerificationRequest q = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.transactionId.eq(transactionId);
        return q.findOneOrEmpty()
                .orElseThrow(EnhancedApplicationException.orElseThrow("unable to find KYC request: " + transactionId, Code.ERR_NOT_FOUND,
                        Reason.UNABLE_TO_FIND_KYC_REQUEST_BY_TRANSACTION_ID,
                        List.of(detail("transactionId", transactionId.toString()))));
    }

    @Override
    public KYCVerificationRequest requiredKycVerificationByTransactionId(UUID transactionId, Transaction tx) throws ApplicationException {
        QKYCVerificationRequest q = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        q.transactionId.eq(transactionId);
        return q.findOneOrEmpty()
                .orElseThrow(EnhancedApplicationException.orElseThrow("unable to find KYC request: " + transactionId, Code.ERR_NOT_FOUND,
                        Reason.UNABLE_TO_FIND_KYC_REQUEST_BY_TRANSACTION_ID,
                        List.of(detail("transactionId", transactionId.toString()))));
    }

    @Override
    public KYCVerificationRequest requiredKycVerificationByScanReference(String provider, String scanReference, Transaction tx)
            throws ApplicationException {
        QKYCVerificationRequest q = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        q.scanReference.eq(scanReference);
        q.provider.eq(provider);
        return q.findOneOrEmpty()
                .orElseThrow(EnhancedApplicationException.orElseThrow("unable to find KYC request: " + scanReference, Code.ERR_NOT_FOUND,
                        Reason.UNABLE_TO_FIND_KYC_REQUEST_BY_SCAN_REF,
                        List.of(detail("scanReference", scanReference))));
    }

    @Override
    public List<KYCVerificationRequest> kycVerificationsSorted(String provider, FraudAccount account, Transaction tx) {
        QKYCVerificationRequest query = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        query.provider.eq(provider);
        query.account.eq(account);
        List<KYCVerificationRequest> kvrs = query.findList();
        return kvrs.stream().sorted(Comparator.comparing(KYCVerificationRequest::getCreatedAt).reversed()).toList();
    }

    @Override
    public KYCVerificationRequest requiredKycVerification(String provider, String code, Transaction tx) throws ApplicationException {
        QKYCVerificationRequest q = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        q.provider.eq(provider);
        q.code.eq(code);
        return q.findOneOrEmpty()
                .orElseThrow(EnhancedApplicationException.orElseThrow("unable to find KYC request: " + code, Code.ERR_NOT_FOUND,
                        Reason.UNABLE_TO_FIND_KYC_REQUEST_BY_CODE,
                        List.of(detail("code", code))));
    }

    @Override
    public List<KYCVerificationRequest> requiredKycVerification(KYCStatusSpec status, FraudAccount account, Transaction tx) {
        QKYCVerificationRequest query = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        query.account.eq(account);
        query.status.eq(status);
        return query.findList();
    }

    @Override
    public List<KYCVerificationRequest> getAccountWithDocIdNumberExists(FraudAccount account, String idNumber, Transaction tx) {
        QKYCVerificationRequest qkvr = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        qkvr.idNumberHash.eq(HashingUtils.hash(idNumber));
        qkvr.account.ne(account);
        qkvr.account.brand.eq(account.getBrand());

        return qkvr.findList();
    }

    @Override
    public List<KYCVerificationRequest> kycVerificationsSorted(FraudAccount account, Transaction tx) {
        var query = new QKYCVerificationRequest(ebean)
                .usingTransaction(tx).account.eq(account)
                .orderBy().createdAt.desc();
        return query.findList();
    }

    @Override
    public void deleteKycVerificationRequests(FraudAccount account, Transaction tx) {
        QKYCVerificationRequest q = new QKYCVerificationRequest(ebean).usingTransaction(tx);
        q.account.eq(account);
        q.delete();
    }

    @Override
    public Optional<AccountKycInfo> accountKycInfo(long accountId, Transaction tx) {
        QAccountKycInfo q = new QAccountKycInfo(ebean).usingTransaction(tx);
        q.id.eq(accountId);
        return q.findOneOrEmpty();
    }
}
