package fraud.repo;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import fraud.model.FraudAccount;
import fraud.model.query.QFraudAccount;
import io.ebean.Database;
import io.ebean.Transaction;

public class DefaultAccountRepo implements AccountRepo {
    private final Database ebean;

    public DefaultAccountRepo(Database ebean) {
        this.ebean = Objects.requireNonNull(ebean);
    }

    @Override
    public Optional<FraudAccount> account(Long id, Transaction tx) {
        QFraudAccount q = new QFraudAccount(ebean).usingTransaction(tx);
        q.setId(id);
        return q.findOneOrEmpty();
    }

    @Override
    public FraudAccount requiredAccount(Long id, Transaction tx) {
        QFraudAccount q = new QFraudAccount(ebean).usingTransaction(tx);
        q.setId(id);
        return q.findOne();
    }

    @Override
    public List<FraudAccount> accounts(List<Long> accountIds, Transaction tx) {
        QFraudAccount q = new QFraudAccount(ebean).usingTransaction(tx);
        q.id.in(accountIds);
        q.amlCheck.fetch();
        return q.findList();
    }

    @Override
    public List<FraudAccount> accountsById(Collection<Long> ids, Transaction tx) {
        QFraudAccount q = new QFraudAccount(ebean).usingTransaction(tx);
        q.id.in(ids);
        return q.findList();
    }
}
