package fraud.repo;

import api.v1.ApplicationException;
import api.v1.Code;
import fraud.model.FraudAccount;
import fraud.model.query.QFraudAccount;
import io.ebean.Database;
import io.ebean.Transaction;

import java.util.Optional;

public class DefaultFraudAccountRepo implements FraudAccountRepo {
    private final Database ebean;

    public DefaultFraudAccountRepo(Database ebean) {
        this.ebean = ebean;
    }

    @Override
    public Optional<FraudAccount> account(Long id, Transaction tx) {
        QFraudAccount q = new QFraudAccount(ebean).usingTransaction(tx);
        q.setId(id);
        return q.findOneOrEmpty();
    }

    @Override
    public FraudAccount requiredAccount(Long id, Transaction tx) throws ApplicationException {
        return account(id, tx).orElseThrow(ApplicationException.orElseThrow("Account was not found", Code.ERR_NOT_FOUND));
    }
}
