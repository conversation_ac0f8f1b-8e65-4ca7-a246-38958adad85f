<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.turbospaces</groupId>
        <artifactId>turbospaces-parent-pom</artifactId>
        <version>11</version>
    </parent>
    <groupId>com.patrianna.uam</groupId>
    <artifactId>lotto-parent</artifactId>
    <version>25.07.9-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>patrianna ::: ${project.artifactId}</name>
    <inceptionYear>2023</inceptionYear>
    <organization>
        <name>patrianna</name>
        <url>https://patrianna.com</url>
    </organization>
    <scm>
        <developerConnection>${scm.developerConnection}</developerConnection>
        <tag>v23.1.1</tag>
    </scm>
    <distributionManagement>
        <snapshotRepository>
            <id>artifact-registry</id>
            <url>artifactregistry://europe-maven.pkg.dev/patrianna-dev/nexus</url>
        </snapshotRepository>
        <repository>
            <id>artifact-registry</id>
            <url>artifactregistry://europe-maven.pkg.dev/patrianna-dev/nexus</url>
        </repository>
    </distributionManagement>
    <repositories>
        <repository>
           <id>artifact-registry-mirror</id>
           <url>artifactregistry://europe-west1-maven.pkg.dev/patrianna-prod/maven-mirror</url>
           <releases>
               <enabled>true</enabled>
           </releases>
           <snapshots>
               <enabled>true</enabled>
           </snapshots>
        </repository>
        <repository>
            <id>artifact-registry</id>
            <url>artifactregistry://europe-maven.pkg.dev/patrianna-dev/nexus</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <properties>
        <maven.compiler.target>23</maven.compiler.target>
        <maven.compiler.source>23</maven.compiler.source>
        <!-- -->
        <scm.provider>git</scm.provider>
        <scm.url>*****************:turbospaces/lotto.git</scm.url>
        <scm.developerConnection>scm:${scm.provider}:${scm.url}</scm.developerConnection>
        <!-- -->
        <!-- JVM options -->
        <!-- -->
        <jvm.gc.options>
            <![CDATA[-XX:+UseZGC -XX:+ZGenerational -XX:MinRAMPercentage=25 -XX:MaxRAMPercentage=40 -XX:+UseContainerSupport]]>
        </jvm.gc.options>
        <jvm.jmx.options>
            <![CDATA[-Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.local.only=false -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false]]>
        </jvm.jmx.options>
        <jvm.common.options>
            <![CDATA[-server -XX:+UnlockDiagnosticVMOptions -XshowSettings:vm -Xlog:gc* -XX:+PrintFlagsFinal -XX:+TieredCompilation -XX:+SegmentedCodeCache -XX:+PrintStringTableStatistics -XX:+PrintNMTStatistics -XX:NativeMemoryTracking=summary -XX:+UseStringDeduplication -XX:+DisableExplicitGC -XX:+ExitOnOutOfMemoryError -Dsun.net.inetaddr.ttl=0 -Djava.net.preferIPv4Stack=true -Djava.awt.headless=true -Dfile.encoding=UTF-8 -Djgroups.tcp.address=NON_LOOPBACK]]>
        </jvm.common.options>
        <jvm.unsafe.options>
            <![CDATA[-XX:+EnableDynamicAgentLoading -XX:+AllowRedefinitionToAddDeleteMethods -Djava.security.manager=allow --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.reflect=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.nio=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.base/sun.nio.ch=ALL-UNNAMED --add-opens=java.base/sun.nio.cs=ALL-UNNAMED --add-opens=java.base/sun.security.action=ALL-UNNAMED --add-opens=java.base/sun.util.calendar=ALL-UNNAMED --add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED]]>
        </jvm.unsafe.options>
        <!-- -->
        <!-- CORE -->
        <!-- -->
        <boot.version>2.0.67</boot.version>
        <common.version>25.07.2</common.version>
        <crm.version>25.07.1</crm.version>
        <!-- -->
        <!-- COMMONS -->
        <!-- -->
        <asm.version>9.8</asm.version>
        <lombok.version>1.18.38</lombok.version>
        <errorprone.version>2.38.0</errorprone.version>
        <opencsv.version>5.6</opencsv.version>
        <protobuf.version>3.25.5</protobuf.version>
        <ebean.version>14.11.0</ebean.version>
        <!-- -->
        <!-- extensions -->
        <!-- -->
        <artifactregistry.version>2.2.5</artifactregistry.version>
        <!-- -->
        <!-- 3-party -->
        <!-- -->
        <aws.sdk.version>1.12.531</aws.sdk.version>
        <!-- -->
        <!-- testing -->
        <!-- -->
        <!-- -->
        <!-- plugins -->
        <!-- -->
        <version.protobuf.plugin>3.11.4</version.protobuf.plugin>
        <version.ebean.plugin>${ebean.version}</version.ebean.plugin>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!-- -->
            <!-- BOMs -->
            <!-- -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-bom</artifactId>
                <version>${protobuf.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.turbospaces.boot</groupId>
                <artifactId>bootstrap-bom</artifactId>
                <version>${boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- -->
            <!-- commons -->
            <!-- -->
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>${opencsv.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- -->
            <!-- aws -->
            <!-- -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>${aws.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- -->
            <!-- commons -->
            <!-- -->
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>core-server-model</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-api-spec-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-api-facade-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-api-tags-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-api-types-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-api-flatten-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-api-identity-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-auth</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-openapi-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>gateway-api-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>frontend-api-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>frontend-server-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>gateway-server-common</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>server-test-common</artifactId>
                <version>${common.version}</version>
            </dependency>

            <!-- -->
            <!-- Auth -->
            <!-- -->
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>auth-integration-bots</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>auth-integration-server</artifactId>
                <version>${crm.version}</version>
            </dependency>

            <!-- -->
            <!-- CRM -->
            <!-- -->
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-server-api</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-gateway-api</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>game-hub-gateway-api</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>game-hub-server-api</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-server-api-common</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-server-model</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-gaming-pragmatic</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-frontend-api</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-integration-server</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-gaming-netent</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-gaming-markortech</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-gaming-relax</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-server</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>crm-integration-bots</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>game-hub-product-provider-core</artifactId>
                <version>${crm.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/data</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
            <testResource>
                <directory>src/test/data</directory>
                <filtering>false</filtering>
            </testResource>
        </testResources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <dependencies>
                        <dependency>
                            <groupId>org.ow2.asm</groupId>
                            <artifactId>asm</artifactId>
                            <version>${asm.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <release>${maven.compiler.target}</release>
                        <compilerArgs>
                            <arg>-XDcompilePolicy=simple</arg>
                            <arg>--should-stop=ifError=FLOW</arg>
                            <arg>-Xplugin:ErrorProne</arg>
                        </compilerArgs>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>com.google.errorprone</groupId>
                                <artifactId>error_prone_core</artifactId>
                                <version>${errorprone.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>io.ebean</groupId>
                    <artifactId>ebean-maven-plugin</artifactId>
                    <version>${version.ebean.plugin}</version>
                    <executions>
                        <execution>
                            <id>main</id>
                            <phase>process-classes</phase>
                            <configuration>
                                <transformArgs>debug=1</transformArgs>
                            </configuration>
                            <goals>
                                <goal>enhance</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>test</id>
                            <phase>process-test-classes</phase>
                            <configuration>
                                <transformArgs>debug=1</transformArgs>
                            </configuration>
                            <goals>
                                <goal>testEnhance</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.github.os72</groupId>
                    <artifactId>protoc-jar-maven-plugin</artifactId>
                    <version>${version.protobuf.plugin}</version>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <addProtoSources>all</addProtoSources>
                                <includeMavenTypes>direct</includeMavenTypes>
                                <includeDirectories>src/main/protobuf</includeDirectories>
                                <protocArtifact>com.google.protobuf:protoc:${protobuf.version}</protocArtifact>
                                <outputTargets>
                                    <outputTarget>
                                        <type>java</type>
                                        <outputDirectory>${project.build.directory}/generated-sources/java</outputDirectory>
                                    </outputTarget>
                                </outputTargets>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm</artifactId>
                        <version>${asm.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <ignoredDependencies>
                        <ignoredDependency>com.google.errorprone:error_prone_annotations</ignoredDependency>
                    </ignoredDependencies>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-gpg-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>${jvm.unsafe.options}</argLine>
                    <systemPropertyVariables combine.children="append">
                        <java.awt.headless>true</java.awt.headless>
                        <java.net.preferIPv4Stack>true</java.net.preferIPv4Stack>
                        <jgroups.tcp.address>NON_LOOPBACK</jgroups.tcp.address>
                        <org.springframework.boot.logging.LoggingSystem>none</org.springframework.boot.logging.LoggingSystem>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
        </plugins>
        <extensions>
            <extension>
                <groupId>com.google.cloud.artifactregistry</groupId>
                <artifactId>artifactregistry-maven-wagon</artifactId>
                <version>${artifactregistry.version}</version>
            </extension>
        </extensions>
    </build>
    <modules>
        <module>server-api-common</module>
        <module>server-api</module>
        <module>server-model</module>
        <module>server-endpoint</module>
        <module>gateway</module>
        <module>frontend</module>
        <module>providers</module>
        <module>server</module>
        <module>test</module>
    </modules>
</project>
