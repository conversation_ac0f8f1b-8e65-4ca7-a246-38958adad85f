<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.patrianna.uam</groupId>
        <artifactId>game-hub-parent</artifactId>
        <version>25.06.2-SNAPSHOT</version>
    </parent>

    <artifactId>game-hub-admin-parent</artifactId>
    <packaging>pom</packaging>
    <name>game-hub ::: ${project.artifactId}</name>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>game-hub-server-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.patrianna.uam</groupId>
                <artifactId>game-hub-server-api-common</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>server</module>
        <module>server-api</module>
        <module>server-endpoint</module>
    </modules>
</project>