pipeline {

  agent {
    node {
      label 'patrianna-dev'
    }
  }

  triggers {
    pollSCM('H/2 * * * *')
  }

  options {
    disableConcurrentBuilds()
    timeout(time: 1, unit: 'HOURS')
  }

  tools {
    maven 'mvn3'
    jdk 'jdk23'
  }

  stages {
    stage('Build') {
      when {
        not {
          changelog '.*\\[maven-release-plugin\\].*'
        }
      }
      
      steps {
        sh 'mvn clean package -DfailIfNoTests=false -U -T 1C'
        sh 'mvn deploy -DskipTests -T 1C'
        sh 'mvn org.codehaus.mojo:build-helper-maven-plugin:remove-project-artifact -Dbuildhelper.removeAll=false'
      }
    }

    stage('Reports') {
      steps {
        allure([
          includeProperties: false,
          jdk: '',
          properties: [],
          reportBuildPolicy: 'ALWAYS',
          results: [
            [path: 'server/target/allure-results']
          ]
        ])
      }
    }
  }


  post {
    always {
      deleteDir()
    }
  }
}