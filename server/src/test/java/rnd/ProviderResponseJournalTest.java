package rnd;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.time.Instant;

import org.junit.jupiter.api.Test;

import com.turbospaces.common.PlatformUtil;

import io.ebean.Transaction;
import rnd.model.data.DataProvider;
import rnd.model.data.ProviderRequest;
import rnd.model.data.query.QProviderRequest;
import rnd.model.data.query.QProviderRequestSrc;
import rnd.model.data.query.QProviderResponsePayloadSrc;

public class ProviderResponseJournalTest extends AbstractDbIntegrationTest {

    @Test
    public void providerResponseJournalingWorks() throws Throwable {

        try (Transaction tx = ebean.newTransaction()) {
            DataProvider dataProvider = new DataProvider(DataProvider.Code.OPTIC_ODDS);
            ebean.save(dataProvider, tx);

            ProviderRequest req1 = new ProviderRequest("/aaa/bbb", PlatformUtil.randomUUID(), "one", 200, "OK", Instant.now());
            req1.setProvider(dataProvider);
            ProviderRequest req2 = new ProviderRequest("/aaa/bbb", PlatformUtil.randomUUID(), "one", 200, "OK", Instant.now());
            ProviderRequest req3 = new ProviderRequest("/aaa/bbb", PlatformUtil.randomUUID(), "two", 200, "OK", Instant.now());

            ebean.save(req1, tx);
            ebean.save(req2, tx);
            ebean.save(req3, tx);

            assertEquals(3, new QProviderRequestSrc(ebean).usingTransaction(tx).findCount());
            assertEquals(2, new QProviderResponsePayloadSrc(ebean).usingTransaction(tx).findCount()); // deduplication works

            ProviderRequest r1 = new QProviderRequest(ebean).usingTransaction(tx).where().id.eq(req1.getId()).findOne();
            assertEquals("/aaa/bbb", r1.getRequest());
            assertEquals(req1.getCorrelationId(), r1.getCorrelationId());
            assertEquals("one", r1.getResponse());
            assertEquals(200, r1.getStatusCode());
            assertEquals("OK", r1.getStatusMessage());
            assertEquals(dataProvider.getId(), req1.getProvider().getId());
            assertNotNull(r1.getCreatedAt());

            ebean.delete(req2, tx);
            assertEquals(2, new QProviderRequestSrc(ebean).usingTransaction(tx).findCount());
            assertEquals(2, new QProviderResponsePayloadSrc(ebean).usingTransaction(tx).findCount()); // still referenced

            ebean.delete(req1, tx);
            assertEquals(1, new QProviderRequestSrc(ebean).usingTransaction(tx).findCount());
            assertEquals(1, new QProviderResponsePayloadSrc(ebean).usingTransaction(tx).findCount()); // unreferenced removed

            ebean.delete(req3, tx);
            assertEquals(0, new QProviderRequestSrc(ebean).usingTransaction(tx).findCount());
            assertEquals(0, new QProviderResponsePayloadSrc(ebean).usingTransaction(tx).findCount()); // unreferenced removed

            tx.rollback();
        }
    }

}
