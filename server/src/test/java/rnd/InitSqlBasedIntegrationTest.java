package rnd;

import org.junit.jupiter.api.BeforeEach;
import org.springframework.test.context.jdbc.Sql;

import fe.api.rnd.common.PingRequest;
import fe.api.rnd.common.PingResponse;

/**
 * @deprecated in favour of {@link DynamicDataTest} instead
 */
@Deprecated(forRemoval = true)
@Sql(scripts = "/init.sql", executionPhase = Sql.ExecutionPhase.BEFORE_TEST_CLASS)
@Sql(scripts = "/clean-all.sql", executionPhase = Sql.ExecutionPhase.AFTER_TEST_CLASS)
public class InitSqlBasedIntegrationTest extends AbstractIntegrationTest {

    @BeforeEach
    void setUp() {
        consumersReadinessChecker.readinessTest(
                () -> post(new PingRequest(), "PingRequest").returnResult(PingResponse.class),
                () -> post(new PingRequest(), "PingModifyRequest").returnResult(PingResponse.class));
    }

    protected boolean notTeamGame(String code) {
        return code.contains("wta");
    }
}
