package rnd;

import org.springframework.util.ResourceUtils;

import com.turbospaces.boot.test.AbstractSpringBootTestContextBootstrapper;
import com.turbospaces.cfg.ApplicationConfig;
import com.turbospaces.common.PlatformUtil;

public class RandomizerSpringBootTestContextBootstrapper extends AbstractSpringBootTestContextBootstrapper<RandomizerServerProperties> {
    @Override
    protected RandomizerServerProperties createProps() throws Exception {
        ApplicationConfig cfg = ApplicationConfig.mock();
        RandomizerServerProperties props = new RandomizerServerProperties(cfg.factory());
        cfg.loadDefaultPropsFromResource(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX + "randomizer-server.properties"));
        cfg.setDefaultProperty(props.CLOUD_APP_PORT.getKey(), PlatformUtil.findAvailableTcpPort());
        cfg.setDefaultProperty(props.CLOUD_APP_SECONDARY_PORT.getKey(), PlatformUtil.findAvailableTcpPort());
        cfg.setDefaultProperty(props.CLOUD_APP_TERTIARY_PORT.getKey(), PlatformUtil.findAvailableTcpPort());
        return props;
    }
}
