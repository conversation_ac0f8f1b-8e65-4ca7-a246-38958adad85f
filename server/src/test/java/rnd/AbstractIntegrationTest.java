package rnd;

import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.JdkClientHttpConnector;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import org.springframework.test.web.reactive.server.WebTestClient;

import com.google.common.util.concurrent.Futures;
import com.google.protobuf.Any;
import com.turbospaces.api.facade.DefaultRequestWrapperFacade;
import com.turbospaces.common.PlatformUtil;
import com.turbospaces.debezium.DebeziumKafkaDiModule;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.rpc.DefaultApiResponse;
import com.turbospaces.rpc.DefaultWrappedQueuePost;

import api.facade.DefaultResponseWrapperFacade;
import api.v1.ApiFactory;
import api.v1.CacheControl;
import api.v1.Code;
import api.v1.ForcementModeSpec;
import bi.bloomreach.data.BloomreachMapper;
import common.JwtTags;
import fe.FrontendCommonObjectMapper;
import fe.di.RandomizerFrontendEndpointsDiModule;
import fe.handlers.shuffle.TicketChangedNotificationHandler;
import identity.GeoLocationDiModule;
import io.cloudevents.core.builder.CloudEventBuilder;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.netty.util.AsciiString;
import io.vavr.CheckedRunnable;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import jakarta.inject.Inject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import rnd.api.v1.internal.TTestBeaconNotification;
import rnd.di.RandomizerAcceptorsDiModule;
import rnd.di.RandomizerAlertsDiModule;
import rnd.di.RandomizerCommonDiModule;
import rnd.di.RandomizerKafkaDiModule;
import rnd.di.RandomizerLifecycleDiModule;
import rnd.di.RandomizerMarketDiModule;
import rnd.enums.Currency;
import rnd.enums.CurrencyMode;
import rnd.event.WebSocketTransport;
import rnd.handlers.pickem.SubmitPickEmEntryRequestHandler;
import rnd.handlers.shuffle.EnterTicketBundleRequestHandler;
import rnd.lifecycle.BloomreachEventsProcessor;
import rnd.mock.BetServiceMock;
import rnd.mock.MockRandomizerQuartzDiModule;
import rnd.mock.MockServerTestHelper;
import rnd.mock.RandomizerTestDatabaseDiModule;
import rnd.mock.RandomizerTestDiModule;
import rnd.model.main.Account;
import rnd.model.main.Brand;
import rnd.model.main.SignUpInfo;
import rnd.model.main.query.QBrand;
import rnd.services.Market;
import rnd.services.PickEmConfig;
import rnd.services.SlackClient;
import rnd.services.TradingAlertsService;
import rnd.services.UamService;
import rnd.services.WebSocketService;
import rnd.test.utils.SportEntityGeneratorUtils;
import rnd.util.Cached;
import rnd.utils.KafkaConsumersReadinessChecker;
import uam.api.UamServiceApi;
import uam.api.v1.AccountBalanceInfo;
import uam.api.v1.AccountInfo;
import uam.api.v1.AccountMeta;
import uam.api.v1.AccountPersonalInfo;
import uam.api.v1.CurrencyPolicies;
import uam.api.v1.GetAccountInfoRequest;
import uam.api.v1.GetAccountInfoResponse;
import uam.api.v1.GetAccountRoutingInfoRequest;
import uam.api.v1.GetAccountRoutingInfoResponse;
import uam.api.v1.JackpotContributionRequest;
import uam.api.v1.JackpotContributionResponse;
import uam.api.v1.LowOnCoinsRequest;
import uam.api.v1.LowOnCoinsResponse;
import uam.api.v1.WalletSessionRequest;
import uam.api.v1.WalletSessionResponse;

@Slf4j
@ContextConfiguration(classes = {
        RandomizerCommonDiModule.class,
        RandomizerAcceptorsDiModule.class,
        RandomizerFrontendEndpointsDiModule.class,
        RandomizerKafkaDiModule.class,
        RandomizerTestDatabaseDiModule.class,
        MockRandomizerQuartzDiModule.class,
        RandomizerTestDiModule.class,
        RandomizerLifecycleDiModule.class,
        TestData.Config.class,
        GeoLocationDiModule.class,
        DebeziumKafkaDiModule.class,
        RandomizerAlertsDiModule.class,
        MockServerTestHelper.class,
        RandomizerMarketDiModule.class,
        KafkaIntegrationTestBase.ReadinessCheckerTestConfiguration.class,
        KafkaIntegrationTestBase.Configuration.class
})
public abstract class AbstractIntegrationTest extends KafkaIntegrationTestBase {
    protected static String BRAND = TestData.BRAND;
    protected static String PLATFORM_WEB = "web";
    protected static AsciiString ROUTING_KEY = AsciiString.of("test_routing");
    public static final String IP_USA_CA_SIGN_UP_ACCOUNT_STATE = "*************";
    protected static final String BUNDLE_CODE = "test-full-bundle";

    protected static final String NO_SESSION = "no-session";

    @Inject
    protected TestData td;
    @Autowired
    protected RandomizerJpaManager ebean;
    @MockitoSpyBean
    protected UamService uamService;
    @MockitoSpyBean
    protected BloomreachEventsProcessor bloomreach;
    @MockitoSpyBean
    protected BloomreachMapper bloomreachMapper;
    @MockitoSpyBean
    protected WebSocketTransport transport;
    @MockitoSpyBean
    protected WebSocketService webSocketService;
    @MockitoBean
    protected UamServiceApi uamServiceApi;
    @MockitoSpyBean
    protected TradingAlertsService tradingAlertsService;
    @MockitoSpyBean
    protected SlackClient slackClient;
    @MockitoSpyBean
    protected BetServiceMock betService;

    @MockitoSpyBean
    protected EnterTicketBundleRequestHandler enterTicketBundleRequestHandler;
    @MockitoSpyBean
    protected SubmitPickEmEntryRequestHandler submitPickEmEntryRequestHandler;
    @MockitoSpyBean
    protected TicketChangedNotificationHandler ticketChangedNotificationHandler;
    @MockitoSpyBean
    protected PickEmConfig pickEmConfig;

    @Autowired
    protected RandomizerServerProperties props;
    @Autowired
    protected ApiFactory apiFactory;
    @Autowired
    protected RandomizerIdentityManager sessionManager;
    @Autowired(required = false)
    private List<Cached> caches;

    protected long accountRemoteId;
    protected WebTestClient client;
    protected Map<Long, WsClient> wsClients = new HashMap<>();

    protected io.cloudevents.core.v1.CloudEventBuilder template;
    protected FrontendCommonObjectMapper mapper = new FrontendCommonObjectMapper();
    protected api.v1.Headers headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();
    protected api.v1.Status status = api.v1.Status.newBuilder().setErrorCode(Code.ERR_OK).build();
    @Autowired
    private WebSocketTransport webSocketTransport;

    @Autowired
    protected KafkaConsumersReadinessChecker consumersReadinessChecker;
    @Autowired
    private Market market;

    @Deprecated // remove me!!!
    @BeforeEach
    public void enablePickemMarketTemporary() {
        doReturn(true).when(pickEmConfig).pickemMarketEnabled();
    }

    @AfterEach
    public void clearCaches() {
        ebean.cacheManager().clearAll();
        if (caches != null) {
            caches.forEach(Cached::evictCache);
            market.evictCache();
        }
    }

    protected void listenWs() throws URISyntaxException, InterruptedException {
        listenWs(accountRemoteId);
    }

    protected void listenWs(long accountRemoteId) throws URISyntaxException, InterruptedException {
        String session = newSession(accountRemoteId);
        String beacon = PlatformUtil.randomUUID().toString();
        var ws = new WsClient(mapper,
                new URI(String.format("ws://localhost:%d/ws?brandName=%s", props.CLOUD_APP_SECONDARY_PORT.get(), BRAND)),
                Map.of("Cookie", BRAND.toUpperCase() + "SID=" + session), beacon);
        ws.connectBlocking();

        webSocketTransport.send(TTestBeaconNotification.newBuilder().setBrand(BRAND).setUuid(beacon));
        await().atMost(3, TimeUnit.SECONDS)
                .until(() -> {
                    boolean gotBeacon = ws.gotBeacon();
                    if (!gotBeacon) {
                        webSocketTransport.send(TTestBeaconNotification.newBuilder().setBrand(BRAND).setUuid(beacon));
                    }
                    return gotBeacon;
                });
        wsClients.put(accountRemoteId, ws);
    }

    @AfterEach
    void closeWs() throws InterruptedException {
        for (WsClient wsClient : wsClients.values()) {
            wsClient.closeBlocking();
        }
        wsClients.clear();
    }

    @BeforeEach
    void beforeEach() {
        client = WebTestClient.bindToServer().baseUrl("http://localhost:" + props.CLOUD_APP_PORT.get())
                .responseTimeout(Duration.ofSeconds(5)).clientConnector(new JdkClientHttpConnector())
                .build();

        var template = CloudEventBuilder.v1().withSource(URI.create(PlatformUtil.randomUUID().toString()));
        var mapper = new CommonObjectMapper();
        var headers = api.v1.Headers.newBuilder().setMessageId(PlatformUtil.randomUUID().toString()).build();
        var status = api.v1.Status.newBuilder().setErrorCode(Code.ERR_OK).build();

        //
        // ~ mock wallet call
        //
        var wsresp = WalletSessionResponse.newBuilder();
        wsresp.setApplied(true);
        wsresp.addAllBalances(List.of(
                AccountBalanceInfo.newBuilder()
                        .setCurrency(Currency.GC.getCode())
                        .setBalance("100")
                        .build(),
                AccountBalanceInfo.newBuilder()
                        .setCurrency(Currency.SC.getCode())
                        .setBalance("100")
                        .build()));
        var wsrespw = new DefaultResponseWrapperFacade(template, mapper, headers, status, Any.pack(wsresp.build()), CacheControl.getDefaultInstance(), props);
        doReturn(new DefaultApiResponse<>(
                props,
                new DefaultWrappedQueuePost(Futures.immediateFuture(wsrespw),
                        new DefaultRequestWrapperFacade(template, headers, Any.pack(WalletSessionRequest.getDefaultInstance()))),
                apiFactory,
                WalletSessionResponse.class)).when(uamServiceApi).walletSession(any(), any());

        //
        // ~ mock low on coins call
        //
        var locresp = LowOnCoinsResponse.newBuilder();
        var locrespw = new DefaultResponseWrapperFacade(template, mapper, headers, status, Any.pack(locresp.build()), CacheControl.getDefaultInstance(), props);
        doReturn(new DefaultApiResponse<>(
                props,
                new DefaultWrappedQueuePost(Futures.immediateFuture(locrespw),
                        new DefaultRequestWrapperFacade(template, headers, Any.pack(LowOnCoinsRequest.getDefaultInstance()))),
                apiFactory,
                LowOnCoinsResponse.class)).when(uamServiceApi).lowOnCoins(any(), any());

        //
        // ~ mock jackpot contribute call
        //
        var jcresp = JackpotContributionResponse.newBuilder().setApplied(true);
        var jcrespw = new DefaultResponseWrapperFacade(template, mapper, headers, status, Any.pack(jcresp.build()), CacheControl.getDefaultInstance(), props);
        doReturn(new DefaultApiResponse<>(
                props,
                new DefaultWrappedQueuePost(Futures.immediateFuture(jcrespw),
                        new DefaultRequestWrapperFacade(template, headers, Any.pack(JackpotContributionRequest.getDefaultInstance()))),
                apiFactory,
                JackpotContributionResponse.class)).when(uamServiceApi).jackpotContribution(any(), any());

        //
        // ~ mock get account info
        //
        mockGetAccountInfo(Collections.emptyMap(), (String) null);
    }

    protected void mockUamGetAccountRoutingInfo(GetAccountRoutingInfoResponse inforesp) {

        DefaultResponseWrapperFacade inforespw = new DefaultResponseWrapperFacade(template, mapper, headers, status, Any.pack(
                inforesp), // inforesp
                CacheControl.getDefaultInstance(), props);

        doReturn(new DefaultApiResponse<>(
                props,
                new DefaultWrappedQueuePost(Futures.immediateFuture(inforespw),
                        new DefaultRequestWrapperFacade(template, headers, Any.pack(GetAccountRoutingInfoRequest.getDefaultInstance()))),
                apiFactory,
                GetAccountRoutingInfoResponse.class)).when(uamServiceApi).getAccountRouting(any());
    }

    protected void mockGetAccountInfo(Map<Currency, CurrencyMode> currencyPolicies, String... state) {
        mockGetAccountInfo(currencyPolicies, ForcementModeSpec.SWEEPSTAKE, state);
    }

    protected void mockGetAccountInfo(Map<Currency, CurrencyMode> currencyPolicies, ForcementModeSpec accountSweepstakeMode, String... state) {
        DefaultApiResponse<?>[] responses = Stream.of(state)
                .map(st -> getAccountInfoResponse(st, accountSweepstakeMode, currencyPolicies))
                .toArray(DefaultApiResponse[]::new);
        if (responses.length > 1) {
            doReturn(responses[0], (Object[]) Arrays.copyOfRange(responses, 1, responses.length))
                    .when(uamServiceApi).getAccountInfo(any(), any());
        } else {
            doReturn(responses[0]).when(uamServiceApi).getAccountInfo(any(), any());
        }
    }

    private DefaultApiResponse<?> getAccountInfoResponse(String state, ForcementModeSpec accountSweepstakeMode, Map<Currency, CurrencyMode> currencyPolicies) {

        Map<String, CurrencyPolicies> currencyModeMap = Arrays.stream(Currency.values())
                .collect(Collectors.toMap(
                        Enum::name,
                        currency -> CurrencyPolicies.newBuilder().setMode(
                                currencyPolicies.getOrDefault(currency, currency == Currency.USD ? CurrencyMode.DISABLED : CurrencyMode.ENABLED).name())
                                .build()));

        var inforesp = GetAccountInfoResponse.newBuilder();
        var personalInfo = AccountPersonalInfo.newBuilder();
        Optional.ofNullable(state).ifPresent(personalInfo::setState);
        inforesp.setInfo(AccountInfo.newBuilder()
                .putAllCurrencyPolicies(currencyModeMap)
                .setMode(accountSweepstakeMode.name().toLowerCase())
                .setPersonalInfo(personalInfo)
                .setMeta(AccountMeta.newBuilder().setSignupIp(IP_USA_CA_SIGN_UP_ACCOUNT_STATE)));
        var inforespw = new DefaultResponseWrapperFacade(
                template,
                mapper,
                headers,
                status,
                Any.pack(inforesp.build()),
                CacheControl.getDefaultInstance(),
                props);
        return new DefaultApiResponse<>(
                props,
                new DefaultWrappedQueuePost(
                        Futures.immediateFuture(inforespw),
                        new DefaultRequestWrapperFacade(template, headers, Any.pack(GetAccountInfoRequest.getDefaultInstance()))),
                apiFactory,
                GetAccountInfoResponse.class);
    }

    @SneakyThrows
    protected String newSession() {
        accountRemoteId = SportEntityGeneratorUtils.generateNextRemoteAccountId();
        return newSession(accountRemoteId);
    }

    protected String newSession(long accountRemoteId) {
        return newSession(accountRemoteId, BRAND, ROUTING_KEY.toString());
    }

    @SneakyThrows
    protected String newSession(long accountRemoteId, String brandName, String routingKey) {
        JwtBuilder jwt = Jwts.builder();
        jwt.setSubject(String.valueOf(accountRemoteId));
        jwt.addClaims(Map.of(
                JwtTags.JWT_BRAND, brandName,
                JwtTags.JWT_ROUTING_KEY_CLAIM, routingKey));
        jwt.setExpiration(DateUtils.addHours(new Date(), 1));
        return sessionManager.encrypt(brandName, jwt);
    }

    protected Tuple2<Account, String> newAccountAndSession(String brandName) {
        Brand brand = new QBrand(ebean).name.eq(brandName).findOne();
        return newAccountAndSession(brand);
    }

    @SneakyThrows
    protected Tuple2<Account, String> newAccountAndSession(Brand brand) {
        accountRemoteId = SportEntityGeneratorUtils.generateNextRemoteAccountId();
        var hash = "test_routing";
        Account account = new Account();
        account.setCode(PlatformUtil.randomUUID());
        account.setRemoteId(accountRemoteId);
        account.setRemoteCode(hash + '/' + accountRemoteId);
        account.setBrand(brand);
        account.setSignUpInfo(SignUpInfo.builder().signUpTimezone("Europe/Budapest").build());
        account.setHash(hash);
        ebean.save(account);

        JwtBuilder jwt = Jwts.builder();
        jwt.setSubject(String.valueOf(accountRemoteId));
        jwt.addClaims(Map.of(
                JwtTags.JWT_BRAND, BRAND,
                JwtTags.JWT_ROUTING_KEY_CLAIM, ROUTING_KEY.toString()));
        jwt.setExpiration(DateUtils.addHours(new Date(), 1));
        return Tuple.of(account, sessionManager.encrypt(BRAND, jwt));
    }

    @SneakyThrows
    protected String newSession(Account account, String routingKey) {
        JwtBuilder jwt = Jwts.builder();
        jwt.setSubject(String.valueOf(account.getRemoteId()));
        jwt.addClaims(Map.of(
                JwtTags.JWT_BRAND, BRAND,
                JwtTags.JWT_ROUTING_KEY_CLAIM, routingKey));
        jwt.setExpiration(DateUtils.addHours(new Date(), 1));
        return sessionManager.encrypt(BRAND, jwt);
    }

    protected Tuple2<Account, String> createUserWithRewards() {
        Tuple2<Account, String> accountSession = newAccountAndSession(BRAND);
        td.issueAccountRewardsFromBundle(accountSession._1, BUNDLE_CODE);
        return accountSession;
    }

    protected void setProperty(String key, Object value) {
        // DANGER: This method mutates shared state!
        props.cfg().setLocalProperty(key, value);
    }

    protected void propertyOverrides(Map<String, Object> properties, CheckedRunnable testLogic) throws Throwable {
        Map<String, Object> oldProperties = new HashMap<>();
        try {
            properties.forEach((key, value) -> {
                oldProperties.put(key, props.cfg().getRawProperty(key));
                props.cfg().setLocalProperty(key, value);
            });
            testLogic.run();
        } finally {
            oldProperties.forEach((key, value) -> props.cfg().setLocalProperty(key, value));
        }
    }

    protected WebTestClient.ResponseSpec post(Object body, String uri) {
        return post(body, uri, null);
    }

    protected WebTestClient.ResponseSpec post(Object body, String uri, String session) {
        return post(body, uri, session, BRAND);
    }

    protected WebTestClient.ResponseSpec postTestDataOnly(Object body, String uri) {
        return post(body, uri, null, null);
    }

    protected WebTestClient.ResponseSpec post(Object body, String uri, String session, String brand) {
        var req = postTemplate(body, uri, session, brand, null, null, null, null)
                .accept(MediaType.APPLICATION_JSON);
        return req.exchange();
    }

    protected WebTestClient.ResponseSpec post(Object body, String uri, String session, String brand, String platform, String appVersion,
            String clientId) {
        var req = postTemplate(body, uri, session, brand, platform, appVersion, clientId, null)
                .accept(MediaType.APPLICATION_JSON);
        return req.exchange();
    }

    protected WebTestClient.ResponseSpec post(Object body, String uri, String session, String brand, String platform, String appVersion,
            String clientId, Map<String, String> headers) {
        var req = postTemplate(body, uri, session, brand, platform, appVersion, clientId, headers)
                .accept(MediaType.APPLICATION_JSON);
        return req.exchange();
    }

    public WebTestClient.RequestHeadersSpec<? extends WebTestClient.RequestHeadersSpec<?>> postTemplate(Object body,
            String uri) {
        return postTemplate(body, uri, null, null, null, null, null, null);
    }

    protected WebTestClient.RequestHeadersSpec<? extends WebTestClient.RequestHeadersSpec<?>> postTemplate(Object body,
            String uri, String session, String brand, String platform, String appVersion, String clientId,
            Map<String, String> headers) {

        String brandRequest = brand != null ? brand : BRAND;
        String platformRequest = platform != null ? platform : PLATFORM_WEB;
        String appVersionRequest = appVersion != null ? appVersion : "";
        String clientIdRequest = clientId != null ? clientId : "";

        var req = client.post()
                .uri("/v2/dispatch/%s?platform=%s&brandName=%s&appVersion=%s&clientId=%s".formatted(uri, platformRequest, brandRequest, appVersionRequest,
                        clientIdRequest))
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(body);

        if (!NO_SESSION.equals(session)) {
            String sessionRequest = session != null ? session : newSession();
            req = req.cookie(brandRequest.toUpperCase() + "SID", sessionRequest);
        }
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                req.header(header.getKey(), header.getValue());
            }
        }

        return req;
    }

    protected void awaitForNotification(long accountRemoteId, String expectedNotification) {
        var ws = wsClients.get(accountRemoteId);
        await().atMost(3, TimeUnit.SECONDS).until(() -> ws.got(expectedNotification));
    }

    protected void awaitForNotificationAndClear(long accountRemoteId, String expectedNotification) {
        awaitForNotification(accountRemoteId, expectedNotification);
        wsClients.values().forEach(WsClient::clear);
    }

    protected TestSession as(Account account) {
        return new TestSession(client, account.getBrand(),
                newSession(account.getRemoteId(), account.getBrand().getName(), account.getRemoteCode().split("/")[0]));
    }

    protected TestSession anonymously(Brand brand) {
        return new TestSession(client, brand);
    }
}
