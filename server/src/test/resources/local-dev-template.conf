cloud {
  application {
    space_name = kiev1-dev
  }
}

service.kafka.uri = "kafka://127.0.0.1:9092"
service.zk.uri = "zk://127.0.0.1:2181"
service.memcached.uri = "memcached://localhost:11211"
service.elastic-search.uri = "http://localhost:9200"
service.blue_dream-bloomreach.uri = "https://f7h6k59qmtoy1a6bdu8jlsrl6xt2thtqpmkdi3y4ptl38ydpyzma8wsmfkl7z1v3:<EMAIL>?projectToken=cef15744-da42-11ee-9e6e-fa5459ecd8f4"

app.data.start-clean=false
app.contracts-migration.enabled=false

#jdbc.slow-query-logger.enable=true
#jdbc.slow-query-logger.millis=5
