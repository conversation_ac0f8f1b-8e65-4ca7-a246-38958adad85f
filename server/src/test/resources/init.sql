SET TIME ZONE 'UTC';

-- Brands
INSERT INTO rnd.brands(id, name, code) VALUES (1, 'blue_dream', gen_random_uuid());
INSERT INTO rnd.brands(id, name, code) VALUES (2, 'blue_dream_2', gen_random_uuid());

-- Verticals
INSERT INTO rnd.verticals(type, created_at, modified_at, version) VALUES ('SHUFFLE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.verticals(type, created_at, modified_at, version) VALUES ('PICKEM', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

-- Sports
INSERT INTO rnd_data.sports(code, is_active, team_sport, created_at, modified_at, version) VALUES ('BASKETBALL', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.sports(code, is_active, team_sport, created_at, modified_at, version) VALUES ('HOCKEY', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.sports(code, is_active, team_sport, created_at, modified_at, version) VALUES ('SOCCER', true, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.sports(code, is_active, team_sport, created_at, modified_at, version) VALUES ('TENNIS', true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.sports(code, is_active, team_sport, created_at, modified_at, version) VALUES ('UNKNOWN', true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

-- Vertical Sports
INSERT INTO rnd.vertical_sports(vertical_id, sport_id) VALUES ((select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.sports where code = 'BASKETBALL'));
INSERT INTO rnd.vertical_sports(vertical_id, sport_id) VALUES ((select id from rnd.verticals where type = 'PICKEM'), (select id from rnd_data.sports where code = 'BASKETBALL'));
INSERT INTO rnd.vertical_sports(vertical_id, sport_id) VALUES ((select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.sports where code = 'HOCKEY'));
INSERT INTO rnd.vertical_sports(vertical_id, sport_id) VALUES ((select id from rnd.verticals where type = 'PICKEM'), (select id from rnd_data.sports where code = 'HOCKEY'));
INSERT INTO rnd.vertical_sports(vertical_id, sport_id) VALUES ((select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.sports where code = 'SOCCER'));
INSERT INTO rnd.vertical_sports(vertical_id, sport_id) VALUES ((select id from rnd.verticals where type = 'PICKEM'), (select id from rnd_data.sports where code = 'SOCCER'));
INSERT INTO rnd.vertical_sports(vertical_id, sport_id) VALUES ((select id from rnd.verticals where type = 'PICKEM'), (select id from rnd_data.sports where code = 'TENNIS'));

-- Leagues
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (1, 'NBA', 'NBA', 'NBA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (2, 'NHL', 'NHL', 'NHL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (3, 'ENG_PREMIER', 'EPL', 'ENG_PREMIER', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (4, 'MLS', 'MLS', 'MLS',  CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (5, 'UEFA_EURO', 'UEFA_EURO','UEFA_EURO', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (6, 'WTA', 'WTA','WTA', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (7, 'UNKNOWN', 'UNKNOWN','UNKNOWN', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 5);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (8, 'CWC', 'FIFA-CLUB-WORLD-CUP','FIFA-CLUB-WORLD-CUP', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3);

INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (9, 'TEST_BASKETBALL', 'TEST_BASKETBALL','TEST_BASKETBALL', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (10, 'TEST_SOCCER', 'TEST_SOCCER','TEST_SOCCER', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3);
INSERT INTO rnd_data.leagues(id, code, label, name, created_at, modified_at, version, sport_id) VALUES (11, 'TEST_TENNIS', 'TEST_TENNIS','TEST_TENNIS', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4);

-- Leagues config
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (1, 1, 1, true, 90, 'https://nba-logo-url', 'https://nba-logo-alt-url', 'https://nba-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (2, 2, 1, true, 70, 'https://nhl-logo-url', 'https://nhl-logo-alt-url', 'https://nhl-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (3, 3, 1, true, 50, 'https://eng-premier-logo-url', 'https://eng-premier-logo-alt-url', 'https://eng-premier-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (4, 4, 1, true, 60, 'https://mls-logo-url', 'https://mls-logo-alt-url', 'https://mls-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (5, 5, 1, true, 0, 'https://uefa-euro-logo-url', 'https://uefa-euro-logo-alt-url', 'https://uefa-euro-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (6, 6, 1, true, 0, 'https://wta-logo-url', 'https://wta-logo-alt-url', 'https://wta-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (7, 8, 1, true, 0, 'https://cwc-logo-url', 'https://cwc-logo-alt-url', 'https://cwc-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (8, 9, 1, true, 90, 'https://test_basketball-logo-url', 'https://test_basketball-logo-alt-url', 'https://test_basketball-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (9, 10, 1, true, 60, 'https://test_soccer-logo-url', 'https://test_soccer-logo-alt-url', 'https://test_soccer-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (10, 11, 1, true, 0, 'https://test_tennis-logo-url', 'https://test_tennis-logo-alt-url', 'https://test_tennis-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (11, 1, 2, true, 90, 'https://nba-logo-url', 'https://nba-logo-alt-url', 'https://nba-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (12, 2, 2, true, 70, 'https://nhl-logo-url', 'https://nhl-logo-alt-url', 'https://nhl-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (13, 3, 2, true, 50, 'https://eng-premier-logo-url', 'https://eng-premier-logo-alt-url', 'https://eng-premier-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (14, 4, 2, true, 60, 'https://mls-logo-url', 'https://mls-logo-alt-url', 'https://mls-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (15, 5, 2, true, 0, 'https://uefa-euro-logo-url', 'https://uefa-euro-logo-alt-url', 'https://uefa-euro-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (16, 6, 2, true, 0, 'https://wta-logo-url', 'https://wta-logo-alt-url', 'https://wta-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (17, 8, 2, true, 0, 'https://cwc-logo-url', 'https://cwc-logo-alt-url', 'https://cwc-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (18, 9, 2, true, 91, 'https://test_basketball-logo-url', 'https://test_basketball-logo-alt-url', 'https://test_basketball-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (19, 10, 2, true, 61, 'https://test_soccer-logo-url', 'https://test_soccer-logo-alt-url', 'https://test_soccer-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd_data.league_config(id, league_id, brand_id, is_active, display_order, logo_url, logo_alt_url, logo_small_alt_url, created_at, modified_at, version) VALUES (20, 11, 2, true, 11, 'https://test_tennis-logo-url', 'https://test_tennis-logo-alt-url', 'https://test_tennis-logo-small-alt-url', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

-- Data Providers
INSERT INTO rnd_data.data_providers(id, code) VALUES (1, 'SWISH_ANALYTICS');
INSERT INTO rnd_data.data_providers(id, code) VALUES (2, 'OPTIC_ODDS');
INSERT INTO rnd_data.data_providers(id, code) VALUES (3, 'TEST_DATA');

-- Data Provider Config
INSERT INTO rnd_data.data_provider_config(id, brand_name, vertical_type, sport_code, league_code, data_provider_code, weight) VALUES (1, null, null, null, null, 'SWISH_ANALYTICS', 1);
INSERT INTO rnd_data.data_provider_config(id, brand_name, vertical_type, sport_code, league_code, data_provider_code, weight) VALUES (2, null, null, 'SOCCER', null, 'OPTIC_ODDS', 10);
INSERT INTO rnd_data.data_provider_config(id, brand_name, vertical_type, sport_code, league_code, data_provider_code, weight) VALUES (4, null, null, 'BASKETBALL', 'TEST_BASKETBALL', 'TEST_DATA', 20);
INSERT INTO rnd_data.data_provider_config(id, brand_name, vertical_type, sport_code, league_code, data_provider_code, weight) VALUES (5, null, null, 'SOCCER', 'TEST_SOCCER', 'TEST_DATA', 20);
INSERT INTO rnd_data.data_provider_config(id, brand_name, vertical_type, sport_code, league_code, data_provider_code, weight) VALUES (6, null, null, 'TENNIS', 'TEST_TENNIS', 'TEST_DATA', 20);

-- Game Categories
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nba-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nba-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nba-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nba-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nba-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream_2'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nba-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream_2'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nba-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream_2'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nba-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NBA'), (select id from rnd.brands where name = 'blue_dream_2'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nhl-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nhl-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nhl-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-nhl-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nhl-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream_2'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nhl-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream_2'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nhl-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream_2'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-nhl-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'NHL'), (select id from rnd.brands where name = 'blue_dream_2'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-epl-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-epl-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-epl-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-epl-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-epl-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream_2'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-epl-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream_2'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-epl-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream_2'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-epl-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), (select id from rnd.brands where name = 'blue_dream_2'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-mls-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-mls-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-mls-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-mls-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-mls-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream_2'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-mls-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream_2'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-mls-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream_2'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream2-shuffle-mls-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'MLS'), (select id from rnd.brands where name = 'blue_dream_2'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-cwc-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'CWC'), (select id from rnd.brands where name = 'blue_dream'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_basketball-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_BASKETBALL'), (select id from rnd.brands where name = 'blue_dream'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_basketball-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_BASKETBALL'), (select id from rnd.brands where name = 'blue_dream'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_basketball-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_BASKETBALL'), (select id from rnd.brands where name = 'blue_dream'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_basketball-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_BASKETBALL'), (select id from rnd.brands where name = 'blue_dream'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_soccer-player', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_SOCCER'), (select id from rnd.brands where name = 'blue_dream'), 'PLAYER', true, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_soccer-team', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_SOCCER'), (select id from rnd.brands where name = 'blue_dream'), 'TEAM', true, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_soccer-match', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_SOCCER'), (select id from rnd.brands where name = 'blue_dream'), 'MATCH', true, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.game_categories(code, vertical_id, league_id, brand_id, type, is_active, display_order, created_at, modified_at, version) VALUES ('blue-dream-shuffle-test_soccer-day', (select id from rnd.verticals where type = 'SHUFFLE'), (select id from rnd_data.leagues where code = 'TEST_SOCCER'), (select id from rnd.brands where name = 'blue_dream'), 'DAY', true, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

-- Shuffle :: Random Multiplier Groups
INSERT INTO rnd.shuffle_random_multiplier_groups(code) values ('3-leg');
INSERT INTO rnd.shuffle_random_multiplier_groups(code) values ('4-leg');
INSERT INTO rnd.shuffle_random_multiplier_groups(code) values ('5-leg');
INSERT INTO rnd.shuffle_random_multiplier_groups(code) values ('free-to-play');
INSERT INTO rnd.shuffle_random_multiplier_groups(code) values ('million-game');

-- Shuffle :: Random multipliers
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='3-leg'), 1200.0, 1, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='3-leg'), 24.0, 30, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='3-leg'), 12.0, 61, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='3-leg'), 6, 700, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='3-leg'), 3, 700, 1492);

INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='4-leg'), 2400.0, 1, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='4-leg'), 48.0, 30, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='4-leg'), 24.0, 61, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='4-leg'), 14, 700, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='4-leg'), 4, 700, 1492);

INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='5-leg'), 4800.0, 1, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='5-leg'), 96.0, 30, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='5-leg'), 48.0, 61, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='5-leg'), 28, 700, 1492);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='5-leg'), 8, 700, 1492);

INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='free-to-play'), 37000.0, 10, 11335);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='free-to-play'), 400.0, 25, 11335);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='free-to-play'), 200.0, 50, 11335);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='free-to-play'), 40.0, 250, 11335);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='free-to-play'), 10.0, 1000, 11335);
INSERT INTO rnd.shuffle_random_multipliers(random_multiplier_group_id, multiplier, probability_numerator, probability_denominator) VALUES ( (select id from rnd.shuffle_random_multiplier_groups where code='free-to-play'), 1.0, 10000, 11335);

INSERT INTO rnd.shuffle_random_multipliers (random_multiplier_group_id, multiplier, probability_numerator, probability_denominator)
VALUES
    ((SELECT id FROM rnd.shuffle_random_multiplier_groups WHERE code='million-game'), 500000, 1, 1400556),
    ((SELECT id FROM rnd.shuffle_random_multiplier_groups WHERE code='million-game'), 10000, 55, 1400556),
    ((SELECT id FROM rnd.shuffle_random_multiplier_groups WHERE code='million-game'), 1000, 500, 1400556),
    ((SELECT id FROM rnd.shuffle_random_multiplier_groups WHERE code='million-game'), 30, 700000, 1400556),
    ((SELECT id FROM rnd.shuffle_random_multiplier_groups WHERE code='million-game'), 15, 700000, 1400556);

-- Shuffle :: Flex Coefficient Groups
INSERT INTO rnd.shuffle_flex_coefficient_groups(id, code, common_coefficient) VALUES (1, '3-leg', 0.5);
INSERT INTO rnd.shuffle_flex_coefficient_groups(id, code, common_coefficient) VALUES (2, '4-leg', 0.5);
INSERT INTO rnd.shuffle_flex_coefficient_groups(id, code, common_coefficient) VALUES (3, '5-leg', 0.5);

-- Shuffle :: Flex Coefficients
insert into rnd.shuffle_flex_coefficients(id, flex_coefficient_group_id, tier, coefficient) values (1, 1, 1, 1.0);
insert into rnd.shuffle_flex_coefficients(id, flex_coefficient_group_id, tier, coefficient) values (2, 1, 2, 0.34);

insert into rnd.shuffle_flex_coefficients(id, flex_coefficient_group_id, tier, coefficient) values (3, 2, 1, 1.0);
insert into rnd.shuffle_flex_coefficients(id, flex_coefficient_group_id, tier, coefficient) values (4, 2, 2, 0.25);

insert into rnd.shuffle_flex_coefficients(id, flex_coefficient_group_id, tier, coefficient) values (5, 3, 1, 1.0);
insert into rnd.shuffle_flex_coefficients(id, flex_coefficient_group_id, tier, coefficient) values (6, 3, 2, 0.2);

-- Teams
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-a','ext1','A','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,'https://logo.com/nba-team-a.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-b','ext2','B','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,'https://logo.com/nba-team-b.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-c','ext3','C','Team ::: C',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,'https://logo.com/nba-team-c.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-a_oo','ext4','A','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,2,'https://logo.com/nba-team-a_oo','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-b_oo','ext5','B','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,2,'https://logo.com/nba-team-b_oo.png','green');

INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-nhl-a','ext6','AA','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,1,'https://logo.com/nhl-team-nhl-a.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-nhl-b','ext7','BB','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,1,'https://logo.com/nhl-team-nhl-b.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-nhl-c','ext8','CC','Team ::: C',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,1,'https://logo.com/nhl-team-nhl-c.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-nhl-a_oo','ext9','AA','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,2,'https://logo.com/nhl-team-nhl-a_oo.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-nhl-b_oo','ext10','BB','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,2,'https://logo.com/nhl-team-nhl-b_oo.png','green');

INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-epl-a_oo','ext11','AA','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,2,'https://logo.com/eng_premier-team-epl-a_oo.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-epl-b_oo','ext12','BB','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,2,'https://logo.com/eng_premier-team-epl-b_oo.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-epl-c_oo','ext13','CC','Team ::: C',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,2,'https://logo.com/eng_premier-team-epl-c_oo.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-epl-a_s','ext14','AA','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,'https://logo.com/eng_premier-team-epl-a_s.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-epl-b_s','ext15','BB','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,'https://logo.com/eng_premier-team-epl-b_s.png','green');

INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-usa_mls-a_oo','ext16','AA','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,2,'https://logo.com/mls-team-usa_mls-a_oo.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-usa_mls-b_oo','ext17','BB','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,2,'https://logo.com/mls-team-usa_mls-b_oo.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-usa_mls-c_oo','ext18','CC','Team ::: C',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,2,'https://logo.com/mls-team-usa_mls-c_oo.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-usa_mls-a_s','ext19','AA','Team ::: A',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,'https://logo.com/mls-team-usa_mls-a_s.png','green');
INSERT INTO rnd_data.teams (code, external_id, abbreviation, name, created_at, modified_at, version, league_id, data_provider_id, jersey_logo_url, jersey_number_color) VALUES ('team-usa_mls-b_s','ext20','BB','Team ::: B',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,'https://logo.com/mls-team-usa_mls-b_s.png','green');
-- Tournaments
INSERT INTO rnd_data.tournaments (code, name, country, created_at, modified_at, version, league_id) VALUES ('default', 'Now', null, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);

-- Seasons
INSERT INTO rnd_data.seasons (code, name, start_at, end_at, current, created_at, modified_at, version, league_id, tournament_id) VALUES ('now', 'Now', CURRENT_DATE - 1, CURRENT_DATE, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 1);

-- Players
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p1', 'e1', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 1, 'Firstname1', 'Lastname1', 'PG', 1, 10, true);

INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p2', 'e2', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 2, 'Firstname2', 'Lastname2', 'PG', 1, 7, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p3', 'e3', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 3, 'Firstname3', 'Lastname3', 'PG', 1, 3, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p4', 'e4', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 1, 'Firstname4', 'Lastname4', 'PG', 1, 11, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p1oo', 'e5', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 1, 'Firstname1', 'Lastname1', 'PG', 2, 14, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p2oo', 'e6', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 2, 'Firstname2', 'Lastname2', 'PG', 2, 8, true);

INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p5', 'e7', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 6, 'Firstname1', 'Lastname1', 'PG', 1, 10, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p6', 'e8', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 7, 'Firstname2', 'Lastname2', 'PG', 1, 7, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p7', 'e9', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 8, 'Firstname3', 'Lastname3', 'PG', 1, 3, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p8', 'e10', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 6, 'Firstname4', 'Lastname4', 'PG', 1, 11, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p5oo', 'e11', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 6, 'Firstname1', 'Lastname1', 'PG', 2, 14, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p6oo', 'e12', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 7, 'Firstname2', 'Lastname2', 'PG', 2, 3, true);

INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p1_epl_oo', 'e13', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3, 11, 'Firstname1', 'Lastname1', 'PG', 2, 10, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p2_epl_oo', 'e14', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3, 12, 'Firstname2', 'Lastname2', 'PG', 2, 7, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p3_epl_oo', 'e15', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3, 13, 'Firstname3', 'Lastname3', 'PG', 2, 3, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p4_epl_oo', 'e16', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3, 11, 'Firstname4', 'Lastname4', 'PG', 2, 11, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p1_epl_s', 'e17', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3, 14, 'Firstname1', 'Lastname1', 'PG', 1, 14, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p2_epl_s', 'e18', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3, 15, 'Firstname2', 'Lastname2', 'PG', 1, 8, true);

INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p1_usa_mls_oo', 'e19', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4, 16, 'Firstname1', 'Lastname1', 'PG', 2, 10, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p2_usa_mls_oo', 'e20', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4, 17, 'Firstname2', 'Lastname2', 'PG', 2, 7, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p3_usa_mls_oo', 'e21', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4, 16, 'Firstname3', 'Lastname3', 'PG', 2, 3, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p4_usa_mls_oo', 'e22', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4, 16, 'Firstname4', 'Lastname4', 'PG', 2, 11, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p1_usa_mls_s', 'e23', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4, 16, 'Firstname1', 'Lastname1', 'PG', 1, 14, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('p2_usa_mls_s', 'e24', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4, 17, 'Firstname2', 'Lastname2', 'PG', 1, 3, true);

INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('mls-player-wo-team', 'e25', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 4, null, 'NoTeamFirstname', 'NoTeamLastname', 'PG', 2, 3, true);

INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active, logo_alt_url) VALUES ('player-a', 'e26', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 3, 15, 'Firstname1', 'Lastname1', 'PG', 1, 8, true, 'https://logoAltUrl-new-test.com/Firstname1-LastName1-a.png');

INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('wta-player-1', 'e27', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 6, null, 'Firstname1', 'Lastname1', null, 1, null, true);
INSERT INTO rnd_data.players (code, external_id, created_at, modified_at, version, league_id, team_id, first_name, last_name, position, data_provider_id, jersey, active) VALUES ('wta-player-2', 'e28', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 6, null, 'Firstname2', 'Lastname2', null, 1, null, true);

-- Matches
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k1','e1',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'),current_timestamp + INTERVAL '1 minute',null,null, 109.481, 109.790, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k2','e2',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'),current_timestamp + INTERVAL '2 minutes',null,null, 104.234, 110.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k3','e3',true,'CANCELED',CURRENT_DATE - 2,CURRENT_DATE + 1,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k4','e4',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'),current_timestamp + INTERVAL '3 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,2,1,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k4oo','e5',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '4 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,2,1,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k5','e6',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '3 minutes',null,null, 109.481, 109.790, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,1,6,7,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k6','e7',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '4 minutes',null,null, 104.234, 110.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,1,6,7,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k7','e8',true,'CANCELED',CURRENT_DATE - 2,CURRENT_DATE + 1,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k8','e9',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '5 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,1,7,6,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k8oo','e10',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '6 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,2,1,7,6,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('test','e11',false,'IN_PROGRESS',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '7 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,2,1,3);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k1+','e12',false,'SCHEDULED',TO_CHAR(current_timestamp + INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '5 minutes',null,null, 109.481, 109.790, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k2+','e13',false,'SCHEDULED',TO_CHAR(current_timestamp + INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '6 minutes',null,null, 104.234, 110.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);

INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k3final','e14',true,'FINISHED',CURRENT_DATE - 2,CURRENT_DATE -1,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k3inprogress','e15',true,'IN_PROGRESS',CURRENT_DATE,CURRENT_DATE -1,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,1,1,1,2,1);

INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k1_epl_oo','e16',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'),current_timestamp + INTERVAL '7 minutes',null,null, 109.481, 109.790, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,11,12,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k2_epl_oo','e17',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'),current_timestamp + INTERVAL '8 minutes',null,null, 104.234, 110.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,11,13,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k2_epl_s','e18',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'),  current_timestamp + INTERVAL '9 minutes',null,null, 102.5, 95.5, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,14,15,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k4_epl_oo','e19',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'),current_timestamp + INTERVAL '8 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,13,11,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k4_epl_s','e20',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '9 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,15,14,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k5_epl_oo','e21',true,'CANCELED',CURRENT_DATE - 2,CURRENT_DATE + 2,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,12,13,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k6_epl_oo','e22',true,'FINISHED',CURRENT_DATE - 2,CURRENT_DATE -2,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,11,12,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k7_epl_oo','e23',false,'IN_PROGRESS',CURRENT_DATE,CURRENT_DATE -3,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,3,1,12,11,2);

INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k1_usa_mls_oo','e24',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '10 minutes',null,null, 109.481, 109.790, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,16,17,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k2_usa_mls_oo','e25',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '11 minutes',null,null, 104.234, 110.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,16,18,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k2_usa_mls_s','e26',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '12 minutes',null,null, 102.5, 95.5, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,18,16,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k4_usa_mls_oo','e27',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '10 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,19,16,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k4_usa_mls_s','e28',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '11 hours',null,null, 102.234 ,95.284, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,20,16,1);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k5_usa_mls_oo','e29',true,'CANCELED',CURRENT_DATE - 2,CURRENT_DATE + 3,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,12,13,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k6_usa_mls_oo','e30',true,'FINISHED',CURRENT_DATE - 2,CURRENT_DATE -3,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,11,12,2);
INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, home_id, away_id, data_provider_id) VALUES ('k7_usa_mls_oo','e31',false,'IN_PROGRESS',CURRENT_DATE,CURRENT_DATE -4,null,null,null,null,false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,4,1,12,11,2);

INSERT INTO rnd_data.matches (code, external_id, closed, status, api_play_at, play_at, home_score, away_score, predicted_home_score, predicted_away_score, dirty, created_at, modified_at, version, league_id, season_id, player1_id, player2_id, data_provider_id) VALUES ('k1_wta','e32',false,'SCHEDULED',TO_CHAR(current_timestamp - INTERVAL '1 day', 'YYYY-MM-DD'), current_timestamp + INTERVAL '20 minutes',null,null, null, null, false,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,1,6,null,27,28,1);

-- Game Url Slugs
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (1, null, 1, 'firstname1-lastname1', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (2, null, 1, 'firstname2-lastname2', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (3, null, 1, 'firstname3-lastname3', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 1, 1, 'team-a', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 2, 1, 'team-b', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 3, 1, 'team-c', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 3, 1, 'some_slug', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (4, null, 1, 'firstname4-lastname4', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (5, null, 1, 'firstname1-lastname1', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (6, null, 1, 'firstname2-lastname2', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 4, 1, 'team-a', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 5, 1, 'team-b', 2);

INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (7, null, 2, 'firstname1-lastname1', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 6, 2, 'team-a', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 7, 2, 'team-b', 1);

INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (13, null, 3, 'p1_epl_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (14, null, 3, 'p2_epl_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (15, null, 3, 'p3_epl_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (16, null, 3, 'p4_epl_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (17, null, 3, 'p1_epl_s', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (18, null, 3, 'p2_epl_s', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 11, 3, 'team_epl_a_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 12, 3, 'team_epl_b_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 13, 3, 'team_epl_c_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 14, 3, 'team_epl_a_s', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 15, 3, 'team_epl_b_s', 1);

INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (19, null, 4, 'p1_usa_mls_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (20, null, 4, 'p2_usa_mls_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (21, null, 4, 'p3_usa_mls_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (22, null, 4, 'p4_usa_mls_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (23, null, 4, 'p1_usa_mls_s', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (24, null, 4, 'p2_usa_mls_s', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 16, 4, 'team_usa_mls_a_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 17, 4, 'team_usa_mls_b_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 18, 4, 'team_usa_mls_c_oo', 2);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 19, 4, 'team_usa_mls_a_s', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (null, 20, 4, 'team_usa_mls_b_s', 1);

INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (25, null, 4, 'noteamfirstname-noteamlastname', 2);

INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (27, null, 6, 'wta_p1', 1);
INSERT INTO rnd.game_url_slugs (player_id, team_id, league_id, url_slug, data_provider_id) VALUES (28, null, 6, 'wta_p2', 1);

-- Predicted Player Match Stats
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (1, 1, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (1, 2, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (2, 1, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 7.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (2, 2, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 7.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (4, 1, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (4, 2, 'HOME', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 6.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (1, 4, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', false, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (2, 4, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 7.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (4, 4, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (5, 5, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (5, 6, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (12, 1, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (12, 2, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (13, 1, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 7.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (13, 2, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 7.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);

INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (6, 7, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (6, 8, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (7, 7, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (7, 8, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (9, 7, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (9, 8, 'HOME', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', false, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (6, 10, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (7, 10, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (9, 10, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (10, 11, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (10, 12, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true, '{}', false);

INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (16, 13, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (16, 16, 'HOME', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (17, 13, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (17, 16, 'HOME', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (18, 17, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (18, 18, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Defensive Midfielder', false, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (19, 13, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (19, 15, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (20, 17, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (20, 18, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (21, 14, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (21, 15, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (22, 14, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (22, 16, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (23, 14, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (23, 13, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);

INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (24, 19, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (24, 22, 'HOME', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (25, 19, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (25, 22, 'HOME', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (26, 23, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (26, 24, 'AWAY', 30.5, 5.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Defensive Midfielder', false, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (27, 19, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (27, 21, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (28, 23, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (28, 24, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (29, 20, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (29, 21, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (30, 20, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (30, 22, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (31, 20, 'HOME', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (31, 19, 'AWAY', 20.5, 10.5, 4.5, 3.5, 6.5, 3.5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true, '{}', false);

INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, aces, points_won, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (32, 27, 5, 10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);
INSERT INTO rnd_data.predicted_player_match_stats(match_id, player_id, aces, points_won, created_at, modified_at, version, data_provider_id, position, is_starting_line, disabled_pick_em_metrics, is_pick_em_disabled) VALUES (32, 28, 5, 10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true, '{}', false);

-- Actual Player Match Stats
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 1, 1, 'HOME', 25, 8, 5, 3, 7, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 1, 2, 'AWAY', 29, 5, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 2, 1, 'HOME', 21, 10, 4, 3, 6, 3, 7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 2, 2, 'AWAY', 30, 5, 4, 3, 6, 3, 7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 4, 1, 'AWAY', 24, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 4, 2, 'HOME', 50, 5, 4, 3, 6, 3, 6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', false);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 1, 4, 'HOME', 10, 10, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 2, 4, 'HOME', 10, 10, 4, 3, 6, 3, 7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 4, 4, 'AWAY', 19, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 5, 5, 'AWAY', 17, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NBA', 5, 6, 'AWAY', 26, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true);

INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 6, 7, 'HOME', 25, 8, 5, 3, 7, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 6, 8, 'AWAY', 29, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 7, 7, 'HOME', 21, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 7, 8, 'AWAY', 30, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 9, 7, 'AWAY', 24, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 9, 8, 'HOME', 50, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'SF', false);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 6, 10, 'HOME', 10, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 7, 10, 'HOME', 10, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 9, 10, 'AWAY', 19, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 10, 11, 'AWAY', 17, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('NHL', 10, 12, 'AWAY', 26, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'PF', true);

INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 16, 13, 'HOME', 21, 11, 4, 3, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 16, 16, 'HOME', 33, 6, 4, 3, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 17, 13, 'HOME', 15, 11, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 17, 16, 'HOME', 31, 12, 5, 3, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 18, 17, 'HOME', 21, 12, 5, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 18, 18, 'AWAY', 33, 6, 5, 4, 6, 5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Defensive Midfielder', false);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 19, 13, 'AWAY', 21, 11, 5, 4, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 19, 15, 'HOME', 22, 11, 4, 3, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 20, 17, 'AWAY', 11, 11, 4, 4, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 20, 18, 'HOME', 21, 13, 4, 4, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 21, 14, 'HOME', 23, 12, 4, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 21, 15, 'AWAY', 22, 12, 3, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 22, 14, 'AWAY', 25, 11, 5, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 22, 16, 'HOME', 21, 5, 6, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 23, 14, 'HOME', 15, 5, 4, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('ENG_PREMIER', 23, 13, 'AWAY', 16, 5, 4, 4, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);

INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 24, 19, 'HOME', 21, 11, 4, 3, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 22, 22, 'HOME', 33, 6, 4, 3, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 25, 19, 'HOME', 15, 11, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 25, 22, 'HOME', 31, 12, 5, 3, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 26, 23, 'HOME', 21, 12, 5, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 26, 24, 'AWAY', 33, 6, 5, 4, 6, 5, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Defensive Midfielder', false);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 27, 19, 'AWAY', 21, 11, 5, 4, 7, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 27, 21, 'HOME', 22, 11, 4, 3, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 28, 23, 'AWAY', 11, 11, 4, 4, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 28, 24, 'HOME', 21, 13, 4, 4, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 29, 20, 'HOME', 23, 12, 4, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 29, 21, 'AWAY', 22, 12, 3, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Defensive Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 30, 20, 'AWAY', 25, 11, 5, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 30, 22, 'HOME', 21, 5, 6, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Attacking Midfielder', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 31, 20, 'HOME', 15, 5, 4, 4, 7, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);
INSERT INTO rnd_data.player_match_stats(type, match_id, player_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id, position, is_starting_line) VALUES ('MLS', 31, 19, 'AWAY', 16, 5, 4, 4, 6, 4, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2, 'Striker', true);

-- Predicted Team Match Stats
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 1, 1, 'HOME', 20, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 1, 2, 'AWAY', 30, 5, 4, 3, 6, 3, 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 4, 2, 'HOME', 30, 5, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 4, 1, 'AWAY', 20, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 2, 1, 'HOME', 30, 5, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 2, 2, 'AWAY', 20, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 3, 1, 'HOME', 30, 5, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 3, 2, 'AWAY', 20, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 5, 4, 'HOME', 30, 5, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 5, 5, 'AWAY', 20, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);

INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 6, 6, 'HOME', 20, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 6, 7, 'AWAY', 30, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 9, 7, 'HOME', 30, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 9, 6, 'AWAY', 20, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 7, 6, 'HOME', 30, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 7, 7, 'AWAY', 20, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 8, 6, 'HOME', 30, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 8, 7, 'AWAY', 20, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 10, 9, 'HOME', 30, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 10, 10, 'AWAY', 20, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);

INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 16, 11, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 16, 12, 'AWAY', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 17, 11, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 17, 13, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 18, 14, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 18, 15, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 19, 13, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 19, 11, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 20, 15, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 20, 14, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 21, 12, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 21, 13, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 22, 11, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 22, 12, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 23, 12, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 23, 11, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);

INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 24, 16, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 24, 17, 'AWAY', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 25, 16, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 25, 18, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 26, 19, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 26, 20, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 27, 18, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 27, 16, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 28, 20, 'HOME', 30, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 28, 19, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 29, 17, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 29, 18, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 30, 16, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 30, 17, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 31, 17, 'HOME', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.predicted_team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 31, 16, 'AWAY', 20, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);

-- Actual Team Match Stats
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 1, 1, 'HOME', 25, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 1, 2, 'AWAY', 45, 5, 4, 3, 6, 9, 5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 4, 2, 'HOME', 27, 5, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 4, 1, 'AWAY', 23, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 2, 1, 'HOME', 50, 5, 4, 3, 2, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 2, 2, 'AWAY', 15, 10, 5, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 3, 1, 'HOME', 34, 5, 4, 3, 6, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 3, 2, 'AWAY', 52, 10, 4, 3, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 5, 4, 'HOME', 30, 5, 3, 3, 9, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, steals, assists, three_pointers_made, rebounds, blocked_shots, turnovers, created_at, modified_at, version, data_provider_id) VALUES ('NBA', 5, 5, 'AWAY', 20, 10, 4, 1, 6, 3, 4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);

INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 6, 6, 'HOME', 25, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 6, 7, 'AWAY', 45, 5, 4, 3, 6, 9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 9, 7, 'HOME', 27, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 9, 6, 'AWAY', 23, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 7, 6, 'HOME', 50, 5, 4, 3, 2, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 7, 7, 'AWAY', 15, 10, 5, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 8, 6, 'HOME', 34, 5, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 8, 7, 'AWAY', 52, 10, 4, 3, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 10, 9, 'HOME', 30, 5, 3, 3, 9, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, points, goals, assists, power_play_points, saves, shots_on_goal, created_at, modified_at, version, data_provider_id) VALUES ('NHL', 10, 10, 'AWAY', 20, 10, 4, 1, 6, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);

INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 16, 11, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 16, 12, 'AWAY', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 17, 11, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 17, 13, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 18, 14, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 18, 15, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 19, 13, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 19, 11, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 20, 15, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 20, 14, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 21, 12, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 21, 13, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 22, 11, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 22, 12, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 23, 12, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('ENG_PREMIER', 23, 11, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);

INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 24, 16, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 24, 17, 'AWAY', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 25, 16, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 25, 18, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 26, 19, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 26, 20, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 27, 18, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 27, 16, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 28, 20, 'HOME', 31, 5, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 28, 19, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 1);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 29, 17, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 29, 18, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 30, 16, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 30, 17, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 31, 17, 'HOME', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);
INSERT INTO rnd_data.team_match_stats(type, match_id, team_id, qualifier, shots, shots_on_target, goals, assists, fouls, tackles, pass_attempts, saves, created_at, modified_at, version, data_provider_id) VALUES ('MLS', 31, 16, 'AWAY', 21, 10, 4, 3, 6, 3, 10, 2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 2);

-- Correlation
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (1, true, 'PG', 1, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (2, false, 'PG', 0, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (3, true, 'SF', 1, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (4, false, 'SF', 2, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (5, true, 'C', 0, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (6, false, 'C', 1, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (7, true, 'SG', 0, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (8, false, 'SG', 2, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (9, true, 'PF', 0, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);
INSERT INTO rnd_data.correlation_default_clusters(id, starting, position, cluster, created_at, modified_at, version) VALUES (10, false, 'PF', 1, '2024-03-13 16:57:15.65+00', '2024-03-13 16:57:15.65+00', 1);

-- Shuffle :: Leg Templates
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'),  gen_random_uuid(), true, 'points', 70, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-player'),  gen_random_uuid(), true, 'points', 70, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-player'),  gen_random_uuid(), true, 'goals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-player'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-player'),  gen_random_uuid(), true, 'powerPlayPoints', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-player'),  gen_random_uuid(), true, 'saves', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-player'),  gen_random_uuid(), true, 'shotsOnGoal', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-team'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-team'),  gen_random_uuid(), true, 'goals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-team'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-team'),  gen_random_uuid(), true, 'powerPlayPoints', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-team'),  gen_random_uuid(), true, 'saves', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-team'),  gen_random_uuid(), true, 'shotsOnGoal', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-match'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-match'),  gen_random_uuid(), true, 'goals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-match'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-match'),  gen_random_uuid(), true, 'powerPlayPoints', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-match'),  gen_random_uuid(), true, 'saves', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-match'),  gen_random_uuid(), true, 'shotsOnGoal', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'),  gen_random_uuid(), true, 'goals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'),  gen_random_uuid(), true, 'powerPlayPoints', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'),  gen_random_uuid(), true, 'saves', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'),  gen_random_uuid(), true, 'shotsOnGoal', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'shots', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'assists', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'fouls', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'shotsOnTarget', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'goals', 100, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'saves', 80, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'passAttempts', 30, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'),  gen_random_uuid(), true, 'tackles', 50, 1.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);


INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'),  gen_random_uuid(), true, 'points', 70, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);

INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'),  gen_random_uuid(), true, 'points', 100, 16.2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'),  gen_random_uuid(), true, 'steals', 50, 1.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'),  gen_random_uuid(), true, 'assists', 50, 4.5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'),  gen_random_uuid(), true, 'rebounds', 50, 5.6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'),  gen_random_uuid(), true, 'threePointersMade', 50, 2.0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'),  gen_random_uuid(), true, 'turnovers', 50, 2.1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);
INSERT INTO rnd.shuffle_leg_templates(game_category_id, code, is_active, metric, weight, min_projection, created_at, modified_at, version) VALUES ((select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'),  gen_random_uuid(), true, 'blockedShots', 50, 0.4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1);


--- Shuffle game templates
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nba-player', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nba-team', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nba-match', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nba-day', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nba-free-to-play', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nba-million-game', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nba-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nba-player', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nba-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nba-team', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nba-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nba-match', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nba-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nba-day', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nba-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nba-free-to-play', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nba-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nba-million-game', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nba-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nhl-player', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nhl-team', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nhl-match', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nhl-day', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nhl-free-to-play', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-nhl-million-game', (select id from rnd.game_categories where code = 'blue-dream-shuffle-nhl-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nhl-player', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nhl-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nhl-team', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nhl-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nhl-match', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nhl-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nhl-day', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nhl-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nhl-free-to-play', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nhl-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-nhl-million-game', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-nhl-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-epl-player', (select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-epl-team', (select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-epl-match', (select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-epl-day', (select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-epl-free-to-play', (select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-epl-million-game', (select id from rnd.game_categories where code = 'blue-dream-shuffle-epl-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-epl-player', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-epl-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-epl-team', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-epl-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-epl-match', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-epl-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-epl-day', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-epl-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-epl-free-to-play', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-epl-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-epl-million-game', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-epl-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-mls-player', (select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-mls-team', (select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-mls-match', (select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-mls-day', (select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-mls-free-to-play', (select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-mls-million-game', (select id from rnd.game_categories where code = 'blue-dream-shuffle-mls-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-mls-player', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-mls-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-mls-team', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-mls-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-mls-match', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-mls-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-mls-day', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-mls-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-mls-free-to-play', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-mls-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream2-mls-million-game', (select id from rnd.game_categories where code = 'blue-dream2-shuffle-mls-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_basketball-player', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_basketball-team', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_basketball-match', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_basketball-day', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_basketball-free-to-play', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_basketball-million-game', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_basketball-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_soccer-player', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_soccer-player'), true, 3, 3, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '3-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_soccer-team', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_soccer-team'), true, 4, 4, -0.5, 0.7, 2.00, 1000.00, 0.20, 100.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '4-leg'), null, now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_soccer-match', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_soccer-match'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_soccer-day', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_soccer-day'), true, 5, 5, -0.5, 0.7, 2.00, 1000.00, 0.20, 50.00, 100, false, (select id from rnd.shuffle_random_multiplier_groups where code = '5-leg'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_soccer-free-to-play', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_soccer-day'), true, 5, 5, -0.5, 0.7, 1.00, 1.00, 0.00, 0.00, 1, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'free-to-play'), (select id from rnd.shuffle_flex_coefficient_groups where code = '5-leg'), now(), now(), 1);
INSERT INTO rnd.shuffle_game_templates(code, game_category_id, is_active, min_legs, max_legs, correlation_threshold_min, correlation_threshold_max, gc_entry_min, gc_entry_max, sc_entry_min, sc_entry_max, max_number_of_tickets, is_free_to_play, is_million_game, random_multiplier_group_id, flex_coefficient_group_id, created_at, modified_at, version) VALUES ('blue-dream-test_soccer-million-game', (select id from rnd.game_categories where code = 'blue-dream-shuffle-test_soccer-day'), true, 5, 5, -0.5, 0.7, 400, 400, 2, 2, 100, false, true, (select id from rnd.shuffle_random_multiplier_groups where code = 'million-game'), null, now(), now(), 1);

-- PickEm :: Multiplier Groups
INSERT INTO rnd.pick_em_multiplier_groups(code, label) values('standard', 'Standard');

-- PickEm :: Multipliers
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 2, 3);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier, flex_multipliers, flex_fixed_payout) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 3, 6, '{2.25,1.25}', 6);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 4, 11);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 5, 20);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 6, 40);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 7, 70);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 8, 130);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 9, 250);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 10, 450);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 11, 800);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (1, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 12, 1500);

INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 2, 3);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier, flex_multipliers, flex_fixed_payout) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 3, 6, '{2.25,1.25}', 6);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 4, 11);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 5, 20);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 6, 40);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 7, 70);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 8, 130);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 9, 250);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 10, 450);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 11, 800);
INSERT INTO rnd.pick_em_multipliers(brand_id, multiplier_group_id, picks, multiplier) values (2, (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 12, 1500);



-- PickEm :: Entry Templates
INSERT INTO rnd.pick_em_entry_templates(is_active, brand_id, multiplier_group_id, usd_entry_min, usd_entry_max, code, version) VALUES (true, (SELECT id FROM rnd.brands where name = 'blue_dream'), (SELECT id FROM rnd.pick_em_multiplier_groups where code = 'standard'), 1, 100, 'standard', 1);

-- PickEm :: Metrics
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'NBA'), true, now(), now(), 1, 5, 'points_code', 'points', 7);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'NBA'), true, now(), now(), 1, 2, 'steals_code', 'steals', 6);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'NBA'), true, now(), now(), 1, 5, 'assists_code', 'assists', 5);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'NBA'), true, now(), now(), 1, 2, 'three_pointers_made_code', 'three_pointers_made', 4);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'NBA'), true, now(), now(), 1, 5, 'rebounds_code', 'rebounds', 3);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'NBA'), true, now(), now(), 1, 2, 'blocked_shots_code', 'blocked_shots', 2);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'NBA'), true, now(), now(), 1, 5, 'turnovers_code', 'turnovers', 1);

INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_shots_code', 'shots', 8);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_assists_code', 'assists', 7);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_shots_on_target_code', 'shots_on_target', 6);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_goals_code', 'goals', 5);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_saves_code', 'saves', 4);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_passing_attempts_code', 'passing_attempts', 3);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_tackles_code', 'tackles', 2);
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, created_at, modified_at, version, min_projection, code, metric, display_order) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'ENG_PREMIER'), true, now(), now(), 1, 2, 'epl_fouls_code', 'fouls', 1);

INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 1, now(), now(), 1, 0.100, 'mls_shots', 'shots');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 2, now(), now(), 1, 0.100, 'mls_saves', 'saves');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 3, now(), now(), 1, 0.100, 'mls_pass_attempts', 'passAttempts');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 4, now(), now(), 1, 0.100, 'mls_assists', 'assists');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 5, now(), now(), 1, 0.100, 'mls_shots_on_target', 'shotsOnTarget');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 6, now(), now(), 1, 0.100, 'mls_goals', 'goals');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 7, now(), now(), 1, 0.100, 'mls_player_tackles', 'tackles');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'MLS'), true, 8, now(), now(), 1, 0.100, 'mls_player_fouls', 'fouls');

INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'WTA'), true, 9, now(), now(), 1, 0.100, 'wta_aces', 'aces');
INSERT INTO rnd.pick_em_metrics (brand_id, league_id, is_active, display_order, created_at, modified_at, version, min_projection, code, metric) VALUES ((SELECT id FROM rnd.brands where name = 'blue_dream'), (select id from rnd_data.leagues where code = 'WTA'), true, 10, now(), now(), 1, 0.100, 'wta_points_won', 'pointsWon');

insert into rnd.account_location_policy (brand_id, vertical_type, country_code, allowed_states, version, created_at, modified_at) VALUES ((select id from rnd.brands where name = 'blue_dream'), 'SHUFFLE', 'US', '{CA}', 1, now(), now());
insert into rnd.account_location_policy (brand_id, vertical_type, country_code, allowed_states, version, created_at, modified_at) VALUES ((select id from rnd.brands where name = 'blue_dream'), 'PICKEM', 'US', '{CA}', 1, now(), now());

insert into rnd.shuffle_game_stats (slug, current, previous, league_id, play_at, created_at, modified_at, version) VALUES ('team-a-vs-team-b', 12, 6, 1, now() + interval '58 hours', now(), now(), 1);
insert into rnd.shuffle_game_stats (slug, current, previous, league_id, play_at, created_at, modified_at, version) VALUES ('p4_usa_mls_oo', 2, 3, 4, now() + interval '8 hours', now(), now(), 1);

INSERT INTO rnd.reward_templates(code, vertical_id, brand_id, expiry_days, multiplier, entry_amount, entry_max, type, entry_currency,created_at, modified_at, version) VALUES (gen_random_uuid(), (select id from rnd.verticals where type = 'SHUFFLE'), (SELECT id FROM rnd.brands where name = 'blue_dream'), 5, 3, null, 200, 'BLITZ_EM_BOOST', 'GC', now(), now(), 1);
INSERT INTO rnd.reward_templates(code, vertical_id, brand_id, expiry_days, multiplier, entry_amount, entry_max, type, entry_currency,created_at, modified_at, version) VALUES (gen_random_uuid(), (select id from rnd.verticals where type = 'SHUFFLE'), (SELECT id FROM rnd.brands where name = 'blue_dream'), 7, null, 25, null, 'FREE_BLITZ', 'GC', now(), now(), 1);
INSERT INTO rnd.reward_templates(code, vertical_id, brand_id, expiry_days, multiplier, entry_amount, entry_max, type, entry_currency,created_at, modified_at, version) VALUES (gen_random_uuid(), (select id from rnd.verticals where type = 'PICKEM'), (SELECT id FROM rnd.brands where name = 'blue_dream'), 4, 1.5, null, 150, 'PICK_EM_BOOST', 'USD', now(), now(), 1);
INSERT INTO rnd.reward_templates(code, vertical_id, brand_id, expiry_days, multiplier, entry_amount, entry_max, type, entry_currency,created_at, modified_at, version) VALUES (gen_random_uuid(), (select id from rnd.verticals where type = 'PICKEM'), (SELECT id FROM rnd.brands where name = 'blue_dream'), 6, null, 5, null, 'FREE_PICK', 'USD', now(), now(), 1);

INSERT INTO rnd.reward_bundles(code, active, created_at, modified_at, brand_id) VALUES ('test-full-bundle', true, now(), now(), (SELECT id FROM rnd.brands where name = 'blue_dream'));

INSERT INTO rnd.reward_bundle_templates(reward_bundle_id, reward_template_id, rewards_count, created_at, modified_at) VALUES ((select id from rnd.reward_bundles where code = 'test-full-bundle'), (select id from rnd.reward_templates where type = 'BLITZ_EM_BOOST'), 5, now(), now());
INSERT INTO rnd.reward_bundle_templates(reward_bundle_id, reward_template_id, rewards_count, created_at, modified_at) VALUES ((select id from rnd.reward_bundles where code = 'test-full-bundle'), (select id from rnd.reward_templates where type = 'FREE_BLITZ'), 3, now(), now());
INSERT INTO rnd.reward_bundle_templates(reward_bundle_id, reward_template_id, rewards_count, created_at, modified_at) VALUES ((select id from rnd.reward_bundles where code = 'test-full-bundle'), (select id from rnd.reward_templates where type = 'PICK_EM_BOOST'), 1, now(), now());
INSERT INTO rnd.reward_bundle_templates(reward_bundle_id, reward_template_id, rewards_count, created_at, modified_at) VALUES ((select id from rnd.reward_bundles where code = 'test-full-bundle'), (select id from rnd.reward_templates where type = 'FREE_PICK'), 4, now(), now());

INSERT INTO rnd_data.brand_props(brand_id, match_id, player_id, team_id, metric, type, line, upcoming, balanced, under_probability, over_probability, rake, created_at, modified_at, version) VALUES
    ((select id from rnd.brands where name = 'blue_dream'), 1, 1, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 1, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 1, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 1, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 1, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 1, 1, 'turnovers', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 1, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 2, 2, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 2, 2, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 2, 2, 'steals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 2, 2, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 2, 2, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 2, 2, 'turnovers', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 2, 2, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 1, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 1, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 1, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 1, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 1, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 1, 1, 'turnovers', 'ALTERNATIVE', 7.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 1, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 2, 2, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 2, 2, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 2, 2, 'steals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 2, 2, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 2, 2, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 2, 2, 'turnovers', 'ALTERNATIVE', 7.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 2, 2, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 1, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 1, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 1, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 1, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 1, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 1, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 2, 2, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 2, 2, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 2, 2, 'steals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 2, 2, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 2, 2, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 2, 2, 'turnovers', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 2, 2, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 4, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 4, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 4, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 4, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 4, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 4, 1, 'turnovers', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 1, 4, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 4, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 4, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 4, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 4, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 4, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 4, 1, 'turnovers', 'ALTERNATIVE', 7.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 2, 4, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 4, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 4, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 4, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 4, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 4, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 4, 1, 'turnovers', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 4, 4, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 5, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 5, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 5, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 5, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 5, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 5, 1, 'turnovers', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 5, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 6, 2, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 6, 2, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 6, 2, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 6, 2, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 6, 2, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 6, 2, 'turnovers', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 5, 6, 2, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 1, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 1, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 1, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 1, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 1, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 1, 1, 'turnovers', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 1, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 2, 2, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 2, 2, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 2, 2, 'steals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 2, 2, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 2, 2, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 2, 2, 'turnovers', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 12, 2, 2, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 1, 1, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 1, 1, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 1, 1, 'steals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 1, 1, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 1, 1, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 1, 1, 'turnovers', 'ALTERNATIVE', 7.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 1, 1, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 2, 2, 'rebounds', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 2, 2, 'blockedShots', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 2, 2, 'steals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 2, 2, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 2, 2, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 2, 2, 'turnovers', 'ALTERNATIVE', 7.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 13, 2, 2, 'threePointersMade', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 7, 6, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 7, 6, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 7, 6, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 7, 6, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 7, 6, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 7, 6, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 8, 7, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 8, 7, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 8, 7, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 8, 7, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 8, 7, 'goals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 8, 7, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 7, 6, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 7, 6, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 7, 6, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 7, 6, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 7, 6, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 7, 6, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 8, 7, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 8, 7, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 8, 7, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 8, 7, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 8, 7, 'goals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 8, 7, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 7, 6, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 7, 6, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 7, 6, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 7, 6, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 7, 6, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 7, 6, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 8, 7, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 8, 7, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 8, 7, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 8, 7, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 8, 7, 'goals', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 8, 7, 'points', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 10, 6, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 10, 6, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 10, 6, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 10, 6, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 10, 6, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 6, 10, 6, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 10, 6, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 10, 6, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 10, 6, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 10, 6, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 10, 6, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 7, 10, 6, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 10, 6, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 10, 6, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 10, 6, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 10, 6, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 10, 6, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 9, 10, 6, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 11, 6, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 11, 6, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 11, 6, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 11, 6, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 11, 6, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 11, 6, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 12, 7, 'saves', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 12, 7, 'assists', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 12, 7, 'shotsOnGoal', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 12, 7, 'powerPlayPoints', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 12, 7, 'goals', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 10, 12, 7, 'points', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 13, 11, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'shotsOnTarget', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'shots', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 16, 16, 11, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 13, 11, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'shotsOnTarget', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'shots', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 17, 16, 11, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 17, 14, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'shotsOnTarget', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'shots', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 18, 18, 15, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 13, 11, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 19, 15, 13, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 17, 14, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 20, 18, 15, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 14, 12, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 21, 15, 13, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 14, 12, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 22, 16, 11, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 14, 12, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 23, 13, 11, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 19, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'shotsOnTarget', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'shots', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 24, 22, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 19, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'shotsOnTarget', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'shots', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 25, 22, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 23, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'shotsOnTarget', 'ALTERNATIVE', 5.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'shots', 'ALTERNATIVE', 30.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 26, 24, 17, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 19, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 27, 21, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 23, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 28, 24, 17, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 20, 17, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 29, 21, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 20, 17, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 30, 22, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 20, 17, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'fouls', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'saves', 'ALTERNATIVE', 2.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'assists', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'passAttempts', 'ALTERNATIVE', 10.0, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'shotsOnTarget', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'tackles', 'ALTERNATIVE', 3.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'shots', 'ALTERNATIVE', 20.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 31, 19, 16, 'goals', 'ALTERNATIVE', 4.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 32, 27, null, 'aces', 'ALTERNATIVE', 6.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1),
    ((select id from rnd.brands where name = 'blue_dream'), 32, 28, null, 'aces', 'ALTERNATIVE', 10.5, true, true, 0.5, 0.5, 0.13, now(), now(), 1);

INSERT INTO rnd_data.starter_lineups(match_id, player_id, starter, position, created_at, modified_at, version) VALUES
  (1, 1, true, 'PF', now(), now(), 1),
  (1, 2, true, 'PF', now(), now(), 1),
  (2, 1, true, 'PF', now(), now(), 1),
  (2, 2, true, 'PF', now(), now(), 1),
  (4, 1, true, 'PF', now(), now(), 1),
  (4, 2, true, 'SF', now(), now(), 1),
  (1, 4, false, 'SF', now(), now(), 1),
  (2, 4, true, 'PF', now(), now(), 1),
  (4, 4, true, 'PF', now(), now(), 1),
  (5, 5, true, 'PF', now(), now(), 1),
  (5, 6, true, 'PF', now(), now(), 1),
  (12, 1, true, 'PF', now(), now(), 1),
  (12, 2, true, 'PF', now(), now(), 1),
  (13, 1, true, 'PF', now(), now(), 1),
  (13, 2, true, 'PF', now(), now(), 1),
  (6, 7, true, 'PF', now(), now(), 1),
  (6, 8, true, 'PF', now(), now(), 1),
  (7, 7, true, 'PF', now(), now(), 1),
  (7, 8, true, 'PF', now(), now(), 1),
  (9, 7, true, 'SF', now(), now(), 1),
  (9, 8, false, 'SF', now(), now(), 1),
  (6, 10, true, 'PF', now(), now(), 1),
  (7, 10, true, 'PF', now(), now(), 1),
  (9, 10, true, 'PF', now(), now(), 1),
  (10, 11, true, 'PF', now(), now(), 1),
  (10, 12, true, 'PF', now(), now(), 1),
  (16, 13, true, 'Attacking Midfielder', now(), now(), 1),
  (16, 16, true, 'Defensive Midfielder', now(), now(), 1),
  (17, 13, true, 'Striker', now(), now(), 1),
  (17, 16, true, 'Defensive Midfielder', now(), now(), 1),
  (18, 17, true, 'Striker', now(), now(), 1),
  (18, 18, false, 'Defensive Midfielder', now(), now(), 1),
  (19, 13, true, 'Striker', now(), now(), 1),
  (19, 15, true, 'Attacking Midfielder', now(), now(), 1),
  (20, 17, true, 'Striker', now(), now(), 1),
  (20, 18, true, 'Striker', now(), now(), 1),
  (21, 14, true, 'Attacking Midfielder', now(), now(), 1),
  (21, 15, true, 'Defensive Midfielder', now(), now(), 1),
  (22, 14, true, 'Attacking Midfielder', now(), now(), 1),
  (22, 16, true, 'Attacking Midfielder', now(), now(), 1),
  (23, 14, true, 'Striker', now(), now(), 1),
  (23, 13, true, 'Striker', now(), now(), 1),
  (24, 19, true, 'Attacking Midfielder', now(), now(), 1),
  (24, 22, true, 'Defensive Midfielder', now(), now(), 1),
  (25, 19, true, 'Striker', now(), now(), 1),
  (25, 22, true, 'Defensive Midfielder', now(), now(), 1),
  (26, 23, true, 'Striker', now(), now(), 1),
  (26, 24, false, 'Defensive Midfielder', now(), now(), 1),
  (27, 19, true, 'Striker', now(), now(), 1),
  (27, 21, true, 'Attacking Midfielder', now(), now(), 1),
  (28, 23, true, 'Striker', now(), now(), 1),
  (28, 24, true, 'Striker', now(), now(), 1),
  (29, 20, true, 'Attacking Midfielder', now(), now(), 1),
  (29, 21, true, 'Defensive Midfielder', now(), now(), 1),
  (30, 20, true, 'Attacking Midfielder', now(), now(), 1),
  (30, 22, true, 'Attacking Midfielder', now(), now(), 1),
  (31, 20, true, 'Striker', now(), now(), 1),
  (31, 19, true, 'Striker', now(), now(), 1);