package engagement;

import static engagement.EngagementProto.ENGAGEMENT_CRM_POSTGRES_APP;
import static engagement.EngagementProto.ENGAGEMENT_CRM_POSTGRES_OWNER;
import static engagement.EngagementProto.ENGAGEMENT_CRM_QUARTZ_APP;
import static engagement.EngagementProto.UPS_CLOUDFLARE_STREAM;
import static engagement.EngagementProto.UPS_CLOUDFLARE_TURN;
import static engagement.EngagementProto.UPS_CLOUDFLARE_WEBHOOK;

import com.google.common.collect.ImmutableSet;
import com.turbospaces.ups.UPSs;

import common.UPSsCollection;

public class EngagementServerUPSs extends ImmutableSet.Builder<String> implements UPSsCollection {
    {
        //
        // ~ common
        //
        add(UPSs.INFRA_CORE);
        add(UPSs.INFRA_SERVER);
        add(UPSs.REDIS);
        add(UPSs.UPS_GCV);

        //
        //  engagement specific
        //
        add(ENGAGEMENT_CRM_POSTGRES_OWNER);
        add(ENGAGEMENT_CRM_POSTGRES_APP);
        add(ENGAGEMENT_CRM_QUARTZ_APP);
        add(UPS_CLOUDFLARE_STREAM);
        add(UPS_CLOUDFLARE_WEBHOOK);
        add(UPS_CLOUDFLARE_TURN);
    }
}

