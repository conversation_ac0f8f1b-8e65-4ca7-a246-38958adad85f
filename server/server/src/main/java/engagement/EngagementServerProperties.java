package engagement;

import com.google.common.collect.Range;
import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.cfg.DynamicPropertyFactory;
import com.turbospaces.cfg.FragmentProperties;

import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import loyalty.LoyaltyServerProperties;
import offerchain.OfferChainServerProperties;
import quest.QuestServerProperties;
import randomreward.RandomRewardServerProperties;
import reward.RewardServerProperties;

import java.time.Duration;
import java.util.List;

public class EngagementServerProperties extends ApplicationProperties {

    public final Property<Integer> REQUEST_REPLY_TIMEOUT;
    public final Property<Boolean> GAME_ROUND_EVENT_LISTENING_ENABLED;

    public final Property<String> PARTITIONING_DEFAULT_QUEST_PRECISION;
    public final Property<Integer> PARTITIONING_DEFAULT_QUEST_SCALE;

    public final Property<Integer> CACHE_SERVICE_CONNECTION_MIN_IDLE_SIZE;
    public final Property<Integer> CACHE_PROCESSING_THREADS_MIN_SIZE;
    public final Property<Integer> CACHE_PROCESSING_THREADS_MAX_SIZE;
    public final Property<Duration> CACHE_PROCESSING_THREADS_MAX_IDLE;

    public final Property<Boolean> GET_CLOUDFLARE_TURN_KEY_JOB_ENABLED;
    public final Property<Boolean> GET_CLOUDFLARE_TURN_KEY_JOB_RUN_ON_START;
    public final Property<Integer> GET_CLOUDFLARE_TURN_KEY_JOB_FREQ_SPEC_HOURS;
    public final Property<Integer> GET_CLOUDFLARE_TURN_KEY_JOB_REDIS_TTL_HOURS;

    public final Property<Integer> GAME_ROUND_EVENT_REPLICATION_THREADS_MIN_SIZE;
    public final Property<Integer> GAME_ROUND_EVENT_REPLICATION_THREADS_MAX_SIZE;
    public final Property<Duration> GAME_ROUND_EVENT_REPLICATION_THREADS_MAX_IDLE;

    public final Property<Boolean> GRAPHYTE_BREAKER_ENABLED;
    public final Property<CircuitBreakerConfig.SlidingWindowType> GRAPHYTE_BREAKER_SLIDING_WINDOW_TYPE;
    public final Property<Integer> GRAPHYTE_BREAKER_SLIDING_WINDOW_SIZE;
    public final Property<Duration> GRAPHYTE_BREAKER_CALLS_DURATION_THRESHOLD;
    public final Property<Float> GRAPHYTE_BREAKER_CALLS_RATE_THRESHOLD;
    public final Property<Integer> GRAPHYTE_BREAKER_NUMBER_OF_CALLS_IN_HALF_OPEN_STATE;
    public final Property<Duration> GRAPHYTE_BREAKER_DURATION_IN_OPEN_STATE;

    public final RewardServerProperties rewardServerProperties;
    public final RandomRewardServerProperties randomRewardServerProperties;
    public final LoyaltyServerProperties loyaltyServerProperties;
    public final QuestServerProperties questServerProperties;
    public final OfferChainServerProperties offerChainServerProperties;

    public EngagementServerProperties(DynamicPropertyFactory pf) {
        super(pf);
        this.rewardServerProperties = new RewardServerProperties(pf);
        this.randomRewardServerProperties = new RandomRewardServerProperties(pf);
        this.loyaltyServerProperties = new LoyaltyServerProperties(pf);
        this.questServerProperties = new QuestServerProperties(pf);
        this.offerChainServerProperties = new OfferChainServerProperties(pf);

        REQUEST_REPLY_TIMEOUT = pf.get("request-reply.timeout", int.class).orElse(60);
        GAME_ROUND_EVENT_LISTENING_ENABLED = pf.get("game-round-event.listening.enabled", boolean.class).orElse(false);

        PARTITIONING_DEFAULT_QUEST_PRECISION = pf.get("partitioning.default.quest.precision", String.class).orElse("month");
        PARTITIONING_DEFAULT_QUEST_SCALE = pf.get("partitioning.default.quest.scale", int.class).orElse(12);

        GAME_ROUND_EVENT_REPLICATION_THREADS_MIN_SIZE = pf.get("game-round-event.replication.threads.min-size", int.class).orElse(0);
        GAME_ROUND_EVENT_REPLICATION_THREADS_MAX_SIZE = pf.get("game-round-event.replication.threads.max-size", int.class).orElse(100);
        GAME_ROUND_EVENT_REPLICATION_THREADS_MAX_IDLE = pf.rangeValue("game-round-event.replication.threads.max-idle", Duration.ofSeconds(1), Range.closed(Duration.ofSeconds(1), Duration.ofHours(1)));

        GET_CLOUDFLARE_TURN_KEY_JOB_ENABLED = pf.get("get-cloudflare-turn-key.job.enabled", boolean.class).orElse(false);
        GET_CLOUDFLARE_TURN_KEY_JOB_RUN_ON_START = pf.get("get-cloudflare-turn-key.job.run-on-start", boolean.class).orElse(false);
        GET_CLOUDFLARE_TURN_KEY_JOB_FREQ_SPEC_HOURS = pf.get("get-cloudflare-turn-key.job.freq-spec-hours", Integer.class).orElse(12);
        GET_CLOUDFLARE_TURN_KEY_JOB_REDIS_TTL_HOURS = pf.get("get-cloudflare-turn-key.job.redis-ttl-hours", Integer.class).orElse(24);

        CACHE_SERVICE_CONNECTION_MIN_IDLE_SIZE = pf.get("cache.service.connection.min.idle.size", Integer.class).orElse(1);
        CACHE_PROCESSING_THREADS_MIN_SIZE = pf.get("cache.processing.threads.min-size", int.class).orElse(0);
        CACHE_PROCESSING_THREADS_MAX_SIZE = pf.get("cache.processing.threads.max-size", int.class).orElse(100);
        CACHE_PROCESSING_THREADS_MAX_IDLE = pf.rangeValue("cache.processing.threads.max-idle", Duration.ofSeconds(1), Range.closed(Duration.ofSeconds(1), Duration.ofHours(1)));

        GRAPHYTE_BREAKER_ENABLED = pf.get("graphtyte.breaker.enabled", Boolean.class).orElse(false);
        GRAPHYTE_BREAKER_SLIDING_WINDOW_TYPE = pf.get("graphtyte.breaker.sliding-window.type", CircuitBreakerConfig.SlidingWindowType.class)
                .orElse(CircuitBreakerConfig.SlidingWindowType.COUNT_BASED);
        GRAPHYTE_BREAKER_SLIDING_WINDOW_SIZE = pf.get("graphtyte.breaker.sliding-window.size", Integer.class).orElse(10);
        GRAPHYTE_BREAKER_CALLS_DURATION_THRESHOLD = pf.get("graphtyte.breaker.cal.duration.threshold", Duration.class).orElse(Duration.ofSeconds(4));
        GRAPHYTE_BREAKER_CALLS_RATE_THRESHOLD = pf.get("graphtyte.breaker.call.rate.threshold", Float.class).orElse(60f);
        GRAPHYTE_BREAKER_NUMBER_OF_CALLS_IN_HALF_OPEN_STATE = pf.get("graphtyte.breaker.number-of-call-in.half-open-state", Integer.class).orElse(4);
        GRAPHYTE_BREAKER_DURATION_IN_OPEN_STATE = pf.get("graphtyte.breaker.duration.in.open-state", Duration.class).orElse(Duration.ofSeconds(10));

    }

    @Override
    public List<FragmentProperties> fragments() {
        return List.of(rewardServerProperties,
                loyaltyServerProperties,
                questServerProperties,
                offerChainServerProperties,
                randomRewardServerProperties);
    }
}
