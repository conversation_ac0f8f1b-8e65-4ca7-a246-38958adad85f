package engagement.di;

import java.security.KeyStore;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.core.MicrometerConsumerListener;
import org.springframework.kafka.support.SendResult;

import com.codahale.metrics.health.HealthCheckRegistry;
import com.turbospaces.api.StackTracer;
import com.turbospaces.api.TopicRegistry;
import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.dispatch.TransactionalRequestHandler;
import com.turbospaces.ebean.CacheManager;
import com.turbospaces.ebean.EbeanJpaManager;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.kafka.consumer.BroadcastKafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaConsumerProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.consumer.KafkaRequestConsumer;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.netty.AdminHttpChannel;
import com.turbospaces.redis.RedisClientFactoryBean;
import com.turbospaces.redis.RedisPostTemplate;
import com.turbospaces.rpc.DefaultNonPersistentPostTemplate;
import com.turbospaces.rpc.DefaultPostTemplate;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import engagement.EngagementServerProperties;
import engagement.EngagementTopics;
import gateway.DefaultRequestReplyHttpChannel;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.composite.CompositeMeterRegistry;
import io.opentracing.Tracer;

@Configuration
public class EngagementAcceptorsDiModule {
    @Bean
    public AdminHttpChannel adminChannel(
            ApplicationProperties props,
            CommonObjectMapper mapper,
            CompositeMeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry) {
        return new AdminHttpChannel(props, mapper, meterRegistry, healthCheckRegistry, props.CLOUD_APP_PORT.get());
    }

    @Bean
    public DefaultRequestReplyHttpChannel requestReplyEndpoint(
            EngagementServerProperties props,
            DefaultRequestReplyMapper reqReplyMapper,
            MeterRegistry meterRegistry,
            HealthCheckRegistry healthCheckRegistry,
            CommonObjectMapper mapper,
            ApiFactory apiFactory,
            RedisClientFactoryBean factory) throws Exception {
        return new DefaultRequestReplyHttpChannel(
                props,
                reqReplyMapper,
                meterRegistry,
                apiFactory,
                healthCheckRegistry,
                mapper,
                factory.getObject(),
                EngagementTopics.RESP);
    }

    @Bean
    public MicrometerConsumerListener<byte[], byte[]> consumerMetricsListener(MeterRegistry meterRegistry) {
        return new MicrometerConsumerListener<>(meterRegistry);
    }

    @Bean
    public KafkaConsumerProperties kafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
        KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        return new KafkaConsumerProperties(props, keyStore, si);
    }

    @Bean
    public BroadcastKafkaConsumerProperties broadcastKafkaConsumerProperties(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
        KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        return new BroadcastKafkaConsumerProperties(props, keyStore, si);
    }

    @Bean
    public DefaultNonPersistentPostTemplate<?> nonPersistentPostTemplate(
            ApplicationProperties props,
            ApiFactory apiFactory,
            RedisClientFactoryBean factory) throws Exception {
        return new DefaultNonPersistentPostTemplate<>(props, apiFactory, new RedisPostTemplate(props, apiFactory, factory.getObject()));
    }

    @Bean
    @Primary
    public DefaultPostTemplate<SendResult<byte[], byte[]>> primaryPostTemplate(
            ApplicationProperties props,
            ApiFactory apiFactory,
            KafkaPostTemplate kafkaPostTemplate,
            DefaultNonPersistentPostTemplate<SendResult<byte[], byte[]>> nonPersistent) {
        return new DefaultPostTemplate<>(props, apiFactory, kafkaPostTemplate, nonPersistent);
    }

    @Bean
    public KafkaRequestConsumer kafkaRequestConsumer(
            ApplicationProperties props,
            Tracer tracer,
            MeterRegistry meterRegistry,
            CacheManager cacheManager,
            TopicRegistry topicRegistry,
            DefaultPostTemplate<SendResult<byte[], byte[]>> template,
            List<TransactionalRequestHandler<?, ?>> handlers,
            KafkaContextWorkerFactory workerFactory,
            ApiFactory apiFactory,
            List<StackTracer> stackTracers,
            List<EbeanJpaManager> ebean) {
        return new KafkaRequestConsumer(
                props,
                tracer,
                meterRegistry,
                cacheManager,
                topicRegistry,
                template,
                handlers,
                workerFactory,
                apiFactory,
                stackTracers::iterator,
                new CurrentTransactionProvider() {
                    @Override
                    public Optional<Object> current() {
                        for (var it : ebean) {
                            if (Objects.nonNull(it.currentServerTransaction())) {
                                return Optional.of(it.currentServerTransaction());
                            }
                        }
                        return Optional.empty();
                    }
                });
    }

}
