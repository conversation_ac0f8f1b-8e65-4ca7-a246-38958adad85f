package engagement.di;

import java.security.KeyStore;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.MicrometerProducerListener;

import com.turbospaces.api.jpa.CurrentTransactionProvider;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.kafka.consumer.KafkaContextWorkerFactory;
import com.turbospaces.kafka.producer.KafkaPostTemplate;
import com.turbospaces.kafka.producer.KafkaProducerProperties;
import com.turbospaces.kafka.producer.KafkaWithMetricsProducerFactory;
import com.turbospaces.rpc.DefaultRequestReplyMapper;
import com.turbospaces.ups.KafkaServiceInfo;
import com.turbospaces.ups.UPSs;

import api.v1.ApiFactory;
import engagement.EngagementServerProperties;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.opentracing.Tracer;

@Configuration
public class EngagementKafkaDiModule {
    @Bean
    public DefaultRequestReplyMapper requestReplyMapper(ApplicationProperties props, MeterRegistry meterRegistry) {
        return new DefaultRequestReplyMapper(props, meterRegistry);
    }

    @Bean
    public KafkaContextWorkerFactory contextWorkerFactory(
            List<CurrentTransactionProvider> providers,
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry) {
        return new KafkaContextWorkerFactory(new CurrentTransactionProvider() {
            @Override
            public Optional<Object> current() {
                for (var it : providers) {
                    if (Objects.nonNull(it.current())) {
                        return Optional.of(it.current());
                    }
                }
                return Optional.empty();
            }
        }, props, meterRegistry, rateLimiterRegistry);
    }

    @Bean
    public KafkaProducerProperties producerProps(ApplicationProperties props, KeyStore keyStore, DynamicCloud cloud) {
        KafkaServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.KAFKA);
        return new KafkaProducerProperties(props, keyStore, si);
    }

    @Bean
    public MicrometerProducerListener<byte[], byte[]> producerMetricsListener(MeterRegistry meterRegistry) {
        return new MicrometerProducerListener<>(meterRegistry);
    }

    @Bean
    public KafkaWithMetricsProducerFactory producerFactory(KafkaProducerProperties configs, MicrometerProducerListener<byte[], byte[]> metrics) {
        return new KafkaWithMetricsProducerFactory(configs, metrics);
    }

    @Bean
    public KafkaPostTemplate kafkaTemplate(
            EngagementServerProperties props,
            Tracer tracer,
            ApiFactory apiFactory,
            DefaultRequestReplyMapper mapper,
            KafkaWithMetricsProducerFactory producerFactory) throws Exception {
        return new KafkaPostTemplate(props, tracer, apiFactory, mapper, producerFactory, props.REQUEST_REPLY_TIMEOUT.map(Duration::ofSeconds));
    }

}
