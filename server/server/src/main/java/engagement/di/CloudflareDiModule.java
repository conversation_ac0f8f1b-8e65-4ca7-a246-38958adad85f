package engagement.di;

import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.json.CommonObjectMapper;

import engagement.cloudflare.CloudflareApiJaxRsApi;
import engagement.cloudflare.CloudflareService;
import engagement.cloudflare.CloudflareServiceImpl;
import engagement.cloudflare.CloudflareTurnApiJaxRsApi;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

@Configuration
public class CloudflareDiModule {
    @Bean
    public CloudflareApiJaxRsApi cloudflareApi(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpClient factory,
            CommonObjectMapper mapper) {
        return new CloudflareApiJaxRsApi(props, meterRegistry, rateLimiterRegistry, factory, mapper);
    }

    @Bean
    public CloudflareTurnApiJaxRsApi cloudflareTurnApi(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpClient factory,
            CommonObjectMapper mapper) {
        return new CloudflareTurnApiJaxRsApi(props, meterRegistry, rateLimiterRegistry, factory, mapper);
    }

    @Bean
    public CloudflareService cloudflareService(
            DynamicCloud cloud,
            CloudflareApiJaxRsApi cloudflareApi,
            CloudflareTurnApiJaxRsApi cloudflareTurnApi
    ) {
        return new CloudflareServiceImpl(cloud, cloudflareApi, cloudflareTurnApi);
    }
}
