package engagement.di;

import api.v1.ApiFactory;
import com.turbospaces.api.MutableNonPersistentReplyTopic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.rpc.QueuePostTemplate;

import loyalty.api.DefaultLoyaltyServiceApi;
import loyalty.api.LoyaltyServiceApi;
import offerchain.api.DefaultOfferchainServiceApi;
import engagement.DefaultQuestlineServiceApi;
import reward.DefaultRewardServiceApi;
import offerchain.api.OfferchainServiceApi;
import engagement.EngagementTopics;
import engagement.QuestlineServiceApi;
import reward.RewardServiceApi;
import org.springframework.cloud.DynamicCloud;
import org.springframework.context.annotation.Bean;

import gamehub.DefaultGameHubServiceApi;
import gamehub.GameHubServiceApi;
import payment.api.DefaultPaymentServiceApi;
import payment.api.PaymentServiceApi;
import uam.api.DefaultUamServiceApi;
import uam.api.UamServiceApi;

public class EngagementServiceApiDiModule {
    @Bean
    public RewardServiceApi rewardServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultRewardServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, EngagementTopics.RESP));
    }

    @Bean
    public OfferchainServiceApi offerChainServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultOfferchainServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, EngagementTopics.RESP));
    }

    @Bean
    public QuestlineServiceApi questlineSeviceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultQuestlineServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, EngagementTopics.RESP));
    }

    @Bean
    public LoyaltyServiceApi loyaltyServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultLoyaltyServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, EngagementTopics.RESP));
    }

    @Bean
    public UamServiceApi uamServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultUamServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, EngagementTopics.RESP));
    }

    @Bean
    public GameHubServiceApi gameHubServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultGameHubServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, EngagementTopics.RESP));
    }

    @Bean
    public PaymentServiceApi paymentServiceApi(ApplicationProperties props, DynamicCloud cloud, QueuePostTemplate<?> postTemplate, ApiFactory apiFactory) {
        return new DefaultPaymentServiceApi(props, postTemplate, apiFactory, new MutableNonPersistentReplyTopic(props, cloud, EngagementTopics.RESP));
    }
}
