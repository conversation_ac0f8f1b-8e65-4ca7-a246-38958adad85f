package engagement.di;

import java.time.Clock;
import java.util.ArrayList;
import java.util.List;

import engagement.QuestTopics;
import loyalty.api.LoyaltyTopics;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

import com.turbospaces.api.AcceptorsTopicRegistry;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.DefaultPlatformExecutorService;
import com.turbospaces.executor.PlatformExecutorService;
import com.turbospaces.executor.ThreadPoolContextWorker;
import com.turbospaces.http.CloseableHttpAsyncClientFactoryBean;
import com.turbospaces.http.CloseableHttpClientBuilderProvider;
import com.turbospaces.http.CloseableHttpClientFactoryBean;
import com.turbospaces.http.PoolingHttpClientConnectionManagerProvider;
import com.turbospaces.json.CommonObjectMapper;
import com.turbospaces.support.TimedConfiguration;

import api.DefaultApiFactory;
import api.v1.ApiFactory;
import io.micrometer.core.instrument.MeterRegistry;
import offerchain.api.OfferChainTopics;
import randomreward.api.RandomRewardTopics;
import reward.RewardTopics;

@Import({ TimedConfiguration.class })
public class CommonEngagementDiModule {

    @Bean
    public Clock clock() {
        return Clock.systemUTC();
    }

    @Bean
    public PoolingHttpClientConnectionManagerProvider clientConnectionManagerProvider(ApplicationProperties props) {
        return new PoolingHttpClientConnectionManagerProvider(props);
    }

    @Bean
    public CloseableHttpClientBuilderProvider closeableHttpClientBuilder(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            PoolingHttpClientConnectionManagerProvider connectionManager) {
        return new CloseableHttpClientBuilderProvider(props, meterRegistry, connectionManager);
    }

    @Bean
    public CloseableHttpClientFactoryBean closeableHttpClient(
            MeterRegistry meterRegistry,
            PoolingHttpClientConnectionManagerProvider connectionManager,
            CloseableHttpClientBuilderProvider builder) {
        return new CloseableHttpClientFactoryBean(meterRegistry, connectionManager, builder);
    }

    @Bean
    public CloseableHttpAsyncClientFactoryBean closeableHttpAsyncClient(ApplicationProperties props, MeterRegistry meterRegistry) throws Exception {
        return new CloseableHttpAsyncClientFactoryBean(props, meterRegistry);
    }

    @Bean
    public CommonObjectMapper objectMapper() {
        return new CommonObjectMapper();
    }

    @Bean
    public ApiFactory apiFactory(ApplicationProperties props, CommonObjectMapper mapper) {
        return new DefaultApiFactory(props, mapper);
    }

    @Bean
    public AcceptorsTopicRegistry acceptorTopicRegistry() throws Exception {
        var loyaltyTopics = new AcceptorsTopicRegistry(LoyaltyTopics.class);
        var rewardTopics = new AcceptorsTopicRegistry(RewardTopics.class);
        var offerChainTopics = new AcceptorsTopicRegistry(OfferChainTopics.class);
        var questTopics = new AcceptorsTopicRegistry(QuestTopics.class);
        var randomRewardTopics = new AcceptorsTopicRegistry(RandomRewardTopics.class);

        List<Topic> allTopics = new ArrayList<>();
        loyaltyTopics.iterator().forEachRemaining(allTopics::add);
        rewardTopics.iterator().forEachRemaining(allTopics::add);
        offerChainTopics.iterator().forEachRemaining(allTopics::add);
        questTopics.iterator().forEachRemaining(allTopics::add);
        randomRewardTopics.iterator().forEachRemaining(allTopics::add);

        return new AcceptorsTopicRegistry(allTopics);
    }

    @Bean
    public PlatformExecutorService defaultPlatformExecutorService(ApplicationProperties props, MeterRegistry meterRegistry) {
        return new DefaultPlatformExecutorService(props, meterRegistry);
    }

    @Bean
    public ThreadPoolContextWorker defaultThreadPoolContextWorker(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            PlatformExecutorService defaultPlatformExecutorService) {
        return new ThreadPoolContextWorker(props, meterRegistry, defaultPlatformExecutorService);
    }
}
