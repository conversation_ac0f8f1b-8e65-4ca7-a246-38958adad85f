package engagement.di;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;

@Configuration
@Import({
        CommonEngagementDiModule.class,
        EngagementDatabaseDiModule.class,
        EngagementKafkaDiModule.class,
        EngagementServiceApiDiModule.class,
        EngagementAcceptorsDiModule.class,
        CloudflareDiModule.class
})
@EnableKafka
public class EngagementServerDiModule {
}
