package engagement.di;

import org.springframework.cloud.DynamicCloud;
import org.springframework.cloud.service.common.RedisServiceInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.ebean.RedisCacheManagerFactoryBean;
import com.turbospaces.redis.RedisClientFactoryBean;
import com.turbospaces.ups.UPSs;

import ebean.DefaultEncryptKeyManager;
import io.ebean.config.EncryptKeyManager;
import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

@Configuration
public class EngagementDatabaseDiModule {

    @Bean
    public RedisClientFactoryBean redissonClient(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            DynamicCloud cloud) {
        RedisServiceInfo si = UPSs.findRequiredServiceInfoByName(cloud, UPSs.REDIS);
        return new RedisClientFactoryBean(props, meterRegistry, si);
    }

    @Bean
    public EncryptKeyManager encryptKeyManager(ApplicationProperties props, DynamicCloud cloud, MeterRegistry meterRegistry) {
        return new DefaultEncryptKeyManager(props, cloud, meterRegistry);
    }

    @Bean
    public RedisCacheManagerFactoryBean cacheManager(
            ApplicationProperties props,
            MeterRegistry meterRegistry,
            RateLimiterRegistry rateLimiterRegistry,
            RedisClientFactoryBean factory) throws Exception {
        return new RedisCacheManagerFactoryBean(props, meterRegistry, rateLimiterRegistry, factory.getObject());
    }
}
