package engagement.di;

import engagement.EngagementServerProperties;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GraphyteBreakerDiModule {
    @Bean("graphyteCircuitBreakerConfig")
    public CircuitBreakerConfig graphyteCircuitBreakerConfig(EngagementServerProperties props) {
        return CircuitBreakerConfig.custom()
                .slidingWindowType(props.GRAPHYTE_BREAKER_SLIDING_WINDOW_TYPE.get())
                .slidingWindowSize(props.GRAPHYTE_BREAKER_SLIDING_WINDOW_SIZE.get())
                .slowCallDurationThreshold(props.GRAPHYTE_BREAKER_CALLS_DURATION_THRESHOLD.get())
                .slowCallRateThreshold(props.GRAPHYTE_BREAKER_CALLS_RATE_THRESHOLD.get())
                .permittedNumberOfCallsInHalfOpenState(props.GRAPHYTE_BREAKER_NUMBER_OF_CALLS_IN_HALF_OPEN_STATE.get())
                .waitDurationInOpenState(props.GRAPHYTE_BREAKER_DURATION_IN_OPEN_STATE.get())
                .build();
    }

    @Bean
    public CircuitBreaker graphyteCheckForFraudCircuitBreaker(CircuitBreakerRegistry registry, CircuitBreakerConfig config) {
        registry.addConfiguration("graphyteCheckForFraudCircuitBreakerConfig", config);
        return registry.circuitBreaker("graphyteCheckForFraudCircuitBreaker", config);
    }
}
