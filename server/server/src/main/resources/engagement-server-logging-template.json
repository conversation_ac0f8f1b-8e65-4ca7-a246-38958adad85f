{"index_patterns": ["logging-engagement-server-*"], "settings": {"number_of_shards": 5, "number_of_replicas": 0, "auto_expand_replicas": false, "index.translog.durability": "async", "index.translog.sync_interval": "15s", "refresh_interval": "15s", "index": {"sort.field": ["@timestamp", "@sequence"], "sort.order": ["asc", "asc"]}, "analysis": {"filter": {"edge_ngram_filter": {"type": "edge_ngram", "min_gram": 5, "max_gram": 10}}, "analyzer": {"edge_ngram_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "edge_ngram_filter"]}}}}, "mappings": {"properties": {"@timestamp": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}, "@sequence": {"type": "long"}, "host": {"type": "keyword"}, "slot": {"type": "keyword"}, "release": {"type": "keyword"}, "level": {"type": "keyword"}, "thread": {"type": "keyword"}, "logger": {"type": "keyword"}, "message": {"type": "object", "enabled": false}, "stacktrace": {"type": "object", "enabled": false}, "operation": {"type": "keyword"}, "messageId": {"type": "keyword"}, "path": {"type": "keyword"}, "traceId": {"type": "keyword"}, "transactionId": {"type": "keyword"}, "routingKey": {"type": "keyword"}, "partition": {"type": "short"}, "offset": {"type": "long"}, "batchId": {"type": "keyword"}, "batchTook": {"type": "long"}, "took": {"type": "long"}, "error": {"type": "keyword"}, "errorCode": {"type": "keyword"}}}}