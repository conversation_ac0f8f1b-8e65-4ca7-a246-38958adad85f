package com.turbospaces.http;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.concurrent.TimeUnit;

import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.reactor.IOReactorException;

import com.turbospaces.cfg.ApplicationProperties;

import jakarta.inject.Provider;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class PoolingHttpAsyncClientConnectionManagerProvider implements Provider<PoolingNHttpClientConnectionManager> {
    private final ApplicationProperties props;

    @Override
    public PoolingNHttpClientConnectionManager get() {
        // Calculate appropriate IO thread count for async HTTP operations under high load
        // Default NETTY_ACCEPTOR_POOL_SIZE (4) is too small for high-throughput scenarios like FlowsSink
        int ioThreadCount;
        if (props.HTTP_ASYNC_IO_THREAD_COUNT.get() > 0) {
            ioThreadCount = props.HTTP_ASYNC_IO_THREAD_COUNT.get();
        } else {
            // Auto-calculate: use at least 16 threads for high-throughput scenarios
            // Scale with CPU cores but ensure minimum capacity for Kafka message processing
            int cpuCores = Runtime.getRuntime().availableProcessors();
            ioThreadCount = Math.max(16, Math.min(64, cpuCores * 8));
        }

        IOReactorConfig ioReactorConfig = IOReactorConfig.custom()
                .setIoThreadCount(ioThreadCount)
                .setConnectTimeout((int) props.TCP_CONNECTION_TIMEOUT.get().toMillis())
                .setSoTimeout((int) props.TCP_SOCKET_TIMEOUT.get().toMillis())
                .setSoKeepAlive(props.TCP_KEEP_ALIVE.get())
                .setTcpNoDelay(props.TCP_NO_DELAY.get())
                .setSoReuseAddress(props.TCP_REUSE_ADDRESS.get())
                .build();

        try {
            PoolingNHttpClientConnectionManager connectionManager = new PoolingNHttpClientConnectionManager(new DefaultConnectingIOReactor(ioReactorConfig));
            connectionManager.setDefaultMaxPerRoute(props.HTTP_POOL_MAX_PER_ROUTE.get());
            connectionManager.setMaxTotal(props.HTTP_POOL_MAX_SIZE.get());

            //
            // ~ https://stackoverflow.com/questions/70175836/apache-httpclient-throws-java-net-socketexception-connection-reset-if-i-use-it
            //
            if (props.TCP_CONNECTION_CLOSE_IDLE_IMMEDIATELY.get()) {
                connectionManager.closeIdleConnections(0, TimeUnit.MICROSECONDS);
            }

            return connectionManager;
        } catch (IOReactorException err) {
            throw new UndeclaredThrowableException(err);
        }
    }
}
