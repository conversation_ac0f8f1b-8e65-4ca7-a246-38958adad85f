package com.turbospaces.http;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.config.AbstractFactoryBean;

import com.netflix.archaius.api.Property;
import com.turbospaces.cfg.ApplicationProperties;

import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Provider;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CloseableHttpAsyncClientFactoryBean extends AbstractFactoryBean<CloseableHttpAsyncClient> implements BeanNameAware {
    private final MeterRegistry meterRegistry;
    private final Provider<PoolingNHttpClientConnectionManager> connectionManager;
    private final Provider<HttpAsyncClientBuilder> builder;
    private String beanName;

    public CloseableHttpAsyncClientFactoryBean(ApplicationProperties props, MeterRegistry meterRegistry) throws Exception {
        this.connectionManager = new PoolingHttpAsyncClientConnectionManagerProvider(props);
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.builder = new CloseableHttpAsyncClientBuilderProvider(props, meterRegistry, connectionManager);
        setSingleton(true);
    }
    public CloseableHttpAsyncClientFactoryBean(
            MeterRegistry meterRegistry,
            Provider<PoolingNHttpClientConnectionManager> connectionManager,
            Provider<HttpAsyncClientBuilder> builder) {
        this.meterRegistry = Objects.requireNonNull(meterRegistry);
        this.connectionManager = Objects.requireNonNull(connectionManager);
        this.builder = Objects.requireNonNull(builder);
        setSingleton(true);
    }
    @Override
    public void setBeanName(String name) {
        this.beanName = name;
    }
    @Override
    public Class<?> getObjectType() {
        return CloseableHttpAsyncClient.class;
    }
    @Override
    @SuppressWarnings("deprecation")
    protected CloseableHttpAsyncClient createInstance() throws Exception {
        CloseableHttpAsyncClient toReturn = builder.get().build();
        toReturn.start();

        if (StringUtils.isNotEmpty(beanName)) {
            new io.micrometer.core.instrument.binder.httpcomponents.PoolingHttpClientConnectionManagerMetricsBinder(connectionManager.get(), "httpclient-" + beanName).bindTo(meterRegistry);
        }

        return toReturn;
    }
    @Override
    protected void destroyInstance(CloseableHttpAsyncClient instance) throws Exception {
        if (instance != null) {
            instance.close();
        }
    }

    public static ConnectionKeepAliveStrategy keepAliveStrategy(ApplicationProperties props) {
        return new DefaultConnectionKeepAliveStrategy() {
            @Override
            public long getKeepAliveDuration(HttpResponse response, HttpContext context) {
                long keepAliveDuration = super.getKeepAliveDuration(response, context);
                if (keepAliveDuration <= 0) {
                    keepAliveDuration = props.TCP_KEEP_ALIVE_TIMEOUT.get().toMillis();
                }
                return keepAliveDuration;
            }
        };
    }
    public static RequestConfig.Builder requestConfig(ApplicationProperties props) {
        return requestConfig(props.TCP_CONNECTION_TIMEOUT, props.TCP_SOCKET_TIMEOUT);
    }
    public static RequestConfig.Builder requestConfig(long connectTimeout, long soTimeout) {
        int connectionTimeout = (int) TimeUnit.SECONDS.toMillis(connectTimeout);
        int socketTimeout = (int) TimeUnit.SECONDS.toMillis(soTimeout);

        RequestConfig.Builder requestCfg = RequestConfig.custom();
        requestCfg.setConnectTimeout(connectionTimeout);
        requestCfg.setSocketTimeout(socketTimeout);
//        requestCfg.setConnectionRequestTimeout(connectionTimeout); // Timeout for getting connection from pool
        requestCfg.setCookieSpec(CookieSpecs.STANDARD);

        return requestCfg;
    }
    public static RequestConfig.Builder requestConfig(Supplier<Integer> connectTimeout, Supplier<Integer> soTimeout) {
        return requestConfig(connectTimeout.get(), soTimeout.get());
    }
    public static RequestConfig.Builder requestConfig(Property<Duration> connectTimeout, Property<Duration> soTimeout) {
        int connectTimeoutSecs = (int) connectTimeout.get().toSeconds();
        int soTimeoutSecs = (int) soTimeout.get().toSeconds();
        return requestConfig(connectTimeoutSecs, soTimeoutSecs);
    }

    /**
     * Removes {@code Content-Length} and
     * {@code Transfer-Encoding} headers from the request if present 'SOAPAction' header.
     * Necessary, because some SAAJ and other SOAP implementations set
     * these headers themselves, and HttpClient throws an exception if they have been set.
     */
    public static class RemoveSoapHeadersInterceptor implements HttpRequestInterceptor {
        @Override
        public void process(HttpRequest request, HttpContext context) {
            //
            // ~ Second condition is not required, but added for double check, these headers will be removed and added by HttpClient later.
            //
            if (request instanceof HttpEntityEnclosingRequest && request.getFirstHeader("SOAPAction") != null) {
                if (request.containsHeader(HTTP.TRANSFER_ENCODING)) {
                    request.removeHeaders(HTTP.TRANSFER_ENCODING);
                }
                if (request.containsHeader(HTTP.CONTENT_LEN)) {
                    request.removeHeaders(HTTP.CONTENT_LEN);
                }
            }
        }
    }
}
