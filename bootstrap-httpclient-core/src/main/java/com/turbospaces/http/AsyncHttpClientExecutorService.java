package com.turbospaces.http;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.executor.AbstractPlatformExecutorService;
import com.turbospaces.executor.PlatformThread;

import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

/**
 * Dedicated executor service for async HTTP client operations.
 * Uses a bounded queue instead of SynchronousQueue to handle high-throughput scenarios.
 */
@Slf4j
public class AsyncHttpClientExecutorService extends AbstractPlatformExecutorService {
    
    public AsyncHttpClientExecutorService(ApplicationProperties props, MeterRegistry meterRegistry) {
        super(props, meterRegistry);
        
        // Use a bounded queue for better handling of high-throughput scenarios
        int corePoolSize = Math.max(16, props.APP_PLATFORM_MIN_SIZE.get());
        int maxPoolSize = Math.max(64, props.APP_PLATFORM_MAX_SIZE.get() * 2);
        int queueCapacity = maxPoolSize * 4; // Allow some queuing
        
        ThreadFactoryBuilder factory = new ThreadFactoryBuilder()
                .setDaemon(false)
                .setNameFormat("async-http-client-%d")
                .setThreadFactory(r -> new PlatformThread(props, r));
        
        executor = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                props.APP_PLATFORM_MAX_IDLE.get().toSeconds(),
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(queueCapacity),
                factory.build(),
                new ThreadPoolExecutor.CallerRunsPolicy() // Fallback to caller thread instead of rejecting
        );
        
        log.info("AsyncHttpClientExecutorService created with core={}, max={}, queue={}", 
                corePoolSize, maxPoolSize, queueCapacity);
    }
}
