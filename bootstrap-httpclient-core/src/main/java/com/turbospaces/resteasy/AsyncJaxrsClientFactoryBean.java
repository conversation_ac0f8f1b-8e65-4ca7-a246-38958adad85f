package com.turbospaces.resteasy;

import java.util.Objects;

import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;

import com.turbospaces.cfg.ApplicationProperties;
import com.turbospaces.http.AsyncHttpClientExecutorService;

import io.github.resilience4j.ratelimiter.RateLimiterRegistry;
import io.micrometer.core.instrument.MeterRegistry;

/**
 * Specialized JAX-RS client factory for async HTTP clients.
 * Uses AsyncHttpClientExecutorService instead of DefaultPlatformExecutorService
 * to handle high-throughput scenarios like FlowsSink processing millions of Kafka messages.
 */
public class AsyncJaxrsClientFactoryBean extends JaxrsClientFactoryBean {
    
    public AsyncJaxrsClientFactoryBean(
            ApplicationProperties props, 
            RateLimiterRegistry rateLimiterRegistry,
            CloseableHttpAsyncClient httpClient, 
            MeterRegistry meterRegistry) {
        super(props, rateLimiterRegistry, httpClient, new AsyncHttpClientExecutorService(props, meterRegistry));
    }
}
