package randomreward.worker;

import com.turbospaces.rpc.QueuePostTemplate;

import java.util.Objects;
import java.util.concurrent.CompletionStage;

import randomreward.worker.api.v1.RandomRewardBackgroundRequest;

public class DefaultRandomRewardWorkerServiceApi implements RandomRewardWorkerServiceApi {

    private final QueuePostTemplate<?> postTemplate;

    public DefaultRandomRewardWorkerServiceApi(QueuePostTemplate<?> postTemplate) {
        this.postTemplate = Objects.requireNonNull(postTemplate);
    }

    @Override
    public CompletionStage<?> processInBackground(RandomRewardBackgroundRequest request) {
        return postTemplate.sendEvent(RandomRewardWorkerTopics.BACKGROUND_REQ, request);
    }

}
