package randomreward.worker;

import java.util.concurrent.TimeUnit;

import org.apache.kafka.common.config.TopicConfig;

import com.turbospaces.api.ReplyTopic;
import com.turbospaces.api.Topic;
import com.turbospaces.cfg.ApplicationConfig;

import io.netty.util.AsciiString;
import lombok.experimental.UtilityClass;

@UtilityClass
public class RandomRewardWorkerTopics {
    public static final Topic BACKGROUND_REQ = new Topic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("random-reward-worker-background-req");
        }

        @Override
        public void configure(ApplicationConfig cfg) {
            cfg.setDefaultProperty(name() + "." + Topic.PARTITIONS, 4);
            cfg.setDefaultProperty(name() + "." + Topic.REPLICATION_FACTOR, 2);
            cfg.setDefaultProperty(name() + "." + TopicConfig.RETENTION_MS_CONFIG, TimeUnit.DAYS.toMillis(1));
            cfg.setDefaultProperty(name() + "." + TopicConfig.MAX_MESSAGE_BYTES_CONFIG, Topic.DEFAULT_MAX_MESSAGE_BYTES);
        }
    };

    public static final ReplyTopic RESP = new ReplyTopic() {
        @Override
        public AsciiString name() {
            return AsciiString.cached("random-reward-worker-server-resp");
        }
    };
}
